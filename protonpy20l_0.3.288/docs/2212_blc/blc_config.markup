h2. Conf list
h3. itp_blc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | bypass / enable, 0: bypass, 1: enable |  |
| lin_enable | u1 | [\] | - |  | lin enable, 0: disable, 1: enable |  |
| lin_lut_x | u8.4 | [15\] | - | lin_lut_x[n\] <= lin_lut_x[n+1\] | lin lut anchor in x_coordinate |  |
| lin_lut_y | u8.6 | [15, 4\] | - | lin_lut_y[n\][m\] <= lin_lut_y[n+1\][m\] | four channel lin lut anchor in y_coordinate |  |
| lin_lut_slope | u12.8 | [16, 4\] | - |  | four channel lin lut slope  |  |
| sbl_corr_enable | u1 | [\] | - |  | sbl enable, 0: disable, 1: enable |  |
| sbl_val | u8.10 | [4\] | - | all | sbl value, channel split, 0: R, 1: Gb, 2: Gr, 3: B | commonly, around sensor bl |
| gbl_corr_enable | u1 | [\] | - |  | gbl enable, 0: disable, 1: enable |  |
| gbl_val | s7.10 | [4\] | - |  | gbl value, channel split, 0: R, 1: Gb, 2: Gr, 3: B |  |
| dgain | u1.8 | [\] | - | all | support affine data | white_level - 16.0(offset_out) / white_level - black_level(min(sbl_val)) |
| dither_enable | u1 | [\] | - |  | 0: disable, 1: enable | set 1 as default |
| dither_seed_enable | u1 | [\] | - |  | 0: use stat dither_seed, 1: use conf dither_seed | set 1 for the 1st frame, then set 0 from the 2nd frame onwards |
| dither_seed | u16 | [2\] | - | [0,2^16), [0,2^15) | value of dither seed | set non 0, when dither enable |
| dither_pmask | u16 | [2\] | - | [0,2^16), [0,2^15) | dither pmask | should set properly, pMask are picked from https://git-ext.axera-tech.com/isp/xyclops/lfsr/-/tree/master/pMask 16.txt,15.txt |
| offset_out | u8.6 | [\] | - | all | offset out | 16.0 |

h3. bl_det
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | bypass / enable, 0: bypass, 1: enable |  |
| active_region | u14 | [4\] | - | <=(image_height, image_width) | active region area, in [y, x, height, width\] order |  |
| det_enable | u1 | [\] | - |  | det enable, 0: disable, 1: enable | set 1 as default |
| bpc_det_th | u8.10 | [\] | - | <=white_level | threshold for quickly checking whether the current point is a bad point |  |
| det_window | u14 | [4\] | - | <= image_size | det window, in [y, x, height, width\] order |  |

