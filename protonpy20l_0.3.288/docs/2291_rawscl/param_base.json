{"configs": {"raw_scl": {"enable": {"acc": [0, 1], "size": [], "description": "", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "crop_enable": {"acc": [0, 1], "size": [], "description": "select roi function enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "roi_size": {"acc": [0, 16], "size": [2], "description": "roi size", "usage": "[height, width]", "constraints": ">=128", "type": "AX_U16", "partition": "-"}, "roi_start": {"acc": [0, 16], "size": [2], "description": "roi location", "usage": "[y, x] of top_left_point", "constraints": ">=0", "type": "AX_U16", "partition": "-"}, "norm_value": {"acc": [0, 0, 6], "size": [2], "description": "sclae normalize value [0]: h [1]: w", "usage": "", "constraints": "norm_val[*] = 1 / ((scale_ratio[*] + 1) )", "type": "AX_U8", "partition": "-"}, "scale_enable": {"acc": [0, 1], "size": [], "description": "scale_enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "scale_ratio": {"acc": [0, 6], "size": [2], "description": "scale ratio [0]: h; [1]: w", "usage": "", "constraints": ">0; (roi_size / (scale_ratio + 1)) % 2 == 0", "type": "AX_U8", "partition": "-"}, "scale_mode": {"acc": [0, 1], "size": [], "description": "choose scale mode", "usage": "0: Average; 1: Decimation", "constraints": "0 or 1", "type": "AX_U8", "partition": "-"}, "output_acc_mode": {"acc": [0, 2], "size": [], "description": "choose ouput acc", "usage": "0: u8.4; 1: u8.2; 2: u8.0", "constraints": "0 or 1 or 2", "type": "AX_U8", "partition": "-"}}}, "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "RAWSCL is 0x2291", "type": "AX_U16"}}, "params": {"enable": {"display": "enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 1, "comment": "rawscl module enable", "hidden": 0, "auto": 0, "target_conf": ["raw_scl.enable"], "dependency": "user"}, "crop_enable": {"display": "crop_enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 0, "comment": "rawscl crop enable", "hidden": 0, "auto": 0, "target_conf": ["raw_scl.crop_enable"], "dependency": "user"}, "crop_size_height": {"display": "crop_size_height", "acc": [0, 16], "range": [128, 65535], "type": "AX_U16", "size": [], "default": 128, "comment": "rawscl roi size", "hidden": 0, "auto": 0, "target_conf": ["raw_scl.roi_size"], "dependency": "user", "constraints": ">=128"}, "crop_size_width": {"display": "crop_size_width", "acc": [0, 16], "range": [128, 65535], "type": "AX_U16", "size": [], "default": 128, "comment": "rawscl roi size", "hidden": 0, "auto": 0, "target_conf": ["raw_scl.roi_size"], "dependency": "user", "constraints": ">=128"}, "crop_start_y": {"display": "crop_start_y", "acc": [0, 16], "range": [0, 65535], "type": "AX_U16", "size": [], "default": 0, "comment": "rawscl roi start y", "hidden": 0, "auto": 0, "target_conf": ["raw_scl.roi_start"], "dependency": "user", "constraints": ">=0"}, "crop_start_x": {"display": "crop_start_x", "acc": [0, 16], "range": [0, 65535], "type": "AX_U16", "size": [], "default": 0, "comment": "rawscl roi start x", "hidden": 0, "auto": 0, "target_conf": ["raw_scl.roi_start"], "dependency": "user", "constraints": ">=0"}, "scale_enable": {"display": "scale_enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 0, "comment": "rawscl scale enable", "hidden": 0, "auto": 0, "target_conf": ["raw_scl.scale_enable"], "dependency": "user"}, "scale_ratio_height": {"display": "scale_ratio_height", "acc": [0, 7], "range": [2, 64], "type": "AX_U8", "size": [], "default": 2, "comment": "rawscl scale ratio height, 2<=ratio<=64", "hidden": 0, "auto": 0, "target_conf": ["raw_scl.scale_ratio"], "dependency": "user", "constraints": ">1"}, "scale_ratio_width": {"display": "scale_ratio_width", "acc": [0, 7], "range": [2, 64], "type": "AX_U8", "size": [], "default": 2, "comment": "rawscl scale ratio width, 2<=ratio<=64", "hidden": 0, "auto": 0, "target_conf": ["raw_scl.scale_ratio"], "dependency": "user", "constraints": ">1"}, "scale_mode": {"display": "scale_mode", "acc": [0, 1], "range": [0, 1], "enum_field": {"0": "Average", "1": "Decimation"}, "type": "AX_U8", "size": [], "default": 0, "comment": "rawscl scale mode; 0: Average; 1: Decimation", "hidden": 0, "auto": 0, "target_conf": ["raw_scl.scale_mode"], "dependency": "user"}, "output_acc_mode": {"display": "output_acc_mode", "acc": [0, 2], "range": [0, 2], "enum_field": {"0": "u8.4", "1": "u8.2", "2": "u8.0"}, "type": "AX_U8", "size": [], "default": 0, "comment": "rawscl output acc mode; 0: u8.4; 1: u8.2; 2: u8.0", "hidden": 0, "auto": 0, "target_conf": ["raw_scl.output_acc_mode"], "dependency": "user"}}, "submodules": {"setup": {"params": [], "configs": []}, "basic": {"params": ["enable", "output_acc_mode"], "configs": []}, "scale": {"params": ["scale_enable", "scale_ratio_height", "scale_ratio_width", "scale_mode"], "configs": ["raw_scl.norm_value"]}, "crop": {"params": ["crop_enable", "crop_size_height", "crop_size_width", "crop_start_y", "crop_start_x"], "configs": []}}, "target_module": {"mc20l": {"raw_scl": {"id": 7300, "method": 0}}}, "partition_configs": [], "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}}