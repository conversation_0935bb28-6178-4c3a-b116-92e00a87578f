{"ycproc_enable": {"api": "nYcprocEn", "display": "ycprocEnable", "comments": "ycproc enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "ycproc_brightness": {"api": "nYcprocBrightness", "display": "ycprocBrightness", "comments": "brightness control", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "ycproc_contrast": {"api": "nYcprocContrast", "display": "ycprocContrast", "comments": "contrast control", "hint": "Accuracy: S4.8 Range: [-4096, 4095]"}, "ycproc_hue": {"api": "nYcprocHue", "display": "ycprocHue", "comments": "hue control", "hint": "Accuracy: S0.15 Range: [-32768, 32767]"}, "ycproc_saturation": {"api": "nYcprocSat", "display": "ycprocSat", "comments": "saturation control", "hint": "Accuracy: U4.12 Range: [0, 65535]"}, "ycrt_enable": {"api": "nYcrtEnable", "display": "ycrtEnable", "comments": "0: bypass (limited range [HDMI]), 1: enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "ycrt_signal_range_mode": {"api": "nYcrtSignalRangeMode", "display": "ycrtSignalRangeMode", "comments": "0: full range, 1: limited range (survilliance)", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "ycrt_clip_level_y": {"api": "nYcrtClipLevelY", "display": "ycrtClipLevelY", "comments": "y clip level", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "ycrt_clip_level_uv": {"api": "nYcrtClipLevelUv", "display": "ycrtClipLevelUv", "comments": "uv clip level", "hint": "Accuracy: S7.0 Range: [-128, 127]"}, "hs2dlut_enable": {"api": "nHs2dlutEn", "display": "hs2dlutEnable", "comments": "", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "hs2dlut_hue_table": {"api": "nHs2dlutHueTable", "display": "hs2dlutHueTable", "comments": "", "hint": "Accuracy: U9.7 Range: [0, 46080]"}, "hs2dlut_sat_table": {"api": "nHs2dlutSatTable", "display": "hs2dlutSatTable", "comments": "", "hint": "Accuracy: U1.15 Range: [0, 32768]"}, "ccmp_enable": {"api": "nCcmpEn", "display": "ccmpEnable", "comments": "", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "ccmp_y_lut": {"api": "nCcmpYLut", "display": "ccmpYLut", "comments": "smaller value means stronger saturation compression", "hint": "Accuracy: U1.9 Range: [0, 512]"}, "ccmp_sat_lut": {"api": "nCcmpSatLut", "display": "ccmpSatLut", "comments": "smaller value means stronger saturation compression", "hint": "Accuracy: U1.9 Range: [0, 512]"}, "scm_enable": {"api": "nScmEn", "display": "scmEnable", "comments": "", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scm_inverse_selection": {"api": "nScmInvSel", "display": "scmInvSel", "comments": "0: normal selection, 1: inverse selection", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scm_src_y": {"api": "nScmSrcY", "display": "scmSrcY", "comments": "color mask center y", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "scm_src_uv": {"api": "nScmSrcUv", "display": "scmSrcUv", "comments": "color mask center uv", "hint": "Accuracy: S7.2 Range: [-512, 511]"}, "scm_radius": {"api": "nScmRadius", "display": "scmRadius", "comments": "color mask radius, [0]: y, [1]: u, [2]: v", "hint": "Accuracy: U7.2 Range: [0, 511]"}, "scm_smooth": {"api": "nScmSmooth", "display": "scmSmooth", "comments": "color mask transition band", "hint": "Accuracy: U3.0 Range: [0, 7]"}, "scm_dst_uv": {"api": "nScmDstUv", "display": "scmDstUv", "comments": "target color", "hint": "Accuracy: S7.2 Range: [-512, 511]"}}