h2. Conf list
h3. lce
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | all | lce enable, 0: disable, 1: enable | set to 1 normally and use each submodule's own enable flag |
| shp_enable | u1 | [\] | - | all | sharpen enable, 0: disable, 1: enable |  |
| shp_debug_mode | u5 | [\] | - | {0, 3 ~ 18} | sharpen debug mode, 0: normal mode, others: visualized intermediate result for debugging |  |
| shp_filter_ud | s0.12 | [3, 3\] | - | all | undirectional filter coeffs |  |
| shp_filter_dir_0 | s0.12 | [9\] | - | all | 0 directional filter coeffs |  |
| shp_filter_dir_22 | s0.12 | [13\] | - | all | 22 directional filter coeffs |  |
| shp_filter_dir_45 | s0.12 | [9\] | - | all | 45 directional filter coeffs |  |
| shp_ud_texture_shift | u3 | [\] | - | all | undirectional texture gain lut x-axis shift bits | smaller value means more texture gain control points in flat or weak texture regions |
| shp_dir_texture_shift | u3 | [\] | - | all | directional texture gain lut x-axis shift bits | smaller value means more texture gain control points in flat or weak edge regions |
| shp_ud_gain_lut | u8.4 | [33\] | - | all | undirectional texture gain lut | set undirectional sharpen gain w.r.t texture strength |
| shp_dir_gain_lut | u8.4 | [33\] | - | all | directional texture gain lut | set directional sharpen gain w.r.t texture strength |
| shp_ud_scale | u0.4 | [\] | - | all | scaling factor for ud filters | larger value means to use more undirectional filter result |
| shp_dir_scale | u0.4 | [\] | - | all | scaling factor for dir filters | larger value means to use more directional filter result |
| shp_edge_sad_noise | u8.4 | [\] | - | all | edge noise level | larger value means stronger noise level and reduce directional filter weight |
| shp_nr_ud_texture_scale | u0.8 | [\] | - | all | scaling factor for undirectional nr's texture map | larger value means more like a texture pixel (not noise pixel), and reduces undirectional nr strength |
| shp_nr_ud_texture_offset | u0.8 | [\] | - | all | coring offset for undirectional nr's texture map | larger value means stronger coring on texutre map, and increases undirectional nr strength |
| shp_nr_ud_limit | u8.2 | [\] | - | all | undirectional noise limit | larger value means less limitation for calculated undirectional noise |
| shp_nr_dir_texture_scale | u0.8 | [\] | - | all | scaling factor for directional nr's texture map | larger value means more like a edge pixel (not noise pixel), and reduces directional nr strength |
| shp_nr_dir_texture_offset | u4.4 | [\] | - | all | coring offset for directional nr's texture map | larger value means stronger coring on texutre map, and increases directional nr strength |
| shp_nr_dir_limit | u8.2 | [\] | - | all | directional noise limit | larger value means less limitation for calculated directional noise |
| shp_nr_level | u1.5 | [\] | - | shp_nr_level <= 1.0 | noise reduction level | larger value means stronger noise reduction |
| shp_coring_ud_level | u3.3 | [\] | - | all | undirectional sharpen coring level | larger value means stronger coring, and reduces undirectional sharpen strength |
| shp_coring_dir_level | u3.3 | [\] | - | all | directional sharpen coring level | larger value means stronger coring, and reduces directional sharpen strength |
| shp_shoot_ref_ratio | u1.5 | [\] | - | shp_shoot_ref_ratio <= 1.0 | the blending ratio of original pixel and local min max to generate shoot reference | larger value means to use more local min max value for shoot reference, and leads to stronger shoot |
| shp_overshoot | u0.7 | [\] | - | all | global overshoot level | larger value means stronger overshoot |
| shp_undershoot | u0.7 | [\] | - | all | global undershoot level | larger value means stronger undershoot |
| shp_motion_mask_enable | u1 | [\] | - | all | motion mask enable, 0: disable, 1: enable |  |
| shp_motion_mask_lut | u0.8 | [9\] | - | all | motion mask lut | used to clip, inverse, or remap the original motion mask |
| shp_motion_ud_gain_lut | u8.4 | [33\] | - | all | undirectional texture gain lut for motion region | set undirectional sharpen gain w.r.t texture strength for motion region |
| shp_motion_dir_gain_lut | u8.4 | [33\] | - | all | directional texture gain lut for motion region | set directional sharpen gain w.r.t texture strength for motion region |
| shp_motion_overshoot | u0.7 | [\] | - | all | overshoot level for motion region | larger value means stronger overshoot for motion region |
| shp_motion_undershoot | u0.7 | [\] | - | all | undershoot level for motion region | larger value means stronger undershoot for motion region |
| shp_luma_mask_enable | u1 | [\] | - | all | luma mask enable, 0: disable, 1: enable |  |
| shp_luma_ref_select | u1 | [\] | - | all | luma mask reference selection, 0: blurred Y, 1: denoised Y | usually set to 0 |
| shp_luma_gain_lut | u1.5 | [2, 33\] | - | all | luma gain lut, [0\]: negative, [1\]: positive | usually set lower gain for dark region to avoid enhancing noises |
| shp_detail_adj_enable | u1 | [\] | - | all | detail enhancement enable, 0: disable, 1: enable |  |
| shp_detail_texture_shift | u3 | [\] | - | all | texture map shift bits for detail region | smaller value means detail region will have more flat or weak texture pixels |
| shp_detail_overshoot_slope | s0.9 | [\] | - | all | detail overshoot slope | automatically calculated in algo logic |
| shp_detail_overshoot_offset | s4.7 | [\] | - | all | detail overshoot offset | automatically calculated in algo logic |
| shp_detail_overshoot_limit | u0.7 | [2\] | - | all | detail overshoot limit | automatically calculated in algo logic |
| shp_detail_undershoot_slope | s0.9 | [\] | - | all | detail undershoot slope | automatically calculated in algo logic |
| shp_detail_undershoot_offset | s4.7 | [\] | - | all | detail undershoot offset | automatically calculated in algo logic |
| shp_detail_undershoot_limit | u0.7 | [2\] | - | all | detail undershoot limit | automatically calculated in algo logic |
| shp_shoot_sup_enable | u1 | [\] | - | all | shoot suppression enable, 0: disable, 1: enable |  |
| shp_shoot_sup_blend_ratio | u1.4 | [\] | - | shp_shoot_sup_blend_ratio <= 1.0 | the blending ratio of two shoot control strategy | larger value means less shoot control for texture region while keeping shoot control for strong edges |
| shp_shoot_sup_var_min_slope | u0.9 | [\] | - | all | shoot control (by min variance) curve slope | automatically calculated in algo logic |
| shp_shoot_sup_var_min_offset | s4.7 | [\] | - | all | shoot control (by min variance) curve offset | automatically calculated in algo logic |
| shp_shoot_sup_var_min_limit | u0.7 | [2\] | - | all | shoot control (by min variance) curve limit | automatically calculated in algo logic |
| shp_shoot_sup_var_diff_slope | s0.9 | [\] | - | all | shoot control (by variance difference) curve slope | automatically calculated in algo logic |
| shp_shoot_sup_var_diff_offset | u4.7 | [\] | - | all | shoot control (by variance difference) curve offset | automatically calculated in algo logic |
| shp_shoot_sup_var_diff_limit | u0.7 | [2\] | - | all | shoot control (by variance difference) curve limit | automatically calculated in algo logic |
| shp_limit_sup_enable | u1 | [\] | - | all | adaptive limit control enable, 0: disable, 1: enable |  |
| shp_limit_sup_max_over_change | u8.2 | [\] | - | all | adaptive limit control max over change | larger value means less limitation for pixel value over change |
| shp_limit_sup_max_under_change | u8.2 | [\] | - | all | adaptive limit control max under change | larger value means less limitation for pixel value under change |
| shp_limit_sup_max_over_gain | u5.5 | [\] | - | all | adaptive limit control over change gain | larger value means less limitation for pixel value over change |
| shp_limit_sup_max_under_gain | u5.5 | [\] | - | all | adaptive limit control under change gain | larger value means less limitation for pixel value under change |
| shp_rgb_ctrl_enable | u1 | [\] | - | all | rgb color sharpen gain control enable, 0: disable, 1: enable |  |
| shp_rgb_ctrl_red_var5_shift | u3 | [\] | - | all | texture map shift bits for red color mask | smaller value means more red edge pixels will not be affected by red sharpen gain |
| shp_rgb_ctrl_red_center | s7.2 | [2\] | - | all | red color center uv | set as uv value of red color |
| shp_rgb_ctrl_red_radius | u8.2 | [2\] | - | all | red color radius | larger value means more pixels will be selected in red color mask |
| shp_rgb_ctrl_red_dist_mode | u1 | [\] | - | all | red color distance calculation mode, 0: diamond, 1: rectangle | set to 0 as default |
| shp_rgb_ctrl_red_dist_weight | u0.3 | [4\] | - | all | red color distance weights of 4 directions | larger value means less pixels will be selected in red color mask |
| shp_rgb_ctrl_red_gain | u1.5 | [\] | - | shp_rgb_ctrl_red_gain <= 1.0 | red color sharpen gain | larger value means stronger sharpen for red color |
| shp_rgb_ctrl_red_gain_slope | u0.9 | [\] | - | all | red color gain slope | automatically calculated in algo logic |
| shp_rgb_ctrl_red_gain_offset | s4.5 | [\] | - | all | red color gain offset | automatically calculated in algo logic |
| shp_rgb_ctrl_blue_var5_shift | u3 | [\] | - | all | texture map shift bits for blue color mask | smaller value means more blue edge pixels will not be affected by blue sharpen gain |
| shp_rgb_ctrl_blue_center | s7.2 | [2\] | - | all | blue color center uv | set as uv value of blue color |
| shp_rgb_ctrl_blue_radius | u8.2 | [2\] | - | all | blue color radius | larger value means more pixels will be selected in blue color mask |
| shp_rgb_ctrl_blue_dist_mode | u1 | [\] | - | all | blue color distance calculation mode, 0: diamond, 1: rectangle | set to 0 as default |
| shp_rgb_ctrl_blue_dist_weight | u0.3 | [4\] | - | all | blue color distance weights of 4 directions | larger value means less pixels will be selected in blue color mask |
| shp_rgb_ctrl_blue_gain | u1.5 | [\] | - | shp_rgb_ctrl_blue_gain <= 1.0 | blue color sharpen gain | larger value means stronger sharpen for blue color |
| shp_rgb_ctrl_blue_gain_slope | u0.9 | [\] | - | all | blue color gain slope | automatically calculated in algo logic |
| shp_rgb_ctrl_blue_gain_offset | s4.5 | [\] | - | all | blue color gain offset | automatically calculated in algo logic |
| shp_rgb_ctrl_green_center | s7.2 | [2\] | - | all | green color center uv | set as uv value of green color |
| shp_rgb_ctrl_green_radius | u8.2 | [2\] | - | all | green color radius | larger value means more pixels will be selected in green color mask |
| shp_rgb_ctrl_green_dist_mode | u1 | [\] | - | all | green color distance calculation mode, 0: diamond, 1: rectangle | set to 0 as default |
| shp_rgb_ctrl_green_dist_weight | u0.3 | [4\] | - | all | green color distance weights of 4 directions | larger value means less pixels will be selected in green color mask |
| shp_rgb_ctrl_green_gain | u3.5 | [\] | - | all | green color sharpen gain | larger value means stronger sharpen for green color |
| shp_rgb_ctrl_green_gain_slope | s3.9 | [\] | - | all | green color gain slope | automatically calculated in algo logic |
| shp_rgb_ctrl_green_gain_offset | s4.5 | [\] | - | all | green color gain offset | automatically calculated in algo logic |
| shp_skin_ctrl_enable | u1 | [\] | - | all | skin color sharpen gain control enable, 0: disable, 1: enable |  |
| shp_skin_ctrl_color_center | s7.2 | [2\] | - | all | skin color center uv | set as uv value of skin color |
| shp_skin_ctrl_color_radius | u8.2 | [2\] | - | all | skin color radius | larger value means more pixels will be selected in skin color mask |
| shp_skin_ctrl_color_dist_mode | u1 | [\] | - | all | red color distance calculation mode, 0: diamond, 1: rectangle | set to 1 as default |
| shp_skin_ctrl_color_dist_weight | u0.3 | [4\] | - | all | skin color distance weights of 4 directions | larger value means less pixels will be selected in skin color mask |
| shp_skin_ctrl_color_slope | u2.9 | [\] | - | all | skin color gain slope | automatically calculated in algo logic |
| shp_skin_ctrl_var5_shift | u3 | [\] | - | all | texture map shift bits for skin color mask | smaller value means more skin edge pixels will not be affected by skin sharpen gain |
| shp_skin_ctrl_var5_gain_slope | s0.9 | [\] | - | all | skin color edge gain slope | automatically calculated in algo logic |
| shp_skin_ctrl_var5_gain_offset | u4.5 | [\] | - | all | skin color edge gain offset | automatically calculated in algo logic |
| shp_skin_ctrl_var5_gain_limit | u1.5 | [2\] | - | all | skin color edge gain limit | automatically calculated in algo logic |
| yadj_enable | u1 | [\] | - | all | yadj enable, 0: disable, 1: enable |  |
| yadj_lut | u8.2 | [33\] | - | all | yadj lut, used to adjust brightness/contrast | normally calculated in algo logic |
| yclip_enable | u1 | [\] | - | all | yclip enable, 0: disable, 1: enable |  |
| yclip_clip | u8.0 | [2\] | - | yclip_clip[0\] <= yclip_clip[1\] | yclip clip limit, [0\]: lower limit, [1\]: upper limit | normally calculated in algo logic |
| hsvc_enable | u1 | [\] | - | all | hsvc enable, 0: disable, 1: enable |  |
| hsvc_h_lut | s8.7 | [25, 17\] | - | all | hsvc hue lut |  |
| hsvc_s_lut | u1.15 | [25, 17\] | - | all | hsvc saturation lut |  |
| hsvc_yuv2rgb_matrix | s2.8 | [3, 3\] | - | all | hsvc yuv2rgb matrix | set as the same as inverted csc matrix |
| hsvc_rgb2yuv_matrix | s2.8 | [3, 3\] | - | all | hsvc rgb2yuv matrix | set as the same as csc matrix |
| hsvc_yuv2rgb_offset | s8.2 | [2, 3\] | - | all | hsvc yuv2rgb offset |  |
| hsvc_rgb2yuv_offset | s8.2 | [2, 3\] | - | all | hsvc rgb2yuv offset |  |
| ccmp_enable | u1 | [\] | - | all | ccmp enable, 0: disable, 1: enable |  |
| ccmp_y_lut | u1.9 | [29\] | - | all | ccmp y gain lut | smaller value means stronger saturation compression |
| ccmp_sat_lut | u1.9 | [23\] | - | all | ccmp saturation gain lut | smaller value means stronger saturation compression |
| cset_enable | u1 | [\] | - | all | cset enable, 0: disable, 1: enable |  |
| cset_io_flag | u1 | [\] | - | all | cset color target inverse selection flag, 0: inverse selection, 1: normal selection | set to 1 normally |
| cset_color | s7.2 | [2\] | - | all | cset color target new color uv value |  |
| cset_center_y | u8.2 | [\] | - | all | cset color target y value | set as y value of the specified color target |
| cset_center_uv | s7.2 | [2\] | - | all | cset color target uv value | set as uv value of the specified color target |
| cset_radius | u7.2 | [3\] | - | all | cset color target radius | larger value means more pixels will be selected in the color mask |
| cset_t_grad | u4 | [3\] | - | all | cset color mask transition gradient | larger value means smoother/wider transition band in the color mask |
| cclip_enable | u1 | [\] | - | all | cclip enable, 0: disable, 1: enable |  |
| cclip_cmtx | s2.5 | [2, 2\] | - | all | cclip color matrix | normally calculated in algo logic |
| cclip_clip | s7.0 | [2\] | - | cclip_clip[0\] <= cclip_clip[1\] | cclip clip limit, [0\]: lower limit, [1\]: upper limit | normally calculated in algo logic |
| desat_enable | u1 | [\] | - |  | desat enable, 0: bypass, 1: enable |  |
| desat_strength | u4.4 | [\] | - |  | desat strength, increase or decrease strength of desat |  |
| desat_luma_lut | u1.7 | [8\] | - |  | the luma ratio table for desat |  |
| desat_sat_lut | u1.7 | [6\] | - |  | the saturation ratio table for desat |  |
| desat_angle_ratio_lut | u1.7 | [16\] | - |  | the angle ratio table for desat |  |
| desat_uv_val | s7.2 | [2\] | - |  | desat uv color |  |
| desat_debug_enable | u1 | [\] | - |  | debug desat mask display enable |  |
| desat_debug_thr | u1.4 | [\] | - |  | debug threshold for desat mask |  |
| desat_debug_color | s8.2 | [3\] | - |  | debug desat color fill |  |

