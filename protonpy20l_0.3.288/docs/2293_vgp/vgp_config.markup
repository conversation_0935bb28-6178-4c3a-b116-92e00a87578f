h2. Conf list
h3. tde
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | N/A | DPU enable control bit | 0 - disable; 1 - enable |
| v0_format | u6 | [\] | - | N/A | v0 format | Set to proper value |
| v0_xT422_matrix | s2.10 | [3, 3\] | - | N/A | RGB to YUV matrix. Only effective when input is RGB. | Set to proper value |
| v0_xT422_offset_in | s8.0 | [3\] | - | N/A | RGB to YUV422 conversion offset in. Only effective when input is RGB. | Set to proper value |
| v0_xT422_offset_out | s8.0 | [3\] | - | N/A | RGB to YUV422 conversion offset out. Only effective when input is RGB. | Set to proper value |
| v0_xT422_decimation_hor | s1.5 | [7\] | - | N/A | YUV444 to YUV422 horizontal decimation weights. | Set to proper value |
| v0_sp_alpha_enable | u1 | [\] | - | N/A | Special alpha channel enable control bit. | 0 - disable; 1 - enable |
| v0_global_alpha_enable | u1 | [\] | - | N/A | Global alpha enable control bit. It's priority is lower than special alpha. | Set to proper value |
| v0_global_alpha | u8 | [\] | - | N/A | v0's global alpha value. If global alpha is used or there is no alpha channel in input v0, this value will be the global alpha for v0. BITMAP's alpha channel will be generated by using color palette if global alpha is not enabled. | Set to proper value |
| v0_global_chroma | u8 | [2\] | - | N/A | v0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10. | Set to proper value |
| v0_up420T422_copy_enable | u1 | [\] | - | N/A | When input format is YUV420, this bit controls whether UV will be copied into YUV422. | 0 - use interpolation to up-sample YUV420 to YUV422; 1 - directly copy UV420 to UV422 |
| quad_v0_enable | u1 | [4\] | - | N/A | Quad enable control bit | Set to proper value |
| quad_v0_in_x_coords | s13.10 | [4, 4\] | - | N/A | x coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed. | Set to proper value |
| quad_v0_in_y_coords | u13 | [4, 4\] | - | N/A | y coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed. | Set to proper value |
| quad_v0_in_y_start | u13 | [4\] | - | N/A | y start coordinates of inner quad | Set to proper value |
| quad_v0_in_y_end | u13 | [4\] | - | N/A | y end coordinates of inner quad | Set to proper value |
| quad_v0_in_steps | s13.10 | [4, 4\] | - | N/A | Inner quad step to form inner quad edges | Set to proper value |
| quad_v0_in_directions | u2 | [4\] | - | N/A | This register controls direction of edge x1 and x2; 0b01 selects right side of line x1 whereas 0b00 selects left side of line x1; 0b10 selects right side of line x2 and 0b00 selects left side of line x2. | Set to proper value |
| quad_v0_out_x_coords | s13.10 | [4, 4\] | - | N/A | x coordinates of intersection points between outer quad's edges, and image rectangle which is constrained by y_st and y_ed. | Set to proper value |
| quad_v0_out_y_coords | u13 | [4, 4\] | - | N/A | y coordinates of intersection points between outer quad's edges, and image rectangle which is constrained by y_st and y_ed. | Set to proper value |
| quad_v0_out_y_start | u13 | [4\] | - | N/A | y start coordinates of outer quad | Set to proper value |
| quad_v0_out_y_end | u13 | [4\] | - | N/A | y end coordinates of outer quad | Set to proper value |
| quad_v0_out_steps | s13.10 | [4, 4\] | - | N/A | Outer quad step to form outer quad edges | Set to proper value |
| quad_v0_out_directions | u2 | [4\] | - | N/A | This register controls direction of edge x1 and x2; 0b01 selects right side of line x1 whereas 0b00 selects left side of line x1; 0b10 selects right side of line x2 and 0b00 selects left side of line x2. | Set to proper value |
| quad_v0_mode | u1 | [4\] | - | N/A | Quad feature mode control bit. | 0 - mosaic; 1 - fill |
| quad_v0_mosaic_step_size | u7 | [2\] | - | N/A | Mosaic step size | Set to proper value |
| quad_v0_fill_out_luma | u8.0 | [4\] | - | N/A | Outer quad luma | Set to proper value |
| quad_v0_fill_out_chroma | u8.0 | [4, 2\] | - | N/A | Outer quad chroma(with 128 bias) | Set to proper value |
| quad_v0_out_alpha | u8.0 | [4\] | - | N/A | Outer quad blending alpha | Set to proper value |
| roi_luma_sum_tl_coords | u12.0 | [8, 2\] | - | N/A | top-left corner coordinates of 8 roi areas | roi_luma_sum_tl_coords[n\][0\] - nth roi area's y coordinate; roi_luma_sum_tl_coords[n\][1\] - nth roi area's x coordinate |
| roi_luma_sum_br_coords | u12.0 | [8, 2\] | - | N/A | bottom-right corner coordinates of 8 roi areas | roi_luma_sum_br_coords[n\][0\] - nth roi area's y coordinate; roi_luma_sum_br_coords[n\][1\] - nth roi area's x coordinate |
| g0_format | u6 | [\] | - | N/A | input format | Set to proper value |
| g0_xT422_matrix | s2.10 | [3, 3\] | - | N/A | RGB to YUV conversion matrix. Only effective when input format is RGB. | Set to proper value |
| g0_xT422_offset_in | s8.0 | [3\] | - | N/A | RGB to YUV conversion offset in. Only effective when input format is RGB. | Set to proper value |
| g0_xT422_offset_out | s8.0 | [3\] | - | N/A | RGB to YUV conversion offset out. Only effective when input format is RGB. | Set to proper value |
| g0_xT422_decimation_hor | s1.5 | [7\] | - | N/A | YUV444 to YUV422 horizontal decimation weights. | Set to proper value |
| g0_global_alpha_enable | u1 | [\] | - | N/A | Global alpha enable control bit. It's priority is lower than special alpha. | Set to proper value |
| g0_global_alpha | u8 | [\] | - | N/A | g0's global alpha value. If global alpha is used or there is no alpha channel in input g0, this value will be the global alpha for g0. BITMAP's alpha channel will be generated by using color palette if global alpha is not enabled. | Set to proper value |
| g0_global_chroma | u8 | [2\] | - | N/A | global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10. | Set to proper value |
| g0_bitmap_color_palette | acc_unknown |  |  |  |  |  |
| g0_up420T422_copy_enable | u1 | [\] | - | N/A | When input format is YUV420, this bit controls whether UV will be copied into YUV422. | Set to proper value |
| blending_enable | u1 | [\] | - | N/A | Blending unit enable control bit | 0 - disable; 1 - enable |
| blending_coord | u16 | [2\] | - | N/A | Blending coord | Set to proper value |
| blending_alpha0_out_mode | u1 | [\] | - | N/A | 0 - v0 alpha not changed; 1 - v0 alpha inversed; Result is intermediate. | Set to proper value |
| blending_alpha1_out_mode | u1 | [\] | - | N/A | 0 - gx alpha not changed; 1 - gx alpha inversed; Result is intermediate. | Set to proper value |
| blending_alpha_out_weight | u1.8 | [\] | - | N/A | v0_alpha \* (1 - weight) + gx_alpha \* weight | Set to proper value |
| blending_color_key_enable | u1 | [\] | - | N/A | Color key enable control bit | 0 - disable; 1 - enable |
| blending_alpha_select | u2 | [\] | - | N/A | Select alpha channel to be used in blending. | Set to proper value |
| blending_color_key_val | s8 | [3, 2\] | - | N/A | Color key color range for YUV. | Set to proper value |
| blending_color_key_inv | u1 | [\] | - | N/A | If enabled, pix whose color is outside of the color-key range will do the color-key process, rather than the one within the color range. | Set to proper value |
| ddr_write_adj_enable | u1 | [\] | - | N/A | After blending, result will be dumped into DDR. When this happens, this bit controls whether the result will be adjusted by using 3x3 matrix. | Set to proper value |
| ddr_write_format | u6 | [\] | - | N/A | DDR dump format | Set to proper value |
| ddr_write_up422T444_copy_enable | u1 | [\] | - | N/A | To do 3x3 matrix adjustment, YUV422 pipe result should be converted into YUV444. This bit controls how to convert UV422 to UV444. | 0 - interpolation; 1 - direct copy |
| ddr_write_adj_matrix | s2.8 | [3, 3\] | - | N/A | 3x3 aadjustment matrix | Set to proper value |
| ddr_write_adj_offset | s8 | [2, 3\] | - | N/A | in && out offset for DDR write adjustment. | Set to proper value |
| ddr_write_vfilter_enable | u1 | [\] | - | N/A | When output needs conversion from YUV422 to YUV420, this bit controls the down-sample approach. | 0 - direct decimation; 1 - filtering |
| ddr_write_decimation_ver | u1.1 | [2\] | - | N/A | When output needs conversion from YUV422 to YUV420, this bit configs the down-sample filtering weights. | Set to proper value |
| dither_enable | u1 | [\] | - | N/A | dither enable control bit | Set to proper value |
| dither_seed_enable | u1 | [\] | - | N/A | dither seed enable bit. | 0 - use stat; 1 - use config |
| dither_seed_r | u16 | [2\] | - | N/A | dither seed for R channel | Set to proper value |
| dither_seed_g | u16 | [2\] | - | N/A | dither seed for G channel | Set to proper value |
| dither_seed_b | u16 | [2\] | - | N/A | dither seed for B channel | Set to proper value |
| dither_pmask_r | u16 | [2\] | - | N/A | pmask for R channel | Set to proper value |
| dither_pmask_g | u16 | [2\] | - | N/A | pmask for G channel | Set to proper value |
| dither_pmask_b | u16 | [2\] | - | N/A | pmask for B channel | Set to proper value |

