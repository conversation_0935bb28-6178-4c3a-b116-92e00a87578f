# structs

![structs](../assets/imgs/struct.jpg)

某些模块的param的定义中存在一些结构体，脚本支持用户自定义这些结构体
1. param_base.json中添加structs域（如果没有struct可缺省）
2. 在struct域中添加要定义的struct。（上图中添加了test_struct_t和test_nested_struct_t两个结构体）
3. 在每个结构体下面添加其中包含的参数。（parameter的type也可以是struct，及支持嵌套的调用）
4. 在常用的param域中，指定参数的type时，就可以使用上面定义的struct了

**要注意，如果是嵌套的struct，要从内层开始往外定义（param_sbase.json中从上到下写）**

## struct中的参数需要具有以下属性

**如果该参数是一般参数：**

属性|说明|是否可缺省
---|---|---
display UI|显示名称|可
acc|参数精度，标识方法同xxx_m0_base.json|不可
type|参数类型|可
size|参数尺寸，标识方法同xxx_m0_base.json|不可
range|参数范围，可支持整形或浮点型|可
default|参数默认值，可支持整形或浮点型|可
comment|参数说明|可

**如果该参数是结构体参数：**

属性|说明|是否可缺省
---|---|---
display UI|显示名称|可
type|参数类型|不可
size|参数尺寸，标识方法同xxx_m0_base.json|不可
comment|参数说明|可

## 生成结果

![](../assets/imgs/structs_result.png)

module.h里会生成对应的struct

![](../assets/imgs/structs_result2.jpg)
WIKI文档中会生成对应的Struct描述

![](../assets/imgs/structs_result3.png)
python wrapper中会生成对应的结构体接口