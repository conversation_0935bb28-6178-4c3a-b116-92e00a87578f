# partition结构体

## 结构体定义

在param_base.json中只需要将参数的`type`定义为`ax_isp_ptn_info_t` 就可以生成Partition Common Interface，其定义如下，与软件接口一致。具体接口如下

![](../assets/imgs/partition_param.jpg)

partition接口的定义在如下文件中
![](../assets/imgs/partition_param_path.png)

## partition顺序
partition param在上述指针所指的内存中，排列顺序为从左到右，从上到下

**例**： 如果ptn_hor_num=3，ptn_ver_num=2，那么partition的编号与其对应位置如下图所示
![](../assets/imgs/partition_param_order.png)

## python wrapper的使用

partition param的python wrapper也已经作为公用的wrapper定义好了，可以直接使用，具体使用方法如下

```python
img_size = pt.ImageSize(1000, 1000)
ptn_cord = pt.<PERSON><PERSON><PERSON>(0, 0, 1000, 1000)
ptn_edge = pt.EdgeInfo(0, 0, 0, 0)
ptn = [pt.PtnParam(1000, 1000, 1000, 1, ptn_cord, ptn_edge)]
partition_info = pt.IspPtnInfo(1, 1, img_size, ptn)

param_locat_mesh_adj = pt.depurple.DEPURPLELocatMeshAdjParam()
param_locat_mesh_adj.partition_info = partition_info
```

逐行对上面这段代码进行说明
```python
img_size = pt.ImageSize(1000, 1000)
```
* 创建一个ImageSize对象，1000, 1000代表图像的宽和高

```python
ptn_cord = pt.WinArea(0, 0, 1000, 1000)
```
* 创建一个WinArea对象，当前这个partition的区域从 (0, 0) 到 (1000, 1000)，即填满了整张图像

```python
ptn_edge = pt.EdgeInfo(0, 0, 0, 0)
```
* 创建一个EdgeInfo对象，当前这个partition的上下左右，edge均为0

```python
ptn = [pt.PtnParam(1000, 1000, 1000, 1, ptn_cord, ptn_edge)]
```
* 创建一个PtnParam的List，这个List里包含了一个PtnParam对象，PtnParam的初始化参数用到了上面定义的几个对象

```python
partition_info = pt.IspPtnInfo(1, 1, img_size, ptn)
* 创建一个IspPtnInfo的对象，其中包含1x1个partition，partition参数为ptn，图像尺寸为img_size
```

```python
param_locat_mesh_adj = pt.depurple.DEPURPLELocatMeshAdjParam()
param_locat_mesh_adj.partition_info = partition_info
```
将之前创建的IspPtnInfo对象赋值给去紫边算法的params中的partition_info

按照这样的方法，就可以完成对partition param的赋值，不同于上面的例子中，一个partition_info中也可以包含多个partition，只需要将ptn的List中添加多个PtnParam对象，并将partition_info的前两个参数对应设置即可