h2. Conf list
h3. dpc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | all | DPC bypass/enable, 0: bypass, 1: enable |  |
| offset_in | u8.6 | [\] | - | all | minus offset from input | normally set [16\] |
| offset_out | u8.6 | [\] | - | all | add offset from output | normally set [16\] |
| noise_lut | u8.6 | [9\] | - | all | noise lut for defect pixel detection | adjust according to sensor gain |
| static_enable | u1 | [\] | - | all | enable static dp correction or not, 0:disable 1:enable |  |
| static_info | u14 | [16384, 2\] | support | coordinates <= image_size.must be in ascending order, first col, then row. | the static defect points coordinates info, [[2^14-1, 2^14-1\]\] mean no static_info, max 16384 coordinates, each coordinate represented by(x, y) |  |
| wb_gain | u4.4 | [2\] | - | [1.0, 16.0) | wb gain for r and b channel, first value represent r gain, second value represent b gain | for linear input, use AWB gain, for non-linear input, algo logic use DPC stat to calc gain |
| inv_wb_gain | u1.7 | [2\] | - | (0, 1.0\] and set inv_wb_gain = 1 / wb_gain | inverse wb gain for r and b channel, first value represent r gain, second value represent b gain | set 1 / wb_gain |
| dp_det_mode | u1 | [\] | - |  | two loacl dif method for defect pixel detection, 0:max-min mode,1:max2-min2 mode | the larger the value, more defect pixels will be detected |
| dp_det_th_ratio | u0.6 | [\] | - | [0,1.0) | local diff str for defect pixel detection, 0:max(max-ave, ave-min)~1.0:min(max-ave, ave-min) | the larger the value, more defect pixels will be detected,  |
| dp_det_m1 | u4.4 | [\] | - | [0,16.0) | local diff gain for defect pixel detection | the larger the value, less defect pixels will be detected |
| dp_det_m2 | u4.8 | [\] | - | [0,16.0) | defect pixel probability gain, the larger the defect pixel probability | the larger the value, the larger the defect pixel probability and more pixel will be corrected |
| dp_cor_ch_mode | u1 | [\] | - |  | mode for direction detection, 0:NON_CH_WISE,  for R channel, use both R channel and surround B,G to dirDirection, 1:CH_WISE, for R channel, just use surround R to dirDirection |  |
| dp_cor_clip_ratio | u0.6 | [\] | - | [0,1.0) | local max and min clip margin, 0:no clip ~ 1.0:clip margin equal to local dif |  |
| dp_cor_dir_wt_r2 | u1.4 | [\] | - | [0,2.0) | channel-wise direction detection ratio for deirection detection, direction = non channel-wise direction + channel-wise direction \* dp_cor_dir_wt_r2 | the larger the value, more channel-wise direction used |
| dp_cor_m3 | u1.7 | [\] | - | [0,2.0) | direction interpolation ratio used in interpolation | the larger the value, more direction interpolated result used in interpolation |
| dp_cor_m4 | u1.7 | [\] | - | [0,1.0\] | interpolation reusult modify ratio according to hot or cold type | the larger the value, more interpolation result used, less modify |
| dp_cor_m5 | u4.4 | [\] | - | [0,16.0) | the threshold to use direction interpoation or local avg interpolation result | the larger the value, more avg interpolation result used, less direction interpolation |
| quick_det_enable | u1 | [\] | - |  | enable quick detection enable or not, 0:disable,all pixel skip to normal deteciton and correction;1:enable,use quick detection to select defect pixel, then for quick detected bad pixel, skip to normal detection and correction. | set enable in usual to save power |
| quick_det_th_slope | u0.4 | [\] | - | [0,1.0) | defect pixel margin gain for quick detection | the larger the value, the larger the margin, less defect pixel detected |
| quick_det_th_offset | u8.6 | [\] | - | [0,256.0) | defect pixel base margin for quick detection | the larger the value, the larger the margin, less defect pixel detected |
| quick_det_th_max | u8.6 | [\] | - | [0,256.0) | defect pixel max margin for quick detection | the larger the value, the larger the margin, less defect pixel detected |
| calc_limit_enable | u1 | [\] | - |  | enable color limit or not, 0:disable,1:enable |  |
| margin_u | u8.6 | [3\] | - | [0,256.0) | up margin for color limit, index 0-3 represented bad pixel type[DYNAMIC_DP, STATIC_DP, NOT_DP\] | the larger the value, less color limitation |
| margin_l | u8.6 | [3\] | - | [0,256.0) | low margin for color limit, index 0-3 represented bad pixel type[DYNAMIC_DP, STATIC_DP, NOT_DP\] | the larger the value, less color limitation |
| color_limit_ratio | u1.7 | [3\] | - | [0, 1.0\] | color limit ratio, index 0-3 represented defect pixel type[DYNAMIC_DP, STATIC_DP, NOT_DP\] | the larger the value, less color limitation, means stronger saturation |

