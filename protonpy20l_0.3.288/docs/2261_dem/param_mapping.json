{"enable": {"api": "nDemEn", "display": "enable", "comments": "dem module enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "mode": {"api": "eMode", "display": "mode", "comments": "0: normal, 1: copy to gray, 2: copy to green", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "edge_direction_high_frequency_sensitivity": {"api": "nEdgeDirHighFreqSens", "display": "edgeDirHighFreqSens", "comments": "bigger value==> more sensitivity to high freq", "hint": "Accuracy: U1.4 Range: [0, 16]"}, "edge_direction_edge_sensitivity_lut": {"api": "nEdgeDirEdgeSensLut", "display": "edgeDirEdgeSensLut", "comments": "default inv noise lut. lut[0]:0, [1]:32, [2]:64, ... [8]:256", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "edge_direction_hv_high_frequency_strength": {"api": "nEdgeDirHvHighFreqStr", "display": "edgeDirHvHighFreqStr", "comments": "level for adjusting high frequency gradient", "hint": "Accuracy: U2.2 Range: [0, 15]"}, "hv_interpolation_hf_coeff": {"api": "nHvInterpHfCoeff", "display": "hvInterpHfCoeff", "comments": "level for adjusting high frequency gradient", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "pn_interpolation_hf_coeff": {"api": "nPnInterpHfCoeff", "display": "pnInterpHfCoeff", "comments": "level for adjusting high frequency gradient", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "x_interpolation_hf_coeff": {"api": "nXInterpHfCoeff", "display": "xInterpHfCoeff", "comments": "level for adjusting high frequency gradient", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "hf_direction_estimation_enhance_level": {"api": "eHfDirEstEnhcLevel", "display": "hfDirEstEnhcLevel", "comments": "0: disable, 1: weak. 2: middle. 3: strong", "hint": "Accuracy: U3.0 Range: [0, 3]"}, "gic_enable": {"api": "nGicEn", "display": "gic<PERSON>nable", "comments": "green imbalance correction enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "gic_strength_factor": {"api": "nGicStrFactor", "display": "gicStrFactor", "comments": "", "hint": "Accuracy: U2.6 Range: [0, 255]"}, "gic_strength_lut": {"api": "nGicStrLut", "display": "gicStrLut", "comments": "lut[0]:0, [1]:32, [2]:64, ... [8]:256", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "gic_ct_lut": {"api": "nGicCtLut", "display": "gicCtLut", "comments": "gic crosstalk level for R/B channel. lut[*][0]:0, [*][1]:32, [*][2]:64, ... [*][8]:256", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "gic_limit_lut": {"api": "nGicLimitLut", "display": "gicLimitLut", "comments": "gic limit lut, bigger value, stronger gic. lut[0]:0, [1]:32, [2]:64, ... [8]:256", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "fcc_enable": {"api": "nFccEn", "display": "fccEnable", "comments": "fc cor enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "fcc_limit": {"api": "nFccLimit", "display": "fccLimit", "comments": "larger value means more pixels will be considered as false color pixels", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "fcc_sensitivity": {"api": "nFccSens", "display": "fccSens", "comments": "larger value means higher fcc sensitivity, makes the corrected color more grayish", "hint": "Accuracy: U2.2 Range: [0, 15]"}, "fcc_saturation_level": {"api": "nFccSatLevel", "display": "fccSatLevel", "comments": "larger value means less pixels will be considered as false color pixels", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "fcc_saturation_lut": {"api": "nFccSatLut", "display": "fccSatLut", "comments": "larger value means current pixel is higher possibility as false color and more need corrected color", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "fcc_strength": {"api": "nFccStr", "display": "fccStr", "comments": "larger value means stronger fcc strength, makes the corrected color more grayish", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "fcc_sat_protect_level": {"api": "nFccSatProtectLevel", "display": "fccSatProtectLevel", "comments": "smaller value means  more corrected color will be used in the final output", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "fcc_ratio_limit": {"api": "nFccRatioLimit", "display": "fccRatioLimit", "comments": "false color correction ratio limit of upper limit", "hint": "Accuracy: U1.8 Range: [0, 256]"}, "edge_enhance_enable": {"api": "nEdgeEnhanceEn", "display": "edgeEnhcEnable", "comments": "edge enhance enable, 0: disable, 1: enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "edge_smoothness_strength": {"api": "nEdgeSmoothStr", "display": "edgeSmoothStr", "comments": "larger values means more smooth. lut[0]:0, [1]:32, [2]:64, ... [8]:256", "hint": "Accuracy: U1.4 Range: [0, 16]"}, "edge_sharpness_strength": {"api": "nEdgeSharpStr", "display": "edgeSharpStr", "comments": "larger values means more sharpen. lut[0]:0, [1]:32, [2]:64, ... [8]:256", "hint": "Accuracy: U1.4 Range: [0, 16]"}}