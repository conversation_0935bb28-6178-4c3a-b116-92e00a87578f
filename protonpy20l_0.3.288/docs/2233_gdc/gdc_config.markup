h2. Conf list
h3. yuv_gdc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| output_size | u14 | [2\] | - |  | undistorted image output size |  |
| input_size | u14 | [2\] | - |  | input image size |  |
| input_bit_mode | u1 | [\] | - |  | 0: 10bit input, 1: 8bit input |  |
| undistort_enable | u1 | [\] | - |  | 1: undistort process, 0: bypass undistort process |  |
| input_mode | u1 | [\] | - |  | 1: 420 input, 0: 422 input |  |
| process_mode | u2 | [\] | - |  | 0: process Y, 1: process YUV, 2: process UV |  |
| rotation_en | u1 | [\] | - |  | 1: do rotation, 1: bypass rotate |  |
| clockwise_on | u1 | [\] | - |  | if clockwisely rotate when rotation is on |  |
| mesh_wise_mode | u1 | [\] | - |  | 0: pixel wise, 1: mesh_wise |  |
| pix_padding_mode | u1 | [\] | - |  | pixel padding is: 1: CONSTANT 0: MIRROR_ABA |  |
| const_Y | u8.2 | [\] | - |  | const value Y for padding pixel |  |
| const_UV | s7.2 | [2\] | - |  | const value UV for pading pixel |  |
| mesh_padding_mode | u1 | [\] | - |  | 0: mesh is cycle padding, 1: mesh is constant padding |  |
| const_dx | s14.10 | [\] | - |  | constant padding mesh value for dx |  |
| const_dy | s14.10 | [\] | - |  | constant padding mesh value for dy |  |
| mesh_start | s8 | [2\] | support |  | image left-top startpoint starts in mesh coordinate |  |
| internal_start | u10 | [2\] | support |  | image left-top startpoint starts in internel mesh block |  |
| pos_start | u0.18 | [2\] | support |  | image left-top startpoint's position in internel mesh block |  |
| mesh_value_mode | u1 | [\] | - |  | 0: mesh is absolute displacement, 1: mesh is relative displacement |  |
| mesh_length | u10 | [2\] | - |  | mesh length |  |
| inv_mesh_length | u0.18 | [2\] | - |  | inv mesh length |  |
| mesh_height | u14 | [\] | - |  | mesh height |  |
| mesh_width | u14 | [\] | - |  | mesh width |  |
| partition_start | u14 | [2\] | support |  | partition start |  |
| mesh_x | s14.10 | [33, 33\] | - |  | mesh x |  |
| mesh_y | s14.10 | [33, 33\] | - |  | mesh y |  |

