{"configs": {"dem": {"enable": {"acc": [0, 1], "size": [], "description": "dem enable, 0: bypass (COPY2GRAY mode), 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "wb_stat_enable": {"acc": [0, 1], "size": [], "description": "enable wb stat or not, 0:disable, 1:enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "wb_stat_roi_t_b_l_r": {"acc": [0, 14], "size": [4], "description": "roi zone, [0]: top, [1]: bottom, [2]: left, [3]: right", "usage": "", "constraints": "0<=roi_t_b_l_r<=(pic.h,pic.w); roi_t_b_l_r % 2 == 0; roi_t_b_l_r[0] < roi_t_b_l_r[1]; roi_t_b_l_r[2] < roi_t_b_l_r[3]", "type": "AX_U16", "partition": "support"}, "wb_stat_mesh_start": {"acc": [0, 3], "size": [2], "description": "mesh start index correspond to current partition's roi start position, [index_y, index_x]", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "wb_stat_mesh_pos_start": {"acc": [0, 14], "size": [2], "description": "pos in wb_stat_mesh_start correspond to current partition's roi start position, [index_y, index_x]", "usage": "", "constraints": "0=<wb_stat_mesh_pos_start<=wb_stat_mesh_shape", "type": "AX_U16", "partition": "support"}, "wb_stat_slice_level": {"acc": [0, 8, 4], "size": [], "description": "slice level for saturation; max setting means slice disable", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "wb_stat_slice_sat_type": {"acc": [0, 1], "size": [], "description": "saturation calculation method. 0: sum; 1: max", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "wb_stat_mesh_shape": {"acc": [0, 11], "size": [2], "description": "mesh shape when do wbc stat calculate, based on whole size raw, [index_y, index_x]", "usage": "mesh_h_shape, mesh_w_shape", "constraints": "both<=1024, MC20L's max support width is 8192", "type": "AX_U16", "partition": "-"}, "wbc_enable": {"acc": [0, 1], "size": [], "description": "local wbc enable, 0: disable, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "wbc_gain_lut_r": {"acc": [0, 1, 7], "size": [8, 8], "description": "local wbc R channel gain lut", "usage": "wbc_gain_lut_r(i, j) = (local_wbc_gain(i, j)[r] / global_wbc_gain[r]) / max(local_wbc_gain(i, j)[:] / global_wbc_gain[:])", "constraints": "[0.25, 1.0]", "type": "AX_U8", "partition": "-"}, "wbc_gain_lut_g": {"acc": [0, 1, 7], "size": [8, 8], "description": "local wbc G channel gain lut", "usage": "wbc_gain_lut_g(i, j) = (local_wbc_gain(i, j)[g] / global_wbc_gain[g]) / max(local_wbc_gain(i, j)[:] / global_wbc_gain[:])", "constraints": "[0.25, 1.0]", "type": "AX_U8", "partition": "-"}, "wbc_gain_lut_b": {"acc": [0, 1, 7], "size": [8, 8], "description": "local wbc B channel gain lut", "usage": "wbc_gain_lut_b(i, j) = (local_wbc_gain(i, j)[b] / global_wbc_gain[b]) / max(local_wbc_gain(i, j)[:] / global_wbc_gain[:])", "constraints": "[0.25, 1.0]", "type": "AX_U8", "partition": "-"}, "wbc_mesh_start": {"acc": [0, 4], "size": [2], "description": "start index of padding gain lut (10 *10) response to partition img start position, basicly same to wb_stat_block_num, [index_y, index_x]", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "wbc_mesh_phase_start": {"acc": [0, 0, 15], "size": [2], "description": "local wbc gain lut mesh initial phase, [index_y, index_x]", "usage": "for first partition, it is 0.5 + wbc_mesh_step / 2", "constraints": "", "type": "AX_U16", "partition": "support"}, "wbc_mesh_pos_start": {"acc": [0, 14], "size": [2], "description": "local wbc gain lut mesh initial position in block, (include overlap area), [index_y, index_x]", "usage": "", "constraints": "0=<wbc_mesh_pos_start<=wb_stat_mesh_shape", "type": "AX_U16", "partition": "support"}, "wbc_mesh_step": {"acc": [0, 0, 14], "size": [2], "description": "local wbc gain lut mesh step, [0]: y step, [1]: x step", "usage": "wbc_mesh_step[0] = 8 / partition_height, wbc_mesh_step[1] = 8 / partition_width", "constraints": "", "type": "AX_U16", "partition": "-"}, "pre_gamma_enable": {"acc": [0, 1], "size": [], "description": "pre-gamma enable, 0: disable, 1: enable", "usage": "normally set 1", "constraints": "", "type": "AX_U8", "partition": "-"}, "mode": {"acc": [0, 4], "size": [], "description": "demosaic mode", "usage": "0: diff ratio(not supported), 1: copy to gray, 2: copy to green, 3: RGGB2RGB(not supported), 4: divider ratio", "constraints": "mode == 1 or mode == 2 or mode == 4", "type": "AX_U8", "partition": "-"}, "gic_enable": {"acc": [0, 1], "size": [], "description": "green imbalance correction enable, 0: disable, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "gic_strength_lut": {"acc": [0, 1, 7], "size": [9], "description": "gic strength lut, [0]:0, [1]:32, ..., [8]:256", "usage": "for gamma domain, the maximum input is 256 - offset_out", "constraints": "", "type": "AX_U8", "partition": "-"}, "gic_ct_lut": {"acc": [0, 8, 4], "size": [2, 9], "description": "gic crosstalk level for R/B channel, column index [0]:0, [1]:32, ..., [8]:256, row[0]: R channel crosstalk level w.r.t. R value, row[1]: B channel crosstalk level w.r.t. B value", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "gic_limit_lut": {"acc": [0, 8, 4], "size": [9], "description": "gic limit lut: [0]:0, [1]:32, ..., [8]:256", "usage": "set gic limit w.r.t. GB/GR value", "constraints": "", "type": "AX_U16", "partition": "-"}, "grad_h_enable": {"acc": [0, 1], "size": [], "description": "enable color diff for high frequency gradient, 0: disable, 1: enable", "usage": "set enable to take into account high frequency gradient when calculating color difference", "constraints": "", "type": "AX_U8", "partition": "-"}, "gradf_ratio": {"acc": [0, 1, 4], "size": [], "description": "high and low frequency gradient filter's blending ratio", "usage": "larger value means that more high frequency gradient will be taken into account", "constraints": "[0.0, 1.0]", "type": "AX_U8", "partition": "-"}, "grad_hh_level": {"acc": [0, 2, 2], "size": [], "description": "level for adjusting high frequency gradient", "usage": "increase this value to enhance horizontal and vertical high frequency texture", "constraints": "", "type": "AX_U8", "partition": "-"}, "inv_noise_lut": {"acc": [0, 4, 8], "size": [9], "description": "1/noise lut, [0]:0, [1]:32, ..., [8]:256", "usage": "for gamma domain, the maximum input is 256 - offset_out", "constraints": "", "type": "AX_U16", "partition": "-"}, "ginterp_hv_hf_ratio": {"acc": [0, 1, 5], "size": [], "description": "hf ratio of h&v direction in green interpolation", "usage": "larger value means adding more high frequency info of h&v direction for interpolated G pixels", "constraints": "ginterp_hv_hf_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "ginterp_pn_hf_ratio": {"acc": [0, 1, 5], "size": [], "description": "hf ratio of diagonal direction in green interpolation", "usage": "larger value means adding more high frequency info of diagonal direction for interpolated G pixels", "constraints": "ginterp_pn_hf_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "ginterp_x_hf_ratio": {"acc": [0, 1, 5], "size": [], "description": "hf ratio of x direction in green interpolation", "usage": "larger value means adding more high frequency info of x direction for interpolated G pixels", "constraints": "ginterp_x_hf_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "gclip_level": {"acc": [0, 0, 4], "size": [], "description": "green pixel clip level", "usage": "larger value means more restricted range for interpolated G pixels", "constraints": "", "type": "AX_U8", "partition": "-"}, "edge_enhance_enable": {"acc": [0, 1], "size": [], "description": "edge enhance enable, 0: disable, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "edge_enhance_ghv_level": {"acc": [0, 2, 4], "size": [], "description": "edge enhance horizontal and vertical direction strength", "usage": "larger value means stronger enhancement for horizontal and vertical direction", "constraints": "", "type": "AX_U8", "partition": "-"}, "edge_enhance_ghv_corner_min": {"acc": [0, 0, 8], "size": [], "description": "edge enhance horizontal/vertical direction corner minimum coring strength", "usage": "increase this value to reduce artifacts on corner pixels", "constraints": "", "type": "AX_U8", "partition": "-"}, "edge_enhance_ghv_corner_slope": {"acc": [0, 2, 4], "size": [], "description": "edge enhance horizontal/vertical direction corner coring slope", "usage": "increase this value to reduce artifacts on corner pixels", "constraints": "", "type": "AX_U8", "partition": "-"}, "edge_enhance_ghv_corner_offset": {"acc": [1, 0, 8], "size": [], "description": "edge enhance horizontal/vertical direction corner coring offset", "usage": "increase this value to reduce artifacts on corner pixels", "constraints": "", "type": "AX_S16", "partition": "-"}, "edge_enhance_gpn_level": {"acc": [0, 2, 4], "size": [], "description": "edge enhance diagonal direction strength", "usage": "larger value means stronger enhancement for diagonal direction", "constraints": "", "type": "AX_U8", "partition": "-"}, "edge_enhance_level_lut": {"acc": [0, 1, 4], "size": [2, 9], "description": "edge enhance strength lut with g channel, row[0]: noise reduction strength lut, row[1]: sharpen strength lut", "usage": "larger values means stronger nr and sharpen level", "constraints": "[0.0, 1.0]", "type": "AX_U8", "partition": "-"}, "fc_cor_enable": {"acc": [0, 1], "size": [], "description": "false color correction enable, 0: disable, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "fc_cor_limit": {"acc": [0, 8, 4], "size": [], "description": "false color correction limit", "usage": "larger value means more pixels will be considered as false color pixels", "constraints": "", "type": "AX_U16", "partition": "-"}, "fc_cor_gain_safe": {"acc": [0, 2, 2], "size": [], "description": "false color correction gain for safe uv", "usage": "larger value means stronger fcc strength, makes the corrected color more grayish", "constraints": "", "type": "AX_U8", "partition": "-"}, "fc_cor_gain": {"acc": [0, 4, 4], "size": [], "description": "false color correction gain", "usage": "larger value means stronger fcc strength, makes the corrected color more grayish", "constraints": "", "type": "AX_U8", "partition": "-"}, "fc_cor_th": {"acc": [0, 4, 4], "size": [], "description": "false color correction threshold", "usage": "larger value means less pixels will be considered as false color pixels", "constraints": "", "type": "AX_U8", "partition": "-"}, "fc_cor_sat_lut": {"acc": [0, 8, 4], "size": [9], "description": "false color correction saturation lut (linear lut with g input), controls the blending weight of original color and corrected color", "usage": "larger value means current pixel is more like an unsaturated gray pixel and more corrected color will be used in the final output", "constraints": "", "type": "AX_U16", "partition": "-"}, "fc_cor_inv_base_sat_level": {"acc": [0, 0, 8], "size": [], "description": "false color correction inverse base saturation level", "usage": "smaller value means larger downscaling of the saturation level and more corrected color will be used in the final output", "constraints": "", "type": "AX_U8", "partition": "-"}, "fc_cor_ratio_limit": {"acc": [0, 1, 8], "size": [2], "description": "false color correction ratio limit, [0]: lower limit, [1]: upper limit", "usage": "normally set [0.0, 1.0]", "constraints": "[0.0, 1.0]", "type": "AX_U16", "partition": "-"}, "post_gamma_enable": {"acc": [0, 1], "size": [], "description": "post-gamma enable, 0: disable, 1: enable", "usage": "normally set 1", "constraints": "", "type": "AX_U8", "partition": "-"}, "offset_in": {"acc": [0, 8, 4], "size": [], "description": "offset in", "usage": "set to 16.0 in usual", "constraints": "", "type": "AX_U16", "partition": "-"}, "offset_out": {"acc": [0, 8, 4], "size": [], "description": "offset out", "usage": "set to 16.0 in usual", "constraints": "", "type": "AX_U16", "partition": "-"}}}, "partition_configs": ["dem.wb_stat_roi_t_b_l_r", "dem.wb_stat_mesh_start", "dem.wb_stat_mesh_pos_start", "dem.wbc_mesh_start", "dem.wbc_mesh_phase_start", "dem.wbc_mesh_pos_start"], "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "DEM is 0x2261", "type": "AX_U16"}, "wbc_previous_local_r_gain": {"display": "", "acc": [0, 1, 7], "size": [8, 8], "comment": "wbc_previous_local_r_gain", "range": [0.2578125, 1.0], "default": [[128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128]]}, "wbc_previous_local_g_gain": {"display": "", "acc": [0, 1, 7], "size": [8, 8], "range": [0.2578125, 1.0], "comment": "wbc_previous_local_g_gain", "default": [[128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128]]}, "wbc_previous_local_b_gain": {"display": "", "acc": [0, 1, 7], "size": [8, 8], "range": [0.2578125, 1.0], "comment": "wbc_previous_local_b_gain", "default": [[128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128], [128, 128, 128, 128, 128, 128, 128, 128]]}}, "params": {"partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "target_conf": ["dem.wb_stat_roi_t_b_l_r", "dem.wb_stat_mesh_start", "dem.wb_stat_mesh_pos_start", "dem.wbc_mesh_start", "dem.wbc_mesh_phase_start", "dem.wbc_mesh_pos_start"], "display": "partition information", "comment": "partition information", "hidden": 1, "dependency": "common", "auto": 0}, "enable": {"display": "enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 1, "comment": "dem module enable", "hidden": 0, "auto": 0, "target_conf": ["dem.mode"], "dependency": "user"}, "init_enable": {"acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 1, "comment": "0: not first frame. 1: first frame", "hidden": 1, "auto": 0, "target_conf": ["dem.wbc_gain_lut_r", "dem.wbc_gain_lut_g", "dem.wbc_gain_lut_b"], "dependency": "common", "display": "init_enable"}, "hf_direction_estimation_enhance_level": {"acc": [0, 3], "range": [0, 3], "enum_field": {"0": "disable", "1": "weak", "2": "middle", "3": "strong"}, "type": "AX_U8", "size": [], "default": 0, "comment": "0: disable, 1: weak. 2: middle. 3: strong", "hidden": 0, "auto": 0, "target_conf": ["dem.wbc_gain_lut_r", "dem.wbc_gain_lut_g", "dem.wbc_gain_lut_b"], "dependency": "user", "display": "hf_direction_estimation_enhance_level"}, "mode": {"acc": [0, 2], "size": [], "range": [0, 2], "default": 1, "comment": "0: normal, 1: copy to gray, 2: copy to green", "enum_field": {"0": "normal", "1": "copy to gray", "2": "copy to green"}, "hidden": 0, "target_conf": ["dem.mode"], "display": "mode", "type": "AX_U8", "auto": 0, "dependency": "user"}, "gic_enable": {"display": "gic_enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "comment": "green imbalance correction enable", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user", "default": 0}, "gic_strength_lut": {"display": "gic_strength_lut", "acc": [0, 1, 7], "type": "AX_U8", "size": [9], "range": [0.0, 1.0], "comment": "lut[0]:0, [1]:32, [2]:64, ... [8]:256", "hidden": 0, "auto": 0, "target_conf": ["dem.gic_strength_lut"], "dependency": "user", "default": [0, 0.15625, 0.375, 0.5, 0.625, 0.6875, 0.8125, 0.875, 1.0]}, "gic_strength_factor": {"display": "gic_strength_factor", "acc": [0, 2, 6], "range": [0.0, 3.984375], "type": "AX_U8", "size": [], "comment": "", "hidden": 0, "auto": 1, "target_conf": ["dem.gic_strength_lut"], "dependency": "user", "default": 1.0}, "gic_ct_lut": {"display": "gic_ct_lut", "acc": [0, 8, 4], "range": [0.0, 255.9375], "type": "AX_U16", "size": [9], "comment": "gic crosstalk level for R/B channel. lut[*][0]:0, [*][1]:32, [*][2]:64, ... [*][8]:256", "hidden": 0, "auto": 0, "target_conf": ["dem.gic_ct_lut"], "dependency": "user", "default": [0, 0, 0, 0, 0, 0, 0, 0, 0]}, "gic_limit_lut": {"display": "gic_limit_lut", "acc": [0, 8, 4], "range": [0.0, 255.9375], "type": "AX_U16", "size": [9], "hidden": 0, "auto": 0, "target_conf": ["dem.gic_limit_lut"], "dependency": "user", "default": [16.0, 16.0, 16.0, 16.0, 16.0, 16.0, 16.0, 16.0, 16.0], "comment": "gic limit lut, bigger value, stronger gic. lut[0]:0, [1]:32, [2]:64, ... [8]:256"}, "edge_direction_high_frequency_sensitivity": {"display": "edge direction high frequency sensitivity", "acc": [0, 1, 4], "range": [0.0, 1.0], "size": [], "default": 0.75, "comment": "bigger value==> more sensitivity to high freq", "hidden": 0, "target_conf": ["dem.gradf_ratio"], "type": "AX_U8", "auto": 0, "dependency": "user"}, "edge_direction_edge_sensitivity_lut": {"display": "edge direction edge sensitivity lut", "acc": [0, 4, 8], "range": [0.0, 15.99609375], "size": [9], "default": [0.5, 1.0, 1.5, 1.75, 2.0, 2.25, 2.5, 2.75, 3.0], "comment": "default inv noise lut. lut[0]:0, [1]:32, [2]:64, ... [8]:256", "hidden": 0, "target_conf": ["dem.inv_noise_lut"], "type": "AX_U16", "auto": 0, "dependency": "user"}, "edge_direction_hv_high_frequency_strength": {"display": "edge_direction_hv_high_frequency_strength", "acc": [0, 2, 2], "range": [0.0, 3.75], "size": [], "default": 1.25, "comment": "level for adjusting high frequency gradient", "hidden": 0, "target_conf": ["dem.grad_hh_level"], "type": "AX_U8", "auto": 0, "dependency": "user"}, "hv_interpolation_hf_coeff": {"display": "hv_interpolation_hf_coeff", "acc": [0, 1, 5], "range": [0.0, 1.0], "size": [], "default": 0.5, "comment": "level for adjusting high frequency gradient", "hidden": 0, "target_conf": ["dem.ginterp_hv_hf_ratio"], "type": "AX_U8", "auto": 0, "dependency": "user"}, "pn_interpolation_hf_coeff": {"display": "pn_interpolation_hf_coeff", "acc": [0, 1, 5], "range": [0.0, 1.0], "size": [], "default": 0.3125, "comment": "level for adjusting high frequency gradient", "hidden": 0, "target_conf": ["dem.ginterp_pn_hf_ratio"], "type": "AX_U8", "auto": 0, "dependency": "user"}, "x_interpolation_hf_coeff": {"display": "x_interpolation_hf_coeff", "acc": [0, 1, 5], "range": [0.0, 1.0], "size": [], "default": 0, "comment": "level for adjusting high frequency gradient", "hidden": 0, "target_conf": ["dem.ginterp_x_hf_ratio"], "type": "AX_U8", "auto": 0, "dependency": "user"}, "edge_enhance_enable": {"display": "edge_enhance_enable", "acc": [0, 1], "range": [0, 1], "size": [], "default": 0, "comment": "edge enhance enable, 0: disable, 1: enable", "hidden": 0, "target_conf": [], "type": "AX_U8", "auto": 0, "dependency": "user"}, "edge_smoothness_strength": {"display": "edge_smoothness_strength", "acc": [0, 1, 4], "size": [9], "range": [0.0, 1.0], "default": [0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25], "comment": "larger values means more smooth. lut[0]:0, [1]:32, [2]:64, ... [8]:256", "hidden": 0, "target_conf": ["dem.edge_enhance_level_lut"], "type": "AX_U8", "auto": 1, "dependency": "user"}, "edge_sharpness_strength": {"display": "edge_sharpness_strength", "acc": [0, 1, 4], "size": [9], "range": [0.0, 1.0], "default": [0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25], "comment": "larger values means more sharpen. lut[0]:0, [1]:32, [2]:64, ... [8]:256", "hidden": 0, "target_conf": ["dem.edge_enhance_level_lut"], "type": "AX_U8", "auto": 1, "dependency": "user"}, "fcc_enable": {"display": "fcc_enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 0, "comment": "fc cor enable", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "fcc_limit": {"display": "fcc_limit", "acc": [0, 8, 4], "type": "AX_U16", "size": [], "range": [0.0, 255.9375], "default": 0.0, "comment": "larger value means more pixels will be considered as false color pixels", "hidden": 0, "auto": 0, "target_conf": ["dem.fc_cor_limit"], "dependency": "user"}, "fcc_sensitivity": {"display": "fcc_sensitivity", "acc": [0, 2, 2], "range": [0.0, 3.75], "type": "AX_U8", "size": [], "default": 0, "comment": "larger value means higher fcc sensitivity, makes the corrected color more grayish", "hidden": 0, "auto": 1, "target_conf": ["dem.fc_cor_gain_safe"], "dependency": "user"}, "fcc_saturation_level": {"display": "fcc_saturation_level", "acc": [0, 4, 4], "type": "AX_U8", "size": [], "range": [0.0, 15.9375], "default": 2.0, "comment": "larger value means less pixels will be considered as false color pixels", "hidden": 0, "auto": 0, "target_conf": ["dem.fc_cor_th"], "dependency": "user"}, "fcc_saturation_lut": {"display": "fcc_saturation_lut", "acc": [0, 8, 4], "range": [0.0, 255.9375], "type": "AX_U16", "size": [9], "default": [0.5, 1.0, 2.0, 4.0, 8.0, 16.0, 16.0, 16.0, 16.0], "comment": "larger value means current pixel is higher possibility as false color and more need corrected color", "hidden": 0, "auto": 0, "target_conf": ["dem.fc_cor_sat_lut"], "dependency": "user"}, "fcc_strength": {"display": "fcc_strength", "acc": [0, 4, 4], "type": "AX_U8", "size": [], "range": [0.0, 15.9375], "default": 0.0, "comment": "larger value means stronger fcc strength, makes the corrected color more grayish", "hidden": 0, "auto": 1, "target_conf": ["dem.fc_cor_gain"], "dependency": "user"}, "fcc_sat_protect_level": {"display": "fcc_sat_protect_level", "acc": [0, 0, 8], "type": "AX_U8", "size": [], "range": [0.0, 0.99609375], "default": 0.015625, "comment": "smaller value means  more corrected color will be used in the final output", "hidden": 0, "auto": 0, "target_conf": ["dem.fc_cor_inv_base_sat_level"], "dependency": "user"}, "fcc_ratio_limit": {"display": "fcc_ratio_limit", "acc": [0, 1, 8], "type": "AX_U16", "size": [], "range": [0.0, 1.0], "default": 1.0, "comment": "false color correction ratio limit of upper limit", "hidden": 0, "auto": 0, "target_conf": ["dem.fc_cor_ratio_limit"], "dependency": "user"}, "offset_in": {"acc": [0, 8, 4], "range": [0.0, 255.9375], "size": [], "default": 16.0, "hidden": 1, "auto": 0, "target_conf": ["dem.offset_in"], "type": "AX_U16", "comment": "offset in", "dependency": "common", "display": "offset in"}, "offset_out": {"acc": [0, 8, 4], "range": [0.0, 255.9375], "size": [], "default": 16.0, "hidden": 1, "auto": 0, "target_conf": ["dem.offset_out"], "type": "AX_U16", "comment": "offset out", "dependency": "common", "display": "offset out"}, "wb_gain": {"display": "wb gain", "size": [4], "range": [0.0, 255.9375], "acc": [0, 4, 8], "default": [1.0, 1.0, 1.0, 1.0], "hidden": 1, "comment": "wb_gain. [0]: rr, [1]: gr, [2]: gb, [3]: bb", "dependency": "common", "target_conf": ["dem.gic_ct_lut"]}, "wbc_stat_sum": {"display": "", "acc": [1, 23, 4], "range": [-8388608.0, 8388607.9375], "type": "AX_S32", "size": [64, 3], "comment": "wbc stat sum, [[r_sum_00, g_sum_00, b_sum_00],...", "hidden": 1, "auto": 0, "target_conf": ["dem.wbc_gain_lut_r", "dem.wbc_gain_lut_g", "dem.wbc_gain_lut_b"], "dependency": "stat", "default": [[0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]]}, "wbc_stat_cnt": {"display": "", "acc": [0, 15], "range": [0, 32767], "type": "AX_U32", "size": [64], "comment": "wbc stat cnt, [[r_cnt_00, g_cnt_00, b_cnt_00],...", "hidden": 1, "auto": 0, "target_conf": ["dem.wbc_gain_lut_r", "dem.wbc_gain_lut_g", "dem.wbc_gain_lut_b"], "dependency": "stat", "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}, "submodules": {"setup": {"params": ["offset_in", "offset_out", "partition_info"], "configs": ["dem.enable", "dem.wb_stat_enable", "dem.wb_stat_mesh_shape", "dem.wbc_mesh_step", "dem.grad_h_enable", "dem.gclip_level", "dem.edge_enhance_ghv_corner_min", "dem.edge_enhance_ghv_corner_slope", "dem.wbc_enable", "dem.edge_enhance_ghv_corner_offset", "dem.wb_stat_slice_level", "dem.edge_enhance_ghv_level", "dem.edge_enhance_gpn_level", "dem.wb_stat_slice_sat_type", "dem.gic_enable", "dem.edge_enhance_enable", "dem.fc_cor_enable"]}, "dem_mode": {"params": ["enable", "mode"], "configs": ["dem.pre_gamma_enable", "dem.post_gamma_enable"]}, "dem_core": {"params": ["edge_direction_high_frequency_sensitivity", "edge_direction_edge_sensitivity_lut", "edge_direction_hv_high_frequency_strength", "hv_interpolation_hf_coeff", "pn_interpolation_hf_coeff", "x_interpolation_hf_coeff"], "configs": []}, "wbc": {"params": ["init_enable", "wbc_stat_sum", "wbc_stat_cnt", "hf_direction_estimation_enhance_level"], "configs": []}, "gic": {"params": ["gic_enable", "gic_strength_factor", "gic_strength_lut", "gic_ct_lut", "gic_limit_lut", "wb_gain"], "configs": []}, "fcc": {"params": ["fcc_enable", "fcc_limit", "fcc_sensitivity", "fcc_saturation_level", "fcc_saturation_lut", "fcc_strength", "fcc_sat_protect_level", "fcc_ratio_limit"], "configs": []}, "sharpen": {"params": ["edge_enhance_enable", "edge_smoothness_strength", "edge_sharpness_strength"], "configs": []}}, "target_module": {"mc20l": {"dem": {"id": 3000, "method": 0}}}, "structs": {}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}}