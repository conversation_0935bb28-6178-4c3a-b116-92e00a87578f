{"enable": {"api": "nRltmEn", "display": "enable", "comments": "SW enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "algo_mode": {"acc": [0, 2, 0], "size": [], "range": [0, 2], "api": "nAlgMode", "display": "Alg Mode", "comments": "0: effect priority mode  1: balance mode 2: performance mode", "hint": "Accuracy: U2.0 Range: [0, 2]", "sdk_only": 1, "associated module": "rltm"}, "multi_cam_sync_mode": {"acc": [0, 2, 0], "size": [], "range": [0, 2], "api": "nMultiCamSyncMode", "display": "Multi Cam Sync Mode", "comments": "0：INDEPEND MODE; 1： MASTER SLAVE MODE; 2: OVERLAP MODE", "hint": "Accuracy: U2.0 Range: [0, 2]", "sdk_only": 1, "associated module": "rltm"}, "gtm_sw_dgain_enable": {"api": "nGtmSwEn", "display": "gtmSwDgainEnable", "comments": "enable dgain mode", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "gtm_sw_dgain": {"api": "nGtmSwDgain", "display": "gtmSwDgain", "comments": "dgain when sw gain is on", "hint": "Accuracy: U8.8 Range: [256, 65535]"}, "alpha": {"api": "nAlpha", "display": "alpha", "comments": "temporal filter strength", "hint": "Accuracy: U1.15 Range: [0, 32768]"}, "reset": {"api": "nReset", "display": "reset", "comments": "the num frame that use current rltm curve only", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "stop_updating": {"api": "nStopUpdating", "display": "stopUpdating", "comments": "stop updating curve", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "dither_mode": {"api": "nDitherMode", "display": "ditherMode", "comments": "dither enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "dither_scale": {"api": "nDitherScale", "display": "ditherScale", "comments": "dither strength", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "luma_weight": {"api": "nLumaWeight", "display": "lumaWeight", "comments": "weight of luma_max and luma_rgb for luma and luma_detail calculate", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "highlight_sup": {"api": "nHighlightSup", "display": "highlightSup", "comments": "highlight suppression", "hint": "Accuracy: U5.3 Range: [0, 255]"}, "pst_gamma": {"api": "nPostGamma", "display": "pstGamma", "comments": "pst gamma", "hint": "Accuracy: U3.5 Range: [32, 255]"}, "local_factor": {"api": "nLocalFactor", "display": "localFactor", "comments": "factor for local hist and global hist", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "rltm_strength": {"api": "nRltmStrength", "display": "rltmStrength", "comments": "rltm strength", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "contrast_strength": {"api": "nContrastStrength", "display": "contrastStrength", "comments": "contrast strength", "hint": "Accuracy: U1.7 Range: [1, 255]"}, "dark_contrast_enhance": {"api": "nDarkContrastEnhc", "display": "darkContrastEnhc", "comments": "Max Luma Value & Pixel Num for Contrast Enhance", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "k_max": {"api": "nKMax", "display": "kMax", "comments": "max gain for rltm", "hint": "Accuracy: U12.4 Range: [16, 65535]"}, "base_curve_strength": {"api": "nBaseCurveStrength", "display": "baseCurveStrength", "comments": "rltm base curve strength", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "base_curve": {"api": "nBaseCurve", "display": "baseCurve", "comments": "base-curve", "hint": "Accuracy: U1.15 Range: [0, 32768]"}, "s_curve": {"api": "nSCurveList", "display": "sCurve", "comments": "s-curve", "hint": "Accuracy: U1.15 Range: [0, 32768]"}, "detail_coring_pos": {"api": "nDetailCoringPos", "display": "detailCoringPos", "comments": "the coring for high freq pos detail", "hint": "Accuracy: U3.13 Range: [0, 65535]"}, "detail_coring_neg": {"api": "nDetailCoringNeg", "display": "detailCoringNeg", "comments": "the coring for high freq neg detail", "hint": "Accuracy: U3.13 Range: [0, 65535]"}, "detail_gain_pos": {"api": "nDetailGainPos", "display": "detailGainPos", "comments": "the gain for high freq pos detail", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "detail_gain_neg": {"api": "nDetailGainNeg", "display": "detailGainNeg", "comments": "the gain for high freq neg detail", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "detail_limit_pos": {"api": "nDetailGainLimitPos", "display": "detailLimitPos", "comments": "the limit for high freq pos detail", "hint": "Accuracy: U3.13 Range: [0, 65535]"}, "detail_limit_neg": {"api": "nDetailGainLimitNeg", "display": "detailLimitNeg", "comments": "the limit for high freq neg detail", "hint": "Accuracy: U3.13 Range: [0, 65535]"}, "detail_extra_strength_pos": {"api": "nDetailExtraStrengthPos", "display": "detailExtraStrengthPos", "comments": "the global strength for high freq pos detail", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "detail_extra_strength_neg": {"api": "nDetailExtraStrengthNeg", "display": "detailExtraStrengthNeg", "comments": "the global strength for high freq neg detail", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "detail_strength_lut": {"api": "nDetailStrengthLut", "display": "detailStrengthLut", "comments": "the strength for high freq detail by detail", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "detail_sigma_dis": {"api": "nDetailSigmaDis", "display": "detailSigmaDis", "comments": "bilateral filter sigma distance", "hint": "Accuracy: U3.13 Range: [1, 65535]"}, "detail_sigma_val": {"api": "nDetailSigmaVal", "display": "detailSigmaVal", "comments": "bilateral filter sigma value", "hint": "Accuracy: U3.13 Range: [1, 65535]"}, "downsample_ratio": {"api": "nDownsampleRatio", "display": "downsampleRatio", "comments": "the downsample ratio of low freq luma", "hint": "Accuracy: U8.0 Range: [0, 1]"}, "detail_low_enable": {"api": "nRltmDetailLowEn", "display": "detailLowEnable", "comments": "low freq enhance enable", "hint": "Accuracy: U8.0 Range: [0, 1]"}, "detail_gain_pos_low": {"api": "nDetailGainPosLow", "display": "detailGainPosLow", "comments": "the gain for low freq pos detail", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "detail_gain_neg_low": {"api": "nDetailGainNegLow", "display": "detailGainNegLow", "comments": "the gain for low freq neg detail", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "detail_limit_pos_low": {"api": "nDetailLimitPosLow", "display": "detailLimitPosLow", "comments": "the limit for low freq pos detail", "hint": "Accuracy: U3.13 Range: [0, 65535]"}, "detail_limit_neg_low": {"api": "nDetailLimitNegLow", "display": "detailLimitNegLow", "comments": "the limit for low freq neg detail", "hint": "Accuracy: U3.13 Range: [0, 65535]"}, "detail_strength_lut_pos_low": {"api": "nDetailStrengthLutPosLow", "display": "detailStrengthLutPosLow", "comments": "the strength for low freq pos detail by luma", "hint": "Accuracy: U1.7 Range: [0, 255]"}, "detail_strength_lut_neg_low": {"api": "nDetailStrengthLutNegLow", "display": "detailStrengthLutNegLow", "comments": "the strength for low freq neg detail by luma", "hint": "Accuracy: U1.7 Range: [0, 255]"}, "coeff_win_r": {"api": "nCoeffWinRad", "display": "coeffWinR", "comments": "guided filter win_radius", "hint": "Accuracy: U3.0 Range: [0, 5]"}, "coeff_eps": {"api": "nCoeffEps", "display": "coeff<PERSON><PERSON>", "comments": "guided filter eps", "hint": "Accuracy: U16.0 Range: [0, 65535]"}, "sigma_dis_pst": {"api": "nSigmaDisPst", "display": "sigmaDisPst", "comments": "spatial domain weight low freq", "hint": "Accuracy: U3.13 Range: [1, 65535]"}, "sigma_val_pst": {"api": "nSigmaValPst", "display": "sigmaValPst", "comments": "intensity domain weight low freq", "hint": "Accuracy: U3.13 Range: [1, 2048]"}, "sigma_dis_blur": {"api": "nSigmaDisBlur", "display": "sigmaDisBlur", "comments": "blur coeff a and b", "hint": "Accuracy: U3.13 Range: [1, 65535]"}, "extra_info": {"BuildScurve": {"attr": "REF", "comment": "RO: only support get api, RW: support set and get api, REF: directly use,  NULL: only support struct", "params": {"pIspScurveParam": {"type": "AX_ISP_IQ_SCURVE_PARAM_T", "size": [], "api": "pIspScurveParam", "member": {"nShadowPoint": {"type": "AX_U8", "size": [], "api": "nShadowPoint", "display": "shadow_point", "comments": "shadow point position", "hint": "Accuracy: U0.8 Range: [1, 255]"}, "nShadowStrength": {"type": "AX_U8", "size": [], "api": "nShadowStrength", "display": "shadow_strength", "comments": "shadow point strength", "hint": "Accuracy: U0.8 Range: [1, 255]"}, "nHighlightPoint": {"type": "AX_U8", "size": [], "api": "nHighlightPoint", "display": "highlight_point", "comments": "highlight point position", "hint": "Accuracy: U0.8 Range: [1, 255]"}, "nHighlightStrength": {"type": "AX_U8", "size": [], "api": "nHighlightStrength", "display": "highlight_strength", "comments": "highlight point strength", "hint": "Accuracy: U0.8 Range: [1, 255]"}, "nShadowControl": {"type": "AX_U8", "size": [], "api": "nShadowControl", "display": "shadow_control", "comments": "shadow control", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "nMiddleControl": {"type": "AX_U8", "size": [], "api": "nMiddleControl", "display": "middle_control", "comments": "middle control", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "nHighlightControl": {"type": "AX_U8", "size": [], "api": "nHighlightControl", "display": "highlight_control", "comments": "highlight control", "hint": "Accuracy: U0.8 Range: [0, 255]"}}}, "nSCurveList": {"type": "AX_U16", "size": [1025], "api": "nSCurveList"}}}, "BuildBaseCurve": {"attr": "REF", "comment": "RO: only support get api, RW: support set and get api, REF: directly use,  NULL: only support struct", "params": {"pIspBaseCurveParam": {"type": "AX_ISP_IQ_BASECURVE_PARAM_T", "size": [], "api": "pIspBaseCurveParam", "member": {"nDarkSuppress": {"type": "AX_U8", "size": [], "api": "nDarkSuppress", "display": "dark_suppress", "comments": "dark suppress", "hint": "Accuracy: U8.0 Range: [1, 30]"}, "nBrightEnhance": {"type": "AX_U8", "size": [], "api": "nBrightEnhance", "display": "bright_enhance", "comments": "bright enhance", "hint": "Accuracy: U8.0 Range: [150, 210]"}, "nHorizontalStretch": {"type": "AX_U8", "size": [], "api": "nHorizontalStretch", "display": "horizontal_stretch", "comments": "base curve horizontal stretch", "hint": "Accuracy: U8.0 Range: [30, 60]"}, "nVerticalCompress": {"type": "AX_U8", "size": [], "api": "nVerticalCompress", "display": "vertical_compress", "comments": "base curve vertical compress", "hint": "Accuracy: U8.0 Range: [100, 200]"}}}, "nBaseCurveList": {"type": "AX_U16", "size": [513], "api": "nBaseCurveList"}}}}}