{"enable": {"api": "nCcEn", "display": "enable", "comments": "clc enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "mode": {"api": "eMode", "display": "mode", "comments": "clc ctrl mode, 0 for basic (global); 1 for advanced (16 phase)", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "ctrl_luma_ratio": {"api": "nCtrlLumaRatio", "display": "ctrlLumaRatio", "comments": "clc control luma ratio, sum will be 1.0", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "ccm": {"api": "nCcm", "display": "ccm", "comments": "clc ccm matrix", "hint": "Accuracy: S3.8 Range: [-2047, 2047]"}, "ccm_sat": {"api": "nCcmSat", "display": "ccmSat", "comments": "clc global saturation adjust", "hint": "Accuracy: S1.6 Range: [-64, 64]"}, "ccm_hue": {"api": "nCcmHue", "display": "ccmHue", "comments": "clc global hue adjust", "hint": "Accuracy: S5.6 Range: [-1920, 1920]"}, "ccm_ctrl_level": {"api": "nCcmCtrlLevel", "display": "ccmCtrlLevel", "comments": "clc global adjust ctrl level", "hint": "Accuracy: U1.8 Range: [0, 256]"}, "xcm_sats": {"api": "nXcmSats", "display": "xcmSats", "comments": "clc local saturation adjust", "hint": "Accuracy: S1.6 Range: [-32, 32]"}, "xcm_hues": {"api": "nXcmHues", "display": "xcmHues", "comments": "clc local hue adjust", "hint": "Accuracy: S5.6 Range: [-640, 640]"}, "xcm_ctrl_levels": {"api": "nXcmCtrlLevels", "display": "xcmCtrlLevels", "comments": "clc local adjust ctrl level", "hint": "Accuracy: U1.8 Range: [0, 256]"}, "sat_mit_enable": {"api": "nSatMitEnable", "display": "satMitEnable", "comments": "clc sat_mit_enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "sat_mit_ratio_lower_lut": {"api": "nSatMitRatioLowerLut", "display": "satMitRatioLowerLut", "comments": "clc sat_mit_ratio_lower_lut", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sat_mit_ratio_upper_lut": {"api": "nSatMitRatioUpperLut", "display": "satMitRatioUpperLut", "comments": "clc sat_mit_ratio_upper_lut", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sat_mit_lower_cp": {"api": "nSatMitLowerCp", "display": "satMitLowerCp", "comments": "clc sat_mit_lower's control point with [x0, y0, x1, y1, x2, y2], [x2, y2] is the dividing point from y=x", "hint": "Accuracy: S8.0 Range: [-255, 255]"}, "sat_mit_upper_cp": {"api": "nSatMitUpperCp", "display": "satMitUpperCp", "comments": "clc sat_mit_upper's control point with [x0, y0, x1, y1, x2, y2], [x0, y0] is the dividing point from y=x, with y0, y1, y2 <= 255", "hint": "Accuracy: U9.0 Range: [0, 511]"}}