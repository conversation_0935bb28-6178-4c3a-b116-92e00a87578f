h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| d_gain |  | u10.8 | AX_U32 | [\] |  [0, 262143\] | [0.0, 1023.99609375\] | 65536 | 256.0 | hidden | 'wbc.d_gain' | isp d_gain | common |
| wbc_gain |  | u4.8 | AX_U16 | [4\] |  [0, 4095\] | [0.0, 15.99609375\] | [256, 256, 256, 256\] | [1.0, 1.0, 1.0, 1.0\] | hidden | 'wbc.wb_gain' | wbc r/gr/gb/b gain | common |
| clip_level |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1048512 | 16383.0 | hidden | 'wbc.clip_level' | Obtained from ACP, represents the maximum range of significant values. It is equivalent to a white level without offset. | common |
| offset_in |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1024 | 16.0 | hidden | 'wbc.offset_in' | wbc offset in | common |
| offset_out |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1024 | 16.0 | hidden | 'wbc.offset_out' | wbc offset out | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'wbc.wb_gain' | SW enable |   | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |