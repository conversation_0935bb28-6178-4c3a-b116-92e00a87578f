{"input_format": {"api": "nInputFormat", "display": "inputFormat", "comments": "input formats", "hint": "Accuracy: U6.0 Range: [0, 63]"}, "input_0_size": {"api": "nIntput0Size", "display": "intput0Size", "comments": "input 0 size", "hint": "Accuracy: U13.0 Range: [0, 4096]"}, "input_1_size": {"api": "nIntput1Size", "display": "intput1Size", "comments": "input 1 size", "hint": "Accuracy: U13.0 Range: [0, 4096]"}, "input_2_size": {"api": "nIntput2Size", "display": "intput2Size", "comments": "input 2 size", "hint": "Accuracy: U13.0 Range: [0, 4096]"}, "input_offset_x": {"api": "nInputOffsetX", "display": "inputOffsetX", "comments": "input offset x", "hint": "Accuracy: S13.10 Range: [0, 4194304]"}, "output_stride": {"api": "nOutputStride", "display": "outputStride", "comments": "output stride", "hint": "Accuracy: U13.0 Range: [0, 4096]"}, "stream_mux": {"api": "nQuadMux", "display": "quadMux", "comments": "Choose which input pic to draw quad on", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "output_format": {"api": "nOutputFormat", "display": "outputFormat", "comments": "output formats", "hint": "Accuracy: U6.0 Range: [0, 63]"}, "quad_enable": {"api": "bQuadEnable", "display": "quadEnable", "comments": "Quad enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "quad_mux": {"api": "nQuadMux", "display": "quadMux", "comments": "Choose which input pic to draw quad on", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "quad_x_coords": {"api": "nXCoords", "display": "xCoords", "comments": "x coordinates of intersection points between quad's edges, and image rectangle which is constrained by y_st and y_ed.", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "quad_y_coords": {"api": "nYCoords", "display": "yCoords", "comments": "y coordinates of intersection points between quad's edges, and image rectangle which is constrained by y_st and y_ed.", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "quad_y_start": {"api": "nYStart", "display": "yStart", "comments": "y start coordinates of quad", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "quad_y_end": {"api": "nInYEnd", "display": "inYEnd", "comments": "y start coordinates of quad", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "quad_mode": {"api": "nQuadMode", "display": "quadMode", "comments": "Quad feature mode control bit. 0 - mosaic; 1 - fill", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "quad_mosaic_step_size": {"api": "nMosaicStepSize", "display": "mosaicStepSize", "comments": "Mosaic step size", "hint": "Accuracy: U7.0 Range: [0, 127]"}, "quad_fill_luma": {"api": "nFillLuma", "display": "fillLuma", "comments": "quad luma", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "quad_fill_chroma": {"api": "nFillChroma", "display": "fillChroma", "comments": "quad chroma(with 128 bias)", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "quad_fill_mode": {"api": "nFillChroma", "display": "fillChroma", "comments": "Quad fill mode. 0 - inner Quad; 1 - outter Quad; 2 - inner clear Quad; 3 - outter clear Quad", "hint": "Accuracy: U2.0 Range: [0, 3]"}, "quad_alpha": {"api": "nAlpha", "display": "alpha", "comments": "Quad blending alpha", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "scale_enable_0": {"api": "bScaleEnable0", "display": "scaleEnable", "comments": "Scalar enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_output_size_0": {"api": "nOutputSize0", "display": "outputSize", "comments": "Scalar output size", "hint": "Accuracy: U13.0 Range: [0, 4096]"}, "scale_filter_kernel_mode_0": {"api": "nScaleFilterKernelMode0", "display": "scaleFilterKernelMode", "comments": "Kernel mode to be selected, 0:triangle, 1:cubic", "hint": "Accuracy: U1 Range: [0, 1]"}, "scale_filter_bicubic_a_0": {"api": "nScaleFilterBicubicA0", "display": "scaleFilterBicubicA", "comments": "Param a of bicubic. The less the sharper. the large the more smooth", "hint": "Accuracy: S1.10 Range: [-1024, 1024]"}, "shp_enable": {"api": "bShpEnable", "display": "shpEnable", "comments": "sharpen enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "shp_ud_gain_lut": {"api": "nUdGainLut", "display": "udGainLut", "comments": "set undirectional gain w.r.t. texture strength", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "shp_ud_gain_lut_anchor": {"api": "nUdGainLutAnchor", "display": "udGainLutAnchor", "comments": "smaller value means more anchor points in flat/weak texture regions", "hint": "Accuracy: U3.0 Range: [0, 7]"}, "shp_dir_gain_lut": {"api": "nDirGainLut", "display": "dir<PERSON><PERSON><PERSON><PERSON>", "comments": "set directional gain w.r.t. edge strength", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "shp_dir_gain_lut_anchor": {"api": "nDirGainLutAnchor", "display": "dirGainLutAnchor", "comments": "smaller value means more anchor points in flat/weak texture regions", "hint": "Accuracy: U3.0 Range: [0, 7]"}, "shp_ud_freq": {"api": "nUdFreq", "display": "udFreq", "comments": "larger value means higher undirectional frequecny will be enhanced", "hint": "Accuracy: U5.5 Range: [0, 1023]"}, "shp_dir_freq": {"api": "nDirFreq", "display": "dir<PERSON><PERSON><PERSON>", "comments": "larger value means higher directional frequecny will be enhanced", "hint": "Accuracy: U5.5 Range: [0, 1023]"}, "shp_edge_strength": {"api": "nEdgeStr", "display": "edgeStr", "comments": "larger value means to use more directional filter's result", "hint": "Accuracy: U4.0 Range: [0, 15]"}, "shp_edge_threshold": {"api": "nEdgeThr", "display": "edgeThr", "comments": "larger value means less pixels will be considered as edge pixels", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "shp_edge_smooth_thin_ratio": {"api": "nEdgeSmoothThinRatio", "display": "edgeSmoothThinRatio", "comments": "larger value means to make the edge much thinner", "hint": "Accuracy: U4.0 Range: [0, 15]"}, "shp_overshoot": {"api": "nOvershoot", "display": "overshoot", "comments": "larger value means stronger overshoot", "hint": "Accuracy: U0.7 Range: [0, 127]"}, "shp_undershoot": {"api": "nUndershoot", "display": "undershoot", "comments": "larger value means stronger undershoot", "hint": "Accuracy: U0.7 Range: [0, 127]"}, "shp_detail_region_threshold": {"api": "nDetailRegionThr", "display": "detailRegionThr", "comments": "larger value means more pixels will be considered as detail region", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "shp_detail_overshoot_adjust": {"api": "nDetailOvershootAdj", "display": "detailOvershootAdj", "comments": "larger value means stronger overshoot for detail region", "hint": "Accuracy: S0.7 Range: [-127, 127]"}, "shp_detail_undershoot_adjust": {"api": "nDetailUndershootAdj", "display": "detailUndershootAdj", "comments": "larger value means stronger undershoot for detail region", "hint": "Accuracy: S0.7 Range: [-127, 127]"}, "shp_shoot_release_ratio": {"api": "nShootReleaseRatio", "display": "shootReleaseRatio", "comments": "larger value means shoot suppression tends to ignore more weak texture/detail region", "hint": "Accuracy: U1.4 Range: [0, 16]"}, "shp_shoot_sup_range": {"api": "nShootSupRange", "display": "shootSupRange", "comments": "larger value means shoot suppression tends to control more edges", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "shp_shoot_sup_strength": {"api": "nShootSupStr", "display": "shootSupStr", "comments": "larger value means stronger shoot suppression on edges", "hint": "Accuracy: U0.7 Range: [0, 127]"}, "shp_limit": {"api": "nShpLimit", "display": "shpLimit", "comments": "smaller value means more restriction on the pixel value change, and may reduce the number of white/black points", "hint": "Accuracy: U5.5 Range: [0, 1023]"}, "shp_luma_gain_lut_negative": {"api": "nLumaGainLutNeg", "display": "lumaGainLutNeg", "comments": "adjust sharpen gain for negative hf based on image brightness", "hint": "Accuracy: U1.5 Range: [0, 63]"}, "shp_luma_gain_lut_positive": {"api": "nLumaGainLutPos", "display": "lumaGainLutPos", "comments": "adjust sharpen gain for positive hf based on image brightness", "hint": "Accuracy: U1.5 Range: [0, 63]"}, "shp_nr_strength": {"api": "nNrStr", "display": "nrStr", "comments": "larger value means stronger noise reduction", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "shp_nr_ud_texture_sensitivity": {"api": "nNrUdTextureSens", "display": "nrUdTextureSens", "comments": "larger value means image has more undirectional textures and apply less noise reduction", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "shp_nr_ud_texture_threshold": {"api": "nNrUdTextureThr", "display": "nrUdTextureThr", "comments": "larger value means less pixels will be considered as real texture and apply more noise reduction", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "shp_nr_ud_limit": {"api": "nNrUdLimit", "display": "nrUdLimit", "comments": "larger value means less restriction on undirectional noise reduction", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "shp_ud_coring": {"api": "nUdCoring", "display": "udCoring", "comments": "larger value means stronger coring for undirectional details", "hint": "Accuracy: U3.3 Range: [0, 63]"}, "shp_nr_dir_edge_sensitivity": {"api": "nNrDirEdgeSens", "display": "nrDirEdgeSens", "comments": "larger value means image has more directional edges and apply less noise reduction", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "shp_nr_dir_edge_threshold": {"api": "nNrDirEdgeThr", "display": "nrDirEdgeThr", "comments": "larger value means less pixels will be considered as real edge and apply more noise reduction", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "shp_nr_dir_limit": {"api": "nNrDirLimit", "display": "nrDirLimit", "comments": "larger value means less restriction on directional noise reduction", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "shp_dir_coring": {"api": "nDirCoring", "display": "dirCoring", "comments": "larger value means stronger coring for directional details", "hint": "Accuracy: U3.3 Range: [0, 63]"}, "rect_enable_0": {"api": "bRectEnable0", "display": "rectEnable", "comments": "Rectangles' enable control bits", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "rect_tl_coord_x_0": {"api": "nTlCoordX0", "display": "tlCoordX", "comments": "top-left x coordinates of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_tl_coord_y_0": {"api": "nTlCoordY0", "display": "tlCoordY", "comments": "top-left y coordinates of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_width_0": {"api": "nRectWidth0", "display": "rectWidth", "comments": "width of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_height_0": {"api": "nRectHeight0", "display": "rectHeight", "comments": "height of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_edge_thickness_0": {"api": "nEdgeThickness0", "display": "edgeThickness", "comments": "Rectangles' edge thickness", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "rect_edge_color_select_0": {"api": "nEdgeColorSelect0", "display": "edgeColorSelect", "comments": "Rectangles' color select", "hint": "Accuracy: U2.0 Range: [0, 3]"}, "rect_edge_color_val_0": {"api": "nEdgeColorVal0", "display": "edgeColorVal", "comments": "Rectangles' value to be selected", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "roi_luma_sum_tl_coords_0": {"api": "nRoiLumaSumTlCoords0", "display": "roiLumaSumTlCoords", "comments": "top-left corner coordinates of 8 roi areas", "hint": "Accuracy: U12.0 Range: [0, 4095]"}, "roi_luma_sum_br_coords_0": {"api": "nRoiLumaSumBrCoords0", "display": "roiLumaSumBrCoords", "comments": "bottom-right corner coordinates of 8 roi areas", "hint": "Accuracy: U12.0 Range: [0, 4095]"}, "osd_enable_0": {"api": "bOsdEnable0", "display": "osdEnable", "comments": "osd enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "osd_tl_coord_x_0": {"api": "nTlCoordX0", "display": "tlCoordX", "comments": "osd blending top-left coord(x)", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "osd_tl_coord_y_0": {"api": "nTlCoordY0", "display": "tlCoordY", "comments": "osd blending top-left coord(y)", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "osd_width_0": {"api": "nOsdWidth0", "display": "osdWidth", "comments": "osd blending width", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "osd_height_0": {"api": "nOsdHeight0", "display": "osdHeight", "comments": "osd blending height", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "yclip_enable_0": {"api": "bYClipEnable0", "display": "yClipEnable", "comments": "Luma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "yclip_gain_0": {"api": "nYClipGain0", "display": "yClipGain", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: S4.8 Range: [-4096, 4095]"}, "yclip_boundary_0": {"api": "nYClipBoundary0", "display": "yClipBoundary", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "cclip_enable_0": {"api": "bCClipEnable0", "display": "cClipEnable", "comments": "Chroma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "cclip_cmtx_0": {"api": "nCClipCmtx0", "display": "cClipCmtx", "comments": "2x2 chroma adjustment matrix", "hint": "Accuracy: U2.5 Range: [-128, 127]"}, "cclip_boundary_0": {"api": "nCClipBoundary0", "display": "cClipBoundary", "comments": "2x2 chroma adjustment --> clip", "hint": "Accuracy: S8.0 Range: [-256, 255]"}, "crop_enable_0": {"api": "bCropEnable0", "display": "cropEnable", "comments": "Crop enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "crop_start_0": {"api": "nCropStart0", "display": "cropStart", "comments": "Crop start coordinates", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "crop_size_0": {"api": "nCropSize0", "display": "cropSize", "comments": "Crop size", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "lba_enable_0": {"api": "bLbaEnable0", "display": "lbaEnable", "comments": "lba enable control bit", "hint": "Accuracy: 1.0 Range: [0, 1]"}, "lba_exd_size_0": {"api": "nLbaExdSize0", "display": "lbaExdSize", "comments": "lba extension size(paddings on edges)", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "lba_background_0": {"api": "nLbaBackground0", "display": "lbaBackground", "comments": "lba background color", "hint": "Accuracy: S8.0 Range: [-256, 256]"}, "scale_input_mux_1": {"api": "ninputMux1", "display": "inputMux", "comments": "Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_enable_1": {"api": "bScaleEnable1", "display": "scaleEnable", "comments": "Scalar enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_output_size_1": {"api": "nOutputSize1", "display": "outputSize", "comments": "Scalar output size", "hint": "Accuracy: U13.0 Range: [0, 4096]"}, "rect_enable_1": {"api": "bRectEnable1", "display": "rectEnable", "comments": "Rectangles' enable control bits", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "rect_tl_coord_x_1": {"api": "nTlCoordX1", "display": "tlCoordX", "comments": "top-left x coordinates of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_tl_coord_y_1": {"api": "nTlCoordY1", "display": "tlCoordY", "comments": "top-left y coordinates of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_width_1": {"api": "nRectWidth1", "display": "rectWidth", "comments": "width of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_height_1": {"api": "nRectHeight1", "display": "rectHeight", "comments": "height of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_edge_thickness_1": {"api": "nEdgeThickness1", "display": "edgeThickness", "comments": "Rectangles' edge thickness", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "rect_edge_color_select_1": {"api": "nEdgeColorSelect1", "display": "edgeColorSelect", "comments": "Rectangles' color select", "hint": "Accuracy: U2.0 Range: [0, 3]"}, "rect_edge_color_val_1": {"api": "nEdgeColorVal1", "display": "edgeColorVal", "comments": "Rectangles' value to be selected", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "yclip_enable_1": {"api": "bYClipEnable1", "display": "yClipEnable", "comments": "Luma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "yclip_gain_1": {"api": "nYClipGain1", "display": "yClipGain", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: S4.8 Range: [-4096, 4095]"}, "yclip_boundary_1": {"api": "nYClipBoundary1", "display": "yClipBoundary", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "cclip_enable_1": {"api": "bCClipEnable1", "display": "cClipEnable", "comments": "Chroma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "cclip_cmtx_1": {"api": "nCClipCmtx1", "display": "cClipCmtx", "comments": "2x2 chroma adjustment matrix", "hint": "Accuracy: U2.5 Range: [-128, 127]"}, "cclip_boundary_1": {"api": "nCClipBoundary1", "display": "cClipBoundary", "comments": "2x2 chroma adjustment --> clip", "hint": "Accuracy: S8.0 Range: [-256, 255]"}, "crop_enable_1": {"api": "bCropEnable1", "display": "cropEnable", "comments": "Crop enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "crop_start_1": {"api": "nCropStart1", "display": "cropStart", "comments": "Crop start coordinates", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "crop_size_1": {"api": "nCropSize1", "display": "cropSize", "comments": "Crop size", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "lba_enable_1": {"api": "bLbaEnable1", "display": "lbaEnable", "comments": "lba enable control bit", "hint": "Accuracy: 1.0 Range: [0, 1]"}, "lba_exd_size_1": {"api": "nLbaExdSize1", "display": "lbaExdSize", "comments": "lba extension size(paddings on edges)", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "lba_background_1": {"api": "nLbaBackground1", "display": "lbaBackground", "comments": "lba background color", "hint": "Accuracy: S8.0 Range: [-256, 256]"}, "scale_input_mux_2": {"api": "ninputMux2", "display": "inputMux", "comments": "Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_enable_2": {"api": "bScaleEnable2", "display": "scaleEnable", "comments": "Scalar enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_output_size_2": {"api": "nOutputSize2", "display": "outputSize", "comments": "Scalar output size", "hint": "Accuracy: U13.0 Range: [0, 4096]"}, "scale_filter_kernel_mode_2": {"api": "nScaleFilterKernelMode2", "display": "scaleFilterKernelMode", "comments": "Kernel mode to be selected, 0:triangle, 1:cubic", "hint": "Accuracy: U1 Range: [0, 1]"}, "scale_filter_bicubic_a_2": {"api": "nScaleFilterBicubicA2", "display": "scaleFilterBicubicA", "comments": "Param a of bicubic. The less the sharper. the large the more smooth", "hint": "Accuracy: S1.10 Range: [-1024, 1024]"}, "rect_enable_2": {"api": "bRectEnable2", "display": "rectEnable", "comments": "Rectangles' enable control bits", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "rect_tl_coord_x_2": {"api": "nTlCoordX2", "display": "tlCoordX", "comments": "top-left x coordinates of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_tl_coord_y_2": {"api": "nTlCoordY2", "display": "tlCoordY", "comments": "top-left y coordinates of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_width_2": {"api": "nRectWidth2", "display": "rectWidth", "comments": "width of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_height_2": {"api": "nRectHeight2", "display": "rectHeight", "comments": "height of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_edge_thickness_2": {"api": "nEdgeThickness2", "display": "edgeThickness", "comments": "Rectangles' edge thickness", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "rect_edge_color_select_2": {"api": "nEdgeColorSelect2", "display": "edgeColorSelect", "comments": "Rectangles' color select", "hint": "Accuracy: U2.0 Range: [0, 3]"}, "rect_edge_color_val_2": {"api": "nEdgeColorVal2", "display": "edgeColorVal", "comments": "Rectangles' value to be selected", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "yclip_enable_2": {"api": "bYClipEnable2", "display": "yClipEnable", "comments": "Luma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "yclip_gain_2": {"api": "nYClipGain2", "display": "yClipGain", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: S4.8 Range: [-4096, 4095]"}, "yclip_boundary_2": {"api": "nYClipBoundary2", "display": "yClipBoundary", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "cclip_enable_2": {"api": "bCClipEnable2", "display": "cClipEnable", "comments": "Chroma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "cclip_cmtx_2": {"api": "nCClipCmtx2", "display": "cClipCmtx", "comments": "2x2 chroma adjustment matrix", "hint": "Accuracy: U2.5 Range: [-128, 127]"}, "cclip_boundary_2": {"api": "nCClipBoundary2", "display": "cClipBoundary", "comments": "2x2 chroma adjustment --> clip", "hint": "Accuracy: S8.0 Range: [-256, 255]"}, "crop_enable_2": {"api": "bCropEnable2", "display": "cropEnable", "comments": "Crop enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "crop_start_2": {"api": "nCropStart2", "display": "cropStart", "comments": "Crop start coordinates", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "crop_size_2": {"api": "nCropSize2", "display": "cropSize", "comments": "Crop size", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "lba_enable_2": {"api": "bLbaEnable2", "display": "lbaEnable", "comments": "lba enable control bit", "hint": "Accuracy: 1.0 Range: [0, 1]"}, "lba_exd_size_2": {"api": "nLbaExdSize2", "display": "lbaExdSize", "comments": "lba extension size(paddings on edges)", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "lba_background_2": {"api": "nLbaBackground2", "display": "lbaBackground", "comments": "lba background color", "hint": "Accuracy: S8.0 Range: [-256, 256]"}, "scale_input_mux_3": {"api": "ninputMux3", "display": "inputMux", "comments": "Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_enable_3": {"api": "bScaleEnable3", "display": "scaleEnable", "comments": "Scalar enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_output_size_3": {"api": "nOutputSize3", "display": "outputSize", "comments": "Scalar output size", "hint": "Accuracy: U13.0 Range: [0, 4096]"}, "rect_enable_3": {"api": "bRectEnable3", "display": "rectEnable", "comments": "Rectangles' enable control bits", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "rect_tl_coord_x_3": {"api": "nTlCoordX3", "display": "tlCoordX", "comments": "top-left x coordinates of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_tl_coord_y_3": {"api": "nTlCoordY3", "display": "tlCoordY", "comments": "top-left y coordinates of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_width_3": {"api": "nRectWidth3", "display": "rectWidth", "comments": "width of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_height_3": {"api": "nRectHeight3", "display": "rectHeight", "comments": "height of rectangles", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "rect_edge_thickness_3": {"api": "nEdgeThickness3", "display": "edgeThickness", "comments": "Rectangles' edge thickness", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "rect_edge_color_select_3": {"api": "nEdgeColorSelect3", "display": "edgeColorSelect", "comments": "Rectangles' color select", "hint": "Accuracy: U2.0 Range: [0, 3]"}, "rect_edge_color_val_3": {"api": "nEdgeColorVal3", "display": "edgeColorVal", "comments": "Rectangles' value to be selected", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "yclip_enable_3": {"api": "bYClipEnable3", "display": "yClipEnable", "comments": "Luma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "yclip_gain_3": {"api": "nYClipGain3", "display": "yClipGain", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: S4.8 Range: [-4096, 4095]"}, "yclip_boundary_3": {"api": "nYClipBoundary3", "display": "yClipBoundary", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "cclip_enable_3": {"api": "bCClipEnable3", "display": "cClipEnable", "comments": "Chroma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "cclip_cmtx_3": {"api": "nCClipCmtx3", "display": "cClipCmtx", "comments": "2x2 chroma adjustment matrix", "hint": "Accuracy: U2.5 Range: [-128, 127]"}, "cclip_boundary_3": {"api": "nCClipBoundary3", "display": "cClipBoundary", "comments": "2x2 chroma adjustment --> clip", "hint": "Accuracy: S8.0 Range: [-256, 255]"}, "crop_enable_3": {"api": "bCropEnable3", "display": "cropEnable", "comments": "Crop enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "crop_start_3": {"api": "nCropStart3", "display": "cropStart", "comments": "Crop start coordinates", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "crop_size_3": {"api": "nCropSize3", "display": "cropSize", "comments": "Crop size", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "lba_enable_3": {"api": "bLbaEnable3", "display": "lbaEnable", "comments": "lba enable control bit", "hint": "Accuracy: 1.0 Range: [0, 1]"}, "lba_exd_size_3": {"api": "nLbaExdSize3", "display": "lbaExdSize", "comments": "lba extension size(paddings on edges)", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "lba_background_3": {"api": "nLbaBackground3", "display": "lbaBackground", "comments": "lba background color", "hint": "Accuracy: S8.0 Range: [-256, 256]"}, "scale_input_mux_4": {"api": "ninputMux4", "display": "inputMux", "comments": "Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_enable_4": {"api": "bScaleEnable4", "display": "scaleEnable", "comments": "Scalar enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_output_size_4": {"api": "nOutputSize4", "display": "outputSize", "comments": "Scalar output size", "hint": "Accuracy: U13.0 Range: [0, 4096]"}, "yclip_enable_4": {"api": "bYClipEnable4", "display": "yClipEnable", "comments": "Luma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "yclip_gain_4": {"api": "nYClipGain4", "display": "yClipGain", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: S4.8 Range: [-4096, 4095]"}, "yclip_boundary_4": {"api": "nYClipBoundary4", "display": "yClipBoundary", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "cclip_enable_4": {"api": "bCClipEnable4", "display": "cClipEnable", "comments": "Chroma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "cclip_cmtx_4": {"api": "nCClipCmtx4", "display": "cClipCmtx", "comments": "2x2 chroma adjustment matrix", "hint": "Accuracy: U2.5 Range: [-128, 127]"}, "cclip_boundary_4": {"api": "nCClipBoundary4", "display": "cClipBoundary", "comments": "2x2 chroma adjustment --> clip", "hint": "Accuracy: S8.0 Range: [-256, 255]"}, "scale_input_mux_5": {"api": "ninputMux5", "display": "inputMux", "comments": "Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_enable_5": {"api": "bScaleEnable5", "display": "scaleEnable", "comments": "Scalar enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_output_size_5": {"api": "nOutputSize5", "display": "outputSize", "comments": "Scalar output size", "hint": "Accuracy: U13.0 Range: [0, 4096]"}, "yclip_enable_5": {"api": "bYClipEnable5", "display": "yClipEnable", "comments": "Luma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "yclip_gain_5": {"api": "nYClipGain5", "display": "yClipGain", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: S4.8 Range: [-4096, 4095]"}, "yclip_boundary_5": {"api": "nYClipBoundary5", "display": "yClipBoundary", "comments": "-offset[0] --> x gain --> +offset[1] --> clip", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "cclip_enable_5": {"api": "bCClipEnable5", "display": "cClipEnable", "comments": "Chroma clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "cclip_cmtx_5": {"api": "nCClipCmtx5", "display": "cClipCmtx", "comments": "2x2 chroma adjustment matrix", "hint": "Accuracy: U2.5 Range: [-128, 127]"}, "cclip_boundary_5": {"api": "nCClipBoundary5", "display": "cClipBoundary", "comments": "2x2 chroma adjustment --> clip", "hint": "Accuracy: S8.0 Range: [-256, 255]"}, "crop_enable_5": {"api": "bCropEnable5", "display": "cropEnable", "comments": "Crop enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "crop_start_5": {"api": "nCropStart5", "display": "cropStart", "comments": "Crop start coordinates", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "crop_size_5": {"api": "nCropSize5", "display": "cropSize", "comments": "Crop size", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "lba_enable_5": {"api": "bLbaEnable5", "display": "lbaEnable", "comments": "lba enable control bit", "hint": "Accuracy: 1.0 Range: [0, 1]"}, "lba_exd_size_5": {"api": "nLbaExdSize5", "display": "lbaExdSize", "comments": "lba extension size(paddings on edges)", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "lba_background_5": {"api": "nLbaBackground5", "display": "lbaBackground", "comments": "lba background color", "hint": "Accuracy: S8.0 Range: [-256, 255]"}, "csc_yuvTrgb_offset_in_5": {"api": "nYuvTRgbOffetIn5", "display": "yuvTRgbOffetIn", "comments": "Scaler5 YUV to RGB conversion offset in.", "hint": "Accuracy: S8.0 Range: [-256, 255]"}}