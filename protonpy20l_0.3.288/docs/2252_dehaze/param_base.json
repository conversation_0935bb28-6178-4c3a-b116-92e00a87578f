{"configs": {"dehaze": {"enable": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "coef_set_en": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "coef_set_a": {"acc": [1, 5, 7], "size": [], "description": "coefficient set a", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "coef_set_b": {"acc": [1, 4, 8], "size": [], "description": "coefficient set b", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "offset_in": {"acc": [0, 8, 2], "size": [], "description": "offset of input", "usage": "Set 16.0", "constraints": "all", "type": "AX_U16", "partition": "-"}, "offset_out": {"acc": [0, 8, 2], "size": [], "description": "offset of output", "usage": "Set 16.0", "constraints": "all", "type": "AX_U16", "partition": "-"}, "a": {"acc": [0, 8, 8], "size": [], "description": "Atmospheric reflectance", "usage": "I = (I - A)/T + A", "constraints": "[0, 255.99609375]", "type": "AX_U16", "partition": "-"}, "locat_offset": {"acc": [0, 14], "size": [2, 2], "description": "coeff spatial offset", "usage": "partition related", "constraints": "(locat_offset[0][1] % 2 == 0 && locat_offset[1][1] % 2 == 0; locat_offset[0][1] < zoom_ratio && locat_offset[1][1] < zoom_ratio; inv_start_offset[0] - inv_zoom_ratio * locat_offset[0][1] >= 0; inv_start_offset[0] + inv_zoom_ratio * (zoom_ratio - 1 - locat_offset[0][1]) <= 1; inv_start_offset[1] - inv_zoom_ratio * locat_offset[1][1] >= 0; inv_start_offset[1] + inv_zoom_ratio * (zoom_ratio - 1 - locat_offset[1][1]) <= 1", "type": "AX_U16", "partition": "support"}, "inv_start_offset": {"acc": [0, 0, 14], "size": [2], "description": "Bilinear interpolation weights", "usage": "partition related", "constraints": "inv_start_offset[0] - inv_zoom_ratio * locat_offset[0][1] >= 0; inv_start_offset[0] + inv_zoom_ratio * (zoom_ratio - 1 - locat_offset[0][1]) <=1; inv_start_offset[1] - inv_zoom_ratio * locat_offset[1][1] >= 0; inv_start_offset[1] + inv_zoom_ratio * (zoom_ratio - 1 - locat_offset[1][1]) <=1", "type": "AX_U16", "partition": "support"}, "gray_weight": {"acc": [0, 1, 8], "size": [3], "description": "weight of R,G,B channel", "usage": "calculate gray", "constraints": "gray_weight[0] + conf.gray_weight[1] + conf.gray_weight[2] == 1", "type": "AX_U16", "partition": "-"}, "tx": {"acc": [0, 1, 8], "size": [], "description": "Lower limit of t", "usage": "", "constraints": "1>=tx>=0", "type": "AX_U16", "partition": "-"}, "zoom_ratio": {"acc": [0, 7], "size": [], "description": "ratio of a,b zoom", "usage": "zoom_ratio", "constraints": "32,64; =1/inv_zoom_ratio", "type": "AX_U8", "partition": "-"}, "inv_zoom_ratio": {"acc": [0, 0, 6], "size": [], "description": "inverse of zoom_ratio", "usage": "", "constraints": "=1/zoom_ratio", "type": "AX_U8", "partition": "-"}, "ptn_offset_h_w": {"acc": [0, 14], "size": [2], "description": "start offset of the partition", "usage": "partition related", "constraints": "ptn_offset_h_w[0] % 2 == 0; ptn_offset_h_w[0] + ptn_h_w[0] <= pic_h_w[0]; ptn_offset_h_w[1] % 2 == 0; ptn_offset_h_w[1] + ptn_h_w[1] <= pic_h_w[1]", "type": "AX_U16", "partition": "support"}, "ptn_roi_t_b_l_r": {"acc": [0, 14], "size": [4], "description": "roi zone", "usage": "partition related", "constraints": "0<=ptn_roi_t_b_l_r<=(pic.h,pic.w); ptn_roi_t_b_l_r % 2 == 0; ptn_roi_t_b_l_r[0] < ptn_roi_t_b_l_r[1]; ptn_roi_t_b_l_r[2] < ptn_roi_t_b_l_r[3]", "type": "AX_U16", "partition": "support"}, "pic_h_w": {"acc": [0, 14], "size": [2], "description": "pic_h_w", "usage": "", "constraints": "pic_h_w == [pic.h,pic.w]", "type": "AX_U16", "partition": "-"}, "ptn_h_w": {"acc": [0, 14], "size": [2], "description": "ptn_h_w", "usage": "partition related", "constraints": "ptn_h_w ==[partition.h,partition.w]", "type": "AX_U16", "partition": "support"}, "graystat_binratio": {"acc": [0, 1], "size": [], "description": "<PERSON>'s shift", "usage": "gray = gray / (1 << (graystat_binratio * 2))", "constraints": "all", "type": "AX_U8", "partition": "-"}, "norm_ratio": {"acc": [0, 1, 20], "size": [3], "description": "calculate bottom_right, top_right, bottom_left block's normalize ratio", "usage": "", "constraints": "(0, 1]", "type": "AX_U32", "partition": "-"}}, "ifa": {"enable": {"acc": [0, 1], "size": [], "description": "enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "mode": {"acc": [0, 4], "size": [], "description": "Select ifa operator to run", "usage": "0 - guided filter", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "sub_mode": {"acc": [0, 4], "size": [], "description": "Select sub_mode inside ifa operator", "usage": "If selected ifa operator has serval sub-mode, use this register to select it.", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "output_hor_crop_enable": {"acc": [0, 1], "size": [], "description": "output horizontal crop enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "output_hor_crop_st": {"acc": [0, 12], "size": [], "description": "output picture's horizontal crop start coordinates", "usage": "out_hor_crop_st[n] for pics[n]", "constraints": "N/A", "type": "AX_U16", "partition": "support"}, "output_hor_crop_width": {"acc": [0, 12], "size": [], "description": "output picture's horizontal crop width", "usage": "out_hor_crop_width[n] for pics[n]", "constraints": "N/A", "type": "AX_U16", "partition": "support"}, "guided_enable": {"acc": [0, 1], "size": [], "description": "guided filter enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "guided_dehaze_dc_adj_enable": {"acc": [0, 1], "size": [], "description": "When used for dehaze, this bit controls whether adjust dark channel using coeff k before calculating guided filter..", "usage": "If enabled, dc = clip(1 - dc * k, 0, 1.0)", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "guided_dehaze_dc_k": {"acc": [1, 4, 12], "size": [], "description": "k value in dehaze logic", "usage": "clip(b - dc * k, 0, 1.0)", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "guided_dehaze_dc_b": {"acc": [0, 1, 12], "size": [], "description": "b value in dehaze logic", "usage": "clip(b - dc * k, 0, 1.0)", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "guided_win_r": {"acc": [0, 3], "size": [], "description": "2 * guided_win_r + 1 == window size", "usage": "Example: 5x5 --> 2 * 2 + 1", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "guided_epsilon": {"acc": [0, 16], "size": [], "description": "It's u16 form for various acc epsilon; For rltm, it's u0.16; For dehaze, it's u12.4; For UVNR, it's u0.16; The guided_epsilon value in guided filter formula.", "usage": "coeff_a = cov_ip / (var_i + guided_epsilon * guided_epsilon)", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "blur_enable": {"acc": [0, 1], "size": [], "description": "guided filter enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "blur_weight": {"acc": [1, 5, 10], "size": [3, 3], "description": "3 x 3 filter weight; Symmetric parameter; Effective 5 x 5 filter weight.", "usage": "Use upper-left corner 3x3 parameter to init internal 5x5 filter weight symmetrically.", "constraibnts": "N/A", "type": "AX_S16", "partition": "-"}}}, "partition_configs": ["dehaze.locat_offset", "dehaze.inv_start_offset", "dehaze.ptn_offset_h_w", "dehaze.ptn_roi_t_b_l_r", "dehaze.ptn_h_w", "ifa.output_hor_crop_st", "ifa.output_hor_crop_width"], "context": {"AN_ID": {"size": [], "acc": [0, 16, 0], "comment": "DEHAZE is 0x2252", "type": "AX_U16"}}, "params": {"init_enable": {"acc": [0, 1], "auto": 0, "comment": "is_first_frame", "default": 0, "hidden": 1, "range": [0, 1], "size": [], "target_conf": ["dehaze.coef_set_en", "dehaze.coef_set_a", "dehaze.coef_set_b", "dehaze.a", "dehaze.tx"], "dependency": "common"}, "air_reflect": {"acc": [0, 8, 8], "auto": 0, "comment": "air reflect. Too small will lead to over exposure of the image, and too large will lead to dead black of the image", "default": 240.0, "hidden": 0, "range": [0.00390625, 255.99609375], "size": [], "target_conf": ["dehaze.a", "ifa.guided_dehaze_dc_k", "dehaze.coef_set_a"], "dependency": "user"}, "blur_enable": {"acc": [0, 1], "auto": 0, "comment": "0 is turned off and 1 is turned on to alleviate the halo problem", "default": 1, "hidden": 0, "size": [], "range": [0, 1], "target_conf": ["ifa.blur_enable"], "dependency": "user"}, "calc_mode": {"acc": [0, 1], "auto": 0, "comment": "0 using traditional algorithm, 1 using accelerated algorithm.", "default": 0, "hidden": 0, "size": [], "range": [0, 1], "target_conf": ["dehaze.coef_set_en", "dehaze.coef_set_a", "dehaze.coef_set_b"], "dependency": "user"}, "effective_strength": {"acc": [0, 1, 15], "auto": 1, "comment": "adjust defogging intensity", "default": 0.45, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["dehaze.coef_set_a", "ifa.guided_dehaze_dc_k"], "dependency": "user"}, "enable": {"acc": [0, 1], "auto": 0, "comment": "SW enable", "default": 1, "hidden": 0, "size": [], "range": [0, 1], "target_conf": ["dehaze.a", "dehaze.tx", "dehaze.coef_set_en", "dehaze.coef_set_a", "dehaze.coef_set_b"], "dependency": "user"}, "eps": {"acc": [0, 8, 4], "auto": 0, "comment": "Generally, no adjustment is required, which affects the intensity of extreme contrast areas", "default": 255.9375, "hidden": 0, "range": [0.0625, 255.9375], "size": [], "target_conf": ["ifa.guided_epsilon"], "dependency": "user"}, "mesh_size": {"acc": [0, 7], "auto": 0, "comment": "Affects the uniformity of defogging intensity. The higher the value, the more uniform the defogging intensity will be, and is equal to 64 when calc_mode == 1", "default": 32, "hidden": 0, "range": "{32, 64}", "size": [], "target_conf": ["dehaze.zoom_ratio", "dehaze.inv_zoom_ratio", "dehaze.graystat_binratio", "dehaze.locat_offset", "dehaze.inv_start_offset", "dehaze.norm_ratio"], "dependency": "user"}, "offset_in": {"acc": [0, 8, 2], "auto": 0, "comment": "offset_in", "default": 16.0, "hidden": 1, "size": [], "range": [0.0, 255.75], "target_conf": ["dehaze.offset_in"], "dependency": "common"}, "offset_out": {"acc": [0, 8, 2], "auto": 0, "comment": "offset_out", "default": 16.0, "hidden": 1, "size": [], "range": [0.0, 255.75], "target_conf": ["dehaze.offset_out"], "dependency": "common"}, "dehaze_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "target_conf": ["dehaze.locat_offset", "dehaze.inv_start_offset", "dehaze.ptn_offset_h_w", "dehaze.ptn_roi_t_b_l_r", "dehaze.ptn_h_w", "dehaze.pic_h_w", "dehaze.norm_ratio"], "dependency": "common", "comment": "dehaze partition", "hidden": 1, "auto": 0}, "ifa_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "target_conf": ["ifa.output_hor_crop_enable", "ifa.output_hor_crop_st", "ifa.output_hor_crop_width"], "dependency": "common", "comment": "ifa partition", "hidden": 1, "auto": 0}, "sigma_blur": {"acc": [0, 3, 13], "auto": 0, "comment": "blur intensity of the halo problem", "default": 7.0, "hidden": 0, "range": [0.0001220703125, 7.9998779296875], "size": [], "target_conf": ["ifa.blur_weight"], "dependency": "user"}, "spatial_smoothness": {"acc": [0, 2], "auto": 0, "comment": "The higher the value, the more global the defogging effect", "default": 2, "hidden": 0, "size": [], "range": [0, 3], "target_conf": ["ifa.guided_win_r"], "dependency": "user"}, "strength_limit": {"acc": [0, 1, 8], "auto": 0, "comment": "effective strength limit", "default": 0.1015625, "hidden": 0, "range": [0.00390625, 1.0], "size": [], "target_conf": ["dehaze.tx"], "dependency": "user"}}, "submodules": {"ctrl": {"configs": ["ifa.guided_dehaze_dc_adj_enable", "ifa.enable", "ifa.mode", "ifa.sub_mode", "ifa.guided_enable", "ifa.guided_dehaze_dc_b"], "params": ["enable", "init_enable", "calc_mode", "air_reflect", "effective_strength", "strength_limit", "spatial_smoothness", "eps", "blur_enable", "sigma_blur"]}, "setup": {"configs": ["dehaze.enable", "dehaze.gray_weight"], "params": ["offset_in", "offset_out"]}, "part": {"configs": [], "params": ["dehaze_partition_info", "mesh_size", "ifa_partition_info"]}}, "target_module": {"mc20l": {"dehaze": {"id": 4200, "method": 0}, "ifa": {"id": 6500, "method": 0}}}, "structs": {}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [12], "ref_interp_method": ["linear"]}}}