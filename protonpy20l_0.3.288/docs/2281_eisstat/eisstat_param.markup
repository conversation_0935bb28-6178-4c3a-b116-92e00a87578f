h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| offset_in |  | u8.2 | AX_U16 | [\] |  [0, 1023\] | [0.0, 255.75\] | 256 | 64.0 | hidden | 'eis_pre.offset_in' | offset in, from ACP | common |
| partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden |  | partition info | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open |  | eisstat enable, 0: disable, 1: enable |  | None | None | None |
| search_range | search range | u9 | AX_U16 | [2\] | [0, 504\] | [None, None\] | [8, 8\] | None | open |  | eisstat post process search range, must be mutiple of 8 |  | None | None | None |
| scale_ratio | scale ratio | u2 | AX_U8 | [\] | [0, 2\] | [None, None\] | 0 | None | open |  | scale ratio, 0: 1/1, 1: 1/2, 2: 1/4 |  | None | None | None |
| lut_enable | lut enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'eis_pre.lut_enable' | lut enable, 0: disable, 1: enable |  | None | None | None |
| lut | lut | u8.2 | AX_U16 | [33\] | [0, 1023\] | [0.0, 255.75\] | [0, 128, 256, 384, 512, 640, 768, 896, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023, 1023\] | [0.0, 32.0, 64.0, 96.0, 128.0, 160.0, 192.0, 224.0, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75, 255.75\] | open | 'eis_pre.lut' | lut table |  | None | None | None |
| proj_h_region_num | proj h region num | u3 | AX_U8 | [2\] | [1, 5\] | [None, None\] | [5, 5\] | None | open |  | horizontal projection region number |  | None | None | None |
| proj_h_roi | proj h roi | u1.14 | AX_U16 | [4\] | [0, 16384\] | [0.0, 1.0\] | [0, 0, 16384, 16384\] | [0.0, 0.0, 1.0, 1.0\] | open |  | horizontal projection normalized roi |  | None | None | None |
| proj_h_shiftbit | proj h shiftbit | u3 | AX_U8 | [\] | [0, 7\] | [None, None\] | 0 | None | open | 'eis_pre.proj_h_shiftbit' | horizontal projection shift bit |  | None | None | None |
| proj_v_region_num | proj v region num | u3 | AX_U8 | [2\] | [1, 5\] | [None, None\] | [5, 5\] | None | open |  | vertical projection region number |  | None | None | None |
| proj_v_roi | proj v roi | u1.14 | AX_U16 | [4\] | [0, 16384\] | [0.0, 1.0\] | [0, 0, 16384, 16384\] | [0.0, 0.0, 1.0, 1.0\] | open |  | vertical projection normalized roi |  | None | None | None |
| proj_v_shiftbit | proj v shiftbit | u3 | AX_U8 | [\] | [0, 7\] | [None, None\] | 0 | None | open | 'eis_pre.proj_v_shiftbit' | vertical projection shift bit |  | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |