h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| aes_is_transposed |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'aestat.grid_hori_region_num', 'aestat.grid_vert_region_num', 'aestat.grid_roi', 'aestat.grid_start_pos', 'aestat.grid_region_height', 'aestat.grid_region_width', 'aestat.item_roi', 'aestat.hist_block_start_pos', 'aestat.hist_block_size', 'aestat.hist_roi_config' | is aes in ITP | common |
| aes_hist_wl_gain |  | u1.8 | AX_U16 | [\] |  [0, 511\] | [0.0, 1.99609375\] | 256 | 1.0 | hidden | 'aestat.hist_wl_gain' | white level used for compute histogram maximum compensation gain | common |
| aes_offset_in |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 0 | 0.0 | hidden | 'aestat.offset_in' | aes offset in | common |
| awbs_is_transposed |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'awbstat.grid_hori_region_num', 'awbstat.grid_vert_region_num', 'awbstat.grid_roi', 'awbstat.grid_start_pos', 'awbstat.grid_region_height', 'awbstat.grid_region_width', 'awbstat.item_roi' | is awbs in ITP | common |
| awbs_offset_in |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 0 | 0.0 | hidden | 'awbstat.offset_in' | awbs offset in | common |
| afs_is_transposed |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'afstat.grid_hori_region_num', 'afstat.grid_vert_region_num', 'afstat.grid_roi', 'afstat.grid_start_pos', 'afstat.grid_region_height', 'afstat.grid_region_width' | is afs in ITP | common |
| afs_wb_gain |  | u4.8 | AX_U16 | [4\] |  [0, 4095\] | [0.0, 15.99609375\] | [256, 256, 256, 256\] | [1.0, 1.0, 1.0, 1.0\] | hidden | 'afstat.wb_gain' | afs wbgain | common |
| afs_drc_mode |  | u8 | AX_U8 | [\] |  [0, 255\] | [None, None\] | 0 | None | hidden | 'afstat.drc_enable', 'afstat.drc_lut' | drc mode: 0: disable, 1: 8.4 -> 8.4(do nothing), 2: 8.4 -> 8.4 (gam), 3: 8.6 -> 8.4(MSB), 4: 8.6 -> 8.4(gam), 5: 14.6 -> 8.4(MSB) 6: 14.6 -> 8.4(gam) | common |
| afs_offset_in |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 0 | 0.0 | hidden | 'afstat.offset_in' | afs offset in | common |
| aes_partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'aestat.grid_hori_region_num', 'aestat.grid_vert_region_num', 'aestat.grid_roi', 'aestat.grid_start_pos', 'aestat.grid_region_height', 'aestat.grid_region_width', 'aestat.hist_roi_config', 'aestat.hist_block_start_pos', 'aestat.hist_block_size', 'aestat.item_roi' | partition information | common |
| awbs_partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'awbstat.grid_hori_region_num', 'awbstat.grid_vert_region_num', 'awbstat.grid_roi', 'awbstat.grid_start_pos', 'awbstat.grid_region_height', 'awbstat.grid_region_width', 'awbstat.item_roi' | partition information | common |
| afs_partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'afstat.grid_hori_region_num', 'afstat.grid_vert_region_num', 'afstat.grid_roi', 'afstat.grid_start_pos', 'afstat.grid_region_height', 'afstat.grid_region_width' | partition information | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| aes_enable | aes enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'aestat.enable' | aes_enable | user | None | None | None |
| aes_grid_enable | aes grid enable | u1 | AX_U8 | [2\] | [0, 1\] | [None, None\] | [0, 0\] | None | open | 'aestat.grid_enable' | aes_grid_enable | user | None | None | None |
| aes_grid_specify_roi | aes specify grid roi | u1 | AX_U8 | [2\] | [0, 1\] | [None, None\] | [0, 0\] | None | open | 'aestat.grid_hori_region_num', 'aestat.grid_vert_region_num', 'aestat.grid_roi', 'aestat.grid_start_pos' | use user specified roi placement, 1: use, 0: use with default | user | None | None | None |
| aes_gridroi_offset | aes gridroi offset | u16 | AX_U16 | [2, 2\] | [0, 8192\] | [None, None\] | [[0, 0\], [0, 0\]\] | None | open | 'aestat.grid_hori_region_num', 'aestat.grid_vert_region_num', 'aestat.grid_roi', 'aestat.grid_start_pos' | user set gridroi offset coordinate [y, x\] of two independent ROIs | user | None | None | None |
| aes_region_num | hdr aes region num | u16 | AX_U16 | [2, 2\] | [0, 8192\] | [None, None\] | [[0, 0\], [0, 0\]\] | None | open | 'aestat.grid_hori_region_num', 'aestat.grid_vert_region_num', 'aestat.grid_roi', 'aestat.grid_start_pos' | user set grid number in region [y_num, x_num\] of two independent ROIs | user | None | None | None |
| aes_gridroi_size | aes gridroi size | u16 | AX_U16 | [2, 2\] | [0, 8192\] | [None, None\] | [[0, 0\], [0, 0\]\] | None | open | 'aestat.grid_region_height', 'aestat.grid_region_width', 'aestat.grid_hori_region_num', 'aestat.grid_vert_region_num', 'aestat.grid_roi', 'aestat.grid_start_pos' | user set gridroi size [height, width\] of two independent ROIs | user | None | None | None |
| aes_grid_4ch_enable | aes grid 4ch enable | u1 | AX_U8 | [2\] | [0, 1\] | [None, None\] | [0, 0\] | None | open | 'aestat.grid_4ch_enable' | static four channel enable | user | None | None | None |
| aes_grid_sat_thr | aes grid sat thr | u20.4 | AX_U32 | [2, 4\] | [0, 16777215\] | [0.0, 1048575.9375\] | [[0, 0, 0, 0\], [0, 0, 0, 0\]\] | [[0.0, 0.0, 0.0, 0.0\], [0.0, 0.0, 0.0, 0.0\]\] | open | 'aestat.grid_sat_thr' | over saturated threshold value | user | None | None | None |
| aes_grid_ycoeff | aes grid ycoeff | u0.12 | AX_U16 | [4\] | [0, 4095\] | [0.0, 0.999755859375\] | [0, 0, 0, 0\] | [0.0, 0.0, 0.0, 0.0\] | open | 'aestat.grid_ycoeff' | coeff for compute luma for grid | user | None | None | None |
| aes_item_enable | aes item enable | u1 | AX_U8 | [2\] | [0, 1\] | [None, None\] | [0, 0\] | None | open | 'aestat.item_enable' | two aes_item enable | user | None | None | None |
| aes_item_specify_roi | aes specify item roi | u1 | AX_U8 | [2\] | [0, 1\] | [None, None\] | [0, 0\] | None | open | 'aestat.item_roi' | use user specified roi placement, 1: use, 0: use with default | user | None | None | None |
| aes_item_roi | aes item roi | u16 | AX_U16 | [2, 4\] | [0, 65535\] | [None, None\] | [[0, 0, 0, 0\], [0, 0, 0, 0\]\] | None | open | 'aestat.item_roi' | two aes_item roi | user | None | None | None |
| aes_hist_log_enable | aes hist log enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'aestat.hist_log_enable' | aes_hist_log_enable | user | None | None | None |
| aes_hist_linear_enable | aes hist linear enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'aestat.hist_linear_enable' | aes_hist_linear_enable | user | None | None | None |
| aes_hist_mode | aes hist mode | u2 | AX_U8 | [\] | [0, 2\] | [None, None\] | 0 | None | open | 'aestat.hist_mode' | 0: HIST_MODE_Y, 1: HIST_MODE_YRGB, 2: HIST_MODE_RGGB | user | None | None | None |
| aes_hist_specify_roi | aes specify hist roi | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'aestat.hist_block_start_pos', 'aestat.hist_block_size', 'aestat.hist_roi_config' | use user specified roi placement, 1: use, 0: use with default | user | None | None | None |
| aes_histroi_offset | aes hist block offset | u16 | AX_U16 | [2\] | [0, 8192\] | [None, None\] | [0, 0\] | None | open | 'aestat.hist_block_start_pos', 'aestat.hist_block_size', 'aestat.hist_roi_config' | user set histroi coordinate [y, x\] for hist stat | user | None | None | None |
| aes_histroi_size | aes hist block size | u16 | AX_U16 | [2\] | [0, 8192\] | [None, None\] | [0, 0\] | None | open | 'aestat.hist_block_start_pos', 'aestat.hist_block_size', 'aestat.hist_roi_config' | user set histroi size [height, width\] for hist stat | user | None | None | None |
| aes_hist_linear_bins | aes hist linear bins | u2 | AX_U8 | [\] | [0, 2\] | [None, None\] | 0 | None | open | 'aestat.hist_linear_bins' | hist's x coord (i.e hist_bin num) will be 256 \* pow(2, hist_linear_bins) | user | None | None | None |
| aes_hist_linear_scale | aes hist linear scale | s4 | AX_S8 | [\] | [-15, 12\] | [None, None\] | 0 | None | open | 'aestat.hist_linear_scale' | for compute clip_value when count the target linear hist, clip_value=val\*pow(2, linear_scale) | user | None | None | None |
| aes_hist_log_scale | aes hist log scale | s4 | AX_S8 | [\] | [-15, 12\] | [None, None\] | 0 | None | open | 'aestat.hist_log_scale' | for compute input_val when count the target log hist, input_value=val\*pow(2, log_scale) | user | None | None | None |
| aes_hist_weight | aes hist weight | u8 | AX_U8 | [256\] | [0, 255\] | [None, None\] | [1, 1, ... , 1\] | None | open | 'aestat.hist_weight' | locationable weight for count hist | user | None | None | None |
| aes_hist_ycoeff | aes hist ycoeff | u0.12 | AX_U16 | [4\] | [0, 4095\] | [0.0, 0.999755859375\] | [0, 0, 0, 0\] | [0.0, 0.0, 0.0, 0.0\] | open | 'aestat.hist_ycoeff' | coeff for compute luma for hist | user | None | None | None |
| aes_dgain | aes dgain | u10.8 | AX_U32 | [4\] | [0, 261888\] | [0.0, 1023.0\] | [256, 256, 256, 256\] | [1.0, 1.0, 1.0, 1.0\] | open | 'aestat.dgain' | dgain for statics | common | None | None | None |
| awbs_enable | awbs enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'awbstat.enable' | enable awb stat |   | None | None | None |
| awbs_grid_enable | awbs grid enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'awbstat.grid_enable' | awbs grid enable | user | None | None | None |
| awbs_grid_mode | awbs grid mode | u2 | AX_U8 | [\] | [0, 3\] | [None, None\] | 1 | None | open | 'awbstat.grid_mode' | awb grid stat mode BAYER3CH = 0, BAYER4CH, BAYER4CH_LUMA4CH, BAYER4CH_LUMA2CH | user | None | None | None |
| awbs_grid_specify_roi | awbs specify grid roi | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'awbstat.grid_hori_region_num', 'awbstat.grid_vert_region_num', 'awbstat.grid_roi', 'awbstat.grid_start_pos' | use user specified roi placement, 1: use, 0: use with default | user | None | None | None |
| awbs_gridroi_offset | awbs gridroi offset | u16 | AX_U16 | [2\] | [0, 8192\] | [None, None\] | [0, 0\] | None | open | 'awbstat.grid_hori_region_num', 'awbstat.grid_vert_region_num', 'awbstat.grid_roi', 'awbstat.grid_start_pos' | user set gridroi offset coordinate [y, x\] | user | None | None | None |
| awbs_region_num | awbs region num | u16 | AX_U16 | [2\] | [0, 8192\] | [None, None\] | [0, 0\] | None | open | 'awbstat.grid_hori_region_num', 'awbstat.grid_vert_region_num', 'awbstat.grid_roi', 'awbstat.grid_start_pos' | user set grid number in region [y_num, x_num\] |   | None | None | None |
| awbs_gridroi_size | awbs gridroi size | u16 | AX_U16 | [2\] | [0, 8192\] | [None, None\] | [0, 0\] | None | open | 'awbstat.grid_region_height', 'awbstat.grid_region_width', 'awbstat.grid_hori_region_num', 'awbstat.grid_vert_region_num', 'awbstat.grid_roi', 'awbstat.grid_start_pos' | user set gridroi size [height, width\] | user | None | None | None |
| awbs_grid_rgb_thr | awbs grid rgb thr | s14.6 | AX_S32 | [8\] | [-1048575, 1048575\] | [-16383.984375, 16383.984375\] | [0, 1048575, 0, 1048575, 0, 1048575, 0, 1048575\] | [0.0, 16383.984375, 0.0, 16383.984375, 0.0, 16383.984375, 0.0, 16383.984375\] | open | 'awbstat.grid_rgb_thr' | awb 4ch of saturated threshold for grid | user | None | None | None |
| awbs_grid_y_thr | awbs grid y thr | s14.6 | AX_S32 | [2\] | [-1048575, 1048575\] | [-16383.984375, 16383.984375\] | [0, 1048575\] | [0.0, 16383.984375\] | open | 'awbstat.grid_y_thr' | awb luma of saturated threshold for grid | user | None | None | None |
| awbs_grid_rgb_sat_thr | awbs grid rgb sat thr | u14.6 | AX_U32 | [4\] | [0, 1048575\] | [0.0, 16383.984375\] | [0, 0, 0, 0\] | [0.0, 0.0, 0.0, 0.0\] | open | 'awbstat.grid_rgb_sat_thr' | awb 4ch of saturated threshold for grid | user | None | None | None |
| awbs_grid_y_sat_thr | awbs grid y sat thr | u14.6 | AX_U32 | [\] | [0, 1048575\] | [0.0, 16383.984375\] | 0 | 0.0 | open | 'awbstat.grid_y_sat_thr' | awb luma ch of saturated threshold for grid | user | None | None | None |
| awbs_grid_luma_slice_thr | awbs grid luma slice thr | s14.6 | AX_S32 | [3\] | [-1048575, 1048575\] | [-16383.984375, 16383.984375\] | [0, 0, 0\] | [0.0, 0.0, 0.0\] | open | 'awbstat.grid_luma_slice_thr' | awb stat split luma threshold for grid | user | None | None | None |
| awbs_grid_ycoeff | awbs grid ycoeff | u0.12 | AX_U16 | [4\] | [0, 4095\] | [0.0, 0.999755859375\] | [1024, 1024, 1024, 1024\] | [0.25, 0.25, 0.25, 0.25\] | open | 'awbstat.grid_ycoeff' | awb coeff for computing luma for grid | user | None | None | None |
| awbs_item_enable | awbs item enable | u1 | AX_U8 | [2\] | [0, 1\] | [None, None\] | [0, 0\] | None | open | 'awbstat.item_enable' | two awbs_item enable | user | None | None | None |
| awbs_item_specify_roi | awbs specify item roi | u1 | AX_U8 | [2\] | [0, 1\] | [None, None\] | [0, 0\] | None | open | 'awbstat.item_roi' | use user specified roi placement, 1: use, 0: use with default | user | None | None | None |
| awbs_item_roi | awbs item roi | u16 | AX_U16 | [2, 4\] | [0, 65535\] | [None, None\] | [[0, 0, 0, 0\], [0, 0, 0, 0\]\] | None | open | 'awbstat.item_roi' | two aes_item roi | user | None | None | None |
| awbs_dgain | aes dgain | u10.8 | AX_U32 | [4\] | [0, 261888\] | [0.0, 1023.0\] | [256, 256, 256, 256\] | [1.0, 1.0, 1.0, 1.0\] | open | 'awbstat.dgain' | dgain for statics | common | None | None | None |
| afs_enable | afs enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'afstat.enable' | enable | user | None | None | None |
| afs_fv_mode | afs fv mode | u1 | AX_U8 | [4\] | [0, 1\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'afstat.fv_mode' | independent fv_mode for V1, V2, H1, H2, 0: sum mode, 1: peak mode | user | None | None | None |
| afs_fir_enable | afs fir enable | u1 | AX_U8 | [2\] | [0, 1\] | [None, None\] | [0, 0\] | None | open | 'afstat.fir_enable' | fir enable for H1, H2 | user | None | None | None |
| afs_grid_specify_roi | afs specify grid roi | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'afstat.grid_hori_region_num', 'afstat.grid_vert_region_num', 'afstat.grid_roi', 'afstat.grid_start_pos' | use user specified roi placement, 1: use, 0: use with default | user | None | None | None |
| afs_gridroi_offset | afs gridroi offset | u16 | AX_U16 | [2\] | [0, 8192\] | [None, None\] | [0, 0\] | None | open | 'afstat.grid_hori_region_num', 'afstat.grid_vert_region_num', 'afstat.grid_roi', 'afstat.grid_start_pos' | user set gridroi offset coordinate [y, x\] | user | None | None | None |
| afs_region_num | afs region num | u16 | AX_U16 | [2\] | [0, 8192\] | [None, None\] | [0, 0\] | None | open | 'afstat.grid_hori_region_num', 'afstat.grid_vert_region_num', 'afstat.grid_roi', 'afstat.grid_start_pos' | user set grid number in region [y_num, x_num\] | user | None | None | None |
| afs_gridroi_size | afs gridroi size | u16 | AX_U16 | [2\] | [0, 8192\] | [None, None\] | [0, 0\] | None | open | 'afstat.grid_region_height', 'afstat.grid_region_width', 'afstat.grid_hori_region_num', 'afstat.grid_vert_region_num', 'afstat.grid_roi', 'afstat.grid_start_pos' | user set gridroi size [height, width\] | user | None | None | None |
| afs_coring_thr | afs coring thr | u8.10 | AX_U32 | [4\] | [0, 262143\] | [0.0, 255.9990234375\] | [262143, 262143, 262143, 262143\] | [255.9990234375, 255.9990234375, 255.9990234375, 255.9990234375\] | open | 'afstat.coring_thr' | coring thr for V1, V2, H1, H2 | user | None | None | None |
| afs_coring_gain | afs coring gain | u5.7 | AX_U16 | [4\] | [0, 4095\] | [0.0, 31.9921875\] | [128, 128, 128, 128\] | [1.0, 1.0, 1.0, 1.0\] | open | 'afstat.coring_gain' | coring gain for V1, V2, H1, H2 | user | None | None | None |
| afs_coring_lut | afs coring lut | u5 | AX_U8 | [4, 16\] | [0, 31\] | [None, None\] | [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\]\] | None | open | 'afstat.coring_lut' | coring gain lut for V1, V2, H1, H2 | user | None | None | None |
| afs_coring_pix_sum_mode | afs coring pix sum mode | u1 | AX_U8 | [4\] | [0, 1\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'afstat.coring_pix_sum_mode' | 0: don't count pixel' val for smaller than coring thr, 1: do count | user | None | None | None |
| afs_coring_pix_cnt_mode | afs coring pix count mode | u1 | AX_U8 | [4\] | [0, 1\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'afstat.coring_pix_cnt_mode' | 0: don't count pixel's count for smaller than coring thr, 1: do count | user | None | None | None |
| afs_scale_ratio | afs scale ratio | u3 | AX_U8 | [\] | [0, 7\] | [None, None\] | 0 | None | open | 'afstat.scale_ratio' | scale ratio for V1, V2, H1, H2 | user | None | None | None |
| afs_scale_weight | afs weight | u1.7 | AX_U8 | [8\] | [0, 128\] | [0.0, 1.0\] | [128, 0, 0, 0, 0, 0, 0, 0\] | [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0\] | open | 'afstat.weight' | scale weight for V1, V2, H1, H2 | user | None | None | None |
| afs_ycoeff | afs ycoeff | u0.12 | AX_U16 | [4\] | [0, 4095\] | [0.0, 0.999755859375\] | [1024, 1024, 1024, 1024\] | [0.25, 0.25, 0.25, 0.25\] | open | 'afstat.ycoeff' | coeff for compute luma from bayer | user | None | None | None |
| afs_vert_enable | afs vert enable | u1 | AX_U8 | [2\] | [0, 1\] | [None, None\] | [0, 0\] | None | open | 'afstat.vert_enable' | enable for V1, V2 | user | None | None | None |
| afs_v_iir_coeff | afs v iir coeff | s2.12 | AX_S16 | [2, 10\] | [-16383, 16383\] | [-3.999755859375, 3.999755859375\] | [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0\], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0\]\] | [[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0\], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0\]\] | open | 'afstat.v_iir_coeff' | iir coeff for V1, V2 | user | None | None | None |
| afs_hori_enable | afs hori enable | u1 | AX_U8 | [2\] | [0, 1\] | [None, None\] | [0, 0\] | None | open | 'afstat.hori_enable' | enable for H1, H2 | user | None | None | None |
| afs_h_iir_coeff | afs h iir coeff | s2.12 | AX_S16 | [2, 10\] | [-16383, 16383\] | [-3.999755859375, 3.999755859375\] | [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0\], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0\]\] | [[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0\], [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0\]\] | open | 'afstat.h_iir_coeff' | iir coeff for H1, H2 | user | None | None | None |
| afs_h_fir_coeff | afs h fir coeff | s6 | AX_S8 | [2, 13\] | [-64, 63\] | [None, None\] | [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0\]\] | None | open | 'afstat.h_fir_coeff' | fir coeff for H1, H2 | user | None | None | None |
| afs_ldg_thr | afs ldg thr | u8.4 | AX_U16 | [4\] | [0, 4095\] | [0.0, 255.9375\] | [0, 0, 0, 0\] | [0.0, 0.0, 0.0, 0.0\] | open | 'afstat.ldg_thr' | luma dependent gain thr independent for V1, V2, H1, H2 | user | None | None | None |
| afs_ldg_slope | afs ldg slope | u0.8 | AX_U8 | [4\] | [0, 255\] | [0.0, 0.99609375\] | [0, 0, 0, 0\] | [0.0, 0.0, 0.0, 0.0\] | open | 'afstat.ldg_slope' | luma dependent gain adj slope independent for V1, V2, H1, H2 | user | None | None | None |
| afs_ldg_lim | afs ldg lim | u1.7 | AX_U8 | [4\] | [0, 255\] | [0.0, 1.9921875\] | [0, 0, 0, 0\] | [0.0, 0.0, 0.0, 0.0\] | open | 'afstat.ldg_lim' | luma dependent gain limit independent for V1, V2, H1, H2 | user | None | None | None |
| afs_ldg_range | afs ldg range | u2 | AX_U8 | [2\] | [0, 3\] | [None, None\] | [0, 0\] | None | open | 'afstat.ldg_range' | luma dependent gain search range share with V1, V2, H1, H2 | user | None | None | None |
| afs_high_luma_cnt_thr | afs high luma cnt thr | u8.4 | AX_U16 | [4\] | [0, 4095\] | [0.0, 255.9375\] | [0, 0, 0, 0\] | [0.0, 0.0, 0.0, 0.0\] | open | 'afstat.high_luma_cnt_thr' | high luma count thr independent V1, V2, H1, H2 | user | None | None | None |
| afs_dgain | afs dgain | u10.8 | AX_U32 | [4\] | [0, 261888\] | [0.0, 1023.0\] | [256, 256, 256, 256\] | [1.0, 1.0, 1.0, 1.0\] | open | 'afstat.dgain' | dgain for statics | common | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |