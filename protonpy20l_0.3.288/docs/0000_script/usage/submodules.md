# Submodules

子模块可以根据功能，语义进行划分。划分由算法owner自行决定，以容易理解，维护为原则。
![](../assets/imgs/submodules.png)

## Setup子模块
为了优化CPU Loading，避免软件每帧调用冗余的API，`Submodules`中要求至少包含一个`setup`子模块，该子模块中包含了不需要每帧刷新的参数，`setup` API不会每帧被调用。

## 子模块对应的params
每个子模块都有对应的`params`（[params](./usage/params.md)），生成的代码中，会将属于同一个子模块的params放在同一个结构体中，方便调用。
**注意**，所有的`params`都应该对应于某个子模块，不应该有遗漏。

每个子模块也有对应的`configs`，按照规则，`configs`是通过`params`中的`target_conf`域自动生成的，**不需要手动指定。**
但是，当有某些`configs`无法和`params`对应起来的时候（例如一些配置固定值的寄存器），需要手动添加到如下的域中。

![](../assets/imgs/submodules_configs.png)