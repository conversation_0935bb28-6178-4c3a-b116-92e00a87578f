<html>

<head>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta charset="UTF-8">
  <link rel="stylesheet" href="https://cdn.staticfile.org/docsify/4.13.1/themes/vue.min.css" />
  <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.css"> -->
</head>

<body>
  <div data-app id="app">Loading</div>
  <script src="https://cdn.staticfile.org/mermaid/10.2.4/mermaid.min.js"></script>
  <script>
    var mm_num = 0;
    mermaid.initialize({ startOnLoad: false });
    window.$docsify = {
      // https://docsify.js.org/#/configuration
      name: "Algo Logic Generative Script",
      loadSidebar: true,
      loadNavbar: true,
      auto2top: true,
      search: "auto",
      relativePath: false,
      subMaxLevel: 2,
      alias: {
        "/.*/_sidebar.md": "/_sidebar.md",
        "/.*/_navbar.md": "/_navbar.md",
      },
      formatUpdated: '{YYYY}/{MM}/{DD} {HH}:{mm}',
      markdown: {
        renderer: {
          code: function (code, lang) {
            if (lang === "mermaid") {
              return (
                '<div class="mermaid">' + mermaid.render('mermaid-svg-' + mm_num++, code) + "</div>"
              );
            } else if (lang === 'drawio') {
              if (window.drawioConverter) {
                console.log('drawio 转化中')
                return window.drawioConverter(code)
              } else {
                return `<div class='drawio-code'>${code}</div>`
              }
            }
            return this.origin.code.apply(this, arguments);
          }
        }
      },
      plugins: [
        function (hook, vm) {
          console.log(window.Docsify.version);
          let _doc_branch = 'master';
          let _doc_repo = 'https://git-ext.axera-tech.com/isp/fwalgo/proton_algo_staging';

          hook.beforeEach((content) => {
            let url = `${_doc_repo}/-/blob/${_doc_branch}/docs/${vm.route.file}`;
            let editButton = `:memo: [Edit this page](${url})\n`;

            return editButton + content;
          });
        }
      ]
    }
  </script>
  <script src="https://cdn.staticfile.org/docsify/4.13.1/docsify.min.js"></script>
  <script src="https://cdn.staticfile.org/docsify-copy-code/2.1.1/docsify-copy-code.min.js"></script>
  <script src="https://cdn.staticfile.org/docsify/4.13.1/plugins/search.min.js"></script>
  <script src="https://cdn.staticfile.org/docsify/4.13.1/plugins/zoom-image.min.js"></script>
  <script src="https://cdn.staticfile.org/docsify/4.13.1/plugins/emoji.min.js"></script>
  <script src="https://cdn.staticfile.org/prism/9000.0.1/components/prism-bash.min.js"></script>
  <script src="https://cdn.staticfile.org/prism/9000.0.1/components/prism-python.min.js"></script>
  <script src="https://cdn.staticfile.org/prism/9000.0.1/components/prism-yaml.min.js"></script>
  <!-- <script src="https://cdn.jsdelivr.net/npm/prismjs@1/components/prism-protobuf.min.js"></script> -->
  <script src="./assets/js/viewer.min.js"></script>
  <script src='./assets/js/drawio.min.js'></script>
</body>

</html>
