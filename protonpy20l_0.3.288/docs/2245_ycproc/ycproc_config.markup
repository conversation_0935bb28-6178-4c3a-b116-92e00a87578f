h2. Conf list
h3. lce
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| yadj_enable | u1 | [\] | - | all | yadj enable, 0: disable, 1: enable |  |
| yadj_lut | u8.2 | [33\] | - | all | yadj lut, used to adjust brightness/contrast | normally calculated in algo logic |
| yclip_enable | u1 | [\] | - | all | yclip enable, 0: disable, 1: enable |  |
| yclip_clip | u8.0 | [2\] | - | yclip_clip[0\] <= yclip_clip[1\] | yclip clip limit, [0\]: lower limit, [1\]: upper limit | normally calculated in algo logic |
| hsvc_enable | u1 | [\] | - | all | hsvc enable, 0: disable, 1: enable |  |
| hsvc_h_lut | s8.7 | [25, 17\] | - | all | hsvc hue lut |  |
| hsvc_s_lut | u1.15 | [25, 17\] | - | all | hsvc saturation lut |  |
| hsvc_yuv2rgb_matrix | s2.8 | [3, 3\] | - | all | hsvc yuv2rgb matrix | set as the same as inverted csc matrix |
| hsvc_rgb2yuv_matrix | s2.8 | [3, 3\] | - | all | hsvc rgb2yuv matrix | set as the same as csc matrix |
| hsvc_yuv2rgb_offset | s8.2 | [2, 3\] | - | all | hsvc yuv2rgb offset |  |
| hsvc_rgb2yuv_offset | s8.2 | [2, 3\] | - | all | hsvc rgb2yuv offset |  |
| ccmp_enable | u1 | [\] | - | all | ccmp enable, 0: disable, 1: enable |  |
| ccmp_y_lut | u1.9 | [29\] | - | all | ccmp y gain lut | smaller value means stronger saturation compression |
| ccmp_sat_lut | u1.9 | [23\] | - | all | ccmp saturation gain lut | smaller value means stronger saturation compression |
| cset_enable | u1 | [\] | - | all | cset enable, 0: disable, 1: enable |  |
| cset_io_flag | u1 | [\] | - | all | cset color target inverse selection flag, 0: inverse selection, 1: normal selection | set to 1 normally |
| cset_color | s7.2 | [2\] | - | all | cset color target new color uv value |  |
| cset_center_y | u8.2 | [\] | - | all | cset color target y value | set as y value of the specified color target |
| cset_center_uv | s7.2 | [2\] | - | all | cset color target uv value | set as uv value of the specified color target |
| cset_radius | u7.2 | [3\] | - | all | cset color target radius | larger value means more pixels will be selected in the color mask |
| cset_t_grad | u4 | [3\] | - | all | cset color mask transition gradient | larger value means smoother/wider transition band in the color mask |
| cclip_enable | u1 | [\] | - | all | cclip enable, 0: disable, 1: enable |  |
| cclip_cmtx | s2.5 | [2, 2\] | - | all | cclip color matrix | normally calculated in algo logic |
| cclip_clip | s7.0 | [2\] | - | cclip_clip[0\] <= cclip_clip[1\] | cclip clip limit, [0\]: lower limit, [1\]: upper limit | normally calculated in algo logic |
| desat_enable | u1 | [\] | - |  | desat enable, 0: bypass, 1: enable |  |
| desat_strength | u4.4 | [\] | - |  | desat strength, increase or decrease strength of desat |  |
| desat_luma_lut | u1.7 | [8\] | - |  | the luma ratio table for desat |  |
| desat_sat_lut | u1.7 | [6\] | - |  | the saturation ratio table for desat |  |
| desat_angle_ratio_lut | u1.7 | [16\] | - |  | the angle ratio table for desat |  |
| desat_uv_val | s7.2 | [2\] | - |  | desat uv color |  |
| desat_debug_enable | u1 | [\] | - |  | debug desat mask display enable |  |
| desat_debug_thr | u1.4 | [\] | - |  | debug threshold for desat mask |  |
| desat_debug_color | s8.2 | [3\] | - |  | debug desat color fill |  |

