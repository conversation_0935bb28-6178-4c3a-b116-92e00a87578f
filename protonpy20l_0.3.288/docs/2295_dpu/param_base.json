{"context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "dpu is 0x2295", "type": "AX_U16"}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "params": {"v0_format": {"acc": [0, 6], "auto": 0, "comment": "v0 input`s format", "default": 0, "display": "v0 format", "hidden": 0, "range": [0, 63], "size": [], "target_conf": ["tde.v0_format", "tde.mat_adj_enable", "tde.mat_adj_matrix", "tde.to422_filter_enable"], "type": "AX_U8", "dependency": "user", "enum_field": {"0": "NV12", "1": "NV21", "2": "NV16", "3": "NV61", "4": "YUYV", "5": "YVYU", "6": "UYVY", "7": "VYUY", "8": "Y", "9": "LAB", "10": "Y_P101010", "11": "Y_P010", "12": "BIT1", "13": "BIT2", "14": "BIT4", "15": "BIT8", "16": "NV12_P101010", "17": "NV21_P101010", "18": "NV16_P101010", "19": "NV61_P101010", "20": "YUYV_P101010", "21": "YVYU_P101010", "22": "UYVY_P101010", "23": "VYUY_P101010", "24": "NV12_P010", "25": "NV21_P010", "26": "NV16_P010", "27": "NV61_P010", "28": "YUYV_P010", "29": "YVYU_P010", "30": "UYVY_P010", "31": "VYUY_P010", "32": "RGB888", "33": "BGR888", "34": "RGB565", "35": "BGR565", "48": "ARGB8888", "49": "ABGR8888", "50": "ARGB8565", "51": "ABGR8565", "52": "ARGB1555", "53": "ABGR1555", "54": "ARGB4444", "55": "ABGR4444", "56": "RGBA8888", "57": "BGRA8888", "58": "RGBA5658", "59": "BGRA5658", "60": "RGBA5551", "61": "BGRA5551", "62": "RGBA4444", "63": "BGRA4444"}}, "v0_global_chroma": {"acc": [0, 8], "auto": 0, "comment": "v0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10", "default": [128, 128], "display": "v0 sp global chroma", "hidden": 0, "range": [0, 255], "size": [2], "target_conf": ["tde.v0_global_chroma"], "type": "AX_U8", "dependency": "user"}, "output_format": {"acc": [0, 3], "auto": 0, "comment": "dpu output format", "default": 0, "display": "output format", "hidden": 0, "range": [0, 7], "size": [], "target_conf": ["tde.output_format", "tde.mat_adj_enable", "tde.mat_adj_matrix", "tde.to422_filter_enable"], "type": "AX_U8", "dependency": "user", "enum_field": {"0": "RGB565", "1": "SRGB565", "2": "SRGB565S", "3": "SRGB666LP", "4": "SRGB888", "5": "YUV422", "6": "SRGB888A", "7": "SRGB888B"}}, "mat_adj_offset": {"acc": [1, 8], "auto": 0, "comment": "in && out offset for DDR write adjustment", "default": [[0, 0, 0], [0, 0, 0]], "display": "mat adj offset", "hidden": 1, "range": [-256, 255], "size": [2, 3], "target_conf": ["tde.mat_adj_offset"], "type": "AX_S16", "dependency": "common"}, "dither_enable": {"display": "dither enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 1, "comment": "dither enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.dither_enable", "tde.dither_seed_ch0", "tde.dither_seed_ch1", "tde.dither_seed_ch2"], "dependency": "user"}, "clip_enable": {"acc": [0, 1], "auto": 0, "comment": "clip enable control bit", "default": 0, "display": "clip enable", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.clip_enable"], "type": "AX_U8", "dependency": "user"}, "clip_gain": {"acc": [0, 2, 8], "auto": 0, "comment": "gains for 3 channels", "default": [1.0, 1.0, 1.0], "display": "clip gain", "hidden": 0, "range": [0.0, 3.99609375], "size": [3], "target_conf": ["tde.clip_gain"], "type": "AX_U16", "dependency": "user"}, "clip_offset": {"acc": [1, 8], "auto": 0, "comment": "Global alpha enable control bit. It's priority is lower than special alpha", "default": [[0, 0, 0], [0, 0, 0]], "display": "clip offset", "hidden": 1, "range": [-256, 255], "size": [2, 3], "target_conf": ["tde.clip_offset"], "type": "AX_S16", "dependency": "common"}, "clip_range": {"acc": [1, 8], "auto": 0, "comment": "clamp range for 3 channels", "default": [[0, 255], [0, 255], [0, 255]], "display": "clip range", "hidden": 0, "range": [-256, 255], "size": [3, 2], "target_conf": ["tde.clip_range"], "type": "AX_S16", "dependency": "user"}, "lba_enable": {"acc": [0, 1], "auto": 0, "comment": "lba enable control bit", "default": 0, "display": "lba enable", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.lba_enable"], "type": "AX_U8", "dependency": "user"}, "lba_ext_size": {"acc": [0, 13], "auto": 0, "comment": "lba extension size(paddings on edges)", "default": [80, 80, 80, 80], "display": "lba ext size", "hidden": 0, "range": [0, 8191], "size": [4], "target_conf": ["tde.lba_ext_size"], "type": "AX_U16", "dependency": "common"}, "lba_background": {"acc": [0, 8], "auto": 0, "comment": "lba background color", "default": [100, 255, 255], "display": "lba background", "hidden": 0, "range": [0, 255], "size": [3], "target_conf": ["tde.lba_background"], "type": "AX_U8", "dependency": "user"}}, "submodules": {"setup": {"params": ["mat_adj_offset"], "configs": ["tde.enable", "tde.to422_filter_weight_hor", "tde.mat_adj_matrix", "tde.dither_pmask_ch0", "tde.dither_pmask_ch1", "tde.dither_pmask_ch2"]}, "format": {"params": ["v0_format", "output_format", "v0_global_chroma"], "configs": []}, "clip": {"params": ["clip_enable", "clip_gain", "clip_offset", "clip_range"], "configs": []}, "lba": {"params": ["lba_enable", "lba_ext_size", "lba_background"], "configs": []}, "dither": {"params": ["dither_enable"], "configs": []}}, "target_module": {"mc20l": {"tde": {"id": 6200, "method": 2}}}, "configs": {"tde": {"enable": {"acc": [0, 1], "size": [], "description": "DPU enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "v0_format": {"acc": [0, 6], "size": [], "description": "v0 format", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "v0_global_chroma": {"acc": [0, 8], "size": [2], "description": "v0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "output_format": {"acc": [0, 3], "size": [], "description": "dpu output format", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "mat_adj_enable": {"acc": [0, 1], "size": [], "description": "After blending, result will be dumped into DDR. When this happens, this bit controls whether the result will be adjusted by using 3x3 matrix.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "mat_adj_matrix": {"acc": [1, 2, 10], "size": [3, 3], "description": "3x3 aadjustment matrix", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "mat_adj_offset": {"acc": [1, 8], "size": [2, 3], "description": "in && out offset for DDR write adjustment.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "dither_enable": {"acc": [0, 1], "size": [], "description": "dither enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "dither_seed_enable": {"acc": [0, 1], "size": [], "description": "dither seed enable bit.", "usage": "0 - use stat; 1 - use config", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "dither_seed_ch0": {"acc": [0, 16], "size": [2], "description": "dither seed for ch0 channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "dither_seed_ch1": {"acc": [0, 16], "size": [2], "description": "dither seed for ch1 channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "dither_seed_ch2": {"acc": [0, 16], "size": [2], "description": "dither seed for ch2 channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "dither_pmask_ch0": {"acc": [0, 16], "size": [2], "description": "pmask for ch0 channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "dither_pmask_ch1": {"acc": [0, 16], "size": [2], "description": "pmask for ch1 channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "dither_pmask_ch2": {"acc": [0, 16], "size": [2], "description": "pmask for ch2 channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "to422_filter_enable": {"acc": [0, 1], "size": [], "description": "If enabled and output format is YUV422, UV will be filtered at horizontal direction using provided filter. After that, UV444 will be decimated to generate UV422.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "to422_filter_weight_hor": {"acc": [1, 1, 5], "size": [7], "description": "The filter weight used to filter UV444 if 'to422_filter_enable' is ON.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S8", "partition": "-"}, "clip_enable": {"acc": [0, 1], "size": [], "description": "clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "clip_gain": {"acc": [0, 2, 8], "size": [3], "description": "gains for 3 channels", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "clip_offset": {"acc": [1, 8, 0], "size": [2, 3], "description": "input -offset[0][x], output + offset[1][x]", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "clip_range": {"acc": [1, 8, 0], "size": [3, 2], "description": "clamp range for 3 channels", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "lba_enable": {"acc": [0, 1], "size": [], "description": "lba enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "lba_ext_size": {"acc": [0, 13], "size": [4], "description": "lba extension size(paddings on edges)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "lba_background": {"acc": [0, 8, 0], "size": [3], "description": "lba background color", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}}}, "partition_configs": []}