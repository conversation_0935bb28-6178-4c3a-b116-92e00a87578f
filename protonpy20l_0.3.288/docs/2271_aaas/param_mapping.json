{"aes_dgain": {"api": "nAesDgain", "display": "aes<PERSON><PERSON>n", "comments": "dgain for statics", "hint": "Accuracy: U10.8 Range: [0, 261888]"}, "aes_enable": {"api": "nAesEn", "display": "aesEnable", "comments": "aes_enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "aes_grid_4ch_enable": {"api": "nAesGrid4chEnable", "display": "aesGrid4chEnable", "comments": "static four channel enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "aes_grid_sat_thr": {"api": "nAesGridSatThr", "display": "aesGridSatThr", "comments": "over saturated threshold value", "hint": "Accuracy: U20.4 Range: [0, 16777215]"}, "aes_grid_ycoeff": {"api": "nAesGrid<PERSON><PERSON><PERSON>", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comments": "coeff for compute luma for grid", "hint": "Accuracy: U0.12 Range: [0, 4095]"}, "aes_hist_mode": {"api": "eAesHistMode", "display": "aesHistMode", "comments": "0: HIST_MODE_Y, 1: HIST_MODE_YRGB, 2: HIST_MODE_RGGB", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "aes_hist_linear_bins": {"api": "nAesHistLinearBins", "display": "aesHistLinearBins", "comments": "hist's x coord (i.e hist_bin num) will be 256 * pow(2, hist_linear_bins)", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "aes_hist_linear_scale": {"api": "nAesHistLinearScale", "display": "aesHistLinearScale", "comments": "for compute clip_value when count the target linear hist, clip_value=val*pow(2, linear_scale)", "hint": "Accuracy: S4.0 Range: [-15, 12]"}, "aes_hist_log_scale": {"api": "nAesHistLogScale", "display": "aesHistLogScale", "comments": "for compute input_val when count the target log hist, input_value=val*pow(2, log_scale)", "hint": "Accuracy: S4.0 Range: [-15, 12]"}, "aes_hist_weight": {"api": "nAesHistWeight", "display": "aesHistWeight", "comments": "locationable weight for count hist", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "aes_hist_ycoeff": {"api": "nAesHist<PERSON>coeff", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comments": "coeff for compute luma for hist", "hint": "Accuracy: U0.12 Range: [0, 4095]"}, "aes_item_enable": {"api": "nAesItemEnable", "display": "aesItemEnable", "comments": "two aes_item enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "aes_grid_enable": {"api": "nAesGridEnable", "display": "aesGridEnable", "comments": "aes_grid_enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "aes_hist_log_enable": {"api": "nAesHistLogEnable", "display": "aesHistLogEnable", "comments": "aes_hist_log_enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "aes_hist_linear_enable": {"api": "nAesHistLinearEnable", "display": "aesHistLinearEnable", "comments": "aes_hist_linear_enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "aes_gridroi_offset": {"api": "nAesRegionOffset", "display": "aesRegionOffset", "comments": "user set region offset coordinate [y, x] of two independent ROIs", "hint": "Accuracy: U16.0 Range: [0, 8192]"}, "aes_region_num": {"api": "nAesRegionNum", "display": "aesRegionNum", "comments": "user set grid number in region [y_num, x_num] of two independent ROIs", "hint": "Accuracy: U16.0 Range: [0, 8192]"}, "aes_gridroi_size": {"api": "nAesRegionSize", "display": "aesRegionSize", "comments": "user set grid size in region [height, width] of two independent ROIs", "hint": "Accuracy: U16.0 Range: [0, 8192]"}, "aes_histroi_offset": {"api": "nAesHistBlockOffset", "display": "aesHistBlockOffset", "comments": "user set histroi coordinate [y, x] for hist stat", "hint": "Accuracy: U16.0 Range: [0, 8192]"}, "aes_histroi_size": {"api": "nAesHistBlockSize", "display": "aesHistBlockSize", "comments": "user set histroi size [height, width] for hist stat", "hint": "Accuracy: U16.0 Range: [0, 8192]"}, "aes_item_roi": {"api": "nAesItemRoi", "display": "aesItemRoi", "comments": "two aes_item roi", "hint": "Accuracy: U16.0 Range: [0, 65535]"}, "awbs_dgain": {"api": "nAwbsDgain", "display": "awbsDgain", "comments": "dgain for statics", "hint": "Accuracy: U10.8 Range: [0, 261888]"}, "awbs_enable": {"api": "nAwbsEn", "display": "awbsEnable", "comments": "enable awb stat", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "awbs_grid_mode": {"api": "eAwbsGridMode", "display": "awbsGridMode", "comments": "awb grid stat mode BAYER3CH = 0, BAYER4CH, BAYER4CH_LUMA4CH, BAYER4CH_LUMA2CH", "hint": "Accuracy: U2.0 Range: [0, 3]"}, "awbs_grid_rgb_thr": {"api": "nAwbsGridRgbThr", "display": "awbsGridRgbThr", "comments": "awb 4ch of saturated threshold for grid", "hint": "Accuracy: S14.6 Range: [-1048575, 1048575]"}, "awbs_grid_y_thr": {"api": "nAwbsGridYThr", "display": "awbsGridYThr", "comments": "awb luma of saturated threshold for grid", "hint": "Accuracy: S14.6 Range: [-1048575, 1048575]"}, "awbs_grid_rgb_sat_thr": {"api": "nAwbsGridRgbSatThr", "display": "awbsGridRgbSatThr", "comments": "awb 4ch of saturated threshold for grid", "hint": "Accuracy: U14.6 Range: [0, 1048575]"}, "awbs_grid_y_sat_thr": {"api": "nAwbsGridYSatThr", "display": "awbsGridYSatThr", "comments": "awb luma ch of saturated threshold for grid", "hint": "Accuracy: U14.6 Range: [0, 1048575]"}, "awbs_grid_luma_slice_thr": {"api": "nAwbsGridLumaSliceThr", "display": "awbsGridLumaSliceThr", "comments": "awb stat split luma threshold for grid", "hint": "Accuracy: S14.6 Range: [-1048575, 1048575]"}, "awbs_grid_ycoeff": {"api": "nAwbsGridYcoeff", "display": "awbsGrid<PERSON><PERSON><PERSON>", "comments": "awb coeff for computing luma for grid", "hint": "Accuracy: U0.12 Range: [0, 4095]"}, "awbs_grid_enable": {"api": "nAwbsGridEnable", "display": "awbsGridEnable", "comments": "awbs grid enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "awbs_item_enable": {"api": "nAwbsItemEnable", "display": "awbsItemEnable", "comments": "two awbs_item enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "awbs_gridroi_offset": {"api": "nAwbsRegionOffset", "display": "awbsRegionOffset", "comments": "user set region offset coordinate [y, x]", "hint": "Accuracy: U16.0 Range: [0, 8192]"}, "awbs_region_num": {"api": "nAwbsRegionNum", "display": "awbsRegionNum", "comments": "user set grid number in region [y_num, x_num]", "hint": "Accuracy: U16.0 Range: [0, 8192]"}, "awbs_gridroi_size": {"api": "nAwbsRegionSize", "display": "awbsRegionSize", "comments": "user set grid size in region [height, width]", "hint": "Accuracy: U16.0 Range: [0, 8192]"}, "awbs_item_roi": {"api": "nAwbsItemRoi", "display": "awbsItemRoi", "comments": "two aes_item roi", "hint": "Accuracy: U16.0 Range: [0, 65535]"}, "afs_dgain": {"api": "nAfsDgain", "display": "afsDgain", "comments": "dgain for statics", "hint": "Accuracy: U10.8 Range: [0, 261888]"}, "afs_enable": {"api": "nAfsEn", "display": "afsEnable", "comments": "enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "afs_fv_mode": {"api": "eAfsFvMode", "display": "afsFvMode", "comments": "independent fv_mode for V1, V2, H1, H2, 0: sum mode, 1: peak mode", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "afs_fir_enable": {"api": "nAfsFirEnable", "display": "afsFirEnable", "comments": "fir enable for H1, H2", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "afs_coring_thr": {"api": "nAfsCoringThr", "display": "afsCoringThr", "comments": "coring thr for V1, V2, H1, H2", "hint": "Accuracy: U8.10 Range: [0, 262143]"}, "afs_coring_gain": {"api": "nAfsCoringGain", "display": "afsCoringGain", "comments": "coring gain for V1, V2, H1, H2", "hint": "Accuracy: U5.7 Range: [0, 4095]"}, "afs_coring_lut": {"api": "nAfsCoringLut", "display": "afsCoringLut", "comments": "coring gain lut for V1, V2, H1, H2", "hint": "Accuracy: U5.0 Range: [0, 31]"}, "afs_coring_pix_sum_mode": {"api": "eAfsCoringPixSumMode", "display": "afsCoringPixSumMode", "comments": "0: don't count pixel' val for smaller than coring thr, 1: do count", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "afs_coring_pix_cnt_mode": {"api": "eAfsCoringPixCntMode", "display": "afsCoringPixCntMode", "comments": "0: don't count pixel's count for smaller than coring thr, 1: do count", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "afs_scale_ratio": {"api": "nAfsScaleRatio", "display": "afsScaleRatio", "comments": "scale ratio for V1, V2, H1, H2", "hint": "Accuracy: U3.0 Range: [0, 7]"}, "afs_scale_weight": {"api": "nAfsScaleWeight", "display": "afsScaleWeight", "comments": "scale weight for V1, V2, H1, H2", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "afs_ycoeff": {"api": "nAfsYcoeff", "display": "afs<PERSON>coeff", "comments": "coeff for compute luma from bayer", "hint": "Accuracy: U0.12 Range: [0, 4095]"}, "afs_v_iir_coeff": {"api": "nAfsVIir<PERSON>oeff", "display": "afs<PERSON><PERSON><PERSON><PERSON><PERSON>", "comments": "iir coeff for V1, V2", "hint": "Accuracy: S2.12 Range: [-16383, 16383]"}, "afs_h_iir_coeff": {"api": "nAfsHIirCoeff", "display": "afsHIirCoeff", "comments": "iir coeff for H1, H2", "hint": "Accuracy: S2.12 Range: [-16383, 16383]"}, "afs_h_fir_coeff": {"api": "nAfsHFir<PERSON>oeff", "display": "afs<PERSON><PERSON><PERSON><PERSON><PERSON>", "comments": "fir coeff for H1, H2", "hint": "Accuracy: S6.0 Range: [-64, 63]"}, "afs_ldg_thr": {"api": "nAfsLdgThr", "display": "afsLdgThr", "comments": "luma dependent gain thr independent for V1, V2, H1, H2", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "afs_ldg_slope": {"api": "nAfsLdgSlope", "display": "afsLdgSlope", "comments": "luma dependent gain adj slope independent for V1, V2, H1, H2", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "afs_ldg_lim": {"api": "nAfsLdgLim", "display": "afsLdgLim", "comments": "luma dependent gain limit independent for V1, V2, H1, H2", "hint": "Accuracy: U1.7 Range: [0, 255]"}, "afs_ldg_range": {"api": "nAfsLdgRange", "display": "afsLdgRange", "comments": "luma dependent gain search range share with V1, V2, H1, H2", "hint": "Accuracy: U2.0 Range: [0, 3]"}, "afs_high_luma_cnt_thr": {"api": "nAfsHighLumaCntThr", "display": "afsHighLumaCntThr", "comments": "high luma count thr independent V1, V2, H1, H2", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "afs_vert_enable": {"api": "nAfsVertEnable", "display": "afsVertEnable", "comments": "enable for V1, V2", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "afs_hori_enable": {"api": "nAfsHoriEnable", "display": "afsHoriEnable", "comments": "enable for H1, H2", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "afs_gridroi_offset": {"api": "nAfsRegionOffset", "display": "afsRegionOffset", "comments": "user set region offset coordinate [y, x]", "hint": "Accuracy: U16.0 Range: [0, 8192]"}, "afs_region_num": {"api": "nAfsRegionNum", "display": "afsRegionNum", "comments": "user set grid number in region [y_num, x_num]", "hint": "Accuracy: U16.0 Range: [0, 8192]"}, "afs_gridroi_size": {"api": "nAfsRegionSize", "display": "afsRegionSize", "comments": "user set grid size in region [height, width]", "hint": "Accuracy: U16.0 Range: [0, 8192]"}}