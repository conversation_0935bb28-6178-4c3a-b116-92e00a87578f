h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| offset_in |  | u8.6 | AX_U16 | [\] |  [0, 16383\] | [0.0, 255.984375\] | 64 | 1.0 | hidden | 'dpc.offset_in' | offset in | common |
| offset_out |  | u8.6 | AX_U16 | [\] |  [0, 16383\] | [0.0, 255.984375\] | 64 | 1.0 | hidden | 'dpc.offset_out' | offset out | common |
| shot_noise_coefs |  | s0.31 | AX_S32 | [4, 2\] |  [-2147483648, 2147483647\] | [-1.0, 0.9999999995343387\] | [[1073741824, 107374182\], [1073741824, 107374182\], [1073741824, 107374182\], [1073741824, 107374182\]\] | [[0.5, 0.049999999813735485\], [0.5, 0.049999999813735485\], [0.5, 0.049999999813735485\], [0.5, 0.049999999813735485\]\] | hidden | 'dpc.noise_lut' | shot noise coefs read from ACP module | common |
| read_noise_coefs |  | s0.31 | AX_S32 | [4, 3\] |  [-2147483648, 2147483647\] | [-1.0, 0.9999999995343387\] | [[42949673, 42949673, 21474836\], [42949673, 42949673, 21474836\], [42949673, 42949673, 21474836\], [42949673, 42949673, 21474836\]\] | [[0.02000000001862645, 0.02000000001862645, 0.009999999776482582\], [0.02000000001862645, 0.02000000001862645, 0.009999999776482582\], [0.02000000001862645, 0.02000000001862645, 0.009999999776482582\], [0.02000000001862645, 0.02000000001862645, 0.009999999776482582\]\] | hidden | 'dpc.noise_lut' | read noise coefs read from ACP module | common |
| partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [\] | [\] | None | None | hidden | 'dpc.static_info' | partition related info | common |
| wb_gain |  | u4.8 | AX_U16 | [4\] |  [0, 4095\] | [0.0, 15.99609375\] | [256, 256, 256, 256\] | [1.0, 1.0, 1.0, 1.0\] | hidden | 'dpc.wb_gain', 'dpc.inv_wb_gain' | wbc gain. calculated wbc(fw) based on sensor setting and stat from blc(fw), This param is obtained from isp_result_context_t, The value of wbgain  is taken from module AWB and  made up of wb_rGain,wb_grGain ,wb_gbGain and wb_bGain. | common |
| again |  | u22.10 | AX_U32 | [\] |  [0, 4294967295\] | [0.0, 4194303.9990234375\] | 1024 | 1.0 | hidden |  | again used in calc noise lut | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | Dpc Enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'dpc.enable' | 0:disable, 1:enable |  | None | None | None |
| noise_ratio | Noise Lut Ratio | u4.10 | AX_U16 | [\] | [0, 16383\] | [0.0, 15.9990234375\] | 1024 | 1.0 | open | 'dpc.noise_lut' | noise level gain, the larger the value, the larger the noise level, less defect pixels |  | ['gain/lux'\] | [16\] | ['linear'\] |
| static_dpc_enable | Static Dpc Enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'dpc.static_enable' | 0:disable static dpc, 1:enable static dpc |  | None | None | None |
| static_dpc_length | Static dpc Length | u16 | AX_U16 | [\] | [0, 4096\] | [None, None\] | 0 | None | open | 'dpc.static_info' | static defect pixel storage length |  | None | None | None |
| static_dpc_buffer | Static dpc Buffer | u32 | AX_U32 | [1024\] | [0, 4294967295\] | [None, None\] | np.zeros(8192).tolist() | None | open | 'dpc.static_info' | static defect pixel coordinates info |  | None | None | None |
| dynamic_dpc_enable | Dynamic Dpc Enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'dpc.dp_det_m2', 'dpc.calc_limit_enable' | 0:disable dynamic dpc, 1:enable dynamic dpc |  | None | None | None |
| predet_enable | predet Enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'dpc.quick_det_enable' | 0:disable predet, 1:enable predet |  | None | None | None |
| predet_level_slope | Predet Level Slope | u0.4 | AX_U8 | [\] | [0, 15\] | [0.0, 0.9375\] | 4 | 0.25 | open | 'dpc.quick_det_th_slope' | defect pixel margin gain for quick detection, the larger the value, less defect pixel detected |  | ['gain/lux'\] | [16\] | ['linear'\] |
| predet_level_offset | Predet Level Offset | u8.6 | AX_U16 | [\] | [0, 16383\] | [0.0, 255.984375\] | 0 | 0.0 | open | 'dpc.quick_det_th_offset' | defect pixel base margin for quick detection, the larger the value, less defect pixel detected |  | ['gain/lux'\] | [16\] | ['linear'\] |
| predet_level_max | Predet Level Max | u8.6 | AX_U16 | [\] | [0, 16383\] | [0.0, 255.984375\] | 16383 | 255.984375 | open | 'dpc.quick_det_th_max' | defect pixel max margin for quick detection, the larger the value, less defect pixel detected |  | ['gain/lux'\] | [16\] | ['linear'\] |
| defective_pixel_type | Defective Pixel Type | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'dpc.dp_det_mode' | Dynamic dp Detect Module, 0:single defective pixel mode, 1:dual defective pixel mode |  | ['gain/lux'\] | [16\] | ['linear'\] |
| det_fine_strength | Detect Fine Strength | u0.6 | AX_U8 | [\] | [0, 63\] | [0.0, 0.984375\] | 48 | 0.75 | open | 'dpc.dp_det_th_ratio' | local detction strength for defect pixel detection, the larger the value, more defect pixels will be detected |  | ['gain/lux'\] | [16\] | ['linear'\] |
| det_coarse_strength | Detect Coarse Strength | u4.4 | AX_U8 | [\] | [0, 255\] | [0.0, 15.9375\] | 236 | 14.75 | open | 'dpc.dp_det_m1' | defect pixel detection threshold, the larger the value, more defect pixels are detected |  | ['gain/lux'\] | [16\] | ['linear'\] |
| dynamic_dpc_strength | Dynamic Dpc Strength | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 128 | 0.5 | open | 'dpc.dp_det_m2' | the larger the value, the larger the defect pixel probability |  | ['gain/lux'\] | [16\] | ['linear'\] |
| edge_strength | Edge Strength | u1.7 | AX_U8 | [\] | [0, 255\] | [0.0, 1.9921875\] | 102 | 0.796875 | open | 'dpc.dp_cor_m3' | direction interpolation ratio used in interpolation, the larger the value, more direction interpolated result used in interpolation, better performance for defective pixels at edge locations |  | ['gain/lux'\] | [16\] | ['linear'\] |
| hot_cold_type_strength | Hot Cold Type Strength | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 32 | 0.25 | open | 'dpc.dp_cor_m4' | hot_cold result ratio, the larger the value, more hot_cold result used |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sup_wink_threshold | Suppress wink Threshold | u4.4 | AX_U8 | [\] | [0, 255\] | [0.0, 15.9375\] | 16 | 1.0 | open | 'dpc.dp_cor_m5' | the threshold to use direction interpoation or local avg interpolation result |  | ['gain/lux'\] | [16\] | ['linear'\] |
| color_limit_enable | Color Limit Enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'dpc.calc_limit_enable' | 0:disable color limit, 1:enable color limit |  | None | None | None |
| dynamic_dp_upperlimit | Dynamic Dp Color Upperlimit Offset | u8.6 | AX_U16 | [\] | [0, 16383\] | [0.0, 255.984375\] | 256 | 4.0 | open | 'dpc.margin_u' | up margin for color limit, the larger the value, less color limitation |  | ['gain/lux'\] | [16\] | ['linear'\] |
| dynamic_dp_lowerlimit | Dynamic Dp Color Lowerlimit Offset | u8.6 | AX_U16 | [\] | [0, 16383\] | [0.0, 255.984375\] | 256 | 4.0 | open | 'dpc.margin_l' | low margin for color limit, the larger the value, less color limitation |  | ['gain/lux'\] | [16\] | ['linear'\] |
| static_dp_upperlimit | Static Dp Color Upperlimit Offset | u8.6 | AX_U16 | [\] | [0, 16383\] | [0.0, 255.984375\] | 256 | 4.0 | open | 'dpc.margin_u' | up margin for color limit, the larger the value, less color limitation |  | ['gain/lux'\] | [16\] | ['linear'\] |
| static_dp_lowerlimit | Static  Dp Color Lowerlimit Offset | u8.6 | AX_U16 | [\] | [0, 16383\] | [0.0, 255.984375\] | 256 | 4.0 | open | 'dpc.margin_l' | low margin for color limit, the larger the value, less color limitation |  | ['gain/lux'\] | [16\] | ['linear'\] |
| normal_pixel_upperlimit | Normal Pixel Color Upperlimit Offset | u8.6 | AX_U16 | [\] | [0, 16383\] | [0.0, 255.984375\] | 256 | 4.0 | open | 'dpc.margin_u' | up margin for color limit, the larger the value, less color limitation |  | ['gain/lux'\] | [16\] | ['linear'\] |
| normal_pixel_lowerlimit | Normal Pixel Color Lowerlimit Offset | u8.6 | AX_U16 | [\] | [0, 16383\] | [0.0, 255.984375\] | 256 | 4.0 | open | 'dpc.margin_l' | low margin for color limit, the larger the value, less color limitation |  | ['gain/lux'\] | [16\] | ['linear'\] |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |