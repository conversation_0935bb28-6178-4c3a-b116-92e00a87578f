{"configs": {"yuv_3dnr": {"enable": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "tf_y_en": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "tf_uv_en": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sf_gau_y_en": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sf_gau_uv_en": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sf_lap_y_en": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sf_lap_uv_en": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "gf_uv_en": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "debug_mode": {"acc": [0, 4], "size": [], "description": "", "usage": "", "constraints": "[0, 13]", "type": "AX_U8", "partition": "-"}, "debug_gain": {"acc": [0, 3], "size": [], "description": "debug bit shift for data visualization", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "init_ref": {"acc": [0, 1], "size": [], "description": "0: no, 1: yes", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "init_tweight": {"acc": [0, 1], "size": [], "description": "0: no, 1: yes", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "init_coef_ab": {"acc": [0, 1], "size": [], "description": "0: no, 1: yes", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "ext_mask_en": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "ext_mask_mode": {"acc": [0, 1], "size": [], "description": "0: motion, 1: hdr", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "ext_mask_thre": {"acc": [0, 1, 7], "size": [], "description": "adjust by ext_mask", "usage": "", "constraints": "ext_mask_thre <= 1.0", "type": "AX_U8", "partition": "-"}, "ext_mask_slope": {"acc": [0, 8, 8], "size": [], "description": "adjust by ext_mask", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "ext_mask_ratio": {"acc": [0, 1, 7], "size": [], "description": "ratio adjust by ext_mask", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_small_win_sel": {"acc": [0, 1], "size": [], "description": "motion feature small win sel, 0:3_7, 1:7_11", "usage": "", "constraints": "motion_large_win_sel >= motion_small_sel_ratio", "type": "AX_U8", "partition": "-"}, "motion_small_sel_ratio": {"acc": [0, 1, 7], "size": [], "description": "motion feature small select ratio", "usage": "", "constraints": "motion_small_sel_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "motion_large_win_sel": {"acc": [0, 1], "size": [], "description": "motion feature large win sel, 0:3_7, 1:7_11", "usage": "", "constraints": "motion_large_win_sel >= motion_small_sel_ratio", "type": "AX_U8", "partition": "-"}, "motion_large_sel_ratio": {"acc": [0, 1, 7], "size": [], "description": "motion feature large select ratio", "usage": "", "constraints": "motion_large_sel_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "motion_feature_gauss_str": {"acc": [0, 1, 7], "size": [], "description": "motion feature gauss str", "usage": "", "constraints": "motion_feature_gauss_str <= 1.0", "type": "AX_U8", "partition": "-"}, "motion_sad_weight_th": {"acc": [0, 1, 7], "size": [2], "description": "sad weight", "usage": "linearClamp((sad-sd).clip(min=0), motion_sad_sd_th, motion_sad_weight_slope, [motion_sad_weight_th[0], motion_sad_weight_th[1]])", "constraints": "motion_sad_weight_th[1] >= motion_sad_weight_th[0], motion_sad_weight_th <= 1.0", "type": "AX_U8", "partition": "-"}, "motion_sad_weight_slope": {"acc": [0, 1, 8], "size": [], "description": "sad weight slope", "usage": "linearClamp((sad-sd).clip(min=0), motion_sad_sd_th, motion_sad_weight_slope, [motion_sad_weight_th[0], motion_sad_weight_th[1]])", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_sad_sd_th": {"acc": [0, 8], "size": [], "description": "sad sub sd th", "usage": "linearClamp((sad-sd).clip(min=0), motion_sad_sd_th, motion_sad_weight_slope, [motion_sad_weight_th[0], motion_sad_weight_th[1]])", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_highlight_diff_th": {"acc": [0, 8, 2], "size": [], "description": "motion sharpen function diff th", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_highlight_luma_th": {"acc": [0, 8, 0], "size": [], "description": "motion sharpen function luma th", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_highlight_luma_slope": {"acc": [0, 2, 8], "size": [], "description": "motion sharpen function luma slope", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_highlight_var_th": {"acc": [0, 7, 0], "size": [], "description": "motion sharpen function var th", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_highlight_var_slope": {"acc": [0, 2, 7], "size": [], "description": "motion sharpen function var slope", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_sharpen_str": {"acc": [0, 3, 5], "size": [], "description": "motion sharpen str", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_red_center": {"acc": [1, 7, 2], "size": [2], "description": "", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "motion_red_radius": {"acc": [0, 8, 2], "size": [2], "description": "", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_red_dist_mode": {"acc": [0, 1, 0], "size": [], "description": "0: avg, 1:max", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_red_dist_weight": {"acc": [0, 1, 3], "size": [4], "description": "", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_red_th": {"acc": [0, 8, 0], "size": [], "description": "", "usage": "linearClamp(red_dist, motion_red_th, motion_red_slope, 0.0, 1.0)", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_red_slope": {"acc": [0, 1, 8], "size": [], "description": "", "usage": "linearClamp(red_dist, motion_red_th, motion_red_slope, 0.0, 1.0)", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_y_red_correct_str": {"acc": [0, 3, 5], "size": [], "description": "", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_uv_red_correct_str": {"acc": [0, 3, 5], "size": [], "description": "", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_blue_center": {"acc": [1, 7, 2], "size": [2], "description": "", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "motion_blue_radius": {"acc": [0, 8, 2], "size": [2], "description": "", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_blue_dist_mode": {"acc": [0, 1, 0], "size": [], "description": "0: avg, 1:max", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_blue_dist_weight": {"acc": [0, 1, 3], "size": [4], "description": "", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_blue_th": {"acc": [0, 8, 0], "size": [], "description": "", "usage": "linearClamp(blue_dist, motion_blue_th, motion_blue_slope, 0.0, 1.0)", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_blue_slope": {"acc": [0, 1, 8], "size": [], "description": "", "usage": "linearClamp(blue_dist, motion_blue_th, motion_blue_slope, 0.0, 1.0)", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_y_blue_correct_str": {"acc": [0, 3, 5], "size": [], "description": "", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_uv_blue_correct_str": {"acc": [0, 3, 5], "size": [], "description": "", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_noise_y_lut": {"acc": [0, 8, 2], "size": [9], "description": "noise in motion_y", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_noise_uv_lut": {"acc": [0, 8, 2], "size": [9], "description": "noise in motion_uv", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_hvs_y_lut": {"acc": [0, 3, 5], "size": [9], "description": "weber law in motion_y", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_hvs_uv_lut": {"acc": [0, 3, 5], "size": [9], "description": "weber law in motion_uv", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_sad_sd_lw_adjust": {"acc": [0, 1, 7], "size": [], "description": "adjust noise in large window size", "usage": "", "constraints": "motion_sad_sd_lw_adjust <= 1.0", "type": "AX_U8", "partition": "-"}, "motion_dec_y_th": {"acc": [0, 8], "size": [], "description": "motion dec th in y channel", "usage": "linearClamp(motion_level_y, motion_dec_y_th, motion_dec_y_slope, [0, 1])", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_dec_y_slope": {"acc": [0, 1, 8], "size": [], "description": "motion dec slope in y channel", "usage": "linearClamp(motion_level_y, motion_dec_y_th, motion_dec_y_slope, [0, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_m2s_dec_y_th": {"acc": [0, 8], "size": [], "description": "motion m2s dec th in y channel", "usage": "linearClamp(motion_level_y, motion_dec_y_th, motion_dec_y_slope, [0, 1])", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_m2s_dec_y_slope": {"acc": [0, 1, 8], "size": [], "description": "motion m2s dec slope in y channel", "usage": "linearClamp(motion_level_y, motion_dec_y_th, motion_dec_y_slope, [0, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_hdr_adjust_y_th": {"acc": [0, 8, 0], "size": [], "description": "motion adjust by hdr_mask", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_dec_uv_th": {"acc": [0, 8], "size": [], "description": "motion dec th in uv channel", "usage": "linearClamp(motion_level_uv, motion_dec_uv_th, motion_dec_uv_slope, [0, 1])", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_dec_uv_slope": {"acc": [0, 1, 8], "size": [], "description": "motion dec slope in uv channel", "usage": "linearClamp(motion_level_uv, motion_dec_uv_th, motion_dec_uv_slope, [0, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_adj_uv_th": {"acc": [0, 8], "size": [], "description": "motion adjust th in uv channel", "usage": "linearClamp(motion_level_uv, motion_adj_uv_th, motion_adj_uv_slope, [0, 1])", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_adj_uv_slope": {"acc": [0, 1, 8], "size": [], "description": "motion adjust slope in uv channel", "usage": "linearClamp(motion_level_uv, motion_adj_uv_th, motion_adj_uv_slope, [0, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "motion_hdr_adjust_uv_th": {"acc": [0, 8, 0], "size": [], "description": "uv motion adjust by hdr_mask", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "ratio_uv_limit": {"acc": [0, 1, 7], "size": [], "description": "uv tweight limit", "usage": "", "constraints": "ratio_uv_limit <= 1.0", "type": "AX_U8", "partition": "-"}, "ratio_uv_adjust_th": {"acc": [0, 1, 7], "size": [], "description": "uv motion th to adjust tweight", "usage": " linearClamp(motion_mask_uv_adjust, ratio_uv_adjust_th, ratio_uv_adjust_slope, [0.0, ratio_uv_adjust])", "constraints": "uv_adjust_th <= 1.0", "type": "AX_U8", "partition": "-"}, "ratio_uv_adjust_slope": {"acc": [0, 4, 8], "size": [], "description": "uv motion slope to adjust tweight", "usage": " linearClamp(motion_mask_uv_adjust, ratio_uv_adjust_th, ratio_uv_adjust_slope, [0.0, ratio_uv_adjust])", "constraints": "", "type": "AX_U16", "partition": "-"}, "ratio_uv_adjust": {"acc": [0, 1, 7], "size": [], "description": "uv motion to adjust tweight ratio", "usage": " linearClamp(motion_mask_uv_adjust, ratio_uv_adjust_th, ratio_uv_adjust_slope, [0.0, ratio_uv_adjust])", "constraints": "uv_adjust_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_gau_y_ref_weight_th": {"acc": [0, 1, 7], "size": [], "description": "tweight th to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_gau_y_ref_weight_th, sf_gau_y_ref_weight_slope, [sf_gau_y_ref_ratio_limit, 1])", "constraints": "sf_gau_y_ref_weight_th <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_gau_y_ref_weight_slope": {"acc": [0, 4, 8], "size": [], "description": "tweight slope to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_gau_y_ref_weight_th, sf_gau_y_ref_weight_slope, [sf_gau_y_ref_ratio_limit, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_y_ref_ratio_limit": {"acc": [0, 1, 7], "size": [], "description": "ratio limit to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_gau_y_ref_weight_th, sf_gau_y_ref_weight_slope, [sf_gau_y_ref_ratio_limit, 1])", "constraints": "sf_gau_y_ref_ratio_limit <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_gau_y_cur_weight_th": {"acc": [0, 1, 7], "size": [], "description": "tweight th to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_gau_y_cur_weight_th, sf_gau_y_cur_weight_slope, [sf_gau_y_cur_ratio_limit, 1])", "constraints": "sf_gau_y_cur_weight_th <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_gau_y_cur_weight_slope": {"acc": [0, 4, 8], "size": [], "description": "tweight slope to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_gau_y_cur_weight_th, sf_gau_y_cur_weight_slope, [sf_gau_y_cur_ratio_limit, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_y_cur_ratio_limit": {"acc": [0, 1, 7], "size": [], "description": "ratio limit to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_gau_y_cur_weight_th, sf_gau_y_cur_weight_slope, [sf_gau_y_cur_ratio_limit, 1])", "constraints": "sf_gau_y_cur_ratio_limit <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_lap_y_ref_weight_th": {"acc": [0, 1, 7], "size": [], "description": "tweight th to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_lap_y_ref_weight_th, sf_lap_y_ref_weight_slope, [sf_lap_y_ref_ratio_limit, 1])", "constraints": "sf_lap_y_ref_weight_th <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_lap_y_ref_weight_slope": {"acc": [0, 4, 8], "size": [], "description": "tweight slope to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_lap_y_ref_weight_th, sf_lap_y_ref_weight_slope, [sf_lap_y_ref_ratio_limit, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_y_ref_ratio_limit": {"acc": [0, 1, 7], "size": [], "description": "tweight th to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_lap_y_ref_weight_th, sf_lap_y_ref_weight_slope, [sf_lap_y_ref_ratio_limit, 1])", "constraints": "sf_lap_y_ref_ratio_limit <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_lap_y_cur_weight_th": {"acc": [0, 1, 7], "size": [], "description": "tweight th to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_lap_y_cur_weight_th, sf_lap_y_cur_weight_slope, [sf_lap_y_cur_ratio_limit, 1])", "constraints": "sf_lap_y_ref_weight_th <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_lap_y_cur_weight_slope": {"acc": [0, 4, 8], "size": [], "description": "tweight slope to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_lap_y_cur_weight_th, sf_lap_y_cur_weight_slope, [sf_lap_y_cur_ratio_limit, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_y_cur_ratio_limit": {"acc": [0, 1, 7], "size": [], "description": "tweight th to sf_guide_y", "usage": " linearClamp(tweight_mask, sf_lap_y_cur_weight_th, sf_lap_y_cur_weight_slope, [sf_lap_y_cur_ratio_limit, 1])", "constraints": "sf_lap_y_cur_ratio_limit <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_gau_uv_ref_weight_th": {"acc": [0, 1, 7], "size": [], "description": "tweight th to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_gau_uv_ref_weight_th, sf_gau_uv_ref_weight_slope, [sf_gau_uv_ref_ratio_limit, 1])", "constraints": "sf_gau_uv_ref_weight_th <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_gau_uv_ref_weight_slope": {"acc": [0, 4, 8], "size": [], "description": "tweight slope to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_gau_uv_ref_weight_th, sf_gau_uv_ref_weight_slope, [sf_gau_uv_ref_ratio_limit, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_uv_ref_ratio_limit": {"acc": [0, 1, 7], "size": [], "description": "ratio limit to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_gau_uv_ref_weight_th, sf_gau_uv_ref_weight_slope, [sf_gau_uv_ref_ratio_limit, 1])", "constraints": "sf_gau_uv_ref_ratio_limit <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_gau_uv_cur_weight_th": {"acc": [0, 1, 7], "size": [], "description": "tweight th to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_gau_uv_cur_weight_th, sf_gau_uv_cur_weight_slope, [sf_gau_uv_cur_ratio_limit, 1])", "constraints": "sf_gau_uv_ref_weight_th <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_gau_uv_cur_weight_slope": {"acc": [0, 4, 8], "size": [], "description": "tweight slope to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_gau_uv_cur_weight_th, sf_gau_uv_cur_weight_slope, [sf_gau_uv_cur_ratio_limit, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_uv_cur_ratio_limit": {"acc": [0, 1, 7], "size": [], "description": "ratio limit to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_gau_uv_cur_weight_th, sf_gau_uv_cur_weight_slope, [sf_gau_uv_cur_ratio_limit, 1])", "constraints": "sf_gau_uv_cur_ratio_limit <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_lap_uv_ref_weight_th": {"acc": [0, 1, 7], "size": [], "description": "tweight th to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_lap_uv_ref_weight_th, sf_lap_uv_ref_weight_slope, [sf_lap_uv_ref_ratio_limit, 1])", "constraints": "sf_lap_uv_ref_weight_th <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_lap_uv_ref_weight_slope": {"acc": [0, 4, 8], "size": [], "description": "tweight slope to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_lap_uv_ref_weight_th, sf_lap_uv_ref_weight_slope, [sf_lap_uv_ref_ratio_limit, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_uv_ref_ratio_limit": {"acc": [0, 1, 7], "size": [], "description": "ratio limit to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_lap_uv_ref_weight_th, sf_lap_uv_ref_weight_slope, [sf_lap_uv_ref_ratio_limit, 1])", "constraints": "sf_lap_uv_ref_ratio_limit <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_lap_uv_cur_weight_th": {"acc": [0, 1, 7], "size": [], "description": "tweight th to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_lap_uv_cur_weight_th, sf_lap_uv_cur_weight_slope, [sf_lap_uv_cur_ratio_limit, 1])", "constraints": "sf_lap_uv_cur_weight_th <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_lap_uv_cur_weight_slope": {"acc": [0, 4, 8], "size": [], "description": "tweight slope to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_lap_uv_cur_weight_th, sf_lap_uv_cur_weight_slope, [sf_lap_uv_cur_ratio_limit, 1])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_uv_cur_ratio_limit": {"acc": [0, 1, 7], "size": [], "description": "ratio limit to sf_guide_uv", "usage": " linearClamp(tweight_mask, sf_lap_uv_cur_weight_th, sf_lap_uv_cur_weight_slope, [sf_lap_uv_cur_ratio_limit, 1])", "constraints": "sf_lap_uv_cur_ratio_limit <= 1.0", "type": "AX_U8", "partition": "-"}, "tweight_sf_en": {"acc": [0, 1], "size": [], "description": "tweight snr, 0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "tweight_str": {"acc": [0, 0, 7], "size": [], "description": "tweight str", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "tweight_static_lut": {"acc": [0, 1, 7], "size": [17], "description": "ref tweight to cur tweight in static areas", "usage": "", "constraints": "tweight_static_lut <= 1.0", "type": "AX_U8", "partition": "-"}, "m2s_motion_cur": {"acc": [0, 1, 7], "size": [], "description": "cur motion th of transition zone", "usage": "", "constraints": "m2s_motion_cur <= 1.0", "type": "AX_U8", "partition": "-"}, "m2s_tweight_ref": {"acc": [0, 1, 7], "size": [], "description": "ref tweight th of transition zone", "usage": "", "constraints": "m2s_tweight_ref <= 1.0", "type": "AX_U8", "partition": "-"}, "tweight_cur_w": {"acc": [0, 1, 7], "size": [], "description": "tweight tnr ratio", "usage": "", "constraints": "tweight_cur_w <= 1.0", "type": "AX_U8", "partition": "-"}, "tweight_pre_limit": {"acc": [0, 1, 7], "size": [], "description": "tweight tnr limit th for pre tweight", "usage": "", "constraints": "tweight_pre_limit <= 1.0", "type": "AX_U8", "partition": "-"}, "tweight_max_win_sel": {"acc": [0, 2], "size": [], "description": "tweight max filter win sel, 0:1_3, 1:3_5, 2:5_7, 3:7_9", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "tweight_max_ratio": {"acc": [0, 1, 7], "size": [], "description": "tweight max filter ratio", "usage": "", "constraints": "tweight_max_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "tweight_smooth_win_sel": {"acc": [0, 2], "size": [], "description": "tweight smooth filter win sel, 0:1_3, 1:3_5, 2:5_7, 3:7_9", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "tweight_smooth_ratio": {"acc": [0, 1, 7], "size": [], "description": "When there is a maximum filtering, it is necessary to perform secondary smoothing on the results", "usage": "", "constraints": "tweight_smooth_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "tweight_sf_sd_y_th": {"acc": [0, 8], "size": [], "description": "sd th for tweight max filter", "usage": "linearClamp(sd, tweight_sf_sd_y_th, tweight_sf_sd_y_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U8", "partition": "-"}, "tweight_sf_sd_y_slope": {"acc": [0, 1, 8], "size": [], "description": "sd slope for tweight max filter", "usage": "linearClamp(sd, tweight_sf_sd_y_th, tweight_sf_sd_y_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U16", "partition": "-"}, "tweight_sf_sad_y_th": {"acc": [0, 8], "size": [], "description": "sad th for tweight max filter", "usage": "linearClamp(sad, tweight_sf_sad_y_th, tweight_sf_sad_y_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U8", "partition": "-"}, "tweight_sf_sad_y_slope": {"acc": [0, 1, 8], "size": [], "description": "sad slope for tweight max filter", "usage": "linearClamp(sad, tweight_sf_sad_y_th, tweight_sf_sad_y_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U16", "partition": "-"}, "tweight_sf_sign_y_th": {"acc": [0, 7], "size": [], "description": "sign th for tweight max filter", "usage": "linearClamp(sign, tweight_sf_sign_y_th, tweight_sf_sign_y_slope, [0.0, 1.0])", "constraints": "tweight_sf_sign_y_th <= 81", "type": "AX_U8", "partition": "-"}, "tweight_sf_sign_y_slope": {"acc": [0, 1, 7], "size": [], "description": "sign slope for tweight max filter", "usage": "linearClamp(sign, tweight_sf_sign_y_th, tweight_sf_sign_y_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U8", "partition": "-"}, "tweight_map_th": {"acc": [0, 1, 7], "size": [], "description": "tweight mapping th for output_mask", "usage": "linearClamp(tweight_ds2, tweight_map_th, tweight_map_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U8", "partition": "-"}, "tweight_map_slope": {"acc": [0, 7, 7], "size": [], "description": "tweight mapping th for output_mask", "usage": "linearClamp(tweight_ds2, tweight_map_th, tweight_map_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_y_edge_th": {"acc": [0, 8], "size": [], "description": "sf_gau edge det th", "usage": "linearClamp(edge, sf_gau_y_edge_th, sf_gau_y_edge_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U8", "partition": "-"}, "sf_gau_y_edge_slope": {"acc": [0, 1, 8], "size": [], "description": "sf_gau edge det slope", "usage": "linearClamp(edge, sf_gau_y_edge_th, sf_gau_y_edge_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_y_detail_th": {"acc": [0, 8], "size": [], "description": "sf_gau detail det th", "usage": "linearClamp(detail, sf_gau_y_detail_th, sf_gau_y_detail_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U8", "partition": "-"}, "sf_gau_y_detail_slope": {"acc": [0, 1, 8], "size": [], "description": "sf_gau detail det slope", "usage": "linearClamp(detail, sf_gau_y_detail_th, sf_gau_y_detail_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_y_sp_weight": {"acc": [0, 1, 8], "size": [3, 15], "description": "sf_gau space weight, [0]:flat, [1]:detail, [2]:edge", "usage": "", "constraints": "[:,0] + 2 * ([:,6] + [:,9] + [:,12]) + 4 * ([:,1] + [:,2] + [:,3] + [:,5] + [:,7] + [:,8] + [:,10] + [:,11] + [:,13] + [:,14]) + 8 * ([:,4]) == 1.0", "type": "AX_U16", "partition": "-"}, "sf_gau_y_thres": {"acc": [0, 8, 2], "size": [3], "description": "sf_gau str th, [0]:flat, [1]:detail, [2]:edge", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_y_slope": {"acc": [0, 2, 8], "size": [3], "description": "sf_gau str slope, [0]:flat, [1]:detail, [2]:edge", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_y_thres_bias_lut": {"acc": [1, 6, 2], "size": [9], "description": "adjust th based on luma", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "sf_gau_y_slope_gain_lut": {"acc": [0, 3, 5], "size": [9], "description": "adjust slope based on luma", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sf_gau_y_sad_alpha": {"acc": [0, 1, 5], "size": [3], "description": "sad block alpha, [0]:flat, [1]:detail, [2]:edge", "usage": "sad_block * sf_gau_y_sad_alpha + sad_point * (1 - sf_gau_y_sad_alpha)", "constraints": "sf_gau_y_sad_alpha <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_gau_y_hdr_adjust_th": {"acc": [0, 8, 2], "size": [], "description": "sf gau hdr adjust th", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_y_ori_str": {"acc": [0, 8, 4], "size": [], "description": "blend ori str", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_y_edge_th": {"acc": [0, 8], "size": [], "description": "sf_lap edge det th", "usage": "linearClamp(edge, sf_lap_y_edge_th, sf_lap_y_edge_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U8", "partition": "-"}, "sf_lap_y_edge_slope": {"acc": [0, 1, 8], "size": [], "description": "sf_lap edge det slope", "usage": "linearClamp(edge, sf_lap_y_edge_th, sf_lap_y_edge_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_y_detail_th": {"acc": [0, 8], "size": [], "description": "sf_gau detail det th", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sf_lap_y_detail_slope": {"acc": [0, 1, 8], "size": [], "description": "sf_lap detail det slope", "usage": "linearClamp(detail, [sf_lap_y_detail_th[0], sf_lap_y_detail_th[1]], sf_lap_y_detail_slope, [0.0, 1.0])", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_y_sp_weight": {"acc": [0, 1, 8], "size": [3, 6], "description": "sf_lap space weight, [0]:flat, [1]:detail, [2]:edge", "usage": "", "constraints": "[:,0] + 4 * ([:,1] + [:,2] + [:,3] + [:,5]) + 8 * ([:,4]) == 1.0", "type": "AX_U16", "partition": "-"}, "sf_lap_y_thres": {"acc": [0, 8, 2], "size": [3], "description": "sf_lap str th, [0]:flat, [1]:detail, [2]:edge", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_y_slope": {"acc": [0, 2, 8], "size": [3], "description": "sf_lap str slope, [0]:flat, [1]:detail, [2]:edge", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_y_thres_bias_lut": {"acc": [1, 6, 2], "size": [9], "description": "adjust th based on luma", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "sf_lap_y_slope_gain_lut": {"acc": [0, 3, 5], "size": [9], "description": "adjust slope based on luma", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sf_lap_y_sad_alpha": {"acc": [0, 1, 5], "size": [3], "description": "sad block alpha, [0]:flat, [1]:detail, [2]:edge", "usage": "sad_block * sf_lap_y_sad_alpha + sad_point * (1 - sf_lap_y_sad_alpha)", "constraints": "sf_lap_y_sad_alpha <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_lap_y_gauss_weight": {"acc": [0, 1, 8], "size": [10], "description": "sf_lap gauss weight", "usage": "", "constraints": "[0] + 4 * ([1] + [2] + [3] + [5] + [6] + [9]) + 8 * ([4] + [7] + [8]) == 1.0", "type": "AX_U16", "partition": "-"}, "sf_lap_y_gauss_ratio": {"acc": [0, 1, 5], "size": [3], "description": "fg blend gauss ratio, [0]:flat, [1]:detail, [2]:edge", "usage": "gauss * sf_lap_y_gauss_ratio + output * (1 - sf_lap_y_gauss_ratio)", "constraints": "sf_lap_y_gauss_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_lap_y_hdr_adjust_th": {"acc": [0, 8, 2], "size": [], "description": "sf lap hdr adjust th", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_y_ori_str": {"acc": [0, 8, 4], "size": [], "description": "blend ori str", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_uv_th": {"acc": [0, 7, 2], "size": [], "description": "sf_gau_uv str", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_uv_slope": {"acc": [0, 2, 7], "size": [], "description": "sf_gau_uv slope", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_uv_limit": {"acc": [0, 1, 7], "size": [], "description": "sf_gau_uv limit", "usage": "", "constraints": "sf_gau_uv_limit <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_gau_uv_thre_pre_lut": {"acc": [0, 8, 2], "size": [9], "description": "sf_gau_uv th0", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_uv_thre_lut": {"acc": [0, 8, 2], "size": [9], "description": "sf_gau_uv th1", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_uv_prot_thre_lut": {"acc": [0, 8, 2], "size": [9], "description": "sf_gau_uv sat protect th", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_uv_prot_slope_lut": {"acc": [0, 2, 8], "size": [9], "description": "sf_gau_uv sat protect slope", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_uv_hdr_adjust_th": {"acc": [0, 8, 2], "size": [], "description": "sf gau uv hdr adjust th", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_gau_uv_ori_ratio": {"acc": [0, 1, 5], "size": [], "description": "blend ori ratio", "usage": "input * uv_gau_ori_ratio + output * (1 - uv_gau_ori_ratio)", "constraints": "uv_gau_ori_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "sf_lap_uv_thre": {"acc": [0, 8, 2], "size": [], "description": "sf_lap_uv th", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_uv_slope": {"acc": [0, 2, 8], "size": [], "description": "sf_lap_uv slope", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sf_lap_uv_hdr_adjust_gain": {"acc": [0, 1, 5], "size": [], "description": "sf gau uv hdr adjust gain", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sf_lap_uv_ori_ratio": {"acc": [0, 1, 5], "size": [], "description": "blend ori ratio", "usage": "input * uv_lap_ori_ratio + output * (1 - uv_lap_ori_ratio)", "constraints": "uv_lap_ori_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "m2s_var_th": {"acc": [0, 7], "size": [], "description": "motion to static detail recovery var th", "usage": "linearClamp(var, m2s_var_th, m2s_var_slope, [0.0, m2s_blend_ratio])", "constraints": "", "type": "AX_U8", "partition": "-"}, "m2s_var_slope": {"acc": [0, 1, 11], "size": [2], "description": "motion to static detail recovery var slope, [0]: lf, [1]: hf", "usage": "linearClamp(var, m2s_var_th, m2s_var_slope, [0.0, m2s_blend_ratio])", "constraints": "", "type": "AX_U16", "partition": "-"}, "m2s_blend_ratio": {"acc": [0, 1, 7], "size": [2], "description": "motion to static detail recovery blend ratio, [0]: lf, [1]: hf", "usage": "linearClamp(var, m2s_var_th, m2s_var_slope, [0.0, m2s_blend_ratio])", "constraints": "m2s_blend_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "gf_uv_diff_th": {"acc": [0, 8, 0], "size": [], "description": "diff between guided filter results and normal UVNR output", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "gf_uv_sat_th": {"acc": [0, 7, 0], "size": [], "description": "saturation th of UVNR output", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "gf_uv_sat_slope": {"acc": [0, 1, 8], "size": [], "description": "saturation slope of UVNR output", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "gf_uv_sat_ratio": {"acc": [0, 1, 7], "size": [2], "description": "blend ratio according to saturation", "usage": "", "constraints": "gf_sat_ratio[1] >= gf_sat_ratio[0], gf_sat_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "gf_uv_blend_ratio_th": {"acc": [0, 1, 7], "size": [], "description": "blend ratio according to ratio_uv", "usage": "", "constraints": "gf_uv_blend_ratio_th <= 1.0", "type": "AX_U8", "partition": "-"}, "gf_uv_blend_ratio_slope": {"acc": [1, 7, 7], "size": [], "description": "ratio slope of UVNR output", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "gf_uv_blend_ratio": {"acc": [0, 1, 7], "size": [2], "description": "gf blend ratio of UVNR output", "usage": "", "constraints": "gf_uv_blend_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "norm_ratio_8x": {"acc": [0, 1, 12], "size": [3], "description": "norm ratio in 16x down sample, [0]:right, [1]:down, [2]:lower right corner", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "ptn_roi_t_b_l_r": {"acc": [0, 14], "size": [4], "description": "roi zone", "usage": "", "constraints": "0<=roi_t_b_l_r<=(pic.h,pic.w); roi_t_b_l_r % 2 == 0; roi_t_b_l_r[0] < roi_t_b_l_r[1]; roi_t_b_l_r[2] < roi_t_b_l_r[3]", "type": "AX_U16", "partition": "support"}, "pic_h_w": {"acc": [0, 14], "size": [2], "description": "pic_h_w", "usage": "", "constraints": "pic_h_w[0] % 4 == 0; pic_h_w[1] % 4 == 0", "type": "AX_U16", "partition": "-"}, "ptn_h_w": {"acc": [0, 14], "size": [2], "description": "partition_h_w", "usage": "", "constraints": "ptn_h_w <= pic_h_w; ptn_h_w[0] % 4 == 0; ptn_h_w[1] % 4 == 0", "type": "AX_U16", "partition": "support"}, "ptn_offset_h_w": {"acc": [0, 14], "size": [2], "description": "start offset of the partition", "usage": "", "constraints": "ptn_offset_h_w[0] % 2 == 0; ptn_offset_h_w[0] + ptn_h_w[0] <= pic_h_w[0]; ptn_offset_h_w[1] % 2 == 0; ptn_offset_h_w[1] + ptn_h_w[1] <= pic_h_w[1]", "type": "AX_U16", "partition": "support"}, "dither_enable": {"acc": [0, 1], "size": [], "description": "dither enable", "usage": "0: disable, 1: enable", "constraints": "", "type": "AX_U8", "partition": "-"}, "dither_seed_enable": {"acc": [0, 1], "size": [], "description": "dither seed enable", "usage": "0: disable, 1: enable", "constraints": "", "type": "AX_U8", "partition": "-"}, "dither_pmask_uv": {"acc": [0, 16], "size": [2], "description": "dither pmask uv", "usage": "dither pmask, use fixed value", "constraints": "", "type": "AX_U16", "partition": "-"}, "dither_seed_uv": {"acc": [0, 16], "size": [2], "description": "dither seed", "usage": "dither seed, use fixed value", "constraints": "", "type": "AX_U16", "partition": "-"}, "tf_converg_offset": {"acc": [0, 0, 8], "size": [2], "description": "tf converg offset, [0]:cur, [1]:ref", "usage": "tf merge fixed-point progress", "constraints": "[0, 0.25]", "type": "AX_U8", "partition": "-"}}, "ifa": {"enable": {"acc": [0, 1], "size": [], "description": "enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "mode": {"acc": [0, 4], "size": [], "description": "Select ifa operator to run", "usage": "0 - guided filter", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "sub_mode": {"acc": [0, 4], "size": [], "description": "Select sub_mode inside ifa operator", "usage": "If selected ifa operator has serval sub-mode, use this register to select it.", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "output_hor_crop_enable": {"acc": [0, 1], "size": [], "description": "output horizontal crop enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "output_hor_crop_st": {"acc": [0, 12], "size": [], "description": "output picture's horizontal crop start coordinates", "usage": "out_hor_crop_st[n] for pics[n]", "constraints": "N/A", "type": "AX_U16", "partition": "support"}, "output_hor_crop_width": {"acc": [0, 12], "size": [], "description": "output picture's horizontal crop width", "usage": "out_hor_crop_width[n] for pics[n]", "constraints": "N/A", "type": "AX_U16", "partition": "support"}, "guided_enable": {"acc": [0, 1], "size": [], "description": "guided filter enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "guided_dehaze_dc_adj_enable": {"acc": [0, 1], "size": [], "description": "When used for dehaze, this bit controls whether adjust dark channel using coeff k before calculating guided filter..", "usage": "If enabled, dc = clip(1 - dc * k, 0, 1.0)", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "guided_dehaze_dc_k": {"acc": [1, 4, 12], "size": [], "description": "k value in dehaze logic", "usage": "clip(b - dc * k, 0, 1.0)", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "guided_dehaze_dc_b": {"acc": [0, 1, 12], "size": [], "description": "b value in dehaze logic", "usage": "clip(b - dc * k, 0, 1.0)", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "guided_win_r": {"acc": [0, 3], "size": [], "description": "2 * guided_win_r + 1 == window size", "usage": "Example: 5x5 --> 2 * 2 + 1", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "guided_epsilon": {"acc": [0, 16], "size": [], "description": "It's u16 form for various acc epsilon; For rltm, it's u0.16; For dehaze, it's u12.4; For UVNR, it's u0.16; The guided_epsilon value in guided filter formula.", "usage": "coeff_a = cov_ip / (var_i + guided_epsilon * guided_epsilon)", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "blur_enable": {"acc": [0, 1], "size": [], "description": "guided filter enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "blur_weight": {"acc": [1, 5, 10], "size": [3, 3], "description": "3 x 3 filter weight; Symmetric parameter; Effective 5 x 5 filter weight.", "usage": "Use upper-left corner 3x3 parameter to init internal 5x5 filter weight symmetrically.", "constraibnts": "N/A", "type": "AX_S16", "partition": "-"}}}, "partition_configs": ["yuv_3dnr.ptn_roi_t_b_l_r", "yuv_3dnr.ptn_h_w", "yuv_3dnr.ptn_offset_h_w", "ifa.output_hor_crop_st", "ifa.output_hor_crop_width"], "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "YUV3DNR is 0x2225", "type": "AX_U16"}}, "params": {"enable": {"acc": [0, 1], "auto": 0, "comment": "sw enable", "range": [0, 1], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.tf_y_en", "yuv_3dnr.tf_uv_en", "yuv_3dnr.sf_gau_y_en", "yuv_3dnr.sf_lap_y_en", "yuv_3dnr.sf_gau_uv_en", "yuv_3dnr.sf_lap_uv_en", "yuv_3dnr.gf_uv_en", "yuv_3dnr.debug_gain", "yuv_3dnr.debug_mode"], "dependency": "user"}, "tf_y_en": {"acc": [0, 1], "auto": 0, "comment": "y tf enable", "range": [0, 1], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.tf_y_en"], "dependency": "user"}, "tf_uv_en": {"acc": [0, 1], "auto": 0, "comment": "uv tf enable", "range": [0, 1], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.tf_uv_en"], "dependency": "user"}, "sf_gau_y_en": {"acc": [0, 1], "auto": 0, "comment": "y gau sf enable", "range": [0, 1], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_y_en"], "dependency": "user"}, "sf_lap_y_en": {"acc": [0, 1], "auto": 0, "comment": "y lap sf enable", "range": [0, 1], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_y_en"], "dependency": "user"}, "sf_gau_uv_en": {"acc": [0, 1], "auto": 0, "comment": "uv gau sf enable", "range": [0, 1], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_uv_en"], "dependency": "user"}, "sf_lap_uv_en": {"acc": [0, 1], "auto": 0, "comment": "uv lap sf enable", "range": [0, 1], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_uv_en"], "dependency": "user"}, "gf_uv_en": {"acc": [0, 1], "auto": 0, "comment": "uv gf enable", "range": [0, 1], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.gf_uv_en"], "dependency": "user"}, "tweight_sf_en": {"acc": [0, 1], "auto": 0, "comment": "tweight sf enable", "range": [0, 1], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.tweight_sf_en"], "dependency": "user"}, "ext_mask_en": {"acc": [0, 1], "auto": 0, "comment": "ext mask enable", "range": [0, 1], "default": 0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.ext_mask_en"], "dependency": "user"}, "debug_mode": {"acc": [0, 4], "auto": 0, "comment": "Debug mode, 0:normal, 1:y motion, 2:uv motion, 3:cur tweight, 4: ref y lf mask, 5:ref y hf mask, 6:motion to static mask, 7:cur y lf edge, , 8: cur y lf detail, 9: cur y lf sf result, 10: cur y hf edge, 11: cur y hf detail, 12: cur y hf sf result, 13:ext mask", "default": 1, "hidden": 1, "range": [0, 13], "size": [], "target_conf": ["yuv_3dnr.debug_mode"], "dependency": "common"}, "debug_gain": {"acc": [0, 3], "auto": 0, "comment": "Shift the debug output left by the number of bits", "default": 1, "hidden": 1, "range": [0, 7], "size": [], "target_conf": ["yuv_3dnr.debug_gain"], "dependency": "common"}, "init_ref": {"acc": [0, 1], "auto": 0, "comment": "is_first_frame", "range": [0, 1], "default": 0, "hidden": 1, "size": [], "target_conf": ["yuv_3dnr.init_ref"], "dependency": "common"}, "init_tweight": {"acc": [0, 1], "auto": 0, "comment": "is_first_frame", "range": [0, 1], "default": 0, "hidden": 1, "size": [], "target_conf": ["yuv_3dnr.init_tweight"], "dependency": "common"}, "init_coef_ab": {"acc": [0, 1], "auto": 0, "comment": "is_first_frame", "range": [0, 1], "default": 0, "hidden": 1, "size": [], "target_conf": ["yuv_3dnr.init_coef_ab"], "dependency": "common"}, "ext_mask_mode": {"acc": [0, 1], "auto": 0, "comment": "ext mask mode, 0:ai, 1:hdr", "range": [0, 1], "default": 0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.ext_mask_mode"], "dependency": "user"}, "ext_mask_thre": {"acc": [0, 1, 7], "auto": 1, "comment": "The starting point for remapping ext mask, below which the probability will be mapped to 0, corresponding to x_th", "default": 0.0, "hidden": 0, "range": [0.0, 1.0], "size": [], "target_conf": ["yuv_3dnr.ext_mask_thre"], "dependency": "user"}, "ext_mask_slope": {"acc": [0, 8, 8], "auto": 1, "comment": "The slope of ext mask remapping, the larger the value, the easier it is to map the mask to 1.0, corresponding slope", "default": 1.0, "hidden": 0, "range": [0.0, 255.99609375], "size": [], "target_conf": ["yuv_3dnr.ext_mask_slope"], "dependency": "user"}, "ext_mask_ratio": {"acc": [0, 1, 7], "auto": 1, "comment": "Only effective in AI mode. The reference level of AI mask. The larger the size, the more likely it is to use an AI mask as the motion mask", "default": 1.0, "hidden": 0, "range": [0.0, 1.0], "size": [], "target_conf": ["yuv_3dnr.ext_mask_ratio"], "dependency": "user"}, "motion_small_sel_ratio": {"acc": [0, 9], "auto": 1, "comment": "Applied to the feature of motion judgement, the larger the value, the larger the detection window used for motion judgement, which improves the noise resistance and smoothness of motion detection while losing the ability to detect texture details.", "default": 128, "hidden": 0, "range": [0, 256], "size": [], "target_conf": ["yuv_3dnr.motion_small_win_sel", "yuv_3dnr.motion_small_sel_ratio"], "dependency": "user"}, "motion_large_sel_ratio": {"acc": [0, 9], "auto": 1, "comment": "The greater the values of tweight_sf_degional_y and tweight_sf_densitive_y features applied to time-domain fusion coefficient filtering, the larger the detection window used for motion judgement. This improves the noise resistance and smoothness of motion detection while losing texture detail detection ability, and is generally slightly larger than the motion_small_sel_ratio.", "default": 128, "hidden": 0, "range": [0, 256], "size": [], "target_conf": ["yuv_3dnr.motion_large_win_sel", "yuv_3dnr.motion_large_sel_ratio"], "dependency": "user"}, "motion_feature_gauss_str": {"acc": [0, 1, 7], "auto": 1, "comment": "The pre smoothing degree of the motion judgement features, the larger the value, the smoother the motion judgement features.", "default": 1.0, "hidden": 0, "range": [0.0, 1.0], "size": [], "target_conf": ["yuv_3dnr.motion_feature_gauss_str"], "dependency": "user"}, "motion_sad_sd_th": {"acc": [0, 8], "auto": 1, "comment": "Representing the difference between two types of motion judgement features, used to map the fusion ratio of texture sensitive motion judgement features, corresponding to x_th. The larger the value, the less texture sensitive motion judgement features are used, and the probability of misjudgment caused by noise decreases. However, it may lead to some areas being misjudged as stationary.", "range": [0, 255], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_sad_sd_th"], "dependency": "user"}, "motion_sad_weight_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "Based on the fusion mapping of texture sensitive motion judgement features using motion_densitive-diff_th, the change speed corresponds to the slope. The larger the value, the faster the texture sensitive motion judgement features are used, and the probability of misjudgment caused by noise increases, resulting in more accurate motion judgement.", "range": [0.0, 1.99609375], "default": 0.1015625, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_sad_weight_slope"], "dependency": "user"}, "motion_sad_weight_th": {"acc": [0, 1, 7], "auto": 1, "comment": "The upper and lower limits of the fusion ratio of texture sensitive motion judgement features correspond to y_th0 and y_th1. The larger the value, the more texture sensitive motion judgement features are used, and the probability of misjudgment caused by noise increases, resulting in more accurate motion judgement", "default": [1.0, 1.0], "hidden": 0, "range": [0.0, 1.0], "size": [2], "target_conf": ["yuv_3dnr.motion_sad_weight_th"], "dependency": "user"}, "motion_highlight_diff_th": {"acc": [0, 8, 2], "auto": 1, "comment": "For highlight areas, the frame difference judgment threshold for motion sharpening is used. If the frame difference is higher than this threshold, sharpening will occur, and the larger the value, the less effective motion sharpening will be.", "range": [0.0, 255.75], "default": 1.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_highlight_diff_th"], "dependency": "user"}, "motion_highlight_luma_th": {"acc": [0, 8], "auto": 1, "comment": "For highlight areas, the brightness judgment threshold for motion sharpening is used. Only when the brightness is higher than this threshold will it sharpen. The larger the value, the less effective the motion sharpening will be, corresponding to x_th.", "range": [0, 255], "default": 1.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_highlight_luma_th"], "dependency": "user"}, "motion_highlight_luma_slope": {"acc": [0, 2, 8], "auto": 1, "comment": "For the highlight area, the brightness judgment gain using motion sharpening is used. The larger the value, the faster the transition from motion sharpening in the dark area to brightness, corresponding to the slope.", "range": [0.0, 3.99609375], "default": 1.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_highlight_luma_slope"], "dependency": "user"}, "motion_highlight_var_th": {"acc": [0, 7], "auto": 1, "comment": "For highlight areas, the variance judgment threshold for motion sharpening is used. Only when the variance is higher than this threshold will the sharpening be effective. The larger the value, the less effective the motion sharpening will be, corresponding to x_th.", "range": [0, 127], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_highlight_var_th"], "dependency": "user"}, "motion_highlight_var_slope": {"acc": [0, 2, 7], "auto": 1, "comment": "For the highlight area, the variance judgment gain of motion sharpening is used. The larger the value, the faster the transition from small variance to large variance motion sharpening, corresponding to the slope", "range": [0.0, 3.9921875], "default": 1.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_highlight_var_slope"], "dependency": "user"}, "motion_sharpen_str": {"acc": [0, 3, 5], "auto": 1, "comment": "For highlight areas, motion sharpening intensity is used. The higher the value, the greater the intensity, making it easier to identify as motion.", "range": [0.0, 7.96875], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_sharpen_str"], "dependency": "user"}, "motion_y_red_correct_str": {"acc": [0, 3, 5], "auto": 1, "comment": "The intensity of Y channel motion correction in the red area, the larger the value, the easier it is to determine as stationary. It does not take effect when the motion_small_sel_ratio is at its maximum.", "range": [0.0, 7.96875], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_y_red_correct_str"], "dependency": "user"}, "motion_uv_red_correct_str": {"acc": [0, 3, 5], "auto": 1, "comment": "The intensity of UV channel motion correction in the red area, the larger the value, the easier it is to determine as stationary. It does not take effect when the motion_stmall_del_ratio is at its maximum.", "range": [0.0, 7.96875], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_uv_red_correct_str"], "dependency": "user"}, "motion_y_blue_correct_str": {"acc": [0, 3, 5], "auto": 1, "comment": "The intensity of Y channel motion correction in the blue area, the larger the value, the easier it is to determine as stationary. It does not take effect when the motion_stmall_del_ratio is at its maximum", "range": [0.0, 7.96875], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_y_blue_correct_str"], "dependency": "user"}, "motion_uv_blue_correct_str": {"acc": [0, 3, 5], "auto": 1, "comment": "The intensity of UV channel motion correction in the blue area, the larger the value, the easier it is to determine as stationary. It does not take effect when the motion_stmall_del_ratio is at its maximum.", "range": [0.0, 7.96875], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_uv_blue_correct_str"], "dependency": "user"}, "motion_dec_y_th": {"acc": [0, 8], "auto": 1, "comment": "Y motion judgement threshold, corresponding to x_th. The higher the value, the more the whole frame tends to be static.", "range": [0, 255], "default": 0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_dec_y_th"], "dependency": "user"}, "motion_dec_y_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "Y determines the transition speed based on movement and corresponds to the slope. The higher the value, the more inclined the whole frame is towards motion.", "range": [0.0, 1.99609375], "default": 0.125, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_dec_y_slope"], "dependency": "user"}, "motion_m2s_dec_y_th": {"acc": [0, 8], "auto": 1, "comment": "From dynamic to static region Y, the threshold for motion judgement corresponds to x_th. The higher the value, the more the whole frame tends to be static.", "range": [0, 255], "default": 0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_m2s_dec_y_th"], "dependency": "user"}, "motion_m2s_dec_y_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "From dynamic to static zone Y, determine the transition speed and corresponding slope. The higher the value, the more inclined the whole frame is towards motion.", "range": [0.0, 1.99609375], "default": 0.125, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_m2s_dec_y_slope"], "dependency": "user"}, "motion_hdr_adjust_y_th": {"acc": [0, 8], "auto": 1, "comment": "When applied to Y, the larger the value, the easier it is to correct the short frame area to be still.", "range": [0, 255], "default": 0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_hdr_adjust_y_th"], "dependency": "user"}, "motion_dec_uv_th": {"acc": [0, 8], "auto": 1, "comment": "UV motion judgement threshold, corresponding to x_th. The higher the value, the more the whole frame tends to be static.", "range": [0, 255], "default": 0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_dec_uv_th"], "dependency": "user"}, "motion_dec_uv_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "UV motion judgement of transition speed, corresponding to slope. The higher the value, the more inclined the whole frame is towards motion.", "range": [0.0, 1.99609375], "default": 0.125, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_dec_uv_slope"], "dependency": "user"}, "motion_adj_uv_th": {"acc": [0, 8], "auto": 1, "comment": "The UV motion judgement correction threshold will make the UV more inclined to use the current frame, corresponding to x_th, based on normal motion judgement. The higher the value, the more the overall UV tends to be static.", "range": [0, 255], "default": 0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_adj_uv_th"], "dependency": "user"}, "motion_adj_uv_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "UV motion judgement correction excessive speed will make UV more inclined to use the current frame and corresponding slope based on normal motion judgement. The higher the value, the more the overall UV tends to move.", "range": [0.0, 1.99609375], "default": 0.125, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_adj_uv_slope"], "dependency": "user"}, "motion_hdr_adjust_uv_th": {"acc": [0, 8], "auto": 1, "comment": "When applied to UV, the larger the value, the easier it is to correct the short frame area to be still. ", "range": [0, 255], "default": 0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.motion_hdr_adjust_uv_th"], "dependency": "user"}, "motion_noise_y_lut": {"acc": [0, 8, 2], "auto": 1, "comment": "Noise correction motion judgement Y channel feature lookup table, with brightness on the x-axis and subtraction method. The larger the value, the stronger the correction, and the easier it is to mistake real motion for stillness.", "range": [0.0, 255.75], "default": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.motion_noise_y_lut"], "dependency": "user"}, "motion_hvs_y_lut": {"acc": [0, 3, 5], "auto": 1, "comment": "Noise correction motion judgement Y channel feature lookup table, with brightness on the x-axis and multiplication method. The smaller the value, the stronger the correction, and the easier it is to mistake real motion for stillness.", "range": [0.0, 7.96875], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.motion_hvs_y_lut"], "dependency": "user"}, "motion_noise_uv_lut": {"acc": [0, 8, 2], "auto": 1, "comment": "Noise correction motion judgement UV channel feature lookup table, with brightness on the x-axis and subtraction method. The larger the value, the stronger the correction, and the easier it is to mistake real motion for stillness.", "range": [0.0, 255.75], "default": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.motion_noise_uv_lut"], "dependency": "user"}, "motion_hvs_uv_lut": {"acc": [0, 3, 5], "auto": 1, "comment": "Noise correction motion judgement UV channel feature lookup table, with brightness on the x-axis and multiplication method. The smaller the value, the stronger the correction, and the easier it is to mistake real motion for stillness.", "range": [0.0, 7.96875], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.motion_hvs_uv_lut"], "dependency": "user"}, "tweight_cur_w": {"acc": [0, 1, 7], "auto": 1, "comment": "The fusion ratio of current motion judgement and historical motion information, the larger the value, the smaller the historical motion information reference, the faster the convergence speed of dynamic and static, and the higher the probability of misjudging as static.", "default": 1.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.tweight_cur_w"], "dependency": "user"}, "tweight_pre_limit": {"acc": [0, 1, 7], "auto": 1, "comment": "The reference threshold for historical motion information, significant motion information greater than this value will be referenced and integrated into the current motion judgment. The larger the value, the smaller the historical motion information reference, and the faster the convergence speed of motion and stillness, increasing the probability of misjudging as stationary.", "default": 1.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.tweight_pre_limit"], "dependency": "user"}, "tweight_max_ratio": {"acc": [0, 10], "auto": 1, "comment": "The maximum filtering strength of the time-domain fusion coefficient is greater, and the larger the value, the greater the filtering strength. The larger the proportion of using the current frame, the higher the probability of raindrop noise appearing.", "default": 128, "hidden": 0, "size": [], "range": [0, 512], "target_conf": ["yuv_3dnr.tweight_max_win_sel", "yuv_3dnr.tweight_max_ratio"], "dependency": "user"}, "tweight_smooth_ratio": {"acc": [0, 10], "auto": 1, "comment": "The average filtering strength of the time-domain fusion coefficient is higher, and the larger the value, the greater the average filtering strength. The proportion of using the current frame is smaller, and the probability of raindrop noise appearing is reduced. ", "default": 128, "hidden": 0, "size": [], "range": [0, 512], "target_conf": ["yuv_3dnr.tweight_smooth_win_sel", "yuv_3dnr.tweight_smooth_ratio"], "dependency": "user"}, "tweight_sf_sd_y_th": {"acc": [0, 8], "auto": 1, "comment": "Guiding the selection of time-domain fusion coefficients based on regional motion judgement features, the larger the value, the more biased the global result is towards mean filtering, corresponding to x_th", "range": [0, 255], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.tweight_sf_sd_y_th"], "dependency": "user"}, "tweight_sf_sd_y_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "Guiding the selection of time-domain fusion coefficients based on regional motion judgement features, the larger the value, the steeper the transition, corresponding to the slope", "range": [0.0, 1.99609375], "default": 0.5, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.tweight_sf_sd_y_slope"], "dependency": "user"}, "tweight_sf_sad_y_th": {"acc": [0, 8], "auto": 1, "comment": "By using detail sensitive motion judgement features to guide the selection of time-domain fusion coefficients, the larger the value, the more the global result tends towards mean filtering, corresponding to x_th", "range": [0, 255], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.tweight_sf_sad_y_th"], "dependency": "user"}, "tweight_sf_sad_y_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "Guiding the selection of time-domain fusion coefficients based on detail sensitive motion judgement features, the larger the value, the steeper the transition, corresponding to the slope", "range": [0.0, 1.99609375], "default": 0.5, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.tweight_sf_sad_y_slope"], "dependency": "user"}, "tweight_sf_sign_y_th": {"acc": [0, 7], "auto": 1, "comment": "Guiding the selection of time-domain fusion coefficients based on pixel relative relationships, the larger the value, the more biased the global result is towards mean filtering, corresponding to x_th", "default": 72, "hidden": 0, "size": [], "range": [0, 81], "target_conf": ["yuv_3dnr.tweight_sf_sign_y_th"], "dependency": "user"}, "tweight_sf_sign_y_slope": {"acc": [0, 1, 7], "auto": 1, "comment": "Guiding the selection of time-domain fusion coefficients through pixel relative relationships, the larger the value, the steeper the transition, corresponding to the slope", "range": [0.0, 1.9921875], "default": 0.5, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.tweight_sf_sign_y_slope"], "dependency": "user"}, "tweight_str": {"acc": [0, 0, 7], "auto": 1, "comment": "The stronger the time-domain filtering strength, the more biased it is towards using the current frame result, and the greater the Y noise in the stationary region.", "range": [0.0, 0.9921875], "default": 0.0703125, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.tweight_str"], "dependency": "user"}, "ratio_uv_adjust_th": {"acc": [0, 1, 7], "auto": 1, "comment": "Based on the modified UV motion judgement, the threshold for mapping the UV correction temporal fusion coefficient is determined. The smaller the value, the more inclined it is to use the current frame for global UV, corresponding to x_th", "default": 0.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.ratio_uv_adjust_th"], "dependency": "user"}, "ratio_uv_adjust_slope": {"acc": [0, 4, 8], "auto": 1, "comment": "According to the modified UV motion judgement, the transition speed of the mapping UV correction temporal fusion coefficient is determined. The larger the value, the more inclined it is to use the current frame for the global UV, corresponding to the slope", "range": [0.0, 15.99609375], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.ratio_uv_adjust_slope"], "dependency": "user"}, "ratio_uv_adjust": {"acc": [0, 1, 7], "auto": 1, "comment": "According to the modified UV motion judgement, the correction strength of the mapping UV correction temporal fusion coefficient is determined. The larger the value, the more inclined it is to use the current frame for the global UV, corresponding to y_th1", "default": 0.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.ratio_uv_adjust"], "dependency": "user"}, "ratio_uv_limit": {"acc": [0, 1, 7], "auto": 1, "comment": "The stronger the UV time-domain denoising intensity, the more the current frame is used, and the greater the UV noise in the stationary area.", "default": 0.1015625, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.ratio_uv_limit"], "dependency": "user"}, "m2s_motion_cur": {"acc": [0, 1, 7], "auto": 1, "comment": "In the determination of moving to stationary areas, the current frame needs to be stationary and the historical frames need to be moving. This corresponds to the threshold for the current frame being stationary, and the larger the value, the more likely it is to be ineffective.", "default": 0.375, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.m2s_motion_cur"], "dependency": "user"}, "m2s_tweight_ref": {"acc": [0, 1, 7], "auto": 1, "comment": "In the determination of moving to stationary areas, the current frame needs to be stationary, and the historical frames need to be motion. Here, the corresponding historical frames are the threshold for motion, and the smaller the value, the more likely it is not to take effect.", "default": 0.375, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.m2s_tweight_ref"], "dependency": "user"}, "m2s_var_th": {"acc": [0, 7], "auto": 1, "comment": "The variance threshold of the texture part in the transition area from motion to stillness is located, and the larger the value, the fewer textures are located, corresponding to x_th", "range": [0, 127], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.m2s_var_th"], "dependency": "user"}, "m2s_var_slope": {"acc": [0, 1, 11], "auto": 1, "comment": "Locate the texture overlap speed of the texture part in the transition zone from motion to stillness, with smaller values indicating less overlap, corresponding to the slope, [0]: gau, [1]: lap", "range": [0.0, 1.99951171875], "default": 0.0, "hidden": 0, "size": [2], "target_conf": ["yuv_3dnr.m2s_var_slope"], "dependency": "user"}, "m2s_blend_ratio": {"acc": [0, 1, 7], "auto": 1, "comment": "Locate the texture overlap ratio of the texture part in the transition area from motion to stillness, with smaller values indicating less overlap, corresponding to y_th1, [0]: gau, [1]: lap", "default": [0.0, 0.0], "hidden": 0, "size": [2], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.m2s_blend_ratio"], "dependency": "user"}, "tweight_static_lut": {"acc": [0, 1, 7], "auto": 1, "comment": "Time domain fusion speed curve, the horizontal axis represents the degree to which the history has been stationary, the smaller the value, the longer the stationary time, the vertical axis represents the current mapped time domain fusion coefficient, and the smaller the value, the more reference frames are used.", "default": [0.05, 0.06, 0.07, 0.09, 0.12, 0.16, 0.2, 0.23, 0.26, 0.29, 0.32, 0.34, 0.36, 0.38, 0.41, 0.45, 0.5], "hidden": 0, "size": [17], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.tweight_static_lut"], "dependency": "user"}, "sf_gau_y_ref_weight_th": {"acc": [0, 1, 7], "auto": 1, "comment": "When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th", "default": 0.1015625, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_gau_y_ref_weight_th"], "dependency": "user"}, "sf_gau_y_ref_weight_slope": {"acc": [0, 4, 8], "auto": 1, "comment": "When providing the next reference frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope", "range": [0.0, 15.99609375], "default": 2.34375, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_y_ref_weight_slope"], "dependency": "user"}, "sf_gau_y_ref_ratio_limit": {"acc": [0, 1, 7], "auto": 1, "comment": "When providing the next reference frame, the larger the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area and the smaller the noise.", "default": 0.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_gau_y_ref_ratio_limit"], "dependency": "user"}, "sf_gau_y_cur_weight_th": {"acc": [0, 1, 7], "auto": 1, "comment": "When provided to the output frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th", "default": 0.1015625, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_gau_y_cur_weight_th"], "dependency": "user"}, "sf_gau_y_cur_weight_slope": {"acc": [0, 4, 8], "auto": 1, "comment": "When providing the output frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope", "range": [0.0, 15.99609375], "default": 2.34375, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_y_cur_weight_slope"], "dependency": "user"}, "sf_gau_y_cur_ratio_limit": {"acc": [0, 1, 7], "auto": 1, "comment": "When providing the output frame, the larger the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area and the smaller the noise.", "default": 1.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_gau_y_cur_ratio_limit"], "dependency": "user"}, "sf_gau_y_edge_th": {"acc": [0, 8], "auto": 1, "comment": "The threshold for low-frequency edge judgment, the larger the value, the fewer areas are judged as edges. Corresponding to x_th", "range": [0, 255], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_y_edge_th"], "dependency": "user"}, "sf_gau_y_edge_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "Low frequency edge judgment transition speed, the smaller the value, the fewer areas are judged as edges. Corresponding Slope", "range": [0.0, 1.99609375], "default": 0.125, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_y_edge_slope"], "dependency": "user"}, "sf_gau_y_detail_th": {"acc": [0, 8], "auto": 1, "comment": "The threshold for low-frequency texture judgment is that the larger the value, the fewer areas are judged as texture. Corresponding to x_th", "range": [0, 255], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_y_detail_th"], "dependency": "user"}, "sf_gau_y_detail_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "Low frequency texture determines transition speed, and the smaller the value, the fewer areas are judged as texture. Corresponding Slope", "range": [0.0, 1.99609375], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_y_detail_slope"], "dependency": "user"}, "sf_gau_y_sp_win_sel": {"acc": [0, 2], "auto": 1, "comment": "The size of the low-frequency denoising window is larger, and the larger the value, the larger the window filter is used, 0:flat, 1:detail, 2:edge", "default": [1, 1, 1], "hidden": 0, "size": [3], "range": [0, 2], "target_conf": ["yuv_3dnr.sf_gau_y_sp_weight"], "dependency": "user"}, "sf_gau_y_sp_sigma": {"acc": [0, 9], "auto": 1, "comment": "The low-frequency denoising spatial filtering coefficient tends to use all pixels within the window as the value increases, and tends to use pixels in the center area of the window as the value decreases., 0:flat, 1:detail, 2:edge", "default": [64, 64, 64], "hidden": 0, "size": [3], "range": [1, 320], "target_conf": ["yuv_3dnr.sf_gau_y_sp_weight"], "dependency": "user"}, "sf_gau_y_thres": {"acc": [0, 8, 2], "auto": 1, "comment": "Low frequency denoising threshold. The larger the value, the greater the denoising intensity, corresponding to x_th, 0:flat, 1:detail, 2:edge", "range": [0.0, 255.75], "default": [0.0, 0.0, 0.0], "hidden": 0, "size": [3], "target_conf": ["yuv_3dnr.sf_gau_y_thres"], "dependency": "user"}, "sf_gau_y_slope": {"acc": [0, 2, 8], "auto": 1, "comment": "The degree of excessive edge preservation in low-frequency denoising. The larger the value, the smaller the denoising intensity, corresponding to the slope, 0:flat, 1:detail, 2:edge", "range": [0.0, 3.99609375], "default": [0.125, 0.125, 0.125], "hidden": 0, "size": [3], "target_conf": ["yuv_3dnr.sf_gau_y_slope"], "dependency": "user"}, "sf_gau_y_sad_alpha": {"acc": [0, 1, 5], "auto": 1, "comment": "The fusion weight of bilateral and NLM filtering tends to use NLM as the weight increases, 0:flat, 1:detail, 2:edge", "range": [0.0, 1.0], "default": [1.0, 1.0, 1.0], "hidden": 0, "size": [3], "target_conf": ["yuv_3dnr.sf_gau_y_sad_alpha"], "dependency": "user"}, "sf_gau_y_hdr_adjust_th": {"acc": [0, 8, 2], "auto": 1, "comment": "Short frame current frame brightness low-frequency noise reduction intensity correction, the larger the value, the greater the low-frequency noise reduction intensity of the short frame", "range": [0.0, 255.75], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_y_hdr_adjust_th"], "dependency": "user"}, "sf_gau_y_ori_str": {"acc": [0, 8, 4], "auto": 1, "comment": "The low-frequency denoising result overlaps the original noise ratio, and the larger the value, the closer it is to the original frame.", "range": [0.0, 255.9375], "default": 1.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_y_ori_str"], "dependency": "user"}, "sf_gau_y_thres_bias_lut": {"acc": [1, 6, 2], "auto": 1, "comment": "Adjust the low-frequency noise reduction threshold based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the stronger the noise reduction intensity.", "range": [-64.0, 63.75], "default": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.sf_gau_y_thres_bias_lut"], "dependency": "user"}, "sf_gau_y_slope_gain_lut": {"acc": [0, 3, 5], "auto": 1, "comment": "Adjust the transition degree of denoising and edge preservation based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the smaller the denoising intensity.", "range": [0.0, 7.96875], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.sf_gau_y_slope_gain_lut"], "dependency": "user"}, "sf_lap_y_ref_weight_th": {"acc": [0, 1, 7], "auto": 1, "comment": "When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th", "default": 0.1015625, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_lap_y_ref_weight_th"], "dependency": "user"}, "sf_lap_y_ref_weight_slope": {"acc": [0, 4, 8], "auto": 1, "comment": "When providing the next reference frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope", "range": [0.0, 15.99609375], "default": 2.34375, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_y_ref_weight_slope"], "dependency": "user"}, "sf_lap_y_ref_ratio_limit": {"acc": [0, 1, 7], "auto": 1, "comment": "When providing the next reference frame, the higher the ratio of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area and the smaller the noise.", "default": 0.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_lap_y_ref_ratio_limit"], "dependency": "user"}, "sf_lap_y_cur_weight_th": {"acc": [0, 1, 7], "auto": 1, "comment": "When provided to the output frame, the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image is smaller, and the smaller the threshold, the more inclined it is to use the denoising result globally, corresponding to x_th", "default": 0.1015625, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_lap_y_cur_weight_th"], "dependency": "user"}, "sf_lap_y_cur_weight_slope": {"acc": [0, 4, 8], "auto": 1, "comment": "When providing the output frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope", "range": [0.0, 15.99609375], "default": 2.34375, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_y_cur_weight_slope"], "dependency": "user"}, "sf_lap_y_cur_ratio_limit": {"acc": [0, 1, 7], "auto": 1, "comment": "When providing the output frame, the higher the ratio of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area and the smaller the noise.", "default": 1.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_lap_y_cur_ratio_limit"], "dependency": "user"}, "sf_lap_y_edge_th": {"acc": [0, 8], "auto": 1, "comment": "The threshold for high-frequency edge judgment is that the larger the value, the fewer areas are judged as edges. Corresponding to x_th", "range": [0, 255], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_y_edge_th"], "dependency": "user"}, "sf_lap_y_edge_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "High frequency edge judgment transition speed, the smaller the value, the fewer areas are judged as edges. Corresponding Slope", "range": [0.0, 1.99609375], "default": 0.125, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_y_edge_slope"], "dependency": "user"}, "sf_lap_y_detail_th": {"acc": [0, 8], "auto": 1, "comment": "High frequency texture judgment threshold, the larger the value, the fewer areas are judged as texture. Corresponding to x_th", "range": [0, 255], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_y_detail_th"], "dependency": "user"}, "sf_lap_y_detail_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "High frequency texture determines transition speed, and the smaller the value, the fewer areas are judged as texture. Corresponding Slope", "range": [0.0, 1.99609375], "default": 1, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_y_detail_slope"], "dependency": "user"}, "sf_lap_y_sp_win_sel": {"acc": [0, 1], "auto": 1, "comment": "The size of the high-frequency edge preservation denoising window is larger, and the larger the value, the larger the window filtering is used. 0:flat, 1:detail, 2:edge", "range": [0, 1], "default": [1, 1, 1], "hidden": 0, "size": [3], "target_conf": ["yuv_3dnr.sf_lap_y_sp_weight"], "dependency": "user"}, "sf_lap_y_sp_sigma": {"acc": [0, 9], "auto": 1, "comment": "The high-frequency edge preserving denoising spatial filtering coefficient tends to use all pixels within the window as the value increases, and tends to use pixels in the center area of the window as the value decreases., 0:flat, 1:detail, 2:edge", "default": [64, 64, 64], "hidden": 0, "size": [3], "range": [1, 320], "target_conf": ["yuv_3dnr.sf_lap_y_sp_weight"], "dependency": "user"}, "sf_lap_y_thres": {"acc": [0, 8, 2], "auto": 1, "comment": "High frequency edge preservation denoising threshold. The larger the value, the greater the denoising intensity, corresponding to x_th, 0:flat, 1:detail, 2:edge", "range": [0.0, 255.75], "default": [0.0, 0.0, 0.0], "hidden": 0, "size": [3], "target_conf": ["yuv_3dnr.sf_lap_y_thres"], "dependency": "user"}, "sf_lap_y_slope": {"acc": [0, 2, 8], "auto": 1, "comment": "High frequency edge preservation denoising and excessive edge preservation. The larger the value, the smaller the denoising intensity, corresponding to the slope, 0:flat, 1:detail, 2:edge", "range": [0.0, 3.99609375], "default": [0.125, 0.125, 0.125], "hidden": 0, "size": [3], "target_conf": ["yuv_3dnr.sf_lap_y_slope"], "dependency": "user"}, "sf_lap_y_sad_alpha": {"acc": [0, 1, 5], "auto": 1, "comment": "The fusion weight of high-frequency edge preservation denoising bilateral and NLM filtering tends to use NLM as the weight increases, 0:flat, 1:detail, 2:edge", "range": [0.0, 1.0], "default": [1.0, 1.0, 1.0], "hidden": 0, "size": [3], "target_conf": ["yuv_3dnr.sf_lap_y_sad_alpha"], "dependency": "user"}, "sf_lap_y_gauss_win_sel": {"acc": [0, 2], "auto": 1, "comment": "The window size of high-frequency non edge preserving denoising filter is larger, and the larger the value, the more pixels are used for denoising, resulting in a blurred effect, 0:flat, 1:detail, 2:edge", "default": 2, "hidden": 0, "size": [], "range": [0, 2], "target_conf": ["yuv_3dnr.sf_lap_y_gauss_weight"], "dependency": "user"}, "sf_lap_y_gauss_sigma": {"acc": [0, 9], "auto": 1, "comment": "High frequency non edge preserving denoising filter spatial filtering coefficient, the larger the value, the more inclined it is to use all pixels within the window, and the smaller the value, the more inclined it is to use pixels in the center area of the window, 0:flat, 1:detail, 2:edge", "default": 64, "hidden": 0, "size": [], "range": [1, 320], "target_conf": ["yuv_3dnr.sf_lap_y_gauss_weight"], "dependency": "user"}, "sf_lap_y_gauss_ratio": {"acc": [0, 1, 5], "auto": 1, "comment": "The fusion coefficient of high-frequency non edge preserving denoising filtering and edge preserving filtering, the larger the value, the more the non edge preserving filtering result is used. 0:flat, 1:detail, 2:edge.", "default": [1.0, 1.0, 1.0], "hidden": 0, "size": [3], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_lap_y_gauss_ratio"], "dependency": "user"}, "sf_lap_y_hdr_adjust_th": {"acc": [0, 8, 2], "auto": 1, "comment": "Short frame current frame brightness high-frequency noise reduction intensity correction, the larger the value, the greater the high-frequency noise reduction intensity of the short frame", "range": [0.0, 255.75], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_y_hdr_adjust_th"], "dependency": "user"}, "sf_lap_y_ori_str": {"acc": [0, 8, 4], "auto": 1, "comment": "The high-frequency denoising result overlaps the original noise ratio, and the larger the value, the closer it is to the original frame.", "range": [0.0, 255.9375], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_y_ori_str"], "dependency": "user"}, "sf_lap_y_thres_bias_lut": {"acc": [1, 6, 2], "auto": 1, "comment": "Adjust the high-frequency edge preservation and noise reduction threshold based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the stronger the noise reduction intensity.", "range": [-64.0, 63.75], "default": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.sf_lap_y_thres_bias_lut"], "dependency": "user"}, "sf_lap_y_slope_gain_lut": {"acc": [0, 3, 5], "auto": 1, "comment": "Adjust the transition degree of denoising and edge preservation based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the smaller the denoising intensity.", "range": [0.0, 7.96875], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.sf_lap_y_slope_gain_lut"], "dependency": "user"}, "sf_gau_uv_ref_weight_th": {"acc": [0, 1, 7], "auto": 1, "comment": "When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th", "default": 0.1015625, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_gau_uv_ref_weight_th"], "dependency": "user"}, "sf_gau_uv_ref_weight_slope": {"acc": [0, 4, 8], "auto": 1, "comment": "When providing the next reference frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope", "range": [0.0, 15.99609375], "default": 2.34375, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_uv_ref_weight_slope"], "dependency": "user"}, "sf_gau_uv_ref_ratio_limit": {"acc": [0, 1, 7], "auto": 1, "comment": "When providing the next reference frame, the larger the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area UV and the smaller the color noise.", "default": 0.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_gau_uv_ref_ratio_limit"], "dependency": "user"}, "sf_gau_uv_cur_weight_th": {"acc": [0, 1, 7], "auto": 1, "comment": "When provided to the output frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th", "default": 0.1015625, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_gau_uv_cur_weight_th"], "dependency": "user"}, "sf_gau_uv_cur_weight_slope": {"acc": [0, 4, 8], "auto": 1, "comment": "When providing the output frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope", "range": [0.0, 15.99609375], "default": 2.34375, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_uv_cur_weight_slope"], "dependency": "user"}, "sf_gau_uv_cur_ratio_limit": {"acc": [0, 1, 7], "auto": 1, "comment": "When provided to the output frame, the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is higher, and the larger the ratio, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area UV and the smaller the color noise.", "default": 1.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_gau_uv_cur_ratio_limit"], "dependency": "user"}, "sf_gau_uv_th": {"acc": [0, 7, 2], "auto": 1, "comment": "The threshold for reducing saturation based on its original size, the smaller the value, the more likely it is to be globally ineffective, corresponding to x_th", "range": [0.0, 127.75], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_uv_th"], "dependency": "user"}, "sf_gau_uv_slope": {"acc": [0, 2, 7], "auto": 1, "comment": "Reduce the transition speed of saturation based on its original size. The larger the value, the more likely it is to be globally ineffective, corresponding to the slope", "range": [0.0, 3.9921875], "default": 0.125, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_uv_slope"], "dependency": "user"}, "sf_gau_uv_limit": {"acc": [0, 1, 7], "auto": 1, "comment": "According to the proportion of saturation overlaid color noise, the smaller the value, the more inclined it is to reduce saturation, corresponding to y_th0", "default": 1.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_gau_uv_limit"], "dependency": "user"}, "sf_gau_uv_hdr_adjust_th": {"acc": [0, 8, 2], "auto": 1, "comment": "Short frame current frame color low-frequency noise reduction intensity correction, the larger the value, the greater the low-frequency noise reduction intensity of the short frame", "range": [0.0, 255.75], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_uv_hdr_adjust_th"], "dependency": "user"}, "sf_gau_uv_ori_str": {"acc": [0, 1, 5], "auto": 1, "comment": "The ratio of low-frequency fading noise to aliasing noise, the larger the value, the closer it is to the original input", "range": [0.0, 1.0], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_gau_uv_ori_ratio"], "dependency": "user"}, "sf_gau_uv_thre_pre_lut": {"acc": [0, 8, 2], "auto": 1, "comment": "Low frequency color first stage denoising intensity, horizontal axis brightness, the larger the value, the greater the denoising intensity", "range": [0.0, 255.75], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.sf_gau_uv_thre_pre_lut"], "dependency": "user"}, "sf_gau_uv_thre_lut": {"acc": [0, 8, 2], "auto": 1, "comment": "Low frequency color two-stage denoising intensity, horizontal axis brightness, the larger the value, the greater the denoising intensity", "range": [0.0, 255.75], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.sf_gau_uv_thre_lut"], "dependency": "user"}, "sf_gau_uv_prot_thre_lut": {"acc": [0, 8, 2], "auto": 1, "comment": "Low frequency color denoising protection threshold, based on the diff aliasing color noise before and after denoising, the larger the value, the more likely it is not to aliasing, corresponding to x_th", "range": [0.0, 255.75], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.sf_gau_uv_prot_thre_lut"], "dependency": "user"}, "sf_gau_uv_prot_slope_lut": {"acc": [0, 2, 8], "auto": 1, "comment": "Low frequency color denoising protection transition speed, based on the diff of color noise before and after denoising, the larger the value, the more likely it is to be overlapped, corresponding to the slope", "range": [0.0, 3.99609375], "default": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "hidden": 0, "size": [9], "target_conf": ["yuv_3dnr.sf_gau_uv_prot_slope_lut"], "dependency": "user"}, "sf_lap_uv_ref_weight_th": {"acc": [0, 1, 7], "auto": 1, "comment": "When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th", "default": 0.1015625, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_lap_uv_ref_weight_th"], "dependency": "user"}, "sf_lap_uv_ref_weight_slope": {"acc": [0, 4, 8], "auto": 1, "comment": "When provided to the next reference frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope", "range": [0.0, 15.99609375], "default": 2.34375, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_uv_ref_weight_slope"], "dependency": "user"}, "sf_lap_uv_ref_ratio_limit": {"acc": [0, 1, 7], "auto": 1, "comment": "When provided to the next reference frame, the higher the ratio of the temporal fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area UV and the smaller the color noise.", "default": 0.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_lap_uv_ref_ratio_limit"], "dependency": "user"}, "sf_lap_uv_cur_weight_th": {"acc": [0, 1, 7], "auto": 1, "comment": "When provided to the output frame, the lower the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th", "default": 0.1015625, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_lap_uv_cur_weight_th"], "dependency": "user"}, "sf_lap_uv_cur_weight_slope": {"acc": [0, 4, 8], "auto": 1, "comment": "When provided to the output frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope", "range": [0.0, 15.99609375], "default": 2.34375, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_uv_cur_weight_slope"], "dependency": "user"}, "sf_lap_uv_cur_ratio_limit": {"acc": [0, 1, 7], "auto": 1, "comment": "When provided to the output frame, the ratio of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is higher, and the larger the ratio, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the stationary area UV and the smaller the color noise.", "default": 1.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_lap_uv_cur_ratio_limit"], "dependency": "user"}, "sf_lap_uv_thre": {"acc": [0, 8, 2], "auto": 1, "comment": "High frequency denoising threshold, the larger the value, the stronger the denoising intensity, corresponding to x_th", "range": [0.0, 255.75], "default": 1.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_uv_thre"], "dependency": "user"}, "sf_lap_uv_slope": {"acc": [0, 2, 8], "auto": 1, "comment": "High frequency denoising transition speed, the larger the value, the weaker the denoising intensity, corresponding to the slope", "range": [0.0, 3.99609375], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_uv_slope"], "dependency": "user"}, "sf_lap_uv_hdr_adjust_gain": {"acc": [0, 1, 5], "auto": 1, "comment": "Short frame current frame color high-frequency noise reduction intensity correction, the larger the value, the greater the high-frequency noise reduction intensity of the short frame", "default": 1.0, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.sf_lap_uv_hdr_adjust_gain"], "dependency": "user"}, "sf_lap_uv_ori_str": {"acc": [0, 1, 5], "auto": 1, "comment": "High frequency color fading noise and aliasing noise, the larger the value, the closer it is to the original image", "range": [0.0, 1.0], "default": 0.0, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.sf_lap_uv_ori_ratio"], "dependency": "user"}, "gf_win_r": {"acc": [0, 3], "auto": 1, "comment": "The size of the window for guiding filtering to remove color noise, the larger the value, the greater the intensity of color noise removal", "default": 5, "hidden": 0, "size": [], "range": [0, 5], "target_conf": ["ifa.guided_win_r"], "dependency": "user"}, "gf_eps": {"acc": [0, 8], "auto": 1, "comment": "The bias size for guiding filtering to remove color noise, the larger the value, the greater the intensity of color noise removal", "default": 5, "hidden": 0, "size": [], "range": [1, 255], "target_conf": ["ifa.guided_epsilon"], "dependency": "user"}, "gf_uv_diff_th": {"acc": [0, 8], "auto": 1, "comment": "The diff protection strength of guided filtering for color noise reduction results is weaker as the value increases, and closer to non guided filtering as the value decreases", "range": [0, 255], "default": 5, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.gf_uv_diff_th"], "dependency": "user"}, "gf_uv_sat_th": {"acc": [0, 7], "auto": 1, "comment": "The results of guided filtering for removing color noise are based on the sat protection strength. The larger the value, the more inclined it is to use the guided filtering result, corresponding to x_th", "range": [0, 127], "default": 5, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.gf_uv_sat_th"], "dependency": "user"}, "gf_uv_sat_slope": {"acc": [0, 1, 8], "auto": 1, "comment": "The guided filtering denoising result is based on the transition speed of SAT protection. The larger the value, the more inclined it is to use the original denoising result, corresponding to the slope", "range": [0.0, 1.99609375], "default": 0.125, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.gf_uv_sat_slope"], "dependency": "user"}, "gf_uv_sat_ratio": {"acc": [0, 1, 7], "auto": 1, "comment": "The guided filtering denoising result is based on the fusion ratio between sat and the original denoising result. The larger the value, the more inclined it is to the original denoising result, corresponding to y_th0 and y_th1", "range": [0.0, 1.9921875], "default": [0.0, 0.0], "hidden": 0, "size": [2], "target_conf": ["yuv_3dnr.gf_uv_sat_ratio"], "dependency": "user"}, "gf_uv_blend_ratio_th": {"acc": [0, 1, 7], "auto": 1, "comment": "Determine the time-domain fusion coefficient threshold for the fusion guided filtering result of the dynamic and static regions based on the time-domain fusion coefficient. It is hoped that different fusion ratios will be used for the dynamic and static regions, corresponding to x_th", "default": 0.1015625, "hidden": 0, "size": [], "range": [0.0, 1.0], "target_conf": ["yuv_3dnr.gf_uv_blend_ratio_th"], "dependency": "user"}, "gf_uv_blend_ratio_slope": {"acc": [1, 7, 7], "auto": 1, "comment": "Determine the transition speed of the fusion guided filtering result for the dynamic and static regions based on the time-domain fusion coefficient. It is hoped that different fusion ratios will be used for the dynamic and static regions, corresponding to the slope", "range": [-128.0, 127.9921875], "default": 2.5, "hidden": 0, "size": [], "target_conf": ["yuv_3dnr.gf_uv_blend_ratio_slope"], "dependency": "user"}, "gf_uv_blend_ratio": {"acc": [0, 1, 7], "auto": 1, "comment": "According to the time-domain fusion coefficient, the proportion of the fusion guided filtering results in the dynamic and static regions is determined. The first value corresponds to the static region, and the second value corresponds to the motion region, corresponding to y_th0 and y_th1. The larger the value, the more inclined it is to use the guided filtering results", "range": [0.0, 1.0], "default": [1.0, 1.0], "hidden": 0, "size": [2], "target_conf": ["yuv_3dnr.gf_uv_blend_ratio"], "dependency": "user"}, "dither_seed_enable": {"acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "0: use stat dither_seed, 1: use conf dither_seed. set 1 for the 1st frame, then set 0 from the 2nd frame onwards.", "hidden": 1, "auto": 0, "target_conf": ["yuv_3dnr.dither_seed_enable"], "dependency": "common"}, "yuv3dnr_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "target_conf": ["yuv_3dnr.ptn_roi_t_b_l_r", "yuv_3dnr.pic_h_w", "yuv_3dnr.ptn_h_w", "yuv_3dnr.ptn_offset_h_w"], "dependency": "common", "comment": "dehaze partition", "hidden": 1, "auto": 0}, "ifa_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "target_conf": ["ifa.output_hor_crop_enable", "ifa.output_hor_crop_st", "ifa.output_hor_crop_width"], "dependency": "common", "comment": "ifa partition", "hidden": 1, "auto": 0}}, "submodules": {"extmask": {"configs": [], "params": ["ext_mask_mode", "ext_mask_thre", "ext_mask_slope", "ext_mask_ratio"]}, "motion": {"configs": [], "params": ["motion_small_sel_ratio", "motion_large_sel_ratio", "motion_feature_gauss_str", "motion_sad_sd_th", "motion_sad_weight_slope", "motion_sad_weight_th", "motion_highlight_diff_th", "motion_highlight_luma_th", "motion_highlight_luma_slope", "motion_highlight_var_th", "motion_highlight_var_slope", "motion_sharpen_str", "motion_y_red_correct_str", "motion_uv_red_correct_str", "motion_y_blue_correct_str", "motion_uv_blue_correct_str", "motion_dec_y_th", "motion_dec_y_slope", "motion_m2s_dec_y_th", "motion_m2s_dec_y_slope", "motion_hdr_adjust_y_th", "motion_dec_uv_th", "motion_dec_uv_slope", "motion_adj_uv_th", "motion_adj_uv_slope", "motion_hdr_adjust_uv_th", "motion_noise_y_lut", "motion_noise_uv_lut", "motion_hvs_y_lut", "motion_hvs_uv_lut"]}, "tnr": {"configs": ["yuv_3dnr.tweight_map_th", "yuv_3dnr.tweight_map_slope"], "params": ["tweight_cur_w", "tweight_pre_limit", "tweight_max_ratio", "tweight_smooth_ratio", "tweight_sf_sd_y_th", "tweight_sf_sd_y_slope", "tweight_sf_sad_y_th", "tweight_sf_sad_y_slope", "tweight_sf_sign_y_th", "tweight_sf_sign_y_slope", "tweight_str", "ratio_uv_adjust_th", "ratio_uv_adjust_slope", "ratio_uv_adjust", "ratio_uv_limit", "m2s_motion_cur", "m2s_tweight_ref", "m2s_var_th", "m2s_var_slope", "m2s_blend_ratio", "tweight_static_lut"]}, "ysf": {"configs": [], "params": ["sf_gau_y_ref_weight_th", "sf_gau_y_ref_weight_slope", "sf_gau_y_ref_ratio_limit", "sf_gau_y_cur_weight_th", "sf_gau_y_cur_weight_slope", "sf_gau_y_cur_ratio_limit", "sf_gau_y_edge_th", "sf_gau_y_edge_slope", "sf_gau_y_detail_th", "sf_gau_y_detail_slope", "sf_gau_y_sp_win_sel", "sf_gau_y_sp_sigma", "sf_gau_y_thres", "sf_gau_y_slope", "sf_gau_y_sad_alpha", "sf_gau_y_hdr_adjust_th", "sf_gau_y_ori_str", "sf_gau_y_thres_bias_lut", "sf_gau_y_slope_gain_lut", "sf_lap_y_ref_weight_th", "sf_lap_y_ref_weight_slope", "sf_lap_y_ref_ratio_limit", "sf_lap_y_cur_weight_th", "sf_lap_y_cur_weight_slope", "sf_lap_y_cur_ratio_limit", "sf_lap_y_edge_th", "sf_lap_y_edge_slope", "sf_lap_y_detail_th", "sf_lap_y_detail_slope", "sf_lap_y_sp_win_sel", "sf_lap_y_sp_sigma", "sf_lap_y_thres", "sf_lap_y_slope", "sf_lap_y_sad_alpha", "sf_lap_y_gauss_win_sel", "sf_lap_y_gauss_sigma", "sf_lap_y_gauss_ratio", "sf_lap_y_hdr_adjust_th", "sf_lap_y_ori_str", "sf_lap_y_thres_bias_lut", "sf_lap_y_slope_gain_lut"]}, "csf": {"configs": [], "params": ["sf_gau_uv_ref_weight_th", "sf_gau_uv_ref_weight_slope", "sf_gau_uv_ref_ratio_limit", "sf_gau_uv_cur_weight_th", "sf_gau_uv_cur_weight_slope", "sf_gau_uv_cur_ratio_limit", "sf_gau_uv_th", "sf_gau_uv_slope", "sf_gau_uv_limit", "sf_gau_uv_hdr_adjust_th", "sf_gau_uv_ori_str", "sf_gau_uv_thre_pre_lut", "sf_gau_uv_thre_lut", "sf_gau_uv_prot_thre_lut", "sf_gau_uv_prot_slope_lut", "sf_lap_uv_ref_weight_th", "sf_lap_uv_ref_weight_slope", "sf_lap_uv_ref_ratio_limit", "sf_lap_uv_cur_weight_th", "sf_lap_uv_cur_weight_slope", "sf_lap_uv_cur_ratio_limit", "sf_lap_uv_thre", "sf_lap_uv_slope", "sf_lap_uv_hdr_adjust_gain", "sf_lap_uv_ori_str"]}, "cgf": {"configs": ["ifa.enable", "ifa.mode", "ifa.sub_mode", "ifa.guided_enable", "ifa.guided_dehaze_dc_adj_enable", "ifa.guided_dehaze_dc_k", "ifa.guided_dehaze_dc_b", "ifa.blur_enable", "ifa.blur_weight"], "params": ["gf_win_r", "gf_eps", "gf_uv_diff_th", "gf_uv_sat_th", "gf_uv_sat_slope", "gf_uv_sat_ratio", "gf_uv_blend_ratio_th", "gf_uv_blend_ratio_slope", "gf_uv_blend_ratio"]}, "ctrl": {"configs": [], "params": ["enable", "tf_y_en", "tf_uv_en", "sf_gau_y_en", "sf_lap_y_en", "sf_gau_uv_en", "sf_lap_uv_en", "gf_uv_en", "tweight_sf_en", "debug_mode", "debug_gain", "init_ref", "init_tweight", "init_coef_ab", "ext_mask_en"]}, "setup": {"configs": ["yuv_3dnr.enable", "yuv_3dnr.dither_enable", "yuv_3dnr.dither_pmask_uv", "yuv_3dnr.tf_converg_offset", "yuv_3dnr.motion_red_center", "yuv_3dnr.motion_red_radius", "yuv_3dnr.motion_red_dist_mode", "yuv_3dnr.motion_red_dist_weight", "yuv_3dnr.motion_red_th", "yuv_3dnr.motion_red_slope", "yuv_3dnr.motion_blue_center", "yuv_3dnr.motion_blue_radius", "yuv_3dnr.motion_blue_dist_mode", "yuv_3dnr.motion_blue_dist_weight", "yuv_3dnr.motion_blue_th", "yuv_3dnr.motion_blue_slope", "yuv_3dnr.motion_sad_sd_lw_adjust"], "params": []}, "dither": {"configs": ["yuv_3dnr.dither_seed_uv"], "params": ["dither_seed_enable"]}, "part": {"configs": ["yuv_3dnr.norm_ratio_8x"], "params": ["yuv3dnr_partition_info", "ifa_partition_info"]}}, "target_module": {"mc20l": {"yuv_3dnr": {"id": 5200, "method": 0}, "ifa": {"id": 6500, "method": 0}}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [12], "ref_interp_method": ["linear"]}}, "constraints": ["motion_large_sel_ratio <= motion_small_sel_ratio", "motion_sad_weight_th[0] <= motion_sad_weight_th[1]", "tweight_static_lut[i] <= tweight_static_lut[i+1]", "gf_uv_sat_ratio[0] <= gf_uv_sat_ratio[1]"], "structs": {}}