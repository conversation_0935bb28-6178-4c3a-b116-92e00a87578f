{"context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "HDR is 0x2221"}, "submodule_exec_order": {"size": [], "acc": [0, 16], "comment": "ctx parameter used to record submodule exec order. If exec order is different from what is expected, error will be raised."}, "sw_enable": {"size": [], "acc": [0, 1], "comment": "Software enable control bit"}, "hw_enable": {"size": [], "acc": [0, 1], "comment": "Hardware enable control bit"}, "internal_lf_indicator": {"size": [], "acc": [0, 1], "comment": "Indicate internal LF index(after internal 'swap_frame' feature). From ACP."}, "exposure_ratio": {"size": [], "acc": [0, 9, 8], "comment": "Exposure ratio from ACP."}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "params": {"use_isp_hdr": {"acc": [0, 1], "size": [], "comment": "From ACP. Default value depends on ACP. 0 - HW disable; 1 - HW enable", "hidden": 1, "target_conf": ["hdr.enable"], "range": [0, 1], "dependency": "common"}, "offset_in": {"acc": [0, 8, 6], "size": [2], "hidden": 1, "target_conf": ["hdr.offset_in", "hdr.d_gain", "hdr.wb_gain", "hdr.dgst_exp_wb_gain", "hdr.mot_sat_x"], "comment": "From ACP. Default value depends on ACP. offset in.", "dependency": "common"}, "offset_out": {"acc": [0, 14, 6], "size": [], "hidden": 1, "target_conf": ["hdr.offset_out", "hdr.inv_wb_gain"], "comment": "From ACP. Default value depends on ACP. offset out.", "dependency": "common"}, "wb_gain": {"acc": [0, 4, 12], "size": [4], "hidden": 1, "target_conf": ["hdr.wb_gain", "hdr.inv_wb_gain", "hdr.dgst_exp_wb_gain"], "comment": "From ACP. Default value depends on ACP. wb gain from AWB / WBC", "dependency": "common"}, "exposure_ratio": {"acc": [0, 9, 8], "size": [], "hidden": 1, "target_conf": ["hdr.dgst_clip_level", "hdr.dgst_exp_wb_gain", "hdr.fus_clip_level", "hdr.fus_exp_gain", "hdr.fus_exp_ratio", "hdr.inv_wb_gain", "hdr.mot_k_ratio", "hdr.mot_sat_x"], "comment": "From ACP. Default value depends on ACP. hdr ratio from AE", "dependency": "common"}, "internal_lf_indicator": {"acc": [0, 1], "size": [], "hidden": 1, "target_conf": ["hdr.dgst_clip_level", "hdr.dgst_exp_wb_gain", "hdr.fus_exp_gain", "hdr.fus_exp_ratio", "hdr.mot_sat_x", "hdr.internal_lf_indicator", "hdr.tnr_mask_base_fid", "hdr.fus_base_fid", "hdr.debug_show_mask"], "comment": "From ACP. Default value depends on ACP. 0 - input0(after swap_frame) is LF; 1 - input1(after swap_frame) is LF", "dependency": "common"}, "a_gain": {"acc": [0, 22, 10], "size": [], "hidden": 1, "target_conf": ["hdr.noise_profile", "hdr.inv_noise_profile"], "comment": "From ACP. Default value depends on ACP. SF a_gain", "dependency": "common"}, "shot_noise_coef": {"acc": [1, 0, 31], "size": [4, 2], "hidden": 1, "target_conf": ["hdr.noise_profile", "hdr.inv_noise_profile"], "comment": "From ACP. Default value depends on ACP. shot noise coefficients", "dependency": "common"}, "read_noise_coef": {"acc": [1, 0, 31], "size": [4, 3], "hidden": 1, "target_conf": ["hdr.noise_profile", "hdr.inv_noise_profile"], "comment": "From ACP. Default value depends on ACP. read-out noise coefficients", "dependency": "common"}, "enable": {"display": "Enable", "acc": [0, 1], "size": [], "default": 1, "comment": "0 - Disable; 1 - Enable", "hidden": 0, "target_conf": [], "range": [0, 1]}, "debug_show_mask": {"display": "Debug Mode", "acc": [0, 3], "size": [], "default": 0, "hidden": 0, "target_conf": ["hdr.debug_show_mask"], "comment": "0 - normal; 1 - general mask; 2 - motion mask; 3 - exposure mask sf; 4 - exposure mask lf", "range": [0, 4], "enum_field": {"0": "NORMAL", "1": "GENERAL_MASK", "2": "MOTION_MASK", "3": "EXPOSURE_MASK_SF", "4": "EXPOSURE_MASK_LF"}}, "noise_lut_scale": {"display": "Noise Lut Scale", "acc": [0, 4, 12], "size": [], "default": 1, "hidden": 0, "auto": 1, "target_conf": ["hdr.noise_profile", "hdr.inv_noise_profile"], "comment": "Noise profile will be multiplied by this param.", "range": [0.0, 15.999755859375]}, "fus_dark_prot_en": {"display": "Dark Protection Enable", "acc": [0, 1], "size": [], "default": 1, "hidden": 0, "target_conf": ["hdr.fus_dark_prot_en"], "comment": "0 - Disable dark protection feature for normal fusion; 1 - Enable dark protection feature for normal fusion", "range": [0, 1]}, "fus_over_exp_prot_thre": {"display": "Over-exposure Protection Threshold", "acc": [0, 8, 8], "size": [2, 2], "default": [[251, 254], [251, 254]], "hidden": 0, "target_conf": ["hdr.fus_prot_slope", "hdr.fus_prot_thre"], "comment": "fus_over_exp_prot_thre[x] for input frame x; For Luma value from fus_over_exp_prot_thre[x][0] to fus_over_exp_prot_thre[x][1], protection weight will drop from 1 to 0. When weight == 0, corresponding frame will not be used in normal fusion(area that satisfies hdr_ratio). Example: fus_over_exp_prot_thre = [[251, 254], [247, 249]], Luma value larger than 254 in frame0 will NOT be used in fusion and 251~254 is transition range; Luma value larger than 249 in frame1 will NOT be used in fusion and 247~249 is transition range.", "range": [0.0, 255.99609375]}, "fus_prot_wb_ratio": {"display": "Over-exposure Protection WB Ratio", "acc": [0, 1, 8], "size": [2], "default": 256, "hidden": 0, "target_conf": ["hdr.fus_prot_wb_ratio"], "comment": "fus_prot_wb_ratio[x] for input frame x. This param controls how much WB gain is considered when calculating Luma value used for over-exposure protection.", "range": [0.0, 1.0]}, "dgst_base_fid": {"display": "De-ghost Base Fid", "acc": [0, 1], "size": [], "default": 1, "hidden": 0, "auto": 1, "target_conf": ["hdr.dgst_base_fid"], "comment": "Specify which frame to be used as dgst fusion result when both exp mask is 0 for a pixel. 0 - SF; 1 - LF", "range": [0, 1], "enum_field": {"0": "SF", "1": "LF"}}, "dgst_enable": {"display": "De-ghost Enable", "acc": [0, 1], "size": [], "default": 1, "hidden": 0, "target_conf": ["hdr.dgst_enable"], "comment": "0 - dgst disable; 1 - dgst enable", "range": [0, 1]}, "dgst_stren_limit": {"display": "De-ghost <PERSON><PERSON>", "acc": [0, 1, 8], "size": [2], "default": [0, 1], "hidden": 0, "target_conf": ["hdr.dgst_stren_limit"], "comment": "Forcibly set motion mask used in dgst to be from dgst_stren_limit[0] to dgst_stren_limit[1]", "range": [0.0, 1.0]}, "exp_weight_gain": {"display": "Exp <PERSON> Gain", "acc": [0, 1, 8], "size": [2], "default": [0, 1], "hidden": 0, "auto": 1, "target_conf": ["hdr.exp_weight_gain"], "comment": "<PERSON><PERSON> applied on exposure mask before exp mask normalization.", "range": [0.0, 1.0]}, "exp_weight_lut_luma_low": {"display": "Exposure Weight Lut Dark", "acc": [0, 1, 15], "size": [2, 33], "default": 1, "hidden": 0, "auto": 1, "target_conf": ["hdr.exp_weight_lut_luma_low"], "comment": "Weight lut of exposure mask for dark area. It's used to generate exposure weight based on pixel luma value.", "range": [0.0, 1.0]}, "exp_weight_lut_luma_high": {"display": "Exposure Weight Lut Bright", "acc": [0, 1, 15], "size": [2, 33], "default": 1, "hidden": 0, "auto": 1, "target_conf": ["hdr.exp_weight_lut_luma_high"], "comment": "Weight lut of exposure mask for bright area. It's used to generate exposure weight based on pixel luma value.", "range": [0.0, 1.0]}, "exp_y_ratio": {"display": "Exp Y Ratio", "acc": [0, 1, 8], "size": [2], "default": 1, "hidden": 0, "target_conf": ["hdr.exp_y_ratio"], "comment": "This param controls how much WB gain is considered when calculating Luma value used for exp mask generation.", "range": [0.0, 1.0]}, "motion_mask_enhance": {"display": "Motion Mask Enhance", "acc": [0, 8, 8], "size": [], "default": 1, "hidden": 0, "auto": 1, "target_conf": ["hdr.mot_coring_slope"], "comment": "This param will enhance motion mask.", "range": [0.0, 255.99609375]}, "motion_mask_noise_level": {"display": "Motion Level Control", "acc": [0, 1, 11], "size": [], "default": 0, "hidden": 0, "auto": 1, "target_conf": ["hdr.mot_coring_thre"], "comment": "This parameter acts like motion mask threshold, cutting the motion mask.", "range": [0.0, 1.99951171875]}, "motion_mask_continuity": {"acc": [0, 1, 8], "size": [], "default": 1, "hidden": 0, "target_conf": ["hdr.mot_mask_max_blend_ratio"], "display": "motion mask continuity control", "comment": "The higher this parameter is, the more continuous(slightly) motion mask can be. Can be helpful for some scenes.", "range": [0.0, 1.0]}}, "submodules": {"setup": {"params": [], "configs": ["hdr.swap_frame", "hdr.fus_weight_min_blend_ratio", "hdr.exp_weight_lut_luma_mid", "hdr.fus_weight_lut"]}, "common": {"params": ["use_isp_hdr", "offset_in", "offset_out", "wb_gain", "exposure_ratio", "internal_lf_indicator", "a_gain", "shot_noise_coef", "read_noise_coef", "enable", "debug_show_mask", "noise_lut_scale"], "configs": []}, "fusion": {"params": ["fus_dark_prot_en", "fus_over_exp_prot_thre", "fus_prot_wb_ratio"], "configs": []}, "deghost": {"params": ["dgst_base_fid", "dgst_enable", "dgst_stren_limit"], "configs": []}, "exposure": {"params": ["exp_weight_gain", "exp_weight_lut_luma_low", "exp_weight_lut_luma_high", "exp_y_ratio"], "configs": []}, "motion": {"params": ["motion_mask_enhance", "motion_mask_noise_level", "motion_mask_continuity"], "configs": []}}, "target_module": {"mc20l": {"hdr": {"id": 2000, "method": 0}}}, "partition_configs": [], "configs": {"hdr": {"enable": {"acc": [0, 1], "size": [], "description": "hdr enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "swap_frame": {"acc": [0, 1], "size": [], "description": "input frame swap bit", "usage": "0 - not swap; 1 - swap input frames", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "internal_lf_indicator": {"acc": [0, 1], "size": [], "description": "Internal LF indication bit.", "usage": "0 - LF is input 0; 1 - LF is input 1", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "tnr_mask_base_fid": {"acc": [0, 1], "size": [], "description": "0 - tnr mask represents input0's ratio in fusion output; 1 - tnr mask represents input1's ratio in fusion output", "usage": "", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "wb_gain": {"acc": [0, 4, 12], "size": [4], "description": "Full range wb gain. It should consider dgain.", "usage": "AWB/WBC's wbgain * dgain = full range wbgain", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "inv_wb_gain": {"acc": [0, 1, 15], "size": [4], "description": "1 / wb_gain", "usage": "1 / wb_gain", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "d_gain": {"acc": [0, 4, 12], "size": [], "description": "dgain should be set so that image * dgain = full range.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "noise_profile": {"acc": [0, 6, 10], "size": [33], "description": "noise profile of SF", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "inv_noise_profile": {"acc": [0, 10, 10], "size": [33], "description": "inverse noise profile of SF", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U32", "partition": "-"}, "dgst_enable": {"acc": [0, 1], "size": [], "description": "dgst enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "mot_sat_x": {"acc": [0, 8, 10], "size": [2], "description": "Motion judgement coefficient which would be calculated in algo logic.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U32", "partition": "-"}, "mot_k_ratio": {"acc": [0, 1, 16], "size": [], "description": "Motion judgement coefficient which would be calculated in algo logic.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U32", "partition": "-"}, "mot_coring_thre": {"acc": [0, 1, 11], "size": [], "description": "Coring threshold for current motion mask. Motion level lower than this will be set to 0.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "mot_coring_slope": {"acc": [0, 8, 8], "size": [], "description": "Coring slope for current motion mask. Motion level larger than this will be multiplied by this to generate a new motion level.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "mot_mask_max_blend_ratio": {"acc": [0, 1, 8], "size": [], "description": "The blending ratio of 'max motion mask'. 'max motion mask' is the mask generated by taking the maximum motion mask value of 4 channels(e.g. RGGB)", "usage": "mot mask = (1 - ratio) * mot_mask + ratio * mot_max_mask", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "exp_y_ratio": {"acc": [0, 1, 8], "size": [2], "description": "When calculate luma value to generate exposure mask, it will use wbgain luma and dgain luma. This ratio controls the blending ratio of wbgain luma generated weight.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "exp_weight_lut_luma_low": {"acc": [0, 1, 15], "size": [2, 33], "description": "Exposure weight lut used in generating exposure mask based on luma value. For u8.4, when luma value is in between 0 ~ 32, this lut is effective.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "exp_weight_lut_luma_mid": {"acc": [0, 1, 15], "size": [2, 33], "description": "Exposure weight lut used in generating exposure mask based on luma value. For u8.4, when luma value is in between 32 ~ 224, this lut is effective.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "exp_weight_lut_luma_high": {"acc": [0, 1, 15], "size": [2, 33], "description": "Exposure weight lut used in generating exposure mask based on luma value. For u8.4, when luma value is in between 224 ~ 256, this lut is effective.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "exp_weight_gain": {"acc": [0, 1, 8], "size": [2], "description": "After exposure mask generation, exposure mask will be multiplied by this value to get exp mask used in dgst process. Note that at this point, exp masks have not yet been normalized.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "fus_dark_prot_en": {"acc": [0, 1], "size": [2], "description": "If enabled, dark area of image will be protected using the similiar logic as over-exposure protection in fusion.", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "fus_prot_thre": {"acc": [0, 8, 8], "size": [2, 2], "description": "Normal fusion protection threshold. Pixel value which is between fus_prot_thre[i][0] ~ fus_prot_thre[i][1] will be considered valid for fusion, where i is the frame index.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "fus_prot_slope": {"acc": [0, 10, 10], "size": [2, 2], "description": "Normal fusion protection slope. Pixel value which is between fus_prot_thre[i][0] ~ fus_prot_thre[i][1] will be considered valid for fusion, where i is the frame index. fus_prot_slope[i][0] and fus_prot_slope[i][1] are slope for transition(smoothing).", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U32", "partition": "-"}, "fus_weight_lut": {"acc": [0, 1, 16], "size": [17], "description": "Fusion weight lut used to make transition area of LF and SF more smooth.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U32", "partition": "-"}, "fus_weight_min_blend_ratio": {"acc": [0, 1, 8], "size": [2], "description": "The blending ratio of 'min weight'. 'min_weight' is the minimum weight of 4 surrounding channels(e.g. RGGB).", "usage": "fus prot weight = (1 - ratio) * normal_weight + ratio * min_weight", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "fus_prot_wb_ratio": {"acc": [0, 1, 8], "size": [2], "description": "When calculating luma to judge if pixel value is over-exposed, both wbgain luma and dgain luma is calculated. This register controls the ratio of wbgain luma in final luma.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "fus_base_fid": {"acc": [0, 1], "size": [], "description": "If all pixel values are considered over-exposed, this register choose which pixel value to use.", "usage": "src[fus_base_fid][y, x] * fus_exp_gain = normal fusion res", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "fus_clip_level": {"acc": [0, 14, 6], "size": [], "description": "Normal fusion result clip level.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U32", "partition": "-"}, "fus_exp_ratio": {"acc": [0, 1, 16], "size": [2], "description": "HDR ratio", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U32", "partition": "-"}, "fus_exp_gain": {"acc": [0, 9, 8], "size": [], "description": "Gain used when both pixel values are considered over-exposed.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U32", "partition": "-"}, "dgst_stren_limit": {"acc": [0, 1, 8], "size": [2], "description": "Motion mask used in dgst will be forcibly set in this range.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "dgst_clip_level": {"acc": [0, 14, 6], "size": [2], "description": "Clip level used when generating dgst fusion result whose ratio in final HDR result is controlled by motion mask.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U32", "partition": "-"}, "dgst_exp_wb_gain": {"acc": [0, 12, 16], "size": [2, 4], "description": "wbgain used when generating dgst fusion result. It should include HDR ratio.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U32", "partition": "-"}, "dgst_base_fid": {"acc": [0, 1], "size": [], "description": "When both exp mask is 0, this register specifies which frame to be used in dgst fusion result.", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "debug_show_mask": {"acc": [0, 3], "size": [], "description": "HDR debug mode control.", "usage": "0 - normal mode; 1 - general mask; 2 - motion mask; 3 - exposure mask 0; 4 - exposure mask 1", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "offset_in": {"acc": [0, 8, 6], "size": [2], "description": "offset in inputs", "usage": "offset_in for inputs of HDR. Since it's gone through BLC, pre-dpc inputs should have the same offset as pst-dpc inputs.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "offset_out": {"acc": [0, 14, 6], "size": [], "description": "offset out", "usage": "Set to proper value.", "constraints": "N/A", "type": "AX_U32", "partition": "-"}}}}