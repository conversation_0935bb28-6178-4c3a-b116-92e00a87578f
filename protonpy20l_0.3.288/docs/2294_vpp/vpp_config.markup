h2. Conf list
h3. tde
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | N/A | VPP enable control bit | Set to proper value |
| input_format | u6 | [4\] | - | N/A | input formats | Set to proper value |
| quad_enable | u1 | [8\] | - | N/A | Quad enable control bit | Set to proper value |
| quad_mux | u2 | [\] | - | N/A | Choose which input pic to draw quad on | Set to proper value |
| quad_x_coords | s13.10 | [8, 4\] | - | N/A | x coordinates of intersection points between quad's edges, and image rectangle which is constrained by y_st and y_ed. | Set to proper value |
| quad_y_coords | u13 | [8, 4\] | - | N/A | y coordinates of intersection points between quad's edges, and image rectangle which is constrained by y_st and y_ed. | Set to proper value |
| quad_y_start | u13 | [8\] | - | N/A | y start coordinates of quad | Set to proper value |
| quad_y_end | u13 | [8\] | - | N/A | y end coordinates of quad | Set to proper value |
| quad_steps | s13.10 | [8, 4\] | - | N/A | Quad step to form quad edges | Set to proper value |
| quad_directions | u2 | [8\] | - | N/A | This register controls direction of edge x1 and x2; 0b01 selects right side of line x1 whereas 0b00 selects left side of line x1; 0b10 selects right side of line x2 and 0b00 selects left side of line x2. | Set to proper value |
| quad_mode | u1 | [8\] | - | N/A | Quad feature mode control bit. | 0 - mosaic; 1 - fill |
| quad_mosaic_step_size | u7 | [2\] | - | N/A | Mosaic step size | Set to proper value |
| quad_fill_luma | u8.0 | [8\] | - | N/A | Outer quad luma | Set to proper value |
| quad_fill_chroma | u8.0 | [8, 2\] | - | N/A | Outer quad chroma(with 128 bias) | Set to proper value |
| quad_fill_mode | u2 | [8\] | - | N/A | Quad fill mode. | 0 - inner Quad; 1 - outter Quad; 2 - inner clear Quad; 3 - outter clear Quad |
| quad_alpha | u8.0 | [8\] | - | N/A | Outer quad blending alpha | Set to proper value |
| stream_mux | u2 | [6\] | - | N/A | Choose different src for 6 streams | Set to proper value |
| scale_enable_0 | u1 | [\] | - | N/A | Scalar enable control bit | Set to proper value |
| scale_step_0 | u6.10 | [2\] | - | N/A | Scalar factor | Set to proper value |
| scale_output_size_0 | u13 | [2\] | - | N/A | Scalar output size | Set to proper value |
| scale_offset_luma_0 | s13.6 | [2\] | - | N/A | Luma channel initial phase | Set to proper value |
| scale_offset_chroma_0 | s13.6 | [2\] | - | N/A | Chroma channel initial phase | Set to proper value |
| scale_filter_x_luma_0 | s1.8 | [8, 5\] | - | N/A | 8-phase filtering weights for LUMA channel horizontal direction | Set to proper value |
| scale_filter_y_luma_0 | s1.8 | [8, 5\] | - | N/A | 8-phase filtering weights for LUMA channel vertical direction | Set to proper value |
| scale_filter_x_chroma_0 | s1.8 | [8, 3\] | - | N/A | 8-phase filtering weights for LUMA channel horizontal direction | Set to proper value |
| scale_filter_y_chroma_0 | s1.8 | [8, 3\] | - | N/A | 8-phase filtering weights for LUMA channel vertical direction | Set to proper value |
| shp_enable_0 | u1 | [\] | - | N/A | sharpen enable, 0: disable, 1: enable |  |
| shp_debug_mode_0 | u5 | [\] | - | {0, 3 ~ 18} | sharpen debug mode, 0: normal mode, others: visualized intermediate result for debugging |  |
| shp_filter_ud_0 | s0.12 | [3, 3\] | - | N/A | undirectional filter coeffs |  |
| shp_filter_dir_0_0 | s0.12 | [9\] | - | N/A | 0 directional filter coeffs |  |
| shp_filter_dir_22_0 | s0.12 | [13\] | - | N/A | 22 directional filter coeffs |  |
| shp_filter_dir_45_0 | s0.12 | [9\] | - | N/A | 45 directional filter coeffs |  |
| shp_ud_texture_shift_0 | u3 | [\] | - | N/A | undirectional texture gain lut x-axis shift bits | smaller value means more texture gain control points in flat or weak texture regions |
| shp_dir_texture_shift_0 | u3 | [\] | - | N/A | directional texture gain lut x-axis shift bits | smaller value means more texture gain control points in flat or weak edge regions |
| shp_ud_gain_lut_0 | u8.4 | [33\] | - | N/A | undirectional texture gain lut | set undirectional sharpen gain w.r.t texture strength |
| shp_dir_gain_lut_0 | u8.4 | [33\] | - | N/A | directional texture gain lut | set directional sharpen gain w.r.t texture strength |
| shp_ud_scale_0 | u0.4 | [\] | - | N/A | scaling factor for ud filters | larger value means to use more undirectional filter result |
| shp_dir_scale_0 | u0.4 | [\] | - | N/A | scaling factor for dir filters | larger value means to use more directional filter result |
| shp_edge_sad_noise_0 | u8.4 | [\] | - | N/A | edge noise level | larger value means stronger noise level and reduce directional filter weight |
| shp_nr_ud_texture_scale_0 | u0.8 | [\] | - | N/A | scaling factor for undirectional nr's texture map | larger value means more like a texture pixel (not noise pixel), and reduces undirectional nr strength |
| shp_nr_ud_texture_offset_0 | u0.8 | [\] | - | N/A | coring offset for undirectional nr's texture map | larger value means stronger coring on texutre map, and increases undirectional nr strength |
| shp_nr_ud_limit_0 | u8.2 | [\] | - | N/A | undirectional noise limit | larger value means less limitation for calculated undirectional noise |
| shp_nr_dir_texture_scale_0 | u0.8 | [\] | - | N/A | scaling factor for directional nr's texture map | larger value means more like a edge pixel (not noise pixel), and reduces directional nr strength |
| shp_nr_dir_texture_offset_0 | u4.4 | [\] | - | N/A | coring offset for directional nr's texture map | larger value means stronger coring on texutre map, and increases directional nr strength |
| shp_nr_dir_limit_0 | u8.2 | [\] | - | N/A | directional noise limit | larger value means less limitation for calculated directional noise |
| shp_nr_level_0 | u1.5 | [\] | - | shp_nr_level <= 1.0 | noise reduction level | larger value means stronger noise reduction |
| shp_coring_ud_level_0 | u3.3 | [\] | - | N/A | undirectional sharpen coring level | larger value means stronger coring, and reduces undirectional sharpen strength |
| shp_coring_dir_level_0 | u3.3 | [\] | - | N/A | directional sharpen coring level | larger value means stronger coring, and reduces directional sharpen strength |
| shp_shoot_ref_ratio_0 | u1.5 | [\] | - | shp_shoot_ref_ratio <= 1.0 | the blending ratio of original pixel and local min max to generate shoot reference | larger value means to use more local min max value for shoot reference, and leads to stronger shoot |
| shp_overshoot_0 | u0.7 | [\] | - | N/A | global overshoot level | larger value means stronger overshoot |
| shp_undershoot_0 | u0.7 | [\] | - | N/A | global undershoot level | larger value means stronger undershoot |
| shp_luma_mask_enable_0 | u1 | [\] | - | N/A | luma mask enable, 0: disable, 1: enable |  |
| shp_luma_ref_select_0 | u1 | [\] | - | N/A | luma mask reference selection, 0: blurred Y, 1: denoised Y | usually set to 0 |
| shp_luma_gain_lut_0 | u1.5 | [2, 33\] | - | N/A | luma gain lut, [0\]: negative, [1\]: positive | usually set lower gain for dark region to avoid enhancing noises |
| shp_detail_adj_enable_0 | u1 | [\] | - | N/A | detail enhancement enable, 0: disable, 1: enable |  |
| shp_detail_texture_shift_0 | u3 | [\] | - | N/A | texture map shift bits for detail region | smaller value means detail region will have more flat or weak texture pixels |
| shp_detail_overshoot_slope_0 | s0.9 | [\] | - | N/A | detail overshoot slope | automatically calculated in algo logic |
| shp_detail_overshoot_offset_0 | s4.7 | [\] | - | N/A | detail overshoot offset | automatically calculated in algo logic |
| shp_detail_overshoot_limit_0 | u0.7 | [2\] | - | N/A | detail overshoot limit | automatically calculated in algo logic |
| shp_detail_undershoot_slope_0 | s0.9 | [\] | - | N/A | detail undershoot slope | automatically calculated in algo logic |
| shp_detail_undershoot_offset_0 | s4.7 | [\] | - | N/A | detail undershoot offset | automatically calculated in algo logic |
| shp_detail_undershoot_limit_0 | u0.7 | [2\] | - | N/A | detail undershoot limit | automatically calculated in algo logic |
| shp_shoot_sup_enable_0 | u1 | [\] | - | N/A | shoot suppression enable, 0: disable, 1: enable |  |
| shp_shoot_sup_blend_ratio_0 | u1.4 | [\] | - | shp_shoot_sup_blend_ratio <= 1.0 | the blending ratio of two shoot control strategy | larger value means less shoot control for texture region while keeping shoot control for strong edges |
| shp_shoot_sup_var_min_slope_0 | u0.9 | [\] | - | N/A | shoot control (by min variance) curve slope | automatically calculated in algo logic |
| shp_shoot_sup_var_min_offset_0 | s4.7 | [\] | - | N/A | shoot control (by min variance) curve offset | automatically calculated in algo logic |
| shp_shoot_sup_var_min_limit_0 | u0.7 | [2\] | - | N/A | shoot control (by min variance) curve limit | automatically calculated in algo logic |
| shp_shoot_sup_var_diff_slope_0 | s0.9 | [\] | - | N/A | shoot control (by variance difference) curve slope | automatically calculated in algo logic |
| shp_shoot_sup_var_diff_offset_0 | u4.7 | [\] | - | N/A | shoot control (by variance difference) curve offset | automatically calculated in algo logic |
| shp_shoot_sup_var_diff_limit_0 | u0.7 | [2\] | - | N/A | shoot control (by variance difference) curve limit | automatically calculated in algo logic |
| shp_limit_sup_enable_0 | u1 | [\] | - | N/A | adaptive limit control enable, 0: disable, 1: enable |  |
| shp_limit_sup_max_over_change_0 | u8.2 | [\] | - | N/A | adaptive limit control max over change | larger value means less limitation for pixel value over change |
| shp_limit_sup_max_under_change_0 | u8.2 | [\] | - | N/A | adaptive limit control max under change | larger value means less limitation for pixel value under change |
| shp_limit_sup_max_over_gain_0 | u5.5 | [\] | - | N/A | adaptive limit control over change gain | larger value means less limitation for pixel value over change |
| shp_limit_sup_max_under_gain_0 | u5.5 | [\] | - | N/A | adaptive limit control under change gain | larger value means less limitation for pixel value under change |
| rect_enable_0 | u1 | [16\] | - | N/A | Rectangles' enable control bits | Set to proper value |
| rect_tl_coord_x_0 | u13 | [16\] | - | N/A | top-left x coordinates of rectangles | Set to proper value |
| rect_tl_coord_y_0 | u13 | [16\] | - | N/A | top-left y coordinates of rectangles | Set to proper value |
| rect_br_coord_x_0 | u13 | [16\] | - | N/A | bottom-right x coordinates of rectangles | Set to proper value |
| rect_br_coord_y_0 | u13 | [16\] | - | N/A | bottom-right y coordinates of rectangles | Set to proper value |
| rect_edge_thickness_0 | u8 | [\] | - | N/A | Rectangles' edge thickness | Set to proper value |
| rect_edge_color_select_0 | u2 | [16\] | - | N/A | Rectangles' color select | Set to proper value |
| rect_edge_color_val_0 | u8 | [4, 4\] | - | N/A | Rectangles' value to be selected | [m\][n\], m, 4 different kinds of colors. n, 0:y, 1:u, 2:v, 3:alpha |
| roi_luma_sum_tl_coords_0 | u12.0 | [8, 2\] | - | N/A | top-left corner coordinates of 8 roi areas | roi_luma_sum_tl_coords[n\][0\] - nth roi area's y coordinate; roi_luma_sum_tl_coords[n\][1\] - nth roi area's x coordinate |
| roi_luma_sum_br_coords_0 | u12.0 | [8, 2\] | - | N/A | bottom-right corner coordinates of 8 roi areas | roi_luma_sum_br_coords[n\][0\] - nth roi area's y coordinate; roi_luma_sum_br_coords[n\][1\] - nth roi area's x coordinate |
| osd_enable_0 | u1 | [\] | - | N/A | osd enable control bit | Set to proper value |
| osd_bitmap_color_palette_0 | acc_unknown |  |  |  |  |  |
| osd_tl_coord_x_0 | u13 | [\] | - | N/A | osd blending top-left coord(x) | Set to proper value |
| osd_br_coord_x_0 | u13 | [\] | - | N/A | osd blending bottom-right coord(x) | Set to proper value |
| osd_tl_coord_y_0 | u13 | [4\] | - | N/A | 4 groups of osd blending top-left coord(y) | Set to proper value |
| osd_br_coord_y_0 | u13 | [4\] | - | N/A | 4 groups of osd blending bottom-right coord(y) | Set to proper value |
| yclip_enable_0 | u1 | [\] | - | N/A | Luma clip enable control bit | Set to proper value |
| yclip_offset_0 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_gain_0 | s4.8 | [\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_boundary_0 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| cclip_enable_0 | u1 | [\] | - | N/A | Chroma clip enable control bit | Set to proper value |
| cclip_cmtx_0 | s2.5 | [2, 2\] | - | N/A | 2x2 chroma adjustment matrix | Set to proper value |
| cclip_boundary_0 | s8 | [2\] | - | N/A | 2x2 chroma adjustment --> clip | Set to proper value |
| crop_enable_0 | u1 | [\] | - | N/A | Crop enable control bit | Set to proper value |
| crop_start_0 | u13 | [2\] | - | N/A | Crop start coordinates | Set to proper value |
| crop_size_0 | u13 | [2\] | - | N/A | Crop size | Set to proper value |
| lba_enable_0 | u1 | [\] | - | N/A | lba enable control bit | Set to proper value |
| lba_exd_size_0 | u13 | [4\] | - | N/A | lba extension size(paddings on edges) | Set to proper value |
| lba_background_0 | s8 | [3\] | - | N/A | lba background color | Set to proper value |
| scale_input_mux_1 | u1 | [\] | - | N/A | Choose the input for scalar | 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output |
| scale_enable_1 | u1 | [\] | - | N/A | Scalar enable control bit | Set to proper value |
| scale_step_1 | u6.10 | [2\] | - | N/A | Scalar factor | Set to proper value |
| scale_output_size_1 | u13 | [2\] | - | N/A | Scalar output size | Set to proper value |
| scale_offset_luma_1 | s13.10 | [2\] | - | N/A | src_coords = dst_coords \* scale_factor + offset | Set to proper value |
| scale_offset_chroma_1 | s13.10 | [2\] | - | N/A | src_coords = dst_coords \* scale_factor + offset | Set to proper value |
| rect_enable_1 | u1 | [16\] | - | N/A | Rectangles' enable control bits | Set to proper value |
| rect_tl_coord_x_1 | u13 | [16\] | - | N/A | top-left x coordinates of rectangles | Set to proper value |
| rect_tl_coord_y_1 | u13 | [16\] | - | N/A | top-left y coordinates of rectangles | Set to proper value |
| rect_br_coord_x_1 | u13 | [16\] | - | N/A | bottom-right x coordinates of rectangles | Set to proper value |
| rect_br_coord_y_1 | u13 | [16\] | - | N/A | bottom-right y coordinates of rectangles | Set to proper value |
| rect_edge_thickness_1 | u8 | [\] | - | N/A | Rectangles' edge thickness | Set to proper value |
| rect_edge_color_select_1 | u2 | [16\] | - | N/A | Rectangles' color select | Set to proper value |
| rect_edge_color_val_1 | u8 | [4, 4\] | - | N/A | Rectangles' value to be selected | [m\][n\], m, 4 different kinds of colors. n, 0:y, 1:u, 2:v, 3:alpha |
| yclip_enable_1 | u1 | [\] | - | N/A | Luma clip enable control bit | Set to proper value |
| yclip_offset_1 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_gain_1 | s4.8 | [\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_boundary_1 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| cclip_enable_1 | u1 | [\] | - | N/A | Chroma clip enable control bit | Set to proper value |
| cclip_cmtx_1 | s2.5 | [2, 2\] | - | N/A | 2x2 chroma adjustment matrix | Set to proper value |
| cclip_boundary_1 | s8 | [2\] | - | N/A | 2x2 chroma adjustment --> clip | Set to proper value |
| crop_enable_1 | u1 | [\] | - | N/A | Crop enable control bit | Set to proper value |
| crop_start_1 | u13 | [2\] | - | N/A | Crop start coordinates | Set to proper value |
| crop_size_1 | u13 | [2\] | - | N/A | Crop size | Set to proper value |
| lba_enable_1 | u1 | [\] | - | N/A | lba enable control bit | Set to proper value |
| lba_exd_size_1 | u13 | [4\] | - | N/A | lba extension size(paddings on edges) | Set to proper value |
| lba_background_1 | s8 | [3\] | - | N/A | lba background color | Set to proper value |
| scale_input_mux_2 | u1 | [\] | - | N/A | Choose the input for scalar | 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output |
| scale_enable_2 | u1 | [\] | - | N/A | Scalar enable control bit | Set to proper value |
| scale_step_2 | u6.10 | [2\] | - | N/A | Scalar factor | Set to proper value |
| scale_output_size_2 | u13 | [2\] | - | N/A | Scalar output size | Set to proper value |
| scale_offset_luma_2 | s13.6 | [2\] | - | N/A | Luma channel initial phase | Set to proper value |
| scale_offset_chroma_2 | s13.6 | [2\] | - | N/A | Chroma channel initial phase | Set to proper value |
| scale_filter_x_luma_2 | s1.8 | [8, 5\] | - | N/A | 8-phase filtering weights for LUMA channel horizontal direction | Set to proper value |
| scale_filter_y_luma_2 | s1.8 | [8, 5\] | - | N/A | 8-phase filtering weights for LUMA channel vertical direction | Set to proper value |
| scale_filter_x_chroma_2 | s1.8 | [8, 3\] | - | N/A | 8-phase filtering weights for LUMA channel horizontal direction | Set to proper value |
| scale_filter_y_chroma_2 | s1.8 | [8, 3\] | - | N/A | 8-phase filtering weights for LUMA channel vertical direction | Set to proper value |
| rect_enable_2 | u1 | [16\] | - | N/A | Rectangles' enable control bits | Set to proper value |
| rect_tl_coord_x_2 | u13 | [16\] | - | N/A | top-left x coordinates of rectangles | Set to proper value |
| rect_tl_coord_y_2 | u13 | [16\] | - | N/A | top-left y coordinates of rectangles | Set to proper value |
| rect_br_coord_x_2 | u13 | [16\] | - | N/A | bottom-right x coordinates of rectangles | Set to proper value |
| rect_br_coord_y_2 | u13 | [16\] | - | N/A | bottom-right y coordinates of rectangles | Set to proper value |
| rect_edge_thickness_2 | u8 | [\] | - | N/A | Rectangles' edge thickness | Set to proper value |
| rect_edge_color_select_2 | u2 | [16\] | - | N/A | Rectangles' color select | Set to proper value |
| rect_edge_color_val_2 | u8 | [4, 4\] | - | N/A | Rectangles' value to be selected | [m\][n\], m, 4 different kinds of colors. n, 0:y, 1:u, 2:v, 3:alpha |
| yclip_enable_2 | u1 | [\] | - | N/A | Luma clip enable control bit | Set to proper value |
| yclip_offset_2 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_gain_2 | s4.8 | [\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_boundary_2 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| cclip_enable_2 | u1 | [\] | - | N/A | Chroma clip enable control bit | Set to proper value |
| cclip_cmtx_2 | s2.5 | [2, 2\] | - | N/A | 2x2 chroma adjustment matrix | Set to proper value |
| cclip_boundary_2 | s8 | [2\] | - | N/A | 2x2 chroma adjustment --> clip | Set to proper value |
| crop_enable_2 | u1 | [\] | - | N/A | Crop enable control bit | Set to proper value |
| crop_start_2 | u13 | [2\] | - | N/A | Crop start coordinates | Set to proper value |
| crop_size_2 | u13 | [2\] | - | N/A | Crop size | Set to proper value |
| lba_enable_2 | u1 | [\] | - | N/A | lba enable control bit | Set to proper value |
| lba_exd_size_2 | u13 | [4\] | - | N/A | lba extension size(paddings on edges) | Set to proper value |
| lba_background_2 | s8 | [3\] | - | N/A | lba background color | Set to proper value |
| scale_input_mux_3 | u1 | [\] | - | N/A | Choose the input for scalar | 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output |
| scale_enable_3 | u1 | [\] | - | N/A | Scalar enable control bit | Set to proper value |
| scale_step_3 | u6.10 | [2\] | - | N/A | Scalar factor | Set to proper value |
| scale_output_size_3 | u13 | [2\] | - | N/A | Scalar output size | Set to proper value |
| scale_offset_luma_3 | s13.10 | [2\] | - | N/A | src_coords = dst_coords \* scale_factor + offset | Set to proper value |
| scale_offset_chroma_3 | s13.10 | [2\] | - | N/A | src_coords = dst_coords \* scale_factor + offset | Set to proper value |
| rect_enable_3 | u1 | [16\] | - | N/A | Rectangles' enable control bits | Set to proper value |
| rect_tl_coord_x_3 | u13 | [16\] | - | N/A | top-left x coordinates of rectangles | Set to proper value |
| rect_tl_coord_y_3 | u13 | [16\] | - | N/A | top-left y coordinates of rectangles | Set to proper value |
| rect_br_coord_x_3 | u13 | [16\] | - | N/A | bottom-right x coordinates of rectangles | Set to proper value |
| rect_br_coord_y_3 | u13 | [16\] | - | N/A | bottom-right y coordinates of rectangles | Set to proper value |
| rect_edge_thickness_3 | u8 | [\] | - | N/A | Rectangles' edge thickness | Set to proper value |
| rect_edge_color_select_3 | u2 | [16\] | - | N/A | Rectangles' color select | Set to proper value |
| rect_edge_color_val_3 | u8 | [4, 4\] | - | N/A | Rectangles' value to be selected | [m\][n\], m, 4 different kinds of colors. n, 0:y, 1:u, 2:v, 3:alpha |
| yclip_enable_3 | u1 | [\] | - | N/A | Luma clip enable control bit | Set to proper value |
| yclip_offset_3 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_gain_3 | s4.8 | [\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_boundary_3 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| cclip_enable_3 | u1 | [\] | - | N/A | Chroma clip enable control bit | Set to proper value |
| cclip_cmtx_3 | s2.5 | [2, 2\] | - | N/A | 2x2 chroma adjustment matrix | Set to proper value |
| cclip_boundary_3 | s8 | [2\] | - | N/A | 2x2 chroma adjustment --> clip | Set to proper value |
| crop_enable_3 | u1 | [\] | - | N/A | Crop enable control bit | Set to proper value |
| crop_start_3 | u13 | [2\] | - | N/A | Crop start coordinates | Set to proper value |
| crop_size_3 | u13 | [2\] | - | N/A | Crop size | Set to proper value |
| lba_enable_3 | u1 | [\] | - | N/A | lba enable control bit | Set to proper value |
| lba_exd_size_3 | u13 | [4\] | - | N/A | lba extension size(paddings on edges) | Set to proper value |
| lba_background_3 | s8 | [3\] | - | N/A | lba background color | Set to proper value |
| scale_input_mux_4 | u1 | [\] | - | N/A | Choose the input for scalar | 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output |
| scale_enable_4 | u1 | [\] | - | N/A | Scalar enable control bit | Set to proper value |
| scale_step_4 | u6.10 | [2\] | - | N/A | Scalar factor | Set to proper value |
| scale_output_size_4 | u13 | [2\] | - | N/A | Scalar output size | Set to proper value |
| scale_offset_luma_4 | s13.10 | [2\] | - | N/A | src_coords = dst_coords \* scale_factor + offset | Set to proper value |
| scale_offset_chroma_4 | s13.10 | [2\] | - | N/A | src_coords = dst_coords \* scale_factor + offset | Set to proper value |
| yclip_enable_4 | u1 | [\] | - | N/A | Luma clip enable control bit | Set to proper value |
| yclip_offset_4 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_gain_4 | s4.8 | [\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_boundary_4 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| cclip_enable_4 | u1 | [\] | - | N/A | Chroma clip enable control bit | Set to proper value |
| cclip_cmtx_4 | s2.5 | [2, 2\] | - | N/A | 2x2 chroma adjustment matrix | Set to proper value |
| cclip_boundary_4 | s8 | [2\] | - | N/A | 2x2 chroma adjustment --> clip | Set to proper value |
| scale_input_mux_5 | u1 | [\] | - | N/A | Choose the input for scalar | 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output |
| scale_enable_5 | u1 | [\] | - | N/A | Scalar enable control bit | Set to proper value |
| scale_step_5 | u6.10 | [2\] | - | N/A | Scalar factor | Set to proper value |
| scale_output_size_5 | u13 | [2\] | - | N/A | Scalar output size | Set to proper value |
| scale_offset_luma_5 | s13.10 | [2\] | - | N/A | src_coords = dst_coords \* scale_factor + offset | Set to proper value |
| scale_offset_chroma_5 | s13.10 | [2\] | - | N/A | src_coords = dst_coords \* scale_factor + offset | Set to proper value |
| yclip_enable_5 | u1 | [\] | - | N/A | Luma clip enable control bit | Set to proper value |
| yclip_offset_5 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_gain_5 | s4.8 | [\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| yclip_boundary_5 | u8 | [2\] | - | N/A | -offset[0\] --> x gain --> +offset[1\] --> clip | Set to proper value |
| cclip_enable_5 | u1 | [\] | - | N/A | Chroma clip enable control bit | Set to proper value |
| cclip_cmtx_5 | s2.5 | [2, 2\] | - | N/A | 2x2 chroma adjustment matrix | Set to proper value |
| cclip_boundary_5 | s8 | [2\] | - | N/A | 2x2 chroma adjustment --> clip | Set to proper value |
| crop_enable_5 | u1 | [\] | - | N/A | Crop enable control bit | Set to proper value |
| crop_start_5 | u13 | [2\] | - | N/A | Crop start coordinates | Set to proper value |
| crop_size_5 | u13 | [2\] | - | N/A | Crop size | Set to proper value |
| lba_enable_5 | u1 | [\] | - | N/A | lba enable control bit | Set to proper value |
| lba_exd_size_5 | u13 | [4\] | - | N/A | lba extension size(paddings on edges) | Set to proper value |
| lba_background_5 | s8 | [3\] | - | N/A | lba background color | Set to proper value |
| csc_yuvTrgb_matrix_5 | s2.8 | [3, 3\] | - | N/A |  | Set to proper value |
| csc_yuvTrgb_offset_in_5 | s8 | [3\] | - | N/A | YUV to RGB conversion offset in. | Set to proper value |
| csc_yuvTrgb_offset_out_5 | s8 | [3\] | - | N/A | YUV to RGB conversion offset out. | Set to proper value |
| csc_gamma_lut_5 | u8.4 | [33\] | - | N/A | To LAB csc gamma lut | Set to proper value |
| csc_gamma_lut_mode_5 | u1 | [\] | - | N/A | 0 - linear lut; 1 - log lut | Set to proper value |
| csc_gamma_offset_out_5 | s8 | [\] | - | N/A | gamma offset out. | Set to proper value |
| csc_rgbTxyz_matrix_5 | s2.13 | [3, 3\] | - | N/A |  | Set to proper value |
| csc_rgbTxyz_offset_out_5 | s8 | [3\] | - | N/A | rgb2xyz offset out. | Set to proper value |
| csc_xyzTlab_cbrt_lut_5 | u0.16 | [129\] | - | N/A | xyz2lab cube root lut. | Set to proper value |
| csc_xyzTlab_gain_out_5 | s3.12 | [3\] | - | N/A | xyz2lab gain out. | Set to proper value |
| csc_xyzTlab_offset_out_5 | s8 | [3\] | - | N/A | xyz2lab offset out. | Set to proper value |
| output_format | u6 | [6\] | - | N/A | Output formats | Set to proper value |

