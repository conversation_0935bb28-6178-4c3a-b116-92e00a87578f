{"partition_configs": [], "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "Y2DNR is 0x2226", "type": "AX_U16", "default": "0x2226"}}, "params": {"enable": {"display": "enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "0:disable, 1:enable", "hidden": 0, "auto": 0, "target_conf": ["y_2dnr.enable"], "type": "AX_U8", "dependency": ""}, "pre_debug_enable": {"display": "pre debug enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "interact with pre module debug mode, if pre module debug enable, then disable y2dnr module. 0:disable, 1:enable", "hidden": 1, "auto": 0, "target_conf": ["y_2dnr.enable"], "type": "AX_U8", "dependency": "common"}, "debug_mode": {"display": "debug mode", "acc": [0, 4], "size": [], "range": [0, 8], "default": 0, "comment": "debug mode, 0: normal, 1: motion mask, 2: bp mask, 3:flat weight, 4:detail weight, 5:edge weight, 6:var, 7:sf1 out, 8:sf2 out", "hidden": 1, "auto": 0, "target_conf": ["y_2dnr.debug_mode"], "type": "AX_U8", "dependency": "common"}, "debug_gain": {"display": "debug gain", "acc": [0, 3], "size": [], "range": [0, 7], "default": 0, "comment": "debug mode gain for visualization", "hidden": 1, "auto": 0, "target_conf": ["y_2dnr.debug_gain"], "type": "AX_U8", "dependency": "common"}, "motion_enable": {"display": "motion enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "motion enable, 0:disable, 1:enable", "hidden": 0, "auto": 0, "target_conf": ["y_2dnr.motion_mask_enable"], "type": "AX_U8", "dependency": ""}, "dir_strength": {"display": "dir strength", "acc": [0, 0, 4], "size": [], "range": [0.0, 0.9375], "default": 0.75, "comment": "direction detection strength", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.dir_det_ud_scale", "y_2dnr.dir_det_dir_scale"], "type": "AX_U8", "dependency": ""}, "dir_base": {"display": "dir base", "acc": [0, 8, 4], "size": [], "range": [0.0, 255.9375], "default": 32.0, "comment": "base noise for direciton detection", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.dir_det_edge_noise"], "type": "AX_U16", "dependency": ""}, "dpc_enable": {"display": "dpc enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "dpc enable, 0:disable, 1:enable", "hidden": 0, "auto": 0, "target_conf": ["y_2dnr.bpc_en"], "type": "AX_U8", "dependency": ""}, "dp_det_mode": {"display": "dp detection mode", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "dp detection mode, 0:signgle, 1:dual", "hidden": 0, "auto": 0, "target_conf": ["y_2dnr.bp_det_mode"], "type": "AX_U8", "dependency": ""}, "dp_det_strength": {"display": "dp detection strength", "acc": [0, 4, 4], "size": [], "range": [0.0, 15.9375], "default": 1.0, "comment": "dp detection strength", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.bp_det_m1"], "type": "AX_U8", "dependency": ""}, "dp_strength": {"display": "dp strength", "acc": [0, 4, 8], "size": [], "range": [0.0, 15.99609375], "default": 1.0, "comment": "dp strength", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.bp_det_m2"], "type": "AX_U16", "dependency": ""}, "dp_noise_level": {"display": "dp noise level", "acc": [0, 8], "size": [], "range": [0, 255], "default": 20, "comment": "dp noise level", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.bp_noise_strength"], "type": "AX_U8", "dependency": ""}, "dp_noise_strength": {"display": "dp noise gain", "acc": [0, 0, 8], "size": [9], "range": [0.0, 0.99609375], "default": [0.99609375, 0.99609375, 0.99609375, 0.99609375, 0.99609375, 0.99609375, 0.99609375, 0.99609375, 0.99609375], "comment": "dp noise gain", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.bp_noise_strength"], "type": "AX_U8", "dependency": ""}, "dp_detail_gain": {"display": "dp detail gain", "acc": [0, 0, 8], "size": [9], "range": [0.0, 0.99609375], "default": 0.99609375, "comment": "dp detail gain", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.bp_detail_lut"], "type": "AX_U8", "dependency": ""}, "dir_det_cor_str_fg": {"display": "dir_det_cor_str_fg", "acc": [0, 8, 2], "size": [2], "range": [0.0, 255.75], "default": [0.0, 0.25], "comment": "direction detection correction strength for foreground", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.dir_det_var_w_thr", "y_2dnr.dir_det_var_w_slope"], "type": "AX_U16", "dependency": ""}, "dir_det_cor_str_bg": {"display": "dir_det_cor_str_bg", "acc": [0, 8, 2], "size": [2], "range": [0.0, 255.75], "default": [0.0, 0.25], "comment": "direction detection correction strength for background", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.dir_det_var_w_thr", "y_2dnr.dir_det_var_w_slope"], "type": "AX_U16", "dependency": ""}, "detail_det_str_fg": {"display": "detail_det_str_fg", "acc": [0, 8, 2], "size": [2], "range": [0.0, 255.75], "default": [0.0, 0.25], "comment": "detail detection strength for foreground", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.detail_w_thr", "y_2dnr.detail_w_slope"], "type": "AX_U16", "dependency": ""}, "detail_det_str_bg": {"display": "detail_det_str_bg", "acc": [0, 8, 2], "size": [2], "range": [0.0, 255.75], "default": [0.0, 0.25], "comment": "detail detection strength for background", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.detail_w_thr", "y_2dnr.detail_w_slope"], "type": "AX_U16", "dependency": ""}, "sf1_str": {"display": "sf1_str", "acc": [0, 1, 8], "size": [], "range": [0.0, 1.0], "default": 1.0, "comment": "sf1 strength, the larger the value, the more nosie reduction", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_str"], "type": "AX_U16", "dependency": ""}, "sf1_local_str": {"display": "sf1_local_str", "acc": [0, 0, 8], "size": [17], "range": [0.0, 0.99609375], "default": 0.99609375, "comment": "sf1 local strength, the larger the value, the more nosie reduction", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_str"], "type": "AX_U8", "dependency": ""}, "sf1_style_adjust": {"display": "sf1_style_adjust", "acc": [0, 8], "size": [], "range": [0, 255], "default": 100, "comment": "sf1 style adjust, the larger the value, the more nosie reduction, also may be more annoying patterns", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_dis_coef"], "type": "AX_U8", "dependency": ""}, "sf1_dir_str_scale": {"display": "sf1_dir_str_scale", "acc": [0, 1, 4], "size": [], "range": [0.0, 1.0], "default": 1.0, "comment": "sf1 direction strength scale, the larger the value, the more direction noise reduction result used", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_dir_str_scale"], "type": "AX_U8", "dependency": ""}, "sf1_dir_str": {"display": "sf1_dir_str", "acc": [0, 8, 2], "size": [2], "range": [0.0, 255.75], "default": [2.0, 10.0], "comment": "sf1 direction strength, the larger the value, the more noise reduction", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_w_thr", "y_2dnr.nlm_w_slope"], "type": "AX_U16", "dependency": ""}, "sf1_detail_str": {"display": "sf1_detail_str", "acc": [0, 8, 2], "size": [2], "range": [0.0, 255.75], "default": [2.0, 10.0], "comment": "sf1 detail strength, the larger the value, the more noise reduction", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_w_thr", "y_2dnr.nlm_w_slope"], "type": "AX_U16", "dependency": ""}, "sf1_flat_str": {"display": "sf1_flat_str", "acc": [0, 8, 2], "size": [2], "range": [0.0, 255.75], "default": [2.0, 10.0], "comment": "sf1 flat strength, the larger the value, the more noise reduction", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_w_thr", "y_2dnr.nlm_w_slope"], "type": "AX_U16", "dependency": ""}, "sf2_str": {"display": "sf2_str", "acc": [0, 1, 8], "size": [], "range": [0.0, 1.0], "default": 1.0, "comment": "sf2 strength, the larger the value, the more nosie reduction", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.bf_str"], "type": "AX_U16", "dependency": ""}, "sf2_local_str": {"display": "sf2_local_str", "acc": [0, 0, 8], "size": [17], "range": [0.0, 0.99609375], "default": 0.99609375, "comment": "sf2 local strength, the larger the value, the more nosie reduction", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.bf_str"], "type": "AX_U8", "dependency": ""}, "sf2_style_adjust": {"display": "sf2_style_adjust", "acc": [0, 8], "size": [], "range": [0, 255], "default": 100, "comment": "sf2 style adjust, the larger the value, the more nosie reduction, also may be more annoying patterns", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.bf_dis_coef"], "type": "AX_U8", "dependency": ""}, "sf2_dir_str": {"display": "sf2_dir_str", "acc": [0, 8, 2], "size": [2], "range": [0.0, 255.75], "default": [2.0, 10.0], "comment": "sf2 direction strength, the larger the value, the more noise reduction", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.bf_w_thr", "y_2dnr.bf_w_slope"], "type": "AX_U16", "dependency": ""}, "sf2_detail_str": {"display": "sf2_detail_str", "acc": [0, 8, 2], "size": [2], "range": [0.0, 255.75], "default": [2.0, 10.0], "comment": "sf2 detail strength, the larger the value, the more noise reduction", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.bf_w_thr", "y_2dnr.bf_w_slope"], "type": "AX_U16", "dependency": ""}, "sf2_flat_str": {"display": "sf2_flat_str", "acc": [0, 8, 2], "size": [2], "range": [0.0, 255.75], "default": [2.0, 10.0], "comment": "sf2 flat strength, the larger the value, the more noise reduction", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.bf_w_thr", "y_2dnr.bf_w_slope"], "type": "AX_U16", "dependency": ""}, "edge_style_adjust_fg": {"display": "edge_style_adjust_fg", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.99609375, "comment": "edge style adjust for foreground, the larger the value, the smoother the edge, the smaller the vaule, the more annoying pixel along edge", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_bf_w"], "type": "AX_U8", "dependency": ""}, "detail_style_adjust_fg": {"display": "detail_style_adjust_fg", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.75, "comment": "detail style adjust for foreground, the larger the value, maybe the more annoying patterns in detail region", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_bf_w"], "type": "AX_U8", "dependency": ""}, "flat_style_adjust_fg": {"display": "flat_style_adjust_fg", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.5, "comment": "flat style adjust for foreground, the larger the value, maybe the more annoying patterns in flat region", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_bf_w"], "type": "AX_U8", "dependency": ""}, "edge_style_adjust_bg": {"display": "edge_style_adjust_bg", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.99609375, "comment": "edge style adjust for background, the larger the value, the smoother the edge, the smaller the vaule, the more annoying pixel along edge", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_bf_w"], "type": "AX_U8", "dependency": ""}, "detail_style_adjust_bg": {"display": "detail_style_adjust_bg", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.75, "comment": "detail style adjust for background, the larger the value, maybe the more annoying patterns in detail region", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_bf_w"], "type": "AX_U8", "dependency": ""}, "flat_style_adjust_bg": {"display": "flat_style_adjust_bg", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.5, "comment": "flat style adjust for background, the larger the value, maybe the more annoying patterns in flat region", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.nlm_bf_w"], "type": "AX_U8", "dependency": ""}, "luma_gain_fg": {"display": "luma_gain_fg", "acc": [0, 9, 2], "size": [17], "range": [0.0, 511.75], "default": 256.0, "comment": "luma adjust strength for foreground, the larger the value, more noise reduciton in corresponding luma region", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.luma_thr_bias_lut"], "type": "AX_U16", "dependency": ""}, "luma_gain_bg": {"display": "luma_gain_bg", "acc": [0, 9, 2], "size": [17], "range": [0.0, 511.75], "default": 256.0, "comment": "luma adjust strength for background, the larger the value, more noise reduciton in corresponding luma region", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.luma_thr_bias_lut"], "type": "AX_U16", "dependency": ""}, "luma_gain_fg_ex": {"display": "luma_gain_fg_ex", "acc": [0, 3, 8], "size": [17], "range": [0.0, 7.99609375], "default": 4.0, "comment": "extension luma adjust strength for foreground, the larger the value, more noise reduciton in corresponding luma region, used when luma_gain_fg is not enough", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.luma_slope_bias_lut"], "type": "AX_U16", "dependency": ""}, "luma_gain_bg_ex": {"display": "luma_gain_bg_ex", "acc": [0, 3, 8], "size": [17], "range": [0.0, 7.99609375], "default": 4.0, "comment": "extension luma adjust strength for background, the larger the value, more noise reduciton in corresponding luma region, used when luma_gain_bg is not enough", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.luma_slope_bias_lut"], "type": "AX_U16", "dependency": ""}, "motion_lut": {"display": "motion_lut", "acc": [0, 0, 8], "size": [9], "range": [0.0, 0.99609375], "default": [0.0, 0.125, 0.25, 0.375, 0.5, 0.625, 0.75, 0.875, 0.99609375], "comment": "motion mapping lut", "hidden": 0, "auto": 1, "target_conf": ["y_2dnr.motion_lut"], "type": "AX_U8", "dependency": ""}, "dither_seed_enable": {"display": "", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "0: use stat dither_seed, 1: use conf dither_seed. set 1 for the 1st frame, then set 0 from the 2nd frame onwards.", "hidden": 1, "auto": 0, "target_conf": ["y_2dnr.dither_seed_enable"], "type": "AX_U8", "dependency": "common"}}, "submodules": {"setup": {"params": [], "configs": ["y_2dnr.dither_enable", "y_2dnr.dither_pmask"]}, "top_control": {"params": ["enable", "motion_enable", "dpc_enable", "pre_debug_enable"], "configs": ["y_2dnr.enable", "y_2dnr.motion_mask_enable", "y_2dnr.bpc_en"]}, "debug_mode": {"params": ["debug_mode", "debug_gain"], "configs": ["y_2dnr.debug_mode", "y_2dnr.debug_gain"]}, "feature": {"params": ["dir_strength", "dir_base", "dir_det_cor_str_fg", "dir_det_cor_str_bg", "detail_det_str_fg", "detail_det_str_bg"], "configs": ["y_2dnr.dir_det_ud_scale", "y_2dnr.dir_det_dir_scale", "y_2dnr.dir_det_edge_noise", "y_2dnr.dir_det_var_w_thr", "y_2dnr.dir_det_var_w_slope", "y_2dnr.detail_w_thr", "y_2dnr.detail_w_slope"]}, "dpc": {"params": ["dp_det_mode", "dp_det_strength", "dp_strength", "dp_noise_level", "dp_noise_strength", "dp_detail_gain"], "configs": ["y_2dnr.bp_det_mode", "y_2dnr.bp_det_m1", "y_2dnr.bp_det_m2", "y_2dnr.bp_noise_strength", "y_2dnr.bp_detail_lut"]}, "sf1": {"params": ["sf1_str", "sf1_local_str", "sf1_style_adjust", "sf1_dir_str_scale", "sf1_dir_str", "sf1_detail_str", "sf1_flat_str"], "configs": ["y_2dnr.nlm_str", "y_2dnr.nlm_dis_coef", "y_2dnr.nlm_dir_str_scale", "y_2dnr.nlm_w_thr", "y_2dnr.nlm_w_slope"]}, "sf2": {"params": ["sf2_str", "sf2_local_str", "sf2_style_adjust", "sf2_dir_str", "sf2_detail_str", "sf2_flat_str"], "configs": ["y_2dnr.bf_str", "y_2dnr.bf_dis_coef", "y_2dnr.bf_w_thr", "y_2dnr.bf_w_slope"]}, "filter_style": {"params": ["edge_style_adjust_fg", "detail_style_adjust_fg", "flat_style_adjust_fg", "edge_style_adjust_bg", "detail_style_adjust_bg", "flat_style_adjust_bg"], "configs": ["y_2dnr.nlm_bf_w"]}, "luma_str": {"params": ["luma_gain_fg", "luma_gain_bg", "luma_gain_fg_ex", "luma_gain_bg_ex"], "configs": ["y_2dnr.luma_thr_bias_lut", "y_2dnr.luma_slope_bias_lut"]}, "motion": {"params": ["motion_lut"], "configs": ["y_2dnr.motion_lut"]}, "dither": {"params": ["dither_seed_enable"], "configs": ["y_2dnr.dither_seed"]}}, "target_module": {"mc20l": {"y_2dnr": {"id": 5600, "method": 0}}}, "structs": {}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [12], "ref_interp_method": ["linear"]}}, "constraints": ["dir_det_cor_str_fg[0] < dir_det_cor_str_fg[1]", "dir_det_cor_str_bg[0] < dir_det_cor_str_bg[1]", "detail_det_str_fg[0] < detail_det_str_fg[1]", "detail_det_str_bg[0] < detail_det_str_bg[1]", "sf1_dir_str[0] < sf1_dir_str[1]", "sf1_detail_str[0] < sf1_detail_str[1]", "sf1_flat_str[0] < sf1_flat_str[1]", "sf2_dir_str[0] < sf2_dir_str[1]", "sf2_detail_str[0] < sf2_detail_str[1]", "sf2_flat_str[0] < sf2_flat_str[1]"], "configs": {"y_2dnr": {"enable": {"acc": [0, 1], "size": [], "description": "y_2dnr bypass/enable, 0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "debug_mode": {"acc": [0, 4], "size": [], "description": "debug mode, 0: normal, 1: motion mask, 2: bp mask, 3:flat weight, 4:detail weight, 5:edge weight, 6:var, 7:nlm out, 8:bf out", "usage": "", "constraints": "[0~8]", "type": "AX_U8", "partition": "-"}, "debug_gain": {"acc": [0, 3], "size": [], "description": "debug bit shift for data visualization", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "motion_mask_enable": {"acc": [0, 1], "size": [], "description": "y_2dnr motion mask, 0: disable, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "dither_enable": {"acc": [0, 1], "size": [], "description": "enable dither or not, 0:disable 1:enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "dither_pmask": {"acc": [0, 16], "size": [2], "description": "dither mask", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "dither_seed": {"acc": [0, 16], "size": [2], "description": "dither seed", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "dither_seed_enable": {"acc": [0, 1], "size": [], "description": "dither seed enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "dir_det_ud_scale": {"acc": [0, 0, 4], "size": [], "description": "non dir scale for dir detection", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "dir_det_dir_scale": {"acc": [0, 0, 4], "size": [], "description": "dir scale for dir detection", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "dir_det_edge_noise": {"acc": [0, 8, 4], "size": [], "description": "edge base for dir detection", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "bp_det_mode": {"acc": [0, 1], "size": [], "description": "two loacl dif method for bad pixel detection, 0:max-min mode,1:max2-min2 mode", "usage": "the larger the value, more bad pixels will be detected", "constraints": "", "type": "AX_U8", "partition": "-"}, "bp_det_m1": {"acc": [0, 4, 4], "size": [], "description": "local diff gain for bad pixel detection", "usage": "the larger the value, less bad pixels will be detected", "constraints": "", "type": "AX_U8", "partition": "-"}, "bp_det_m2": {"acc": [0, 4, 8], "size": [], "description": "bad pixel probability gain, the larger the bad pixel probability", "usage": "the larger the value, the larger the bad pixel probability and more pixel will be corrected", "constraints": "", "type": "AX_U16", "partition": "-"}, "bp_noise_strength": {"acc": [0, 8], "size": [9], "description": "noise str for bad pixel detection", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "bp_detail_lut": {"acc": [0, 0, 8], "size": [9], "description": "bad pixel modify str lut", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "dir_det_var_w_thr": {"acc": [0, 8, 2], "size": [2], "description": "thr for flat region dir modify curve", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "dir_det_var_w_slope": {"acc": [0, 2, 8], "size": [2], "description": "slope for flat region dir modify curve", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "detail_w_thr": {"acc": [0, 8, 2], "size": [2], "description": "thr for detail detection curve", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "detail_w_slope": {"acc": [0, 2, 8], "size": [2], "description": "slope for detail detection curve", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "bpc_en": {"acc": [0, 1], "size": [], "description": "replace center bp pixel for nlm and bf filter, 0:disable, 1:enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "nlm_w_thr": {"acc": [0, 8, 2], "size": [3], "description": "nlm thr for dif weight curve, [0-2] represents edge, detail flat separately", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "nlm_w_slope": {"acc": [0, 2, 8], "size": [3], "description": "nlm slope for dif weight curve, [0-2] represents edge, detail flat separately", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "nlm_dis_coef": {"acc": [0, 1, 8], "size": [6], "description": "nlm dis weight", "usage": "", "constraints": "[0] + 4*[1] + 4*[2] + 4*[3] + 8*[4] + 4*[5] == 1.0", "type": "AX_U16", "partition": "-"}, "bf_w_thr": {"acc": [0, 8, 2], "size": [3], "description": "bilateral thr for dif weight curve, [0-2] represents edge, detail flat separately", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "bf_w_slope": {"acc": [0, 2, 8], "size": [3], "description": "bilateral slope for dif weight curve, [0-2] represents edge, detail flat separately", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "bf_dis_coef": {"acc": [0, 1, 8], "size": [10], "description": "bilateral dis weight", "usage": "", "constraints": "[0] + 4*[1] + 4*[2] + 4*[3] + 8*[4] + 4*[5] + 4*[6] +8*[7] + 8*[8] + 4*[9]== 1.0", "type": "AX_U16", "partition": "-"}, "nlm_bf_w": {"acc": [0, 0, 8], "size": [6], "description": "nlm and bilateral blending weight, [0-2] represents foreground edge, detail flat separately, [3-5] represents background edge, detail flat separately", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "nlm_dir_str_scale": {"acc": [0, 1, 4], "size": [], "description": "nlm dir str", "usage": "", "constraints": "[0.0, 1.0]", "type": "AX_U8", "partition": "-"}, "nlm_str": {"acc": [0, 0, 8], "size": [17], "description": "nlm global str lut", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "bf_str": {"acc": [0, 0, 8], "size": [17], "description": "bilateral global str lut", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "luma_thr_bias_lut": {"acc": [1, 8, 2], "size": [2, 17], "description": "luma thr bias for nr, 0: foreground, 1: background", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "luma_slope_bias_lut": {"acc": [1, 2, 8], "size": [2, 17], "description": "luma slope bias for nr, 0: foreground, 1: background", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "motion_lut": {"acc": [0, 0, 8], "size": [9], "description": "motion lut", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}}}}