# 自动生成脚本使用方法

## 脚本位置
![](../assets/imgs/script_dir.jpg)
脚本位于根目录下的scripts路径下，基于之前的脚本进行修改而成

## 调用命令
```
python3 gen_module.py aaa --an-id bbbb --param-file ./params_base_json.json
python3 gen_module.py aaa --an-id bbbb --param-file ./param_base.json --update
python3 gen_module.py aaa --an-id bbbb --param-file ./param_base.json --no-sync
python3 gen_module.py aaa --an-id bbbb --param-file ./param_base.json --no-sync --update
```

如上所示，调用脚本时，需要指定模块名，以及对应的an-id，同时需要指定参数文件的路径。

如果文件名缺省，则默认`param_base.json`的路径是`./docs/bbbb-aaa/param_base.json`。

第一次调用上述命令生成空的algo logic代码时，可以不带`--update`。如果已经完成了algo logic的代码，需要重新刷新接口或者文档，需要加上`--update`。

如果不需要从xyclops获取config，则可以加上`--no-sync`，原则上不推荐这样做。