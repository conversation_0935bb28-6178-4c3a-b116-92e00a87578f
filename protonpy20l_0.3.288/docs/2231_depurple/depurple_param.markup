h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| offset_in |  | u8.4 | AX_U16 | [\] |  [0, 4095\] | [0.0, 255.9375\] | 256 | 16.0 | hidden | 'pfd.offset_in' | offset_in | common |
| partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'pfd.mesh_start', 'pfd.internal_start', 'pfd.pos_start', 'pfd.mesh_length', 'pfd.inv_mesh_length' | partition information | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| det_enable | det enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'pfd.mask_coef' | software enable for pfd | user | None | None | None |
| det_c_ratio | color ratio | s1.6 | AX_S8 | [4\] | [-64, 64\] | [-1.0, 1.0\] | [-32, 0, 0, 32\] | [-0.5, 0.0, 0.0, 0.5\] | open | 'pfd.c_ratio' | weight for computr color channel using bayer | user | None | None | None |
| det_adj_y_gam | pfd adj y channel with gamma | u3.5 | AX_U8 | [\] | [0, 224\] | [0.0, 7.0\] | 70 | 2.1875 | open | 'pfd.gamma_lut' | for gamma correction to meet with decent gam | user | None | None | None |
| det_edge_c_enable | det c enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'pfd.detedge_slope', 'pfd.detedge_offset' | for color channel edge offset | user | None | None | None |
| det_edge_slope_y | edge slope y | u1.6 | AX_U8 | [\] | [0, 64\] | [0.0, 1.0\] | 20 | 0.3125 | open | 'pfd.detedge_slope' | for luma channel edge gain | user | None | None | None |
| det_edge_offset_y | edge offset y | s1.6 | AX_S8 | [\] | [-64, 64\] | [-1.0, 1.0\] | -50 | -0.78125 | open | 'pfd.detedge_offset' | for luma channel edge offset | user | None | None | None |
| det_edge_slope_c | edge slope c | u1.6 | AX_U8 | [\] | [0, 64\] | [0.0, 1.0\] | 20 | 0.3125 | open | 'pfd.detedge_slope' | for color channel edge gain | user | None | None | None |
| det_edge_offset_c | edge offset c | s1.6 | AX_S8 | [\] | [-64, 64\] | [-1.0, 1.0\] | -50 | -0.78125 | open | 'pfd.detedge_offset' | for color channel edge offset | user | None | None | None |
| det_seledge_slope_y | edge y slope for compensate luma | u1.8 | AX_U16 | [\] | [0, 256\] | [0.0, 1.0\] | 20 | 0.078125 | open | 'pfd.seledge_slope_y' | for compensate luma with slope for | user | None | None | None |
| det_seledge_thr_y | edge y thr for compensate luma | u8.6 | AX_U16 | [\] | [0, 16383\] | [0.0, 255.984375\] | 9600 | 150.0 | open | 'pfd.seledge_thr_y_mesh' | for luma compensation when detect edge, used to compute for mesh-wisely | user | ['gain/lux'\] | [16\] | ['linear'\] |
| det_seledge_thr_y_mesh | location y_thr mesh | u1.7 | AX_U8 | [17, 17\] | [0, 128\] | [0.0, 1.0\] | [[128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\], [128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128, 128\]\] | [[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\]\] | open | 'pfd.mesh' | location wise strength adjust for edge compensation | user | None | None | None |
| det_seledge_weight | edge detection weight | u1.6 | AX_U8 | [7\] | [0, 64\] | [0.0, 1.0\] | [64, 0, 0, 0, 0, 0, 0\] | [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0\] | open | 'pfd.seledge_dist_weight' | edge detection weight | user | None | None | None |
| det_mask_strength | det strength | u3.6 | AX_U16 | [\] | [0, 511\] | [0.0, 7.984375\] | 64 | 1.0 | open | 'pfd.mask_coef' | strength adjust for mask | user | ['gain/lux'\] | [16\] | ['linear'\] |
| det_mask_dialate_enable | det mask dialate enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'pfd.mask_dialate_enable' | dialate mask enable | user | None | None | None |
| det_mask_dialate_weight | det mask dialate weight | u1.6 | AX_U8 | [7\] | [0, 64\] | [0.0, 1.0\] | [127, 0, 0, 0, 0, 0, 0\] | [1.984375, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0\] | open | 'pfd.mask_dist_weight' | dialate weight for mask | user | None | None | None |
| det_adj_mesh_enable | adjust mesh enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'pfd.mesh_adj_mask_enable' | for enable locationwise strength adjustment | user | None | None | None |
| det_adj_mesh | location strength adj | u2.6 | AX_U8 | [17, 17\] | [0, 255\] | [0.0, 3.984375\] | [[64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\], [64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64, 64\]\] | [[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0\]\] | open | 'pfd.mesh' | mesh for adjust depurple strength | user | None | None | None |
| desat_enable | desat enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'lce.desat_enable' | desat_enable | user | None | None | None |
| desat_strength | desat strength | u4.4 | AX_U8 | [\] | [0, 240\] | [0.0, 15.0\] | 32 | 2.0 | open | 'lce.desat_strength' | desat_strength | user | ['gain/lux'\] | [16\] | ['linear'\] |
| desat_luma_lut | desaturation luma ratio lut | u1.7 | AX_U8 | [8\] | [0, 128\] | [0.0, 1.0\] | [0, 0, 0, 6, 100, 115, 128, 128\] | [0.0, 0.0, 0.0, 0.046875, 0.78125, 0.8984375, 1.0, 1.0\] | open | 'lce.desat_luma_lut' | desat_luma_lut | user | None | None | None |
| desat_sat_lut | desaturation saturation ratio lut | u1.7 | AX_U8 | [6\] | [0, 128\] | [0.0, 1.0\] | [0, 25, 64, 100, 128, 128\] | [0.0, 0.1953125, 0.5, 0.78125, 1.0, 1.0\] | open | 'lce.desat_sat_lut' | desat_sat_lut | user | None | None | None |
| desat_angle_ratio_lut | desaturation angle ratio lut | u1.7 | AX_U8 | [16\] | [0, 128\] | [0.0, 1.0\] | [128, 128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 128, 128\] | [1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0\] | open | 'lce.desat_angle_ratio_lut' | calculate 16 phase ratio | user | ['gain/lux'\] | [16\] | ['linear'\] |
| desat_uv_val | desaturation uv value | s7.2 | AX_S16 | [2\] | [-511, 511\] | [-127.75, 127.75\] | [0, 0\] | [0.0, 0.0\] | open | 'lce.desat_uv_val' | depurple aera fill color with UV | user | None | None | None |
| desat_debug_enable | debug enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'lce.desat_debug_enable' | desat debug enable | user | None | None | None |
| desat_debug_thr | debug thr | u1.4 | AX_U8 | [\] | [0, 16\] | [0.0, 1.0\] | 0 | 0.0 | open | 'lce.desat_debug_thr' | debug depurple threshold for showing debug info | user | None | None | None |
| desat_debug_color | debug color | s8.2 | AX_S16 | [3\] | [-1023, 1023\] | [-255.75, 255.75\] | [0, 0, 0\] | [0.0, 0.0, 0.0\] | open | 'lce.desat_debug_color' | debug mode's color fill for depurple aera with YUV | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |