{"enable": {"api": "nBlcEn", "display": "enable", "comments": "soft enable switch for the entire module", "hint": "Accuracy: U1.0 Range: [0, 1]", "out_of_manual": 1}, "lin_enable": {"api": "nLinEn", "display": "linEnable", "comments": "0: disable, 1: enable", "hint": "Accuracy: U1.0 Range: [0, 1]", "out_of_manual": 1}, "lin_lut_x": {"api": "nLinLutX", "display": "linLutX", "comments": "x coordinate of lin lut, assume there is a leading zero point by default", "hint": "Accuracy: U8.4 Range: [16, 4095]"}, "lin_lut_y": {"api": "nLinLutY", "display": "linLutY", "comments": "y coordinate of lin lut, assume there is a leading zero point by default", "hint": "Accuracy: U8.6 Range: [64, 16383]"}, "sbl_corr_enable": {"api": "nSblEnable", "display": "sblCorrEnable", "comments": "sbl corr enable", "hint": "Accuracy: U1.0 Range: [0, 1]", "out_of_manual": 1}, "sbl_val": {"api": "nSblVal", "display": "sblVal", "comments": "auto param, interpolation based on exposure time and gain", "hint": "Accuracy: U8.10 Range: [0, 262143]"}, "gbl_corr_enable": {"api": "nGblEnable", "display": "gblCorrEnable", "comments": "gbl corr enable", "hint": "Accuracy: U1.0 Range: [0, 1]", "out_of_manual": 1}, "gbl_val": {"api": "nGblVal", "display": "gblVal", "comments": "auto param, interpolation based on exposure time and gain", "hint": "Accuracy: S7.10 Range: [-131072, 131071]"}, "det_enable": {"api": "nDetEnable", "display": "detEnable", "comments": "det enable, 0: disable, 1: enable", "hint": "Accuracy: U1.0 Range: [0, 1]", "out_of_manual": 1}, "bpc_det_th": {"api": "nDpcDetTh", "display": "bpcDetTh", "comments": "threshold for quickly checking whether the current point is a bad point", "hint": "Accuracy: U8.10 Range: [0, 262143]"}, "sdk_special_config": {"blc": {"manual_num": 2, "hcglcg_auto": 1}}}