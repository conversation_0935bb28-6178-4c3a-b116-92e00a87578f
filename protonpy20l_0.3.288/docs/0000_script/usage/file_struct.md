# 从0开始创建Algo Logic需要哪些文件？

新建一个名为`aaa`的模块的Algo Logic需要增改的文件（在上述repo的根目录下）

路径|文件名|增改|描述|自动生成
---|---|---|---|---
src/algo/aaa|_aaa_private.h|new|aaa模块的algo logic|YES
src/algo/aaa|aaa_api.c|new|aaa模块的algo logic|YES
src/algo/aaa|aaa_impl.c|new|aaa模块的algo logic|NO
src/include/proton_algo|aaa.h|new|aaa模块的algo logic接口定义|YES
src/include/proton_algo|proton_algo.h|modify|增加aaa模块的algo logic定义|YES
src/python|wrapper.cpp|modify|增加aaa模块的python wrapper|YES
src/python|wrapper.hpp|modify|增加aaa模块的python wrapper|YES
src/python/modules|aaa.cpp|new|定义aaa模块的python wrapper|YES
ctests/aaa|CMakeLists.txt|new|aaa模块algo logic的c测试文件|YES
ctests/aaa|test_aaa.cpp|new|aaa模块algo logic的c测试文件|NO
pytests/|test_aaa.py|new|aaa模块algo logic的python测试文件|NO
docs/aaa|aaa_config.mu|new|aaa模块的寄存器接口定义文档|YES
docs/aaa|aaa_param.mu|new|aaa模块的Tuning接口定义文档|YES
samples/aaa|__main__.py|new|aaa模块的主观测试代码|NO
samples/aaa|data_utils.py|new|aaa模块的主观测试代码|NO
samples/aaa|logic.py|new|aaa模块的主观测试代码|NO


另外，还需要手动在AN这里增加对应的Algo Logic相关的章节 （详见[调用自动生成脚本之后](./after_usage.md)）
https://wiki.aixin-chip.com/display/MC50ISPAN/MC20E+ISP+Application+Notes

![](../assets/imgs/wiki_page.jpg)
