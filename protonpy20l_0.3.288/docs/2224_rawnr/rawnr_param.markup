h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| offset_in |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1024 | 16.0 | hidden | 'raw_2dnr.offset_in' | Offset In | common |
| offset_out |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1024 | 16.0 | hidden | 'raw_2dnr.offset_out' | Offset Out | common |
| again |  | u8.8 | AX_U16 | [\] |  [0, 65535\] | [0.0, 255.99609375\] | 4096 | 16.0 | hidden | 'raw_2dnr.noise_profile_bayer_lut', 'raw_2dnr.inv_noise_profile_bayer_lut', 'raw_2dnr.noise_profile_y_lut', 'raw_2dnr.inv_noise_profile_y_lut' | again for noise_profile | common |
| hdr_ratio |  | u9.8 | AX_U32 | [\] |  [0, 131071\] | [0.0, 511.99609375\] | 256 | 1.0 | hidden | 'raw_2dnr.noise_profile_bayer_lut', 'raw_2dnr.inv_noise_profile_bayer_lut', 'raw_2dnr.noise_profile_y_lut', 'raw_2dnr.inv_noise_profile_y_lut' | hdr_ratio | common |
| read_noise_coeffs |  | s0.31 | AX_S32 | [4, 3\] |  [-2147483648, 2147483647\] | [-1.0, 0.9999999995343387\] | [[42949673, 42949673, 0\], [42949673, 42949673, 0\], [42949673, 42949673, 0\], [42949673, 42949673, 0\]\] | [[0.02000000001862645, 0.02000000001862645, 0.0\], [0.02000000001862645, 0.02000000001862645, 0.0\], [0.02000000001862645, 0.02000000001862645, 0.0\], [0.02000000001862645, 0.02000000001862645, 0.0\]\] | hidden | 'raw_2dnr.noise_profile_bayer_lut', 'raw_2dnr.inv_noise_profile_bayer_lut', 'raw_2dnr.noise_profile_y_lut', 'raw_2dnr.inv_noise_profile_y_lut' | read_noise_coeffs | common |
| shot_noise_coeffs |  | s0.31 | AX_S32 | [4, 2\] |  [-2147483648, 2147483647\] | [-1.0, 0.9999999995343387\] | [[1073741824, 107374182\], [1073741824, 107374182\], [1073741824, 107374182\], [1073741824, 107374182\]\] | [[0.5, 0.049999999813735485\], [0.5, 0.049999999813735485\], [0.5, 0.049999999813735485\], [0.5, 0.049999999813735485\]\] | hidden | 'raw_2dnr.noise_profile_bayer_lut', 'raw_2dnr.inv_noise_profile_bayer_lut', 'raw_2dnr.noise_profile_y_lut', 'raw_2dnr.inv_noise_profile_y_lut' | shot_noise_coeffs | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open |  | soft enable switch for the entire module |   | None | None | None |
| mask_thre | mask thre | u0.8 | AX_U8 | [2\] | [0, 255\] | [0.0, 0.99609375\] | [0, 255\] | [0.0, 0.99609375\] | open | 'raw_2dnr.mask_coring_thre', 'raw_2dnr.mask_coring_slope' | mask threshold for coring |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| mask_limit | mask limit | u0.8 | AX_U8 | [2\] | [0, 255\] | [0.0, 0.99609375\] | [0, 255\] | [0.0, 0.99609375\] | open | 'raw_2dnr.mask_coring_limit', 'raw_2dnr.mask_coring_slope' | mask limit for coring |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| edge_preserve_ratio | edge_preserve_ratio | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 128 | 0.5 | open | 'raw_2dnr.freqsep_edge_preserve' | edge_preserve_ratio for freq separation |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| inter_g_strength | inter g strength | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 128 | 0.5 | open | 'raw_2dnr.freqsep_inter_g_strength' | inter_g_strength for freq separation |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| hf_nr_enable | hf_nr_enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'raw_2dnr.wiener_org_blend' | hf noise reduction enable |   | None | None | None |
| hf_nr_stren | hf nr stren | u0.8 | AX_U8 | [2\] | [0, 255\] | [0.0, 0.99609375\] | [128, 128\] | [0.5, 0.5\] | open | 'raw_2dnr.wiener_org_blend' | hf noise reduction strength |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| hf_nr_stren_rgb | hf nr stren rgb | u0.8 | AX_U8 | [2, 3\] | [0, 255\] | [0.0, 0.99609375\] | [255, 255, 255, 255, 255, 255\] | [0.99609375, 0.99609375, 0.99609375, 0.99609375, 0.99609375, 0.99609375\] | open | 'raw_2dnr.wiener_org_blend' | hf noise reduction strength for rgb channel |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| wiener_factor | wiener factor | u6.4 | AX_U16 | [2, 17\] | [0, 1023\] | [0.0, 63.9375\] | [16, 16, ... , 16\] | [1.0, 1.0, ... , 1.0\] | open | 'raw_2dnr.wiener_factor' | hf noise reduction wiener factor for different brightness |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| mf_nr_enable | mf nr enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'raw_2dnr.nlm_org_blend' | mf noise reduction enable |   | None | None | None |
| mf_nr_stren | mf nr stren | u0.8 | AX_U8 | [2\] | [0, 255\] | [0.0, 0.99609375\] | [128, 128\] | [0.5, 0.5\] | open | 'raw_2dnr.nlm_org_blend' | mf noise reduction strength |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| mf_nr_stren_rgb | mf nr stren rgb | u0.8 | AX_U8 | [2, 3\] | [0, 255\] | [0.0, 0.99609375\] | [255, 255, 255, 255, 255, 255\] | [0.99609375, 0.99609375, 0.99609375, 0.99609375, 0.99609375, 0.99609375\] | open | 'raw_2dnr.nlm_org_blend' | mf noise reduction strength for rgb channel |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| nlm_factor | nlm factor | u4.6 | AX_U16 | [2, 17\] | [0, 1023\] | [0.0, 15.984375\] | [32, 32, ... , 32\] | [0.5, 0.5, ... , 0.5\] | open | 'raw_2dnr.nlm_thre_factor', 'raw_2dnr.nlm_inv_thre_factor' | mf noise reduction nlm factor for different brightness |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| inter_channel_strength | inter channel strength | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 255 | 0.99609375 | open | 'raw_2dnr.nlm_inter_g_strength' | inter_channel_strength for mf nlm |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| lut_mode | lut mode | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 255 | None | open | 'raw_2dnr.lut_affine_k' | lut_mode for noise_profile. 0: SDR 256, 1: HDR 512, 2: HDR 1024, 3: HDR 2048, 4: HDR 4096, 5: HDR 8192, 6: HDR 16384, 255: AUTO |   | None | None | None |
| lut_type | lut type | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'raw_2dnr.lut_type' | lut_type for noise_profile, 0: Linear, 1: Log/Exponential |   | None | None | None |
| noise_profile_factor | noise profile factor | u3.7 | AX_U16 | [\] | [0, 1023\] | [0.0, 7.9921875\] | 128 | 1.0 | open | 'raw_2dnr.noise_profile_bayer_lut', 'raw_2dnr.inv_noise_profile_bayer_lut', 'raw_2dnr.noise_profile_y_lut', 'raw_2dnr.inv_noise_profile_y_lut' | noise_profile_factor for noise_profile |   | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |