h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| v0_xT422_offset_in |  | s8 | AX_S16 | [3\] |  [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | hidden | 'tde.v0_xT422_offset_in' | RGB to YUV422 conversion offset in. Only effective when input is RGB, need not tune | common |
| v0_xT422_offset_out |  | s8 | AX_S16 | [3\] |  [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | hidden | 'tde.v0_xT422_offset_out' | v0`s ToYuv420 csc output offset, need not tune | common |
| g0_xT422_offset_in |  | s8 | AX_S16 | [3\] |  [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | hidden | 'tde.g0_xT422_offset_in' | RGB to YUV conversion offset in. Only effective when input format is RGB, need not tune | common |
| g0_xT422_offset_out |  | s8 | AX_S16 | [3\] |  [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | hidden | 'tde.g0_xT422_offset_out' | RGB to YUV conversion offset out. Only effective when input format is RGB, need not tune | common |
| ddr_write_adj_offset |  | s8 | AX_S16 | [2, 3\] |  [np.int64(-256), np.int64(255)\] | [None, None\] | [[0, 0, 0\], [0, 0, 0\]\] | None | hidden | 'tde.ddr_write_adj_offset' | in && out offset for DDR write adjustment | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| v0_format | v0 format | u6 | AX_U8 | [\] | [np.int64(0), np.int64(63)\] | [None, None\] | 0 | None | open | 'tde.v0_format' | v0 input`s format | user | None | None | None |
| v0_sp_alpha_enable | v0 sp alpha enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 1 | None | open | 'tde.v0_sp_alpha_enable' | Special alpha channel enable control bit | user | None | None | None |
| v0_global_alpha_enable | v0 global alpha enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.v0_global_alpha_enable' | Global alpha enable control bit. It's priority is lower than special alpha | user | None | None | None |
| v0_global_alpha | v0 sp global alpha | u8 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.v0_global_alpha' | v0's global alpha value. If global alpha is used or there is no alpha channel in input v0, this value will be the global alpha for v0. BITMAP's alpha channel will be generated by using color palette if global alpha is not enabled | user | None | None | None |
| v0_global_chroma | v0 sp global chroma | u8 | AX_U8 | [2\] | [np.int64(0), np.int64(255)\] | [None, None\] | [128, 128\] | None | open | 'tde.v0_global_chroma' | v0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10 | user | None | None | None |
| quad_v0_enable | quad v0 enable | u1 | AX_U8 | [4\] | [np.int64(0), np.int64(1)\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'tde.quad_v0_enable' | Quad enable control bit | user | None | None | None |
| quad_v0_in_x_coords | quad v0 in x coords | s13 | AX_S16 | [4, 4\] | [np.int64(-8192), np.int64(8191)\] | [None, None\] | [[0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\]\] | None | open | 'tde.quad_v0_in_x_coords', 'tde.quad_v0_in_steps', 'tde.quad_v0_in_directions' | x coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed | user | None | None | None |
| quad_v0_in_y_coords | quad v0 in y coords | s13 | AX_S16 | [4, 4\] | [np.int64(-8192), np.int64(8191)\] | [None, None\] | [[0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\]\] | None | open | 'tde.quad_v0_in_y_coords', 'tde.quad_v0_in_steps', 'tde.quad_v0_in_directions' | y coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed | user | None | None | None |
| quad_v0_out_x_coords | quad v0 out x coords | s13 | AX_S16 | [4, 4\] | [np.int64(-8192), np.int64(8191)\] | [None, None\] | [[0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\]\] | None | open | 'tde.quad_v0_out_x_coords', 'tde.quad_v0_out_steps', 'tde.quad_v0_out_directions' | x coordinates of intersection points between outer quad's edges, and image rectangle which is constrained by y_st and y_ed | user | None | None | None |
| quad_v0_out_y_coords | quad v0 out y coords | s13 | AX_S16 | [4, 4\] | [np.int64(-8192), np.int64(8191)\] | [None, None\] | [[0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\]\] | None | open | 'tde.quad_v0_out_y_coords', 'tde.quad_v0_out_steps', 'tde.quad_v0_out_directions' | y coordinates of intersection points between outer quad's edges, and image rectangle which is constrained by y_st and y_ed | user | None | None | None |
| quad_v0_mode | quad out mode | u1 | AX_U8 | [4\] | [np.int64(0), np.int64(1)\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'tde.quad_v0_mode' | Quad feature mode control bit. 0 - mosaic; 1 - fill | user | None | None | None |
| quad_v0_mosaic_step_size | mosaic step size | u7 | AX_U8 | [2\] | [np.int64(0), np.int64(127)\] | [None, None\] | [16, 16\] | None | open | 'tde.quad_v0_mosaic_step_size' | Mosaic step size | user | None | None | None |
| quad_v0_fill_out_luma | outer quad luma | u8 | AX_U8 | [4\] | [np.int64(0), np.int64(255)\] | [None, None\] | [128, 128, 128, 128\] | None | open | 'tde.quad_v0_fill_out_luma' | Outer quad luma | user | None | None | None |
| quad_v0_fill_out_chroma | Outer quad chroma | u8 | AX_U8 | [4, 2\] | [np.int64(0), np.int64(255)\] | [None, None\] | [[128, 128\], [128, 128\], [128, 128\], [128, 128\]\] | None | open | 'tde.quad_v0_fill_out_chroma' | Outer quad chroma(with 128 bias) | user | None | None | None |
| quad_v0_out_alpha | outer quad blending alpha | u8 | AX_U8 | [4\] | [np.int64(0), np.int64(255)\] | [None, None\] | [255, 255, 255, 255\] | None | open | 'tde.quad_v0_out_alpha' | outer quad blending alpha | user | None | None | None |
| roi_luma_sum_tl_coords | top-left corner coordinates of 8 roi areas | u12 | AX_U16 | [8, 2\] | [np.int64(0), np.int64(4095)\] | [None, None\] | [[0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\]\] | None | open | 'tde.roi_luma_sum_tl_coords' | roi luma sum tl coords | user | None | None | None |
| roi_luma_sum_br_coords | bottom-right corner coordinates of 8 roi areas | u12 | AX_U16 | [8, 2\] | [np.int64(0), np.int64(4095)\] | [None, None\] | [[0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\]\] | None | open | 'tde.roi_luma_sum_br_coords' | roi luma sum br coords | user | None | None | None |
| g0_format | g0 format | u6 | AX_U8 | [\] | [np.int64(0), np.int64(63)\] | [None, None\] | 0 | None | open | 'tde.g0_format' | g0 input`s format | user | None | None | None |
| g0_global_alpha_enable | v0 global alpha enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.g0_global_alpha_enable' | Global alpha enable control bit. It's priority is lower than special alpha | user | None | None | None |
| g0_global_alpha | g0 sp global alpha | u8 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 200 | None | open | 'tde.g0_global_alpha' | g0's global alpha value. If global alpha is used or there is no alpha channel in input g0, this value will be the global alpha for g0. BITMAP's alpha channel will be generated by using color palette if global alpha is not enabled | user | None | None | None |
| g0_global_chroma | g0 sp global chroma | u8 | AX_U8 | [2\] | [np.int64(0), np.int64(255)\] | [None, None\] | [128, 128\] | None | open | 'tde.g0_global_chroma' | g0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10 | user | None | None | None |
| blending_enable | blending enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.blending_enable' | Blending unit enable control bit | user | None | None | None |
| blending_coord | blending coord | u16 | AX_U16 | [2\] | [np.int64(0), np.int64(65535)\] | [None, None\] | [0, 0\] | None | open | 'tde.blending_coord' | Blending coord | user | None | None | None |
| blending_alpha0_out_mode | blending alpha0 out mode | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.blending_alpha0_out_mode' | 0 - v0 alpha not changed; 1 - v0 alpha inversed; Result is intermediate | user | None | None | None |
| blending_alpha1_out_mode | blending alpha1 out mode | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.blending_alpha1_out_mode' | 0 - gx alpha not changed; 1 - gx alpha inversed; Result is intermediate | user | None | None | None |
| blending_alpha_out_weight | blending alpha1 out mode | u1.8 | AX_U16 | [\] | [np.int64(0), np.int64(256)\] | [np.float64(0.0), np.float64(1.0)\] | 128 | 0.5 | open | 'tde.blending_alpha_out_weight' | v0_alpha \* (1 - weight) + gx_alpha \* weight | user | None | None | None |
| blending_color_key_enable | blending enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.blending_color_key_enable' | blending color key enable | user | None | None | None |
| blending_alpha_select | blending alpha select | u2 | AX_U8 | [\] | [np.int64(0), np.int64(3)\] | [None, None\] | 0 | None | open | 'tde.blending_alpha_select' | Select alpha channel to be used in blending | user | None | None | None |
| blending_color_key_val | blending alpha select | s8 | AX_S16 | [3, 2\] | [np.int64(-255), np.int64(255)\] | [None, None\] | [[100, 120\], [30, 60\], [30, 60\]\] | None | open | 'tde.blending_color_key_val' | Color key color range for YUV. | user | None | None | None |
| blending_color_key_inv | blending color key inv | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.blending_color_key_inv' | If enabled, pix whose color is outside of the color-key range will do the color-key process, rather than the one within the color range | user | None | None | None |
| ddr_write_format | ddr write format | u6 | AX_U8 | [\] | [np.int64(0), np.int64(63)\] | [None, None\] | 0 | None | open | 'tde.ddr_write_format', 'tde.ddr_write_vfilter_enable', 'tde.ddr_write_adj_enable' | DDR dump format | user | None | None | None |
| dither_enable | dither enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 1 | None | open | 'tde.dither_enable', 'tde.dither_seed_r', 'tde.dither_seed_g', 'tde.dither_seed_b' | dither enable control bit | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |