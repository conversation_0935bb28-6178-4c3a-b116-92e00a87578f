{"enable": {"api": "nDehazeEn", "display": "dehazeEn", "comments": "SW enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "calc_mode": {"api": "nCalcMode", "display": "calcMode", "comments": "0 using traditional algorithm, 1 using accelerated algorithm.", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "air_reflect": {"api": "nAirReflect", "display": "airReflect", "comments": "air reflect. Too small will lead to over exposure of the image, and too large will lead to dead black of the image", "hint": "Accuracy: U8.8 Range: [1, 65535]"}, "effective_strength": {"api": "nEffectStrength", "display": "effectStr", "comments": "adjust defogging intensity", "hint": "Accuracy: U1.15 Range: [0, 32768]"}, "strength_limit": {"api": "nStrengthLimit", "display": "strLimit", "comments": "effective strength limit", "hint": "Accuracy: U1.8 Range: [1, 256]"}, "spatial_smoothness": {"api": "nSpatialSmoothness", "display": "spSmooth", "comments": "The higher the value, the more global the defogging effect", "hint": "Accuracy: U2.0 Range: [0, 3]"}, "eps": {"api": "nEps", "display": "eps", "comments": "Generally, no adjustment is required, which affects the intensity of extreme contrast areas", "hint": "Accuracy: U8.4 Range: [1, 4095]"}, "blur_enable": {"api": "nBlurEnable", "display": "blurEnable", "comments": "0 is turned off and 1 is turned on to alleviate the halo problem", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "sigma_blur": {"api": "nSigmaBlur", "display": "sigmaBlur", "comments": "blur intensity of the halo problem", "hint": "Accuracy: U3.13 Range: [1, 65535]"}, "mesh_size": {"api": "nMeshSize", "display": "meshSize", "comments": "Affects the uniformity of defogging intensity. The higher the value, the more uniform the defogging intensity will be, and is equal to 64 when calc_mode == 1", "hint": "Accuracy: U7.0 Range: {32, 64}"}, "extra_info": {"DehazeStatistics": {"attr": "RO", "comment": "RO: only support get api, RW: support set and get api, REF: directly use,  NULL: only support struct", "params": {"pIspDehazeStat": {"type": "AX_ISP_DEHAZE_STAT_INFO_T", "size": [], "api": "pIspDehazeStat", "member": {"nSeqNum": {"type": "AX_U64", "size": [], "api": "nSeqNum", "display": "", "comments": "frame num", "hint": "Accuracy: U64.0"}, "tShape": {"type": "AX_ISP_DEHAZE_STAT_SHAPE_T", "size": [], "member": {"nShapeWidth": {"type": "AX_U16", "size": [], "api": "nShapeWidth", "display": "", "comments": "dc shape width", "hint": "Accuracy: U16.0"}, "nShapeHeight": {"type": "AX_U16", "size": [], "api": "nShapeHeight", "display": "", "comments": "dc shape height", "hint": "Accuracy: U16.0"}}}, "nDcStats": {"type": "AX_U16", "size": [32400], "api": "nDcStats", "display": "", "comments": "dc statistics", "hint": "Accuracy: U8.2"}}}}}}}