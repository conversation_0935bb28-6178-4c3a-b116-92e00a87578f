h2. Conf list
h3. raw_ltm
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | 0, 1 | bypass/enable | 0: bypass, 1: enable |
| dither_mode | u1 | [\] | - | 0, 1 | disable/enable | 0: bypass, 1: enable, set 0 as default |
| dither_scale | u8.4 | [\] | - | dither_scale = [0, 255.9375\] | dither strength | set non 0, when dither enable |
| dither_seed_enable | u1.0 | [\] | - | 0, 1 | 0: use stat dither_seed, 1: use conf dither_seed | set 1 for the 1st frame, then set 0 from the 2nd frame onwards |
| dither_seed | u16.0 | [2\] | - | dither_seed[0\]: (0, 65536), dither_seed[10\]: (0, 32768) | value of dither seed | set non 0, when dither enable |
| luma_weight | u1.7 | [2\] | - | [0, 1\] | weight of luma_max and luma_rgb for luma and luma_detail calculate | normal set [1, 0\] |
| win_size | u12.0 | [\] | - | win_size = 128, 256, 512, 1024, see more in Appendix | raw_ltm window size |  |
| offset_h_w | u15.0 | [2\] | support | offset_tile_h_w[0\] < pic_h_w[0\], offset_tile_h_w[1\] < pic_h_w[1\], see more in Appendix | current partition's offset of height and width | for no partition case, set [0, 0\] |
| ptn_h_w | u15.0 | [2\] | support | the height and width of the partition need to be smaller than the full picture size considering offset | the size of partition | for no partition case, set as the full picture resolution |
| ptn_hist_roi_t_b_l_r | u15.0 | [4\] | support | the coordinates should not outside full picture | the coordinates (top, bottom, left, right) of the histogram area in the current partition | for no partition case, set as the [0, pic_h_w[0\], 0, pic_h_w[1\]\] |
| curve | u8.4 | [104, 257\] | - | the lut size should keep right with full picture resolution and window size | ltm tone LUT calculate by algo logic inside the hist roi area |  |
| log10_lut | u3.13 | [257\] | - | log10_lut[i-1\] <= log10_lut[i\], log10_lut[-1\] < 6.3 | log10 lut calculate by 3a++ |  |
| gtm_lut | u8.4 | [129\] | - | gtm_lut[i-1\] <= gtm_lut[i\] | gtm lut calculate by algo logic |  |
| pmask_16bit | u16.0 | [\] | - |  | 16 bit dither mask |  |
| pmask_15bit | u15.0 | [\] | - |  | 15 bit dither mask |  |
| dgain | u10.6 | [\] | - | make input picture white level up to U14.6, [1, 1023.984375\] | digital gain for input picture |  |
| pic_h_w | u15.0 | [2\] | - |  | full input picture size |  |
| max_gain | u12.16 | [\] | - | [1, 4095.9999847412\] | the max gain of raw_ltm module |  |
| limit_luma_in | u14.6 | [\] | - | [0.0625, 16383.984375\] | limit the minimum value of input luma |  |
| limit_luma_out | u8.4 | [\] | - | [0.0, 255.9375\] | limit the minimum value of output luma |  |
| kernel_dis | u1.11 | [3, 3\] | - | [0, 1\], kernel_dis.sum() == 1 | spatial filter coefficient of high frequency detail filter |  |
| sigma_val_lut | u1.11 | [33\] | - | [0, 1\] | range filter coefficient lut for high frequency detail filter |  |
| detail_coring_pos | u3.13 | [\] | - | [0, 7.99987793\] | coring value of positive high frequency detail enhancement |  |
| detail_coring_neg | u3.13 | [\] | - | [0, 7.99987793\] | coring value of negative high frequency detail enhancement |  |
| detail_gain_pos | u3.5 | [\] | - | [0, 7.96875\] | gain value of positive high frequency detail enhancement |  |
| detail_gain_neg | u3.5 | [\] | - | [0, 7.96875\] | gain value of negative high frequency detail enhancement |  |
| detail_limit_pos | u3.13 | [\] | - | [0, 7.99987793\] | limit value of positive high frequency detail enhancement |  |
| detail_limit_neg | u3.13 | [\] | - | [0, 7.99987793\] | limit value of negative high frequency detail enhancement |  |
| detail_extra_strength_pos | u4.4 | [\] | - | [0, 15.9375\] | extra strength of positive high frequency detail enhancement |  |
| detail_extra_strength_neg | u4.4 | [\] | - | [0, 15.9375\] | extra strength of negative high frequency detail enhancement |  |
| detail_strength_lut | u4.4 | [33\] | - | [0, 15.9375\] | lut of high frequency detail strength |  |
| detail_strength_lut_pos_low | u1.7 | [33\] | - | [0, 1.9921875\] | lut of pos low frequency detail strength |  |
| detail_strength_lut_neg_low | u1.7 | [33\] | - | [0, 1.9921875\] | lut of neg low frequency detail strength |  |
| exp10_lut_pos | u14.14 | [65\] | - | [1, 16383.99993896\] | positive exp10 lut |  |
| exp10_lut_neg | u1.14 | [65\] | - | [0, 1\] | negative exp10 lut |  |
| downsample_ratio | u1 | [\] | - | 0, 1 | downsample ratio for log luma calculate |  |
| detail_low_enable | u1 | [\] | - | 0, 1 | low frequency enhancement bypass/enable control |  |
| detail_low_reset_coeffab | u1 | [\] | - | 0, 1 | low frequency enhancement coeff a and b reset to 1 and 0 |  |
| detail_gain_pos_low | u3.5 | [\] | - |  | gain value of positive low frequency enhancement |  |
| detail_gain_neg_low | u3.5 | [\] | - |  | gain value of negative low frequency enhancement |  |
| detail_limit_pos_low | u3.13 | [\] | - |  | limit value of positive low frequency enhancement |  |
| detail_limit_neg_low | u3.13 | [\] | - |  | limit value of negative low frequency enhancement |  |
| offset_in | u14.6 | [\] | - |  | offset of pipeline input |  |
| offset_out | u8.4 | [\] | - |  | offset of pipeline output |  |

h3. raw_ltm_pst
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | 0, 1 | bypass/enable | 0: bypass, 1: enable |
| mode_pst | u2 | [\] | support |  | raw_ltm work mode | 0: first partition, 1: last partition, 2: other partition |
| kernel_dis_pst | u1.11 | [3, 3\] | - | N/A | Bilateral filter's distance kernel. | Effective kernel size is 5x5. 3x3 register will be used to initialize 5x5 kernel by using mirror copy. |
| sigma_val_lut_pst | u1.11 | [33\] | - | N/A | This LUT is used to convert pixel value difference to weight in bilateral filter. |  |
| downsample_ratio | u1 | [\] | - | 0, 1 | downsample ratio for log luma calculate | 0: output 1/32\*1/32 full picture size log_luma, 1: output 1/64\*1/64 full picture size log_luma |
| norm_ratio_lut | u1.15 | [3\] | - | (0, 1\] | calculate bottom_right, top_right, bottom_left block's normalize ratio |  |

h3. ifa
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | N/A | enable control bit | 0 - disable; 1 - enable |
| mode | u4 | [\] | - | N/A | Select ifa operator to run | 0 - guided filter |
| sub_mode | u4 | [\] | - | N/A | Select sub_mode inside ifa operator | If selected ifa operator has serval sub-mode, use this register to select it. |
| output_hor_crop_enable | u1 | [\] | - | N/A | output horizontal crop enable control bit | 0 - disable; 1 - enable |
| output_hor_crop_st | u12 | [\] | support | N/A | output picture's horizontal crop start coordinates | out_hor_crop_st[n\] for pics[n\] |
| output_hor_crop_width | u12 | [\] | support | N/A | output picture's horizontal crop width | out_hor_crop_width[n\] for pics[n\] |
| guided_enable | u1 | [\] | - | N/A | guided filter enable control bit | 0 - disable; 1 - enable |
| guided_dehaze_dc_adj_enable | u1 | [\] | - | N/A | When used for dehaze, this bit controls whether adjust dark channel using coeff k before calculating guided filter.. | If enabled, dc = clip(1 - dc \* k, 0, 1.0) |
| guided_dehaze_dc_k | s4.12 | [\] | - | N/A | k value in dehaze logic | clip(b - dc \* k, 0, 1.0) |
| guided_dehaze_dc_b | u1.12 | [\] | - | N/A | b value in dehaze logic | clip(b - dc \* k, 0, 1.0) |
| guided_win_r | u3 | [\] | - | N/A | 2 \* guided_win_r + 1 == window size | Example: 5x5 --> 2 \* 2 + 1 |
| guided_epsilon | u16 | [\] | - | N/A | It's u16 form for various acc epsilon; For rltm, it's u0.16; For dehaze, it's u12.4; For UVNR, it's u0.16; The guided_epsilon value in guided filter formula. | coeff_a = cov_ip / (var_i + guided_epsilon \* guided_epsilon) |
| blur_enable | u1 | [\] | - | N/A | guided filter enable control bit | 0 - disable; 1 - enable |
| blur_weight | s5.10 | [3, 3\] | - |  | 3 x 3 filter weight; Symmetric parameter; Effective 5 x 5 filter weight. | Use upper-left corner 3x3 parameter to init internal 5x5 filter weight symmetrically. |

