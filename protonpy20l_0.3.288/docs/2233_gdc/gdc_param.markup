h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'yuv_gdc.mesh_start', 'yuv_gdc.pos_start', 'yuv_gdc.internal_start', 'yuv_gdc.partition_start', 'yuv_gdc.mesh_length', 'yuv_gdc.inv_mesh_length' | yuv_gdc partition information | common |
| com_matrix |  | s14.10 | AX_S32 | [3, 3\] |  [-16777216, 16777215\] | [-16384.0, 16383.9990234375\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | hidden |  | common matrix | user |
| input_format |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'yuv_gdc.input_mode', 'yuv_gdc.input_bit_mode', 'yuv_gdc.process_mode' | 1: 420 input, 0: 422 input | user |
| mesh_wise_mode |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'yuv_gdc.mesh_wise_mode' | 0: pixel wise, 1: mesh_wise | user |
| mesh_height |  | u14 | AX_U16 | [\] |  [0, 16383\] | [None, None\] | 0 | None | hidden | 'yuv_gdc.mesh_height' | mesh height | user |
| mesh_width |  | u14 | AX_U16 | [\] |  [0, 16383\] | [None, None\] | 0 | None | hidden | 'yuv_gdc.mesh_width' | mesh width | user |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| ldc_enable | ldc_enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open |  | ldc enable | user | None | None | None |
| ldc_intrinsic_matrix | ldc intrinsic matrix | s7.8 | AX_S16 | [3, 3\] | [-32768, 32767\] | [-128.0, 127.99609375\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open |  | ldc intrinsic matrix | user | None | None | None |
| ldc_distortion_coeff | ldc distortion coeff | s7.8 | AX_S16 | [8\] | [-32768, 32767\] | [-128.0, 127.99609375\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open |  | ldc distortion coeff | user | None | None | None |
| dis_enable | dis enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open |  | dis enable | user | None | None | None |
| dis_sad_thres | dis sad thres | u8.8 | AX_U16 | [\] | [0, 65535\] | [0.0, 255.99609375\] | 0 | 0.0 | open |  | dis sad thres | user | None | None | None |
| dis_frame_pos_weight | dis frame pos weight | u8.8 | AX_U16 | [16\] | [0, 65535\] | [0.0, 255.99609375\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open |  | dis frame pos weight | user | None | None | None |
| rotate_enable | rotate enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open |  | rotate enable | user | None | None | None |
| rotate_angle_mode | rotate angle mode | u2 | AX_U8 | [\] | [0, 3\] | [None, None\] | 0 | None | open |  | 4 kind of rotation angle 0/90/180/270, 0: 0°, 1: 90° 2: 180° 3: 270° | user | None | None | None |
| flip_enable | flip enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open |  | flip enable | user | None | None | None |
| mirror_enable | mirror enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open |  | mirror enable | user | None | None | None |
| crop_enable | crop enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open |  | crop enable | user | None | None | None |
| crop_start | crop start | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open |  | Crop start coordinates | user | None | None | None |
| crop_size | crop size | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open |  | Crop size | user | None | None | None |
| scale_enable | scale enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open |  | scale enable | user | None | None | None |
| scale_step | scale step | u6.10 | AX_U16 | [2\] | [np.int64(0), np.int64(65535)\] | [np.float64(0.0), np.float64(63.9990234375)\] | [1024, 1024\] | [np.float64(1.0), np.float64(1.0)\] | open | 'yuv_gdc.output_size' | Scalar factor | user | None | None | None |
| enable | enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 1 | None | open | 'yuv_gdc.enable' | soft enable switch for the entire module | user | None | None | None |
| undistort_enable | undistort enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'yuv_gdc.undistort_enable' | 1: undistort process, 0: bypass undistort process | user | None | None | None |
| pix_padding_mode | pix padding mode | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'yuv_gdc.pix_padding_mode' | pixel padding is: 1: CONSTANT 0: MIRROR_ABA | user | None | None | None |
| const_Y | const Y | u8.2 | AX_U16 | [\] | [0, 1023\] | [0.0, 255.75\] | 0 | 0.0 | open | 'yuv_gdc.const_Y' | const value Y for padding pixel | user | None | None | None |
| const_UV | const UV | s7.2 | AX_S16 | [2\] | [-512, 511\] | [-128.0, 127.75\] | [0, 0\] | [np.float64(0.0), np.float64(0.0)\] | open | 'yuv_gdc.const_UV' | const value UV for pading pixel | user | None | None | None |
| mesh_padding_mode | mesh padding mode | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'yuv_gdc.mesh_padding_mode' | 0: mesh is cycle padding, 1: mesh is constant padding | user | None | None | None |
| const_dx | const x | s14.10 | AX_S32 | [\] | [-16777216, 16777215\] | [-16384.0, 16383.9990234375\] | 0 | 0.0 | open | 'yuv_gdc.const_dx' | constant padding mesh value for dx | user | None | None | None |
| const_dy | const y | s14.10 | AX_S32 | [\] | [-16777216, 16777215\] | [-16384.0, 16383.9990234375\] | 0 | 0.0 | open | 'yuv_gdc.const_dy' | constant padding mesh value for dy | user | None | None | None |
| mesh_value_mode | mesh value mode | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'yuv_gdc.mesh_value_mode' | 0: mesh is absolute displacement, 1: mesh is relative displacement | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |