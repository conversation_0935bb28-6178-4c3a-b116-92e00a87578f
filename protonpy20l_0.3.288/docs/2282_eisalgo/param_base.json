{"context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "EISALGO is 0x2282", "type": "AX_U16", "default": "0x2282"}}, "configs": {"eisalgo": {"gdc_mesh": {"acc": [0, 64], "size": [1188], "description": "gdc_mesh 33x36", "usage": "", "constraints": "", "type": "AX_U64", "partition": "-"}, "mesh_width": {"acc": [0, 16], "size": [], "description": "mesh_width", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "mesh_height": {"acc": [0, 16], "size": [], "description": "mesh_height", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "homography": {"acc": [0, 32], "size": [9], "description": "homography 3x3", "usage": "", "constraints": "", "type": "AX_S32", "partition": "-"}, "motion_vector": {"acc": [0, 32], "size": [2], "description": "motion_vector 1x2", "usage": "", "constraints": "", "type": "AX_S32", "partition": "-"}, "rotation": {"acc": [0, 8], "size": [], "description": "rotation", "usage": "0: 0, 1: 90, 2: 180, 3: 270", "constraints": "0, 1, 2, 3", "type": "AX_U8", "partition": "-"}, "mirror": {"acc": [0, 8], "size": [], "description": "mirror", "usage": "0: No, 1: Yes", "constraints": "0, 1", "type": "AX_U8", "partition": "-"}, "filp": {"acc": [0, 8], "size": [], "description": "filp", "usage": "0: No, 1: Yes", "constraints": "0, 1", "type": "AX_U8", "partition": "-"}}}, "params": {"enable": {"acc": [0, 1], "auto": 0, "comment": "0: DIS Disable  1: DIS Enable", "default": 1, "hidden": 0, "range": [0, 1], "size": [], "target_conf": [], "type": "AX_U8", "dependency": "user"}, "sw_calc_enable": {"acc": [0, 1], "auto": 0, "comment": "0: use HW offset 1: use projection calculate offset", "default": 1, "hidden": 0, "range": [0, 1], "size": [], "target_conf": [], "type": "AX_U8", "dependency": "user"}, "delay_frame_num": {"acc": [0, 8], "auto": 0, "comment": "delay num, the more, the smoother. defalut 0. Accuracy: U8 Range: [0, 15]", "default": 0, "hidden": 0, "range": [0, 15], "size": [], "target_conf": [], "type": "AX_U8", "dependency": "user"}, "history_frame_num": {"acc": [0, 8], "auto": 0, "comment": "history frame, the more, the smoother", "default": 0, "hidden": 0, "range": [1, 16], "size": [], "target_conf": [], "type": "AX_U8", "dependency": "user"}, "crop_ratio": {"acc": [0, 8], "auto": 0, "comment": "crop_ratio for warped image", "default": 205, "hidden": 0, "range": [127, 255], "size": [], "target_conf": [], "type": "AX_U8", "dependency": "user"}, "sad_threshold": {"acc": [0, 32], "auto": 0, "comment": "threshord for valid offset", "default": 65535, "hidden": 0, "range": [0, 65535], "size": [], "target_conf": [], "type": "AX_U32", "dependency": "user"}, "frame_pos_weights": {"acc": [0, 8], "auto": 0, "comment": "the weight of history frame position for weighted sum", "default": [0, 32, 64, 96, 128, 160, 192, 224, 255, 255, 255, 255, 255, 255, 255, 255], "range": [0, 255], "hidden": 0, "size": [16], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "img_height": {"acc": [0, 16], "auto": 0, "comment": "height of img which send to gdc", "default": 65535, "hidden": 1, "range": [0, 65535], "size": [], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "img_width": {"acc": [0, 16], "auto": 0, "comment": "width of img which send to gdc", "default": 65535, "hidden": 1, "range": [0, 65535], "size": [], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "offset_vh": {"acc": [1, 7], "auto": 0, "comment": "Vertical offset", "default": "np.zeros(256).tolist()", "range": [-128, 127], "hidden": 1, "size": [256], "target_conf": [], "type": "AX_S8", "dependency": "user"}, "proj_vh": {"acc": [0, 16], "auto": 0, "comment": "Vertical Projection", "default": "np.zeros(256).tolist()", "range": [0, 65535], "hidden": 1, "size": [256], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "sad_vh": {"acc": [0, 32], "auto": 0, "comment": "Vertical Projection", "default": "np.zeros(256).tolist()", "range": [0, 65535], "hidden": 1, "size": [256], "target_conf": [], "type": "AX_U32", "dependency": "user"}, "proj_vh_num": {"acc": [0, 16], "auto": 0, "comment": "Vertical Projection NUM", "default": [65535, 65535], "hidden": 1, "range": [0, 65535], "size": [2], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "proj_vh_size": {"acc": [0, 16], "auto": 0, "comment": "Vertical Projection Size", "default": [65535, 65535], "hidden": 1, "range": [0, 65535], "size": [2], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "sad_vh_num": {"acc": [0, 16], "auto": 0, "comment": "sad_v and offset_v valid num", "default": [65535, 65535], "hidden": 1, "range": [0, 65535], "size": [2], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "sad_vh_size": {"acc": [0, 16], "auto": 0, "comment": "sad_v size", "default": [65535, 65535], "hidden": 1, "range": [0, 65535], "size": [2], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "offset_scale_ratio": {"acc": [0, 8], "auto": 0, "comment": "offset scale = (1 << scale ratio)", "default": 0, "hidden": 1, "range": [0, 2], "size": [], "target_conf": [], "type": "AX_U8", "dependency": "user"}, "scale_ratio": {"acc": [0, 8], "auto": 0, "comment": "quant scale = (1 << scale ratio)", "default": 0, "hidden": 0, "range": [0, 16], "size": [], "target_conf": [], "type": "AX_U8", "dependency": "user"}, "rotation": {"acc": [0, 8], "auto": 0, "comment": "0: 0, 1: 90, 2: 180, 3: 270", "default": 0, "hidden": 1, "range": [0, 3], "size": [], "target_conf": [], "type": "AX_U8", "dependency": "user"}, "mirror": {"acc": [0, 8], "auto": 0, "comment": "0: No, 1: Yes", "default": 0, "hidden": 1, "range": [0, 255], "size": [], "target_conf": [], "type": "AX_U8", "dependency": "user"}, "flip": {"acc": [0, 8], "auto": 0, "comment": "0: No, 1: Yes", "default": 0, "hidden": 1, "range": [0, 255], "size": [], "target_conf": [], "type": "AX_U8", "dependency": "user"}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": 16, "ref_interp_method": ["linear"]}}, "submodules": {"setup": {"params": ["img_height", "img_width", "rotation", "mirror", "flip"], "configs": []}, "gdc_mesh": {"params": ["enable", "sw_calc_enable", "delay_frame_num", "history_frame_num", "crop_ratio", "sad_threshold", "frame_pos_weights", "offset_vh", "proj_vh", "sad_vh", "proj_vh_num", "proj_vh_size", "sad_vh_num", "sad_vh_size", "offset_scale_ratio", "scale_ratio"], "configs": ["eisalgo.gdc_mesh", "eisalgo.mesh_width", "eisalgo.mesh_height", "eisalgo.homography", "eisalgo.motion_vector", "eisalgo.rotation", "eisalgo.mirror", "eisalgo.filp"]}}, "target_module": {"mc20l": {"eisstat": {"id": 8700, "method": 0}}}}