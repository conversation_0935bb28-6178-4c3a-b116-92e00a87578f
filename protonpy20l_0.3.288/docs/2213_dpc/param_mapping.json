{"static_dpc_length": {"api": "nStaticDpcLength", "display": "staticDpcLength", "comments": "static defect pixel storage length", "hint": "Accuracy: U16.0 Range: [0, 4096]"}, "static_dpc_buffer": {"api": "nStaticDpcBuffer", "display": "staticDpcBuffer", "comments": "static defect pixel coordinates info", "hint": "Accuracy: U32.0 Range: [0, 4294967295]"}, "noise_ratio": {"api": "nNoiseRatio", "display": "noiseRatio", "comments": "noise level gain, the larger the value, the larger the noise level, less defect pixels", "hint": "Accuracy: U4.10 Range: [0, 16383]"}, "enable": {"api": "nDpcEn", "display": "enable", "comments": "0:disable, 1:enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "predet_enable": {"api": "nPredetEnable", "display": "predetEnable", "comments": "0:disable predet, 1:enable predet", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "predet_level_slope": {"api": "nPredetLevelSlope", "display": "predetLevelSlope", "comments": "defect pixel margin gain for quick detection, the larger the value, less defect pixel detected", "hint": "Accuracy: U0.4 Range: [0, 15]"}, "predet_level_offset": {"api": "nPredetLevelOffset", "display": "predetLevelOffset", "comments": "defect pixel base margin for quick detection, the larger the value, less defect pixel detected", "hint": "Accuracy: U8.6 Range: [0, 16383]"}, "predet_level_max": {"api": "nPredetLevelMax", "display": "predetLevelMax", "comments": "defect pixel max margin for quick detection, the larger the value, less defect pixel detected", "hint": "Accuracy: U8.6 Range: [0, 16383]"}, "static_dpc_enable": {"api": "nStaticDpcEnable", "display": "staticDpcEnable", "comments": "0:disable static dpc, 1:enable static dpc", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "dynamic_dpc_enable": {"api": "nDynamicDpcEnable", "display": "dynamicDpcEnable", "comments": "0:disable dynamic dpc, 1:enable dynamic dpc", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "color_limit_enable": {"api": "nColorLimitEnable", "display": "colorLimitEnable", "comments": "0:disable color limit, 1:enable color limit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "defective_pixel_type": {"api": "nDefectivePixelType", "display": "defectivePixelType", "comments": "Dynamic dp Detect Module, 0:single defective pixel mode, 1:dual defective pixel mode", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "det_fine_strength": {"api": "nDetFineStr", "display": "detFineStr", "comments": "local detction strength for defect pixel detection, the larger the value, more defect pixels will be detected", "hint": "Accuracy: U0.6 Range: [0, 63]"}, "det_coarse_strength": {"api": "nDetCoarseStr", "display": "detCoarseStr", "comments": "defect pixel detection threshold, the larger the value, more defect pixels are detected", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "dynamic_dpc_strength": {"api": "nDynamicDpcStr", "display": "dynamicDpcStr", "comments": "the larger the value, the larger the defect pixel probability", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "edge_strength": {"api": "nEdgeStr", "display": "edgeStr", "comments": "direction interpolation ratio used in interpolation, the larger the value, more direction interpolated result used in interpolation, better performance for defective pixels at edge locations", "hint": "Accuracy: U1.7 Range: [0, 255]"}, "hot_cold_type_strength": {"api": "nHotColdTypeStr", "display": "hotColdTypeStr", "comments": "hot_cold result ratio, the larger the value, more hot_cold result used", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sup_wink_threshold": {"api": "nSupWinkThr", "display": "supWinkThr", "comments": "the threshold to use direction interpoation or local avg interpolation result", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "dynamic_dp_upperlimit": {"api": "nDynamicDpUpperlimit", "display": "dynamicDpUpperlimit", "comments": "up margin for color limit, the larger the value, less color limitation", "hint": "Accuracy: U8.6 Range: [0, 16383]"}, "dynamic_dp_lowerlimit": {"api": "nDynamicDpLowerlimit", "display": "dynamicDpLowerlimit", "comments": "low margin for color limit, the larger the value, less color limitation", "hint": "Accuracy: U8.6 Range: [0, 16383]"}, "static_dp_upperlimit": {"api": "nStaticDpUpperlimit", "display": "staticDpUpperlimit", "comments": "up margin for color limit, the larger the value, less color limitation", "hint": "Accuracy: U8.6 Range: [0, 16383]"}, "static_dp_lowerlimit": {"api": "nStaticDpLowerlimit", "display": "staticDpLowerlimit", "comments": "low margin for color limit, the larger the value, less color limitation", "hint": "Accuracy: U8.6 Range: [0, 16383]"}, "normal_pixel_upperlimit": {"api": "nNormalPixelUpperlimit", "display": "normalPixelUpperlimit", "comments": "up margin for color limit, the larger the value, less color limitation", "hint": "Accuracy: U8.6 Range: [0, 16383]"}, "normal_pixel_lowerlimit": {"api": "nNormalPixelLowerlimit", "display": "normalPixelLowerlimit", "comments": "low margin for color limit, the larger the value, less color limitation", "hint": "Accuracy: U8.6 Range: [0, 16383]"}}