{"partition_configs": ["aestat.item_enable", "aestat.grid_enable", "aestat.hist_log_enable", "aestat.hist_linear_enable", "aestat.grid_hori_region_num", "aestat.grid_vert_region_num", "aestat.grid_roi", "aestat.grid_start_pos", "aestat.grid_region_height", "aestat.grid_region_width", "aestat.hist_block_start_pos", "aestat.hist_block_size", "aestat.hist_roi_config", "aestat.item_roi", "awbstat.grid_enable", "awbstat.item_enable", "awbstat.grid_hori_region_num", "awbstat.grid_vert_region_num", "awbstat.grid_roi", "awbstat.grid_start_pos", "awbstat.grid_region_height", "awbstat.grid_region_width", "awbstat.item_roi", "afstat.vert_enable", "afstat.hori_enable", "afstat.grid_hori_region_num", "afstat.grid_vert_region_num", "afstat.grid_roi", "afstat.grid_start_pos", "afstat.grid_region_height", "afstat.grid_region_width"], "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [12], "ref_interp_method": ["linear"]}}, "context": {"AN_ID": {"size": [], "acc": [0, 16, 0], "comment": "AAAS is 0x2271", "type": "AX_U16"}}, "params": {"aes_is_transposed": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "is aes in ITP", "hidden": 1, "auto": 0, "target_conf": ["aestat.grid_hori_region_num", "aestat.grid_vert_region_num", "aestat.grid_roi", "aestat.grid_start_pos", "aestat.grid_region_height", "aestat.grid_region_width", "aestat.item_roi", "aestat.hist_block_start_pos", "aestat.hist_block_size", "aestat.hist_roi_config"], "dependency": "common"}, "aes_enable": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "aes_enable", "hidden": 0, "auto": 0, "target_conf": ["aestat.enable"], "display": "aes enable", "dependency": "user"}, "aes_grid_enable": {"acc": [0, 1], "type": "AX_U8", "size": [2], "range": [0, 1], "default": [0, 0], "comment": "aes_grid_enable", "hidden": 0, "auto": 0, "target_conf": ["aestat.grid_enable"], "display": "aes grid enable", "dependency": "user"}, "aes_grid_specify_roi": {"acc": [0, 1], "type": "AX_U8", "size": [2], "range": [0, 1], "default": [0, 0], "comment": "use user specified roi placement, 1: use, 0: use with default", "hidden": 0, "auto": 0, "target_conf": ["aestat.grid_hori_region_num", "aestat.grid_vert_region_num", "aestat.grid_roi", "aestat.grid_start_pos"], "dependency": "user", "display": "aes specify grid roi"}, "aes_gridroi_offset": {"acc": [0, 16], "type": "AX_U16", "size": [2, 2], "range": [0, 8192], "default": [[0, 0], [0, 0]], "comment": "user set gridroi offset coordinate [y, x] of two independent ROIs", "hidden": 0, "auto": 0, "target_conf": ["aestat.grid_hori_region_num", "aestat.grid_vert_region_num", "aestat.grid_roi", "aestat.grid_start_pos"], "dependency": "user", "display": "aes gridroi offset"}, "aes_region_num": {"acc": [0, 16], "type": "AX_U16", "size": [2, 2], "range": [0, 8192], "default": [[0, 0], [0, 0]], "comment": "user set grid number in region [y_num, x_num] of two independent ROIs", "hidden": 0, "auto": 0, "target_conf": ["aestat.grid_hori_region_num", "aestat.grid_vert_region_num", "aestat.grid_roi", "aestat.grid_start_pos"], "dependency": "user", "display": "hdr aes region num"}, "aes_gridroi_size": {"acc": [0, 16], "type": "AX_U16", "size": [2, 2], "range": [0, 8192], "default": [[0, 0], [0, 0]], "comment": "user set gridroi size [height, width] of two independent ROIs", "hidden": 0, "auto": 0, "target_conf": ["aestat.grid_region_height", "aestat.grid_region_width", "aestat.grid_hori_region_num", "aestat.grid_vert_region_num", "aestat.grid_roi", "aestat.grid_start_pos"], "dependency": "user", "display": "aes gridroi size"}, "aes_grid_4ch_enable": {"acc": [0, 1], "type": "AX_U8", "size": [2], "range": [0, 1], "default": [0, 0], "comment": "static four channel enable", "hidden": 0, "auto": 0, "target_conf": ["aestat.grid_4ch_enable"], "dependency": "user", "display": "aes grid 4ch enable"}, "aes_grid_sat_thr": {"acc": [0, 20, 4], "type": "AX_U32", "size": [2, 4], "range": [0.0, 1048575.9375], "default": [[0, 0, 0, 0], [0, 0, 0, 0]], "comment": "over saturated threshold value", "hidden": 0, "auto": 0, "target_conf": ["aestat.grid_sat_thr"], "dependency": "user", "display": "aes grid sat thr"}, "aes_grid_ycoeff": {"acc": [0, 0, 12], "type": "AX_U16", "size": [4], "range": [0.0, 0.999755859375], "default": [0, 0, 0, 0], "comment": "coeff for compute luma for grid", "hidden": 0, "auto": 0, "target_conf": ["aestat.grid_ycoeff"], "dependency": "user", "display": "aes grid ycoeff"}, "aes_item_enable": {"acc": [0, 1], "type": "AX_U8", "size": [2], "range": [0, 1], "default": [0, 0], "comment": "two aes_item enable", "hidden": 0, "auto": 0, "target_conf": ["aestat.item_enable"], "display": "aes item enable", "dependency": "user"}, "aes_item_specify_roi": {"acc": [0, 1], "type": "AX_U8", "size": [2], "range": [0, 1], "default": [0, 0], "comment": "use user specified roi placement, 1: use, 0: use with default", "hidden": 0, "auto": 0, "target_conf": ["aestat.item_roi"], "dependency": "user", "display": "aes specify item roi"}, "aes_item_roi": {"acc": [0, 16], "type": "AX_U8", "size": [2, 4], "range": [0, 65535], "default": [[0, 0, 0, 0], [0, 0, 0, 0]], "comment": "two aes_item roi", "hidden": 0, "auto": 0, "target_conf": ["aestat.item_roi"], "display": "aes item roi", "dependency": "user"}, "aes_hist_log_enable": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "aes_hist_log_enable", "hidden": 0, "auto": 0, "target_conf": ["aestat.hist_log_enable"], "display": "aes hist log enable", "dependency": "user"}, "aes_hist_linear_enable": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "aes_hist_linear_enable", "hidden": 0, "auto": 0, "target_conf": ["aestat.hist_linear_enable"], "display": "aes hist linear enable", "dependency": "user"}, "aes_hist_mode": {"acc": [0, 2], "type": "AX_U8", "size": [], "range": [0, 2], "default": 0, "comment": "0: HIST_MODE_Y, 1: HIST_MODE_YRGB, 2: HIST_MODE_RGGB", "enum_field": {"0": "HIST_MODE_Y", "1": "HIST_MODE_YRGB", "2": "HIST_MODE_RGGB"}, "hidden": 0, "auto": 0, "target_conf": ["aestat.hist_mode"], "dependency": "user", "display": "aes hist mode"}, "aes_hist_specify_roi": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "use user specified roi placement, 1: use, 0: use with default", "hidden": 0, "auto": 0, "target_conf": ["aestat.hist_block_start_pos", "aestat.hist_block_size", "aestat.hist_roi_config"], "dependency": "user", "display": "aes specify hist roi"}, "aes_histroi_offset": {"acc": [0, 16], "type": "AX_U16", "size": [2], "range": [0, 8192], "default": [0, 0], "comment": "user set histroi coordinate [y, x] for hist stat", "hidden": 0, "auto": 0, "target_conf": ["aestat.hist_block_start_pos", "aestat.hist_block_size", "aestat.hist_roi_config"], "dependency": "user", "display": "aes hist block offset"}, "aes_histroi_size": {"acc": [0, 16], "type": "AX_U16", "size": [2], "range": [0, 8192], "default": [0, 0], "comment": "user set histroi size [height, width] for hist stat", "hidden": 0, "auto": 0, "target_conf": ["aestat.hist_block_start_pos", "aestat.hist_block_size", "aestat.hist_roi_config"], "dependency": "user", "display": "aes hist block size"}, "aes_hist_linear_bins": {"acc": [0, 2], "type": "AX_U8", "size": [], "range": [0, 2], "default": 0, "comment": "hist's x coord (i.e hist_bin num) will be 256 * pow(2, hist_linear_bins)", "hidden": 0, "auto": 0, "target_conf": ["aestat.hist_linear_bins"], "dependency": "user", "display": "aes hist linear bins"}, "aes_hist_linear_scale": {"acc": [1, 4], "type": "AX_S8", "size": [], "range": [-15, 12], "default": 0, "comment": "for compute clip_value when count the target linear hist, clip_value=val*pow(2, linear_scale)", "hidden": 0, "auto": 0, "target_conf": ["aestat.hist_linear_scale"], "dependency": "user", "display": "aes hist linear scale"}, "aes_hist_log_scale": {"acc": [1, 4], "type": "AX_S8", "size": [], "range": [-15, 12], "default": 0, "comment": "for compute input_val when count the target log hist, input_value=val*pow(2, log_scale)", "hidden": 0, "auto": 0, "target_conf": ["aestat.hist_log_scale"], "dependency": "user", "display": "aes hist log scale"}, "aes_hist_weight": {"acc": [0, 8], "type": "AX_U8", "size": [256], "range": [0, 255], "default": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "comment": "locationable weight for count hist", "hidden": 0, "auto": 0, "target_conf": ["aestat.hist_weight"], "dependency": "user", "display": "aes hist weight"}, "aes_hist_ycoeff": {"acc": [0, 0, 12], "type": "AX_U16", "size": [4], "range": [0.0, 0.999755859375], "default": [0, 0, 0, 0], "comment": "coeff for compute luma for hist", "hidden": 0, "auto": 0, "target_conf": ["aestat.hist_y<PERSON>ff"], "dependency": "user", "display": "aes hist ycoeff"}, "aes_hist_wl_gain": {"acc": [0, 1, 8], "type": "AX_U16", "size": [], "range": [0.0, 1.99609375], "default": 1.0, "comment": "white level used for compute histogram maximum compensation gain", "hidden": 1, "auto": 0, "target_conf": ["aestat.hist_wl_gain"], "dependency": "common", "display": "aes hist wl gain"}, "aes_dgain": {"acc": [0, 10, 8], "type": "AX_U32", "size": [4], "range": [0.0, 1023.0], "default": [1.0, 1.0, 1.0, 1.0], "comment": "dgain for statics", "hidden": 0, "auto": 0, "target_conf": ["aestat.dgain"], "dependency": "common", "display": "aes dgain"}, "aes_offset_in": {"acc": [0, 14, 6], "type": "AX_U32", "size": [], "range": [0.0, 16383.984375], "default": 0, "comment": "aes offset in", "hidden": 1, "auto": 0, "target_conf": ["aestat.offset_in"], "dependency": "common", "display": "aes offset in"}, "awbs_is_transposed": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "is awbs in ITP", "hidden": 1, "auto": 0, "target_conf": ["awbstat.grid_hori_region_num", "awbstat.grid_vert_region_num", "awbstat.grid_roi", "awbstat.grid_start_pos", "awbstat.grid_region_height", "awbstat.grid_region_width", "awbstat.item_roi"], "dependency": "common"}, "awbs_enable": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 1, "comment": "enable awb stat", "hidden": 0, "auto": 0, "target_conf": ["awbstat.enable"], "display": "awbs enable"}, "awbs_grid_enable": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "awbs grid enable", "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_enable"], "display": "awbs grid enable", "dependency": "user"}, "awbs_grid_mode": {"acc": [0, 2], "type": "AX_U8", "size": [], "range": [0, 3], "default": 1, "comment": "awb grid stat mode BAYER3CH = 0, BAYER4CH, BAYER4CH_LUMA4CH, BAYER4CH_LUMA2CH", "enum_field": {"0": "BAYER3CH", "1": "BAYER4CH", "2": "BAYER4CH_LUMA4CH", "3": "BAYER4CH_LUMA2CH"}, "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_mode"], "display": "awbs grid mode", "dependency": "user"}, "awbs_grid_specify_roi": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "use user specified roi placement, 1: use, 0: use with default", "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_hori_region_num", "awbstat.grid_vert_region_num", "awbstat.grid_roi", "awbstat.grid_start_pos"], "dependency": "user", "display": "awbs specify grid roi"}, "awbs_gridroi_offset": {"acc": [0, 16], "type": "AX_U16", "size": [2], "range": [0, 8192], "default": [0, 0], "comment": "user set gridroi offset coordinate [y, x]", "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_hori_region_num", "awbstat.grid_vert_region_num", "awbstat.grid_roi", "awbstat.grid_start_pos"], "dependency": "user", "display": "awbs gridroi offset"}, "awbs_region_num": {"acc": [0, 16], "type": "AX_U16", "size": [2], "range": [0, 8192], "default": [0, 0], "comment": "user set grid number in region [y_num, x_num]", "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_hori_region_num", "awbstat.grid_vert_region_num", "awbstat.grid_roi", "awbstat.grid_start_pos"], "display": "awbs region num"}, "awbs_gridroi_size": {"acc": [0, 16], "type": "AX_U16", "size": [2], "range": [0, 8192], "default": [0, 0], "comment": "user set gridroi size [height, width]", "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_region_height", "awbstat.grid_region_width", "awbstat.grid_hori_region_num", "awbstat.grid_vert_region_num", "awbstat.grid_roi", "awbstat.grid_start_pos"], "display": "awbs gridroi size", "dependency": "user"}, "awbs_grid_rgb_thr": {"acc": [1, 14, 6], "type": "AX_S32", "size": [8], "range": [-16383.984375, 16383.984375], "default": [0, 16383.984375, 0, 16383.984375, 0, 16383.984375, 0, 16383.984375], "comment": "awb 4ch of saturated threshold for grid", "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_rgb_thr"], "display": "awbs grid rgb thr", "dependency": "user"}, "awbs_grid_y_thr": {"acc": [1, 14, 6], "type": "AX_S32", "size": [2], "range": [-16383.984375, 16383.984375], "default": [0, 16383.984375], "comment": "awb luma of saturated threshold for grid", "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_y_thr"], "display": "awbs grid y thr", "dependency": "user"}, "awbs_grid_rgb_sat_thr": {"acc": [0, 14, 6], "type": "AX_U32", "size": [4], "range": [0.0, 16383.984375], "default": [0.0, 0.0, 0.0, 0.0], "comment": "awb 4ch of saturated threshold for grid", "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_rgb_sat_thr"], "display": "awbs grid rgb sat thr", "dependency": "user"}, "awbs_grid_y_sat_thr": {"acc": [0, 14, 6], "type": "AX_U32", "size": [], "range": [0.0, 16383.984375], "default": 0.0, "comment": "awb luma ch of saturated threshold for grid", "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_y_sat_thr"], "display": "awbs grid y sat thr", "dependency": "user"}, "awbs_grid_luma_slice_thr": {"acc": [1, 14, 6], "type": "AX_S32", "size": [3], "range": [-16383.984375, 16383.984375], "default": [0.0, 0.0, 0.0], "comment": "awb stat split luma threshold for grid", "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_luma_slice_thr"], "display": "awbs grid luma slice thr", "dependency": "user"}, "awbs_grid_ycoeff": {"acc": [0, 0, 12], "type": "AX_U16", "size": [4], "range": [0.0, 0.999755859375], "default": [0.25, 0.25, 0.25, 0.25], "comment": "awb coeff for computing luma for grid", "hidden": 0, "auto": 0, "target_conf": ["awbstat.grid_ycoeff"], "display": "awbs grid ycoeff", "dependency": "user"}, "awbs_item_enable": {"acc": [0, 1], "type": "AX_U8", "size": [2], "range": [0, 1], "default": [0, 0], "comment": "two awbs_item enable", "hidden": 0, "auto": 0, "target_conf": ["awbstat.item_enable"], "display": "awbs item enable", "dependency": "user"}, "awbs_item_specify_roi": {"acc": [0, 1], "type": "AX_U8", "size": [2], "range": [0, 1], "default": [0, 0], "comment": "use user specified roi placement, 1: use, 0: use with default", "hidden": 0, "auto": 0, "target_conf": ["awbstat.item_roi"], "dependency": "user", "display": "awbs specify item roi"}, "awbs_item_roi": {"acc": [0, 16], "type": "AX_U8", "size": [2, 4], "range": [0, 65535], "default": [[0, 0, 0, 0], [0, 0, 0, 0]], "comment": "two aes_item roi", "hidden": 0, "auto": 0, "target_conf": ["awbstat.item_roi"], "display": "awbs item roi", "dependency": "user"}, "awbs_dgain": {"acc": [0, 10, 8], "type": "AX_U32", "size": [4], "range": [0.0, 1023.0], "default": [1.0, 1.0, 1.0, 1.0], "comment": "dgain for statics", "hidden": 0, "auto": 0, "target_conf": ["awbstat.dgain"], "dependency": "common", "display": "aes dgain"}, "awbs_offset_in": {"acc": [0, 14, 6], "type": "AX_U32", "size": [], "range": [0.0, 1048575.0], "default": 0, "comment": "awbs offset in", "hidden": 1, "auto": 0, "target_conf": ["awbstat.offset_in"], "display": "awbs offset in", "dependency": "common"}, "afs_is_transposed": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "is afs in ITP", "hidden": 1, "auto": 0, "target_conf": ["afstat.grid_hori_region_num", "afstat.grid_vert_region_num", "afstat.grid_roi", "afstat.grid_start_pos", "afstat.grid_region_height", "afstat.grid_region_width"], "dependency": "common"}, "afs_enable": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 1, "comment": "enable", "hidden": 0, "auto": 0, "target_conf": ["afstat.enable"], "display": "afs enable", "dependency": "user"}, "afs_fv_mode": {"acc": [0, 1], "type": "AX_U8", "size": [4], "range": [0, 1], "default": [0, 0, 0, 0], "comment": "independent fv_mode for V1, V2, H1, H2, 0: sum mode, 1: peak mode", "enum_field": {"0": "SUM_MODE", "1": "PEAK_MODE"}, "hidden": 0, "auto": 0, "target_conf": ["afstat.fv_mode"], "display": "afs fv mode", "dependency": "user"}, "afs_wb_gain": {"acc": [0, 4, 8], "type": "AX_U16", "size": [4], "range": [0.0, 15.99609375], "default": 1.0, "comment": "afs wbgain", "hidden": 1, "auto": 0, "target_conf": ["afstat.wb_gain"], "display": "wb_gain", "dependency": "common"}, "afs_fir_enable": {"acc": [0, 1], "type": "AX_U8", "size": [2], "range": [0, 1], "default": [0, 0], "comment": "fir enable for H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.fir_enable"], "display": "afs fir enable", "dependency": "user"}, "afs_grid_specify_roi": {"acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "use user specified roi placement, 1: use, 0: use with default", "hidden": 0, "auto": 0, "target_conf": ["afstat.grid_hori_region_num", "afstat.grid_vert_region_num", "afstat.grid_roi", "afstat.grid_start_pos"], "dependency": "user", "display": "afs specify grid roi"}, "afs_gridroi_offset": {"acc": [0, 16], "type": "AX_U16", "size": [2], "range": [0, 8192], "default": [0, 0], "comment": "user set gridroi offset coordinate [y, x]", "hidden": 0, "auto": 0, "target_conf": ["afstat.grid_hori_region_num", "afstat.grid_vert_region_num", "afstat.grid_roi", "afstat.grid_start_pos"], "dependency": "user", "display": "afs gridroi offset"}, "afs_region_num": {"acc": [0, 16], "type": "AX_U16", "size": [2], "range": [0, 8192], "default": [0, 0], "comment": "user set grid number in region [y_num, x_num]", "hidden": 0, "auto": 0, "target_conf": ["afstat.grid_hori_region_num", "afstat.grid_vert_region_num", "afstat.grid_roi", "afstat.grid_start_pos"], "display": "afs region num", "dependency": "user"}, "afs_gridroi_size": {"acc": [0, 16], "type": "AX_U16", "size": [2], "range": [0, 8192], "default": [0, 0], "comment": "user set gridroi size [height, width]", "hidden": 0, "auto": 0, "target_conf": ["afstat.grid_region_height", "afstat.grid_region_width", "afstat.grid_hori_region_num", "afstat.grid_vert_region_num", "afstat.grid_roi", "afstat.grid_start_pos"], "display": "afs gridroi size", "dependency": "user"}, "afs_coring_thr": {"acc": [0, 8, 10], "type": "AX_U32", "size": [4], "range": [0.0, 255.9990234375], "default": [255.9990234375, 255.9990234375, 255.9990234375, 255.9990234375], "comment": "coring thr for V1, V2, H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.coring_thr"], "display": "afs coring thr", "dependency": "user"}, "afs_coring_gain": {"acc": [0, 5, 7], "type": "AX_U16", "size": [4], "range": [0.0, 31.9921875], "default": [1.0, 1.0, 1.0, 1.0], "comment": "coring gain for V1, V2, H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.coring_gain"], "display": "afs coring gain", "dependency": "user"}, "afs_coring_lut": {"acc": [0, 5], "type": "AX_U8", "size": [4, 16], "range": [0, 31], "default": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "comment": "coring gain lut for V1, V2, H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.coring_lut"], "display": "afs coring lut", "dependency": "user"}, "afs_coring_pix_sum_mode": {"acc": [0, 1], "type": "AX_U8", "size": [4], "range": [0, 1], "default": [0, 0, 0, 0], "comment": "0: don't count pixel' val for smaller than coring thr, 1: do count", "enum_field": {"0": "DO_NOT_COUNT", "1": "DO_COUNT"}, "hidden": 0, "auto": 0, "target_conf": ["afstat.coring_pix_sum_mode"], "display": "afs coring pix sum mode", "dependency": "user"}, "afs_coring_pix_cnt_mode": {"acc": [0, 1], "type": "AX_U8", "size": [4], "range": [0, 1], "default": [0, 0, 0, 0], "comment": "0: don't count pixel's count for smaller than coring thr, 1: do count", "enum_field": {"0": "DO_NOT_COUNT", "1": "DO_COUNT"}, "hidden": 0, "auto": 0, "target_conf": ["afstat.coring_pix_cnt_mode"], "display": "afs coring pix count mode", "dependency": "user"}, "afs_scale_ratio": {"acc": [0, 3], "type": "AX_U8", "size": [], "range": [0, 7], "default": 0, "comment": "scale ratio for V1, V2, H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.scale_ratio"], "display": "afs scale ratio", "dependency": "user"}, "afs_scale_weight": {"acc": [0, 1, 7], "type": "AX_U16", "size": [8], "range": [0.0, 1.0], "default": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "comment": "scale weight for V1, V2, H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.weight"], "display": "afs weight", "dependency": "user"}, "afs_ycoeff": {"acc": [0, 0, 12], "type": "AX_U16", "size": [4], "range": [0.0, 0.999755859375], "default": [0.25, 0.25, 0.25, 0.25], "comment": "coeff for compute luma from bayer", "hidden": 0, "auto": 0, "target_conf": ["afstat.ycoeff"], "display": "afs ycoeff", "dependency": "user"}, "afs_vert_enable": {"acc": [0, 1], "type": "AX_U8", "size": [2], "range": [0, 1], "default": [0, 0], "comment": "enable for V1, V2", "hidden": 0, "auto": 0, "target_conf": ["afstat.vert_enable"], "display": "afs vert enable", "dependency": "user"}, "afs_v_iir_coeff": {"acc": [1, 2, 12], "type": "AX_S16", "size": [2, 10], "range": [-3.999755859375, 3.999755859375], "default": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "comment": "iir coeff for V1, V2", "hidden": 0, "auto": 0, "target_conf": ["afstat.v_iir_coeff"], "display": "afs v iir coeff", "dependency": "user"}, "afs_hori_enable": {"acc": [0, 1], "type": "AX_U8", "size": [2], "range": [0, 1], "default": [0, 0], "comment": "enable for H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.hori_enable"], "display": "afs hori enable", "dependency": "user"}, "afs_h_iir_coeff": {"acc": [1, 2, 12], "type": "AX_S16", "size": [2, 10], "range": [-3.999755859375, 3.999755859375], "default": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "comment": "iir coeff for H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.h_iir_coeff"], "display": "afs h iir coeff", "dependency": "user"}, "afs_h_fir_coeff": {"acc": [1, 6], "type": "AX_S8", "size": [2, 13], "range": [-64, 63], "default": [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], "comment": "fir coeff for H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.h_fir_coeff"], "display": "afs h fir coeff", "dependency": "user"}, "afs_ldg_thr": {"acc": [0, 8, 4], "type": "AX_U16", "size": [4], "range": [0.0, 255.9375], "default": [0, 0, 0, 0], "comment": "luma dependent gain thr independent for V1, V2, H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.ldg_thr"], "display": "afs ldg thr", "dependency": "user"}, "afs_ldg_slope": {"acc": [0, 0, 8], "type": "AX_U8", "size": [4], "range": [0.0, 0.99609375], "default": [0, 0, 0, 0], "comment": "luma dependent gain adj slope independent for V1, V2, H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.ldg_slope"], "display": "afs ldg slope", "dependency": "user"}, "afs_ldg_lim": {"acc": [0, 1, 7], "type": "AX_U8", "size": [4], "range": [0.0, 1.9921875], "default": [0, 0, 0, 0], "comment": "luma dependent gain limit independent for V1, V2, H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.ldg_lim"], "display": "afs ldg lim", "dependency": "user"}, "afs_ldg_range": {"acc": [0, 2], "type": "AX_U8", "size": [2], "range": [0, 3], "default": [0, 0], "comment": "luma dependent gain search range share with V1, V2, H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.ldg_range"], "display": "afs ldg range", "dependency": "user"}, "afs_high_luma_cnt_thr": {"acc": [0, 8, 4], "type": "AX_U16", "size": [4], "range": [0.0, 255.9375], "default": [0, 0, 0, 0], "comment": "high luma count thr independent V1, V2, H1, H2", "hidden": 0, "auto": 0, "target_conf": ["afstat.high_luma_cnt_thr"], "display": "afs high luma cnt thr", "dependency": "user"}, "afs_drc_mode": {"acc": [0, 8], "type": "AX_U8", "size": [], "range": [0, 255], "default": 0, "comment": "drc mode: 0: disable, 1: 8.4 -> 8.4(do nothing), 2: 8.4 -> 8.4 (gam), 3: 8.6 -> 8.4(MSB), 4: 8.6 -> 8.4(gam), 5: 14.6 -> 8.4(MSB) 6: 14.6 -> 8.4(gam)", "enum_field": {"0": "DISABLE", "1": "DO_NOTHING_8.4", "2": "GAM_8.4", "3": "MSB_8.6", "4": "GAM_8.6", "5": "MSB_14.6", "6": "GAM_14.6"}, "hidden": 1, "auto": 0, "target_conf": ["afstat.drc_enable", "afstat.drc_lut"], "display": "afs drc mode", "dependency": "common"}, "afs_dgain": {"acc": [0, 10, 8], "type": "AX_U32", "size": [4], "range": [0.0, 1023.0], "default": [1.0, 1.0, 1.0, 1.0], "comment": "dgain for statics", "hidden": 0, "auto": 0, "target_conf": ["afstat.dgain"], "dependency": "common", "display": "afs dgain"}, "afs_offset_in": {"acc": [0, 14, 6], "type": "AX_U32", "size": [], "range": [0.0, 16383.984375], "default": 0, "hidden": 1, "auto": 0, "target_conf": ["afstat.offset_in"], "display": "afs offset in", "comment": "afs offset in", "dependency": "common"}, "aes_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "hidden": 1, "auto": 0, "target_conf": ["aestat.grid_hori_region_num", "aestat.grid_vert_region_num", "aestat.grid_roi", "aestat.grid_start_pos", "aestat.grid_region_height", "aestat.grid_region_width", "aestat.hist_roi_config", "aestat.hist_block_start_pos", "aestat.hist_block_size", "aestat.item_roi"], "display": "partition information", "comment": "partition information", "dependency": "common"}, "awbs_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "hidden": 1, "auto": 0, "target_conf": ["awbstat.grid_hori_region_num", "awbstat.grid_vert_region_num", "awbstat.grid_roi", "awbstat.grid_start_pos", "awbstat.grid_region_height", "awbstat.grid_region_width", "awbstat.item_roi"], "display": "partition information", "comment": "partition information", "dependency": "common"}, "afs_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "hidden": 1, "auto": 0, "target_conf": ["afstat.grid_hori_region_num", "afstat.grid_vert_region_num", "afstat.grid_roi", "afstat.grid_start_pos", "afstat.grid_region_height", "afstat.grid_region_width"], "display": "partition information", "comment": "partition information", "dependency": "common"}}, "submodules": {"aes_setup": {"params": ["aes_offset_in", "aes_hist_wl_gain", "aes_dgain"], "configs": []}, "aes_adj": {"params": ["aes_enable", "aes_grid_4ch_enable", "aes_grid_sat_thr", "aes_grid_ycoeff", "aes_hist_mode", "aes_hist_linear_bins", "aes_hist_linear_scale", "aes_hist_log_scale", "aes_hist_weight", "aes_hist_y<PERSON>ff"], "configs": []}, "aes_ptn": {"params": ["aes_is_transposed", "aes_item_enable", "aes_grid_enable", "aes_hist_log_enable", "aes_hist_linear_enable", "aes_grid_specify_roi", "aes_gridroi_offset", "aes_region_num", "aes_gridroi_size", "aes_hist_specify_roi", "aes_histroi_offset", "aes_histroi_size", "aes_item_specify_roi", "aes_item_roi", "aes_partition_info"], "configs": []}, "awbs_setup": {"params": ["awbs_offset_in", "awbs_dgain"], "configs": []}, "awbs_adj": {"params": ["awbs_enable", "awbs_grid_mode", "awbs_grid_rgb_thr", "awbs_grid_y_thr", "awbs_grid_rgb_sat_thr", "awbs_grid_y_sat_thr", "awbs_grid_luma_slice_thr", "awbs_grid_ycoeff"], "configs": []}, "awbs_ptn": {"params": ["awbs_is_transposed", "awbs_grid_enable", "awbs_item_enable", "awbs_grid_specify_roi", "awbs_gridroi_offset", "awbs_region_num", "awbs_gridroi_size", "awbs_item_specify_roi", "awbs_item_roi", "awbs_partition_info"], "configs": []}, "afs_setup": {"params": ["afs_offset_in", "afs_dgain", "afs_wb_gain"], "configs": []}, "afs_adj": {"params": ["afs_enable", "afs_fv_mode", "afs_fir_enable", "afs_coring_thr", "afs_coring_gain", "afs_coring_lut", "afs_coring_pix_sum_mode", "afs_coring_pix_cnt_mode", "afs_scale_ratio", "afs_scale_weight", "afs_ycoeff", "afs_v_iir_coeff", "afs_h_iir_coeff", "afs_h_fir_coeff", "afs_ldg_thr", "afs_ldg_slope", "afs_ldg_lim", "afs_ldg_range", "afs_high_luma_cnt_thr"], "configs": []}, "afs_ptn": {"params": ["afs_is_transposed", "afs_vert_enable", "afs_hori_enable", "afs_grid_specify_roi", "afs_gridroi_offset", "afs_region_num", "afs_gridroi_size", "afs_partition_info"], "configs": []}, "afs_drc": {"params": ["afs_drc_mode"], "configs": []}}, "target_module": {"mc20l": {"aestat": {"id": 8200, "method": 0}, "awbstat": {"id": 8400, "method": 0}, "afstat": {"id": 8000, "method": 0}}}, "structs": {}, "configs": {"aestat": {"enable": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "grid_enable": {"acc": [0, 1], "size": [2], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "grid_hori_region_num": {"acc": [0, 8], "size": [2], "description": "grid horizontal region num", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "grid_vert_region_num": {"acc": [0, 16], "size": [2], "description": "grid vertical region num", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "grid_region_height": {"acc": [0, 10], "size": [2], "description": "grid region height", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "grid_region_width": {"acc": [0, 13], "size": [2], "description": "grid region width", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "grid_4ch_enable": {"acc": [0, 1], "size": [2], "description": "grid 4ch enable, 0: 1ch (Y) ,  1: 4ch (RGGB)", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "grid_sat_thr": {"acc": [0, 14, 6], "size": [2, 4], "description": "saturation threshold (R(orY)/Gr/Gb/B) for (grid0, grid1)", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "grid_roi": {"acc": [0, 16], "size": [2, 4], "description": "ROI  : (x, y, w, h)  for (grid0, grid1)", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "grid_start_pos": {"acc": [0, 13], "size": [2, 2], "description": "offset in 1st grid: (x, y)  for (grid0, grid1)", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "grid_ycoeff": {"acc": [0, 0, 12], "size": [4], "description": "grid Y coefficient, calc Y for grid", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "item_enable": {"acc": [0, 1], "size": [2], "description": "item ROI enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "item_roi": {"acc": [0, 16], "size": [2, 4], "description": "ROI  : (x, y, w, h)  for various item", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "hist_log_enable": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "hist_linear_enable": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "hist_mode": {"acc": [0, 2], "size": [], "description": "hist mode, 0: Y, 1:YRGB, 2:RGGB", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "hist_roi_config": {"acc": [0, 16], "size": [4], "description": "hist ROI setting (x, y, w, h)", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "hist_linear_bins": {"acc": [0, 2], "size": [], "description": "hist linear bin num, 0: 256, 1: 512, 2:1024", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "hist_linear_scale": {"acc": [1, 4], "size": [], "description": "hist linear scale", "usage": "", "constraints": "", "type": "AX_S8", "partition": "-"}, "hist_log_scale": {"acc": [1, 4], "size": [], "description": "hist log scale", "usage": "", "constraints": "", "type": "AX_S8", "partition": "-"}, "hist_weight": {"acc": [0, 8], "size": [256], "description": "hist weight (16x16grid)", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "hist_block_start_pos": {"acc": [0, 10], "size": [2], "description": "hist weight block pos offset in 1st grid (x, y)", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "hist_block_size": {"acc": [0, 10], "size": [2], "description": "hist weight block size (x, y)", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "hist_ycoeff": {"acc": [0, 0, 12], "size": [4], "description": "Y coefficient, calc Y for hist", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "hist_wl_gain": {"acc": [0, 1, 8], "size": [], "description": "white level gain for hist", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "dgain": {"acc": [0, 10, 8], "size": [4], "description": "dgain for AEstat input (R, Gr, Gb, B)", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "offset_in": {"acc": [0, 14, 6], "size": [], "description": "", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}}, "awbstat": {"enable": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "grid_enable": {"acc": [0, 1], "size": [], "description": "enable for grid", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "grid_mode": {"acc": [0, 2], "size": [], "description": "grid_mode, 0: RGB, 1:RGGB, 2:RGGBxLuma4ch 3: RGGBxLuma2ch", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "grid_hori_region_num": {"acc": [0, 8], "size": [], "description": "region num(H)", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "grid_vert_region_num": {"acc": [0, 8], "size": [], "description": "region num(V)", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "grid_region_height": {"acc": [0, 10], "size": [], "description": "region height", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "grid_region_width": {"acc": [0, 10], "size": [], "description": "region width", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "grid_rgb_thr": {"acc": [1, 14, 6], "size": [8], "description": "RGB thr for valid sum and count", "usage": "", "constraints": "", "type": "AX_S32", "partition": "-"}, "grid_y_thr": {"acc": [1, 14, 6], "size": [2], "description": "Y thr for valid sum and count", "usage": "", "constraints": "", "type": "AX_S32", "partition": "-"}, "grid_rgb_sat_thr": {"acc": [0, 14, 6], "size": [4], "description": "RGB thr for sat sum and count", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "grid_y_sat_thr": {"acc": [0, 14, 6], "size": [], "description": "Y thr for sat sum and count", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "grid_roi": {"acc": [0, 16], "size": [4], "description": "grid ROI", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "grid_start_pos": {"acc": [0, 10], "size": [2], "description": "grid start position", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "grid_luma_slice_thr": {"acc": [1, 14, 6], "size": [3], "description": "thr for luma slice (grid)", "usage": "", "constraints": "", "type": "AX_S32", "partition": "-"}, "grid_ycoeff": {"acc": [0, 0, 12], "size": [4], "description": "Y coefficient for grid", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "item_enable": {"acc": [0, 1], "size": [2], "description": "item ROI enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "item_roi": {"acc": [0, 16], "size": [2, 4], "description": "ROI  : (x, y, w, h)  for various item", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "dgain": {"acc": [0, 10, 8], "size": [4], "description": "dgain for AWBstat input (R, Gr, Gb, B)", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "offset_in": {"acc": [0, 14, 6], "size": [], "description": "", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}}, "afstat": {"enable": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "fv_mode": {"acc": [0, 1], "size": [4], "description": "af stat mode, 0: sum mode, 1: peak mode", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "wb_gain": {"acc": [0, 4, 8], "size": [4], "description": "white balance gain [0]:R,[1]:Gr,[2]:Gb,[3]:B", "usage": "", "constraints": "all", "type": "AX_U16", "partition": "-"}, "fir_enable": {"acc": [0, 1], "size": [2], "description": "fir stat enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "grid_vert_region_num": {"acc": [0, 8], "size": [], "description": "grid vertical region number", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "grid_hori_region_num": {"acc": [0, 8], "size": [], "description": "grid horizontal region number", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "grid_region_height": {"acc": [0, 10], "size": [], "description": "grid region height", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "grid_region_width": {"acc": [0, 10], "size": [], "description": "grid region width", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "coring_thr": {"acc": [0, 8, 10], "size": [4], "description": "coring threshold", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "coring_gain": {"acc": [0, 5, 7], "size": [4], "description": "coring gain", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "coring_lut": {"acc": [0, 5], "size": [4, 16], "description": "coring adjust lut", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "coring_pix_sum_mode": {"acc": [0, 1], "size": [4], "description": "af coring pix cnt mode, 1: sum all pix, 0: count only > threshold", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "coring_pix_cnt_mode": {"acc": [0, 1], "size": [4], "description": "af coring pix cnt mode, 1: count all pix, 0: count only > threshold", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "scale_ratio": {"acc": [0, 3], "size": [], "description": "scale down ratio", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "ycoeff": {"acc": [0, 0, 12], "size": [4], "description": "coeff for compute Y", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "vert_enable": {"acc": [0, 1], "size": [2], "description": "vertical enable for v0, v1", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "v_iir_coeff": {"acc": [1, 2, 12], "size": [2, 10], "description": "vertical iir coeff", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "hori_enable": {"acc": [0, 1], "size": [2], "description": "horizontal enable for v0, v1", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "h_iir_coeff": {"acc": [1, 2, 12], "size": [2, 10], "description": "horizontal iir coeff", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "h_fir_coeff": {"acc": [1, 6], "size": [2, 13], "description": "horizontal fir coeff", "usage": "", "constraints": "", "type": "AX_S8", "partition": "-"}, "weight": {"acc": [0, 1, 7], "size": [8], "description": "downsample image weight", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "ldg_thr": {"acc": [0, 8, 4], "size": [4], "description": "luma depend gain thr", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "ldg_slope": {"acc": [0, 0, 8], "size": [4], "description": "luma depend gain slope", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "ldg_lim": {"acc": [0, 1, 7], "size": [4], "description": "luma depend gain limit", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "ldg_range": {"acc": [0, 2], "size": [2], "description": "luma depend gain pix range", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "high_luma_cnt_thr": {"acc": [0, 8, 4], "size": [4], "description": "high luma count thr", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "grid_roi": {"acc": [0, 16], "size": [4], "description": "grid ROI", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "grid_start_pos": {"acc": [0, 10], "size": [2], "description": "grid start position", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "drc_enable": {"acc": [0, 1], "size": [], "description": "drc enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "drc_lut": {"acc": [0, 8, 4], "size": [15], "description": "drc lut", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "dgain": {"acc": [0, 10, 8], "size": [4], "description": "dgain for AFstat input (R, Gr, Gb, B)", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "offset_in": {"acc": [0, 14, 6], "size": [], "description": "", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}}}}