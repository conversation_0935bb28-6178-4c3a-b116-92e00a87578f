h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||

| None | | | | | | | | | | | | |

h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'raw_scl.enable' | rawscl module enable | user | None | None | None |
| crop_enable | crop_enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'raw_scl.crop_enable' | rawscl crop enable | user | None | None | None |
| crop_size_height | crop_size_height | u16 | AX_U16 | [\] | [128, 65535\] | [None, None\] | 128 | None | open | 'raw_scl.roi_size' | rawscl roi size | user | None | None | None |
| crop_size_width | crop_size_width | u16 | AX_U16 | [\] | [128, 65535\] | [None, None\] | 128 | None | open | 'raw_scl.roi_size' | rawscl roi size | user | None | None | None |
| crop_start_y | crop_start_y | u16 | AX_U16 | [\] | [0, 65535\] | [None, None\] | 0 | None | open | 'raw_scl.roi_start' | rawscl roi start y | user | None | None | None |
| crop_start_x | crop_start_x | u16 | AX_U16 | [\] | [0, 65535\] | [None, None\] | 0 | None | open | 'raw_scl.roi_start' | rawscl roi start x | user | None | None | None |
| scale_enable | scale_enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'raw_scl.scale_enable' | rawscl scale enable | user | None | None | None |
| scale_ratio_height | scale_ratio_height | u7 | AX_U8 | [\] | [2, 64\] | [None, None\] | 2 | None | open | 'raw_scl.scale_ratio' | rawscl scale ratio height, 2<=ratio<=64 | user | None | None | None |
| scale_ratio_width | scale_ratio_width | u7 | AX_U8 | [\] | [2, 64\] | [None, None\] | 2 | None | open | 'raw_scl.scale_ratio' | rawscl scale ratio width, 2<=ratio<=64 | user | None | None | None |
| scale_mode | scale_mode | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'raw_scl.scale_mode' | rawscl scale mode; 0: Average; 1: Decimation | user | None | None | None |
| output_acc_mode | output_acc_mode | u2 | AX_U8 | [\] | [0, 2\] | [None, None\] | 0 | None | open | 'raw_scl.output_acc_mode' | rawscl output acc mode; 0: u8.4; 1: u8.2; 2: u8.0 | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |