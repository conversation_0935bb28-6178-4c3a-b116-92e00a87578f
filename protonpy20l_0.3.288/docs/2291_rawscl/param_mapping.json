{"enable": {"api": "nRawsclEn", "display": "enable", "comments": "rawscl module enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "output_acc_mode": {"api": "eOutputAccMode", "display": "outputAccMode", "comments": "rawscl output acc mode; 0: u8.4; 1: u8.2; 2: u8.0", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "scale_enable": {"api": "nScaleEnable", "display": "scaleEnable", "comments": "rawscl scale enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "scale_ratio_height": {"api": "nScaleRatioHeight", "display": "scaleRatioHeight", "comments": "rawscl scale ratio height, 2<=ratio<=64", "hint": "Accuracy: U7.0 Range: [2, 64]"}, "scale_ratio_width": {"api": "nScaleRatioWidth", "display": "scaleRatioWidth", "comments": "rawscl scale ratio width, 2<=ratio<=64", "hint": "Accuracy: U7.0 Range: [2, 64]"}, "scale_mode": {"api": "eScaleMode", "display": "scaleMode", "comments": "rawscl scale mode; 0: Average; 1: Decimation", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "crop_enable": {"api": "nCropEnable", "display": "cropEnable", "comments": "rawscl crop enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "crop_size_height": {"api": "nCropSizeHeight", "display": "cropSizeHeight", "comments": "rawscl roi size", "hint": "Accuracy: U16.0 Range: [128, 65535]"}, "crop_size_width": {"api": "nCropSizeWidth", "display": "cropSizeWidth", "comments": "rawscl roi size", "hint": "Accuracy: U16.0 Range: [128, 65535]"}, "crop_start_y": {"api": "nCropStartY", "display": "cropStartY", "comments": "rawscl roi start y", "hint": "Accuracy: U16.0 Range: [0, 65535]"}, "crop_start_x": {"api": "nCropStartX", "display": "cropStartX", "comments": "rawscl roi start x", "hint": "Accuracy: U16.0 Range: [0, 65535]"}}