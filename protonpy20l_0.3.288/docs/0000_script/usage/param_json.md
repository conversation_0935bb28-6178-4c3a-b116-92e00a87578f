# 使用脚本前需要准备的文件

调用脚本最核心的文件是`param_base.json`，所有的代码，文档都基于此文件进行生成。

## param_base.json的最小构成
![params_base_json](../assets/imgs/param_json.jpg)

param_base.json的最小集合由`params`,`submodules`和`target_module`三个域组成

* [params](./usage/params.md)是tuning接口，需要手写。
* [submodules](./usage/submodules.md)是对tuning接口的细分，通常可以根据功能语义进行细分。
* [target_module](./usage/target_modules.md)是对应的硬件模块名，id，以及method。一个algo logic可以对应多个硬件模块。

## param_base.json的可选域
![options](../assets/imgs/options.png)

param_base.json的可选域包括[context](./usage/context.md)，[partition_configs](./usage/partition_configs.md)，[structs](./usage/structs.md)和[controls](./usage/controls.md)四个域，这些域可以根据实际需求进行添加。