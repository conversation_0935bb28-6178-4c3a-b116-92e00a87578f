{"enable": {"api": "nSharpenEn", "display": "enable", "comments": "sharpen enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "ud_gain_lut": {"api": "nUdGainLut", "display": "udGainLut", "comments": "set undirectional gain w.r.t. texture strength", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "ud_gain_lut_anchor": {"api": "nUdGainLutAnchor", "display": "udGainLutAnchor", "comments": "smaller value means more anchor points in flat/weak texture regions", "hint": "Accuracy: U3.0 Range: [0, 7]"}, "dir_gain_lut": {"api": "nDirGainLut", "display": "dir<PERSON><PERSON><PERSON><PERSON>", "comments": "set directional gain w.r.t. edge strength", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "dir_gain_lut_anchor": {"api": "nDirGainLutAnchor", "display": "dirGainLutAnchor", "comments": "smaller value means more anchor points in flat/weak texture regions", "hint": "Accuracy: U3.0 Range: [0, 7]"}, "ud_freq": {"api": "nUdFreq", "display": "udFreq", "comments": "larger value means higher undirectional frequecny will be enhanced", "hint": "Accuracy: U5.5 Range: [0, 1023]"}, "dir_freq": {"api": "nDirFreq", "display": "dir<PERSON><PERSON><PERSON>", "comments": "larger value means higher directional frequecny will be enhanced", "hint": "Accuracy: U5.5 Range: [0, 1023]"}, "edge_strength": {"api": "nEdgeStr", "display": "edgeStr", "comments": "larger value means to use more directional filter's result", "hint": "Accuracy: U4.0 Range: [0, 15]"}, "edge_threshold": {"api": "nEdgeThr", "display": "edgeThr", "comments": "larger value means less pixels will be considered as edge pixels", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "edge_smooth_thin_ratio": {"api": "nEdgeSmoothThinRatio", "display": "edgeSmoothThinRatio", "comments": "larger value means to make the edge much thinner", "hint": "Accuracy: U4.0 Range: [0, 15]"}, "overshoot": {"api": "nOvershoot", "display": "overshoot", "comments": "larger value means stronger overshoot", "hint": "Accuracy: U0.7 Range: [0, 127]"}, "undershoot": {"api": "nUndershoot", "display": "undershoot", "comments": "larger value means stronger undershoot", "hint": "Accuracy: U0.7 Range: [0, 127]"}, "detail_region_threshold": {"api": "nDetailRegionThr", "display": "detailRegionThr", "comments": "larger value means more pixels will be considered as detail region", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "detail_overshoot_adjust": {"api": "nDetailOvershootAdj", "display": "detailOvershootAdj", "comments": "larger value means stronger overshoot for detail region", "hint": "Accuracy: S0.7 Range: [-127, 127]"}, "detail_undershoot_adjust": {"api": "nDetailUndershootAdj", "display": "detailUndershootAdj", "comments": "larger value means stronger undershoot for detail region", "hint": "Accuracy: S0.7 Range: [-127, 127]"}, "shoot_release_ratio": {"api": "nShootReleaseRatio", "display": "shootReleaseRatio", "comments": "larger value means shoot suppression tends to ignore more weak texture/detail region", "hint": "Accuracy: U1.4 Range: [0, 16]"}, "shoot_sup_range": {"api": "nShootSupRange", "display": "shootSupRange", "comments": "larger value means shoot suppression tends to control more edges", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "shoot_sup_strength": {"api": "nShootSupStr", "display": "shootSupStr", "comments": "larger value means stronger shoot suppression on edges", "hint": "Accuracy: U0.7 Range: [0, 127]"}, "shp_extra_gain": {"api": "nShpExtraGain", "display": "shpExtraGain", "comments": "larger value means more extra gain applied to ud/dir gain lut", "hint": "Accuracy: U4.4 Range: [16, 255]"}, "shp_limit": {"api": "nShpLimit", "display": "shpLimit", "comments": "smaller value means more restriction on the pixel value change, and may reduce the number of white/black points", "hint": "Accuracy: U5.5 Range: [0, 1023]"}, "luma_gain_lut_negative": {"api": "nLumaGainLutNeg", "display": "lumaGainLutNeg", "comments": "adjust sharpen gain for negative hf based on image brightness", "hint": "Accuracy: U1.5 Range: [0, 63]"}, "luma_gain_lut_positive": {"api": "nLumaGainLutPos", "display": "lumaGainLutPos", "comments": "adjust sharpen gain for positive hf based on image brightness", "hint": "Accuracy: U1.5 Range: [0, 63]"}, "red_gain": {"api": "nRedGain", "display": "red<PERSON>ain", "comments": "smaller value means less sharpen for red region", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "green_gain": {"api": "nGreenGain", "display": "greenGain", "comments": "smaller value means less sharpen for green region", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "blue_gain": {"api": "nBlueGain", "display": "blueGain", "comments": "smaller value means less sharpen for blue region", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "skin_gain": {"api": "nSkinGain", "display": "skinGain", "comments": "smaller value means less sharpen for skin region", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "skin_center": {"api": "nSkinCenter", "display": "skinCenter", "comments": "skin color center", "hint": "Accuracy: S7.2 Range: [-512, 511]"}, "skin_radius": {"api": "nSkinRadius", "display": "skinRadius", "comments": "skin color radius", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "motion_mask_enable": {"api": "nMotionMaskEnable", "display": "motionMaskEnable", "comments": "motion mask enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "motion_overshoot": {"api": "nMotionOvershoot", "display": "motionOvershoot", "comments": "larger value means stronger overshoot", "hint": "Accuracy: U0.7 Range: [0, 127]"}, "motion_undershoot": {"api": "nMotionUndershoot", "display": "motionUndershoot", "comments": "larger value means stronger undershoot", "hint": "Accuracy: U0.7 Range: [0, 127]"}, "motion_ud_gain_lut": {"api": "nMotionUdGainLut", "display": "motionUdGainLut", "comments": "set undirectional gain w.r.t. texture strength", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "motion_dir_gain_lut": {"api": "nMotionDirGainLut", "display": "motionDirGainLut", "comments": "set directional gain w.r.t. edge strength", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "motion_mask_lut": {"api": "nMotionMaskLut", "display": "motionMaskLut", "comments": "used to threshold/reverse the input motion lut", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "nr_strength": {"api": "nNrStr", "display": "nrStr", "comments": "larger value means stronger noise reduction", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "nr_ud_texture_sensitivity": {"api": "nNrUdTextureSens", "display": "nrUdTextureSens", "comments": "larger value means image has more undirectional textures and apply less noise reduction", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "nr_ud_texture_threshold": {"api": "nNrUdTextureThr", "display": "nrUdTextureThr", "comments": "larger value means less pixels will be considered as real texture and apply more noise reduction", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "nr_ud_limit": {"api": "nNrUdLimit", "display": "nrUdLimit", "comments": "larger value means less restriction on undirectional noise reduction", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "ud_coring": {"api": "nUdCoring", "display": "udCoring", "comments": "larger value means stronger coring for undirectional details", "hint": "Accuracy: U3.3 Range: [0, 63]"}, "nr_dir_edge_sensitivity": {"api": "nNrDirEdgeSens", "display": "nrDirEdgeSens", "comments": "larger value means image has more directional edges and apply less noise reduction", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "nr_dir_edge_threshold": {"api": "nNrDirEdgeThr", "display": "nrDirEdgeThr", "comments": "larger value means less pixels will be considered as real edge and apply more noise reduction", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "nr_dir_limit": {"api": "nNrDirLimit", "display": "nrDirLimit", "comments": "larger value means less restriction on directional noise reduction", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "dir_coring": {"api": "nDirCoring", "display": "dirCoring", "comments": "larger value means stronger coring for directional details", "hint": "Accuracy: U3.3 Range: [0, 63]"}}