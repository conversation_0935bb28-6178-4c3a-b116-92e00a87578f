{"context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "EISSTAT is 0x2281", "type": "AX_U16", "default": "0x2281"}, "enable": {"size": [], "acc": [0, 1], "comment": "eisstat enable, 0: disable, 1: enable", "type": "AX_U8", "default": 1}, "scale_ratio": {"size": [], "acc": [0, 2], "comment": "scale ratio, 0: 1/1, 1: 1/2, 2: 1/4", "type": "AX_U8", "default": 0}, "search_range": {"size": [2], "acc": [0, 9], "comment": "eisstat post process search range, must be mutiple of 8", "type": "AX_U16", "default": [8, 8]}, "proj_v_region_len": {"size": [2], "acc": [0, 14], "comment": "vertical projection region length", "type": "AX_U16", "default": [5, 5]}, "proj_h_region_len": {"size": [2], "acc": [0, 14], "comment": "horizontal projection region length", "type": "AX_U16", "default": [5, 5]}, "proj_v_region_num": {"size": [2], "acc": [0, 3], "comment": "vertical projection region number", "type": "AX_U16", "default": [5, 5]}, "proj_h_region_num": {"size": [2], "acc": [0, 3], "comment": "horizontal projection region number", "type": "AX_U16", "default": [5, 5]}, "proj_v_roi": {"size": [4], "acc": [0, 14], "comment": "vertical projection normalized roi", "type": "AX_U16", "default": [0, 0, 1920, 1080]}, "proj_h_roi": {"size": [4], "acc": [0, 14], "comment": "horizontal projection normalized roi", "type": "AX_U16", "default": [0, 0, 1920, 1080]}, "partition_info": {"size": [], "acc": [0, 1], "comment": "partition info", "type": "ax_isp_ptn_info_t", "default": ""}}, "params": {"enable": {"acc": [0, 1], "auto": 0, "comment": "eisstat enable, 0: disable, 1: enable", "default": 1, "hidden": 0, "range": [0, 1], "size": [], "target_conf": [], "type": "AX_U8", "dependency": ""}, "search_range": {"acc": [0, 9], "auto": 0, "comment": "eisstat post process search range, must be mutiple of 8", "default": [8, 8], "hidden": 0, "range": [0, 504], "size": [2], "target_conf": [], "type": "AX_U16", "dependency": ""}, "scale_ratio": {"acc": [0, 2], "auto": 0, "comment": "scale ratio, 0: 1/1, 1: 1/2, 2: 1/4", "default": 0, "hidden": 0, "range": [0, 2], "size": [], "target_conf": [], "type": "AX_U8", "dependency": ""}, "lut_enable": {"acc": [0, 1], "auto": 0, "comment": "lut enable, 0: disable, 1: enable", "default": 1, "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["eis_pre.lut_enable"], "type": "AX_U8", "dependency": ""}, "lut": {"acc": [0, 8, 2], "auto": 0, "comment": "lut table", "default": [0, 32, 64, 96, 128, 160, 192, 224, 256, 288, 320, 352, 384, 416, 448, 480, 512, 544, 576, 608, 640, 672, 704, 736, 768, 800, 832, 864, 896, 928, 960, 992, 1023], "range": [0.0, 255.75], "hidden": 0, "size": [33], "target_conf": ["eis_pre.lut"], "type": "AX_U16", "dependency": ""}, "offset_in": {"acc": [0, 8, 2], "auto": 0, "comment": "offset in, from ACP", "default": 64, "range": [0.0, 255.75], "hidden": 1, "size": [], "target_conf": ["eis_pre.offset_in"], "type": "AX_U16", "dependency": "common"}, "proj_h_region_num": {"acc": [0, 3], "auto": 0, "comment": "horizontal projection region number", "default": [5, 5], "range": [1, 5], "hidden": 0, "size": [2], "target_conf": [], "type": "AX_U16", "dependency": ""}, "proj_h_roi": {"acc": [0, 1, 14], "auto": 0, "comment": "horizontal projection normalized roi", "default": [0.0, 0.0, 1.0, 1.0], "range": [0.0, 1.0], "hidden": 0, "size": [4], "target_conf": [], "type": "AX_U16", "dependency": ""}, "proj_h_shiftbit": {"acc": [0, 3], "auto": 0, "comment": "horizontal projection shift bit", "default": 0, "range": [0, 7], "hidden": 0, "size": [], "target_conf": ["eis_pre.proj_h_shiftbit"], "type": "AX_U8", "dependency": ""}, "proj_v_region_num": {"acc": [0, 3], "auto": 0, "comment": "vertical projection region number", "default": [5, 5], "range": [1, 5], "hidden": 0, "size": [2], "target_conf": [], "type": "AX_U16", "dependency": ""}, "proj_v_roi": {"acc": [0, 1, 14], "auto": 0, "comment": "vertical projection normalized roi", "default": [0.0, 0.0, 1.0, 1.0], "range": [0.0, 1.0], "hidden": 0, "size": [4], "target_conf": [], "type": "AX_U16", "dependency": ""}, "proj_v_shiftbit": {"acc": [0, 3], "auto": 0, "comment": "vertical projection shift bit", "default": 0, "range": [0, 7], "hidden": 0, "size": [], "target_conf": ["eis_pre.proj_v_shiftbit"], "type": "AX_U8", "dependency": ""}, "partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "hidden": 1, "auto": 0, "target_conf": [], "dependency": "common"}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": 16, "ref_interp_method": ["linear"]}}, "submodules": {"setup": {"params": ["partition_info", "offset_in"], "configs": []}, "common": {"params": ["enable", "scale_ratio", "search_range", "proj_v_region_num", "proj_h_region_num", "proj_v_roi", "proj_h_roi"], "configs": []}, "eis_pre": {"params": ["lut_enable", "lut", "proj_v_shiftbit", "proj_h_shiftbit"], "configs": ["eis_pre.enable", "eis_pre.scale_ratio", "eis_pre.proj_v_region_len", "eis_pre.proj_v_start_pos", "eis_pre.proj_v_start_index", "eis_pre.proj_v_roi", "eis_pre.proj_h_region_len", "eis_pre.proj_h_start_pos", "eis_pre.proj_h_start_index", "eis_pre.proj_h_roi"]}, "eis_post": {"params": [], "configs": ["eis_post.offset_calc_enable", "eis_post.scale_ratio", "eis_post.search_range", "eis_post.proj_v_region_num", "eis_post.proj_h_region_num", "eis_post.proj_v_region_len", "eis_post.proj_h_region_len"]}}, "target_module": {"mc20l": {"eisstat": {"id": 8700, "method": 0}}}, "partition_configs": ["eis_pre.proj_h_roi", "eis_pre.proj_v_roi", "eis_pre.proj_h_start_pos", "eis_pre.proj_v_start_pos", "eis_pre.proj_h_start_index", "eis_pre.proj_v_start_index", "eis_post.offset_calc_enable"], "configs": {"eis_pre": {"enable": {"acc": [0, 1], "size": [], "type": "AX_U8", "partition": "-"}, "scale_ratio": {"acc": [0, 2], "size": [], "type": "AX_U8", "partition": "-"}, "lut_enable": {"acc": [0, 1], "size": [], "type": "AX_U8", "partition": "-"}, "lut": {"acc": [0, 8, 2], "size": [33], "type": "AX_U16", "partition": "-"}, "proj_v_region_len": {"acc": [0, 14], "size": [2], "type": "AX_U16", "partition": "-"}, "proj_v_start_pos": {"acc": [0, 14], "size": [2], "type": "AX_U16", "partition": "-"}, "proj_v_start_index": {"acc": [0, 5], "size": [], "type": "AX_U8", "partition": "-"}, "proj_v_roi": {"acc": [0, 14], "size": [4], "type": "AX_U16", "partition": "-"}, "proj_v_shiftbit": {"acc": [0, 3], "size": [], "type": "AX_U8", "partition": "-"}, "proj_h_region_len": {"acc": [0, 14], "size": [2], "type": "AX_U16", "partition": "-"}, "proj_h_start_pos": {"acc": [0, 14], "size": [2], "type": "AX_U16", "partition": "-"}, "proj_h_start_index": {"acc": [0, 5], "size": [], "type": "AX_U8", "partition": "-"}, "proj_h_roi": {"acc": [0, 14], "size": [4], "type": "AX_U16", "partition": "-"}, "proj_h_shiftbit": {"acc": [0, 3], "size": [], "type": "AX_U8", "partition": "-"}, "offset_in": {"acc": [0, 8, 2], "size": [], "type": "AX_U16", "partition": "-"}}, "eis_post": {"offset_calc_enable": {"acc": [0, 1], "size": [], "type": "AX_U8", "partition": "-"}, "scale_ratio": {"acc": [0, 2], "size": [], "type": "AX_U8", "partition": "-"}, "proj_v_region_num": {"acc": [0, 5], "size": [2], "type": "AX_U8", "partition": "-"}, "proj_v_region_len": {"acc": [0, 14], "size": [2], "type": "AX_U16", "partition": "-"}, "proj_h_region_num": {"acc": [0, 5], "size": [2], "type": "AX_U8", "partition": "-"}, "proj_h_region_len": {"acc": [0, 14], "size": [2], "type": "AX_U16", "partition": "-"}, "search_range": {"acc": [0, 9], "size": [2], "type": "AX_U16", "partition": "-"}}}}