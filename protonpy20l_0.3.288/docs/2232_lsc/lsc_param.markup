h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| offset_in |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1024 | 16.0 | hidden | 'lsc.offset_in' | offset in | common |
| offset_out |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1024 | 16.0 | hidden | 'lsc.offset_out' | offset out | common |
| partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'lsc.mesh_length', 'lsc.inv_mesh_length', 'lsc.locat', 'lsc.mesh_start', 'lsc.inv_mesh_start', 'lsc.mesh_end' | partition information | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'lsc.rr_mesh', 'lsc.gr_mesh', 'lsc.gb_mesh', 'lsc.bb_mesh' | 0:disable, 1:enable |   | None | None | None |
| luma_ratio | luma ratio | u8 | AX_U8 | [\] | [0, 100\] | [None, None\] | 100 | None | open | 'lsc.rr_mesh', 'lsc.gr_mesh', 'lsc.gb_mesh', 'lsc.bb_mesh' | luma shading attenuation ratio |   | ['gain/lux'\] | [12\] | ['linear'\] |
| color_ratio | color ratio | u8 | AX_U8 | [\] | [0, 100\] | [None, None\] | 100 | None | open | 'lsc.rr_mesh', 'lsc.gr_mesh', 'lsc.gb_mesh', 'lsc.bb_mesh' | color shading attenuation ratio |   | ['color_temp'\] | [10\] | ['linear'\] |
| luma_mesh | luma mesh | u4.14 | AX_U32 | [15, 19\] | [16384, 262143\] | [1.0, 15.99993896484375\] | [16384, 16384, ... , 16384\] | [1.0, 1.0, ... , 1.0\] | open | 'lsc.rr_mesh', 'lsc.gr_mesh', 'lsc.gb_mesh', 'lsc.bb_mesh' | luma shading correction mesh lut |   | None | None | None |
| color_mesh_rr | color mesh rr | u4.14 | AX_U32 | [15, 19\] | [16384, 262143\] | [1.0, 15.99993896484375\] | [16384, 16384, ... , 16384\] | [1.0, 1.0, ... , 1.0\] | open | 'lsc.rr_mesh' | color shading R channel correction mesh lut |   | ['color_temp'\] | [10\] | ['linear'\] |
| color_mesh_gr | color mesh gr | u4.14 | AX_U32 | [15, 19\] | [16384, 262143\] | [1.0, 15.99993896484375\] | [16384, 16384, ... , 16384\] | [1.0, 1.0, ... , 1.0\] | open | 'lsc.gr_mesh' | color shading GR channel correction mesh lut |   | ['color_temp'\] | [10\] | ['linear'\] |
| color_mesh_gb | color mesh gb | u4.14 | AX_U32 | [15, 19\] | [16384, 262143\] | [1.0, 15.99993896484375\] | [16384, 16384, ... , 16384\] | [1.0, 1.0, ... , 1.0\] | open | 'lsc.gb_mesh' | color shading GB channel correction mesh lut |   | ['color_temp'\] | [10\] | ['linear'\] |
| color_mesh_bb | color mesh bb | u4.14 | AX_U32 | [15, 19\] | [16384, 262143\] | [1.0, 15.99993896484375\] | [16384, 16384, ... , 16384\] | [1.0, 1.0, ... , 1.0\] | open | 'lsc.bb_mesh' | color shading B channel correction mesh lut |   | ['color_temp'\] | [10\] | ['linear'\] |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |