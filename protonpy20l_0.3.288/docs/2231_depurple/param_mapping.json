{"det_enable": {"api": "nDepurpleEn", "display": "detEnable", "comments": "software enable for pfd", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "det_c_ratio": {"api": "nDetCRatio", "display": "detCRatio", "comments": "in pfd hardware, weight for computr color channel using bayer", "hint": "Accuracy: S1.6 Range: [-64, 64]"}, "det_adj_y_gam": {"api": "nDetAdjYGam", "display": "detAdjYGam", "comments": "for gamma correction to meet with decent gam", "hint": "Accuracy: U3.5 Range: [0, 224]"}, "det_edge_c_enable": {"api": "nDetEdgeCEnable", "display": "detEdgeCEnable", "comments": "in pfd hardware, for color channel edge offset", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "det_edge_slope_y": {"api": "nDetEdgeSlopeY", "display": "detEdgeSlopeY", "comments": "in pfd hardware, for luma channel edge gain", "hint": "Accuracy: U1.6 Range: [0, 64]"}, "det_edge_offset_y": {"api": "nDetEdgeOffsetY", "display": "detEdgeOffsetY", "comments": "in pfd hardware, for luma channel edge offset", "hint": "Accuracy: S1.6 Range: [-64, 64]"}, "det_edge_slope_c": {"api": "nDetEdgeSlopeC", "display": "detEdgeSlopeC", "comments": "in pfd hardware, for color channel edge gain", "hint": "Accuracy: U1.6 Range: [0, 64]"}, "det_edge_offset_c": {"api": "nDetEdgeOffsetC", "display": "detEdgeOffsetC", "comments": "in pfd hardware, for color channel edge offset", "hint": "Accuracy: S1.6 Range: [-64, 64]"}, "det_seledge_slope_y": {"api": "nDetSeledgeSlopeY", "display": "detSeledgeSlopeY", "comments": "in pfd hardware, for compensate luma with slope for", "hint": "Accuracy: U1.8 Range: [0, 256]"}, "det_seledge_thr_y": {"api": "nDetSeledgeThrY", "display": "detSeledgeThrY", "comments": "for luma compensation when detect edge, used to compute for mesh-wisely", "hint": "Accuracy: U8.6 Range: [0, 16383]"}, "det_seledge_thr_y_mesh": {"api": "nDetSeledgeThrYMesh", "display": "detSeledgeThrYMesh", "comments": "location wise strength adjust for edge compensation", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "det_seledge_weight": {"api": "nDetSeledgeWeight", "display": "detSeledgeWeight", "comments": "in pfd hardware, edge detection weight", "hint": "Accuracy: U1.6 Range: [0, 64]"}, "det_mask_strength": {"api": "nDetMaskStr", "display": "detMaskStr", "comments": " in pfd hardware, strength adjust for mask", "hint": "Accuracy: U3.6 Range: [0, 511]"}, "det_mask_dialate_enable": {"api": "nDetMaskDialateEnable", "display": "detMaskDialateEnable", "comments": " in pfd hardware, dialate mask enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "det_mask_dialate_weight": {"api": "nDetMaskDialateWeight", "display": "detMaskDialateWeight", "comments": " in pfd hardware, dialate weight for mask", "hint": "Accuracy: U1.6 Range: [0, 64]"}, "det_adj_mesh_enable": {"api": "nDetAdjMeshEnable", "display": "detAdjMeshEnable", "comments": "in pfd hardware, for enable locationwise strength adjustment", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "det_adj_mesh": {"api": "nDetAdjMesh", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comments": "in pfd hardware, mesh for adjust depurple strength", "hint": "Accuracy: U2.6 Range: [0, 255]"}, "desat_enable": {"api": "nDepurpleEn", "display": "desatEnable", "comments": "in lce hardware, desat_enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "desat_strength": {"api": "nDesatStr", "display": "desatStr", "comments": "in lce hardware, desat_strength", "hint": "Accuracy: U4.4 Range: [0, 240]"}, "desat_luma_lut": {"api": "nDesatLumaLut", "display": "desatLumaLut", "comments": "in lce hardware, desat_luma_lut", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "desat_sat_lut": {"api": "nDesatSatLut", "display": "desatSatLut", "comments": "in lce hardware, desat_sat_lut", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "desat_angle_ratio_lut": {"api": "nDesatAngleRatioLut", "display": "desatAngleRatioLut", "comments": "in lce hardware, will calculate 16 phase ratio for clc hardware, color adjust 16 phase ratio for mask receiving from pfd", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "desat_uv_val": {"api": "nDesatUvVal", "display": "desatUvVal", "comments": "in lce hardware, depurple aera fill color with UV", "hint": "Accuracy: S7.2 Range: [-511, 511]"}, "desat_debug_enable": {"api": "nDesatDebugEnable", "display": "desatDebugEnable", "comments": "in lce hardware, desat debug enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "desat_debug_thr": {"api": "nDesatDebugThr", "display": "desatDebugThr", "comments": "in lce hardware, debug depurple threshold for showing debug info", "hint": "Accuracy: U1.4 Range: [0, 16]"}, "desat_debug_color": {"api": "nDesatDebugColor", "display": "desatDebugColor", "comments": "in lce hardware, debug mode's color fill for depurple aera with YUV", "hint": "Accuracy: S8.2 Range: [-1023, 1023]"}}