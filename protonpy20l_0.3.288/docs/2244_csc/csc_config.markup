h2. Conf list
h3. csc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | all | 0: bypass, 1: enable |  |
| input_type | u1 | [\] | - | all | 0: YUV422, 1: RGB | set 1 as default |
| output_type | u1 | [\] | - | all | 0: YUV422, 1: YUV420 | set 1 as default |
| matrix | s2.10 | [3, 3\] | - | all | csc conversion matrix | set to the calculation result of chroma matrix and color space standards such as BT601, BT709, BT2020.we can convert the range(limited or full) by this matrix. but it's not recommended |
| offset_in | s8.2 | [3\] | - | all | minus offset from input | normally set [16,16,16\] |
| offset_out | s8.2 | [3\] | - | all | add offset from output | normally set [16,0,0\] |
| decimation_h | s1.5 | [7\] | - | sum equal to 1 | YUV444 to YUV422 horizontal chroma subsampling filter coef | set [0.0, 0.0, 0.0, 0.5, 0,5, 0, 0\] as default |
| decimation_v | u1.1 | [2\] | - | sum equal to 1 | YUV422 to YUV420 vertical chroma  subsampling filter coef | set [0.5, 0,5\] |

