{"context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "BLC is 0x2212"}, "enable": {"size": [], "acc": [0, 1], "default": 1}, "channel_id": {"size": [], "acc": [0, 1], "default": 0, "comment": "0: SDR/HDR_Main, 1: HDR_Sub"}, "offset_out": {"size": [], "acc": [0, 8, 6], "default": 16.0}}, "autos": {"1": {"ref_mode": ["exp_time", "again"], "ref_group_num": [10, 16], "ref_interp_method": ["linear", "linear"]}}, "params": {"enable": {"acc": [0, 1], "size": [], "default": 1, "range": [0, 1], "hidden": 0, "auto": 0, "comment": "soft enable switch for the entire module", "dependency": " ", "target_conf": ["itp_blc.dgain", "itp_blc.dither_enable", "itp_blc.offset_out"]}, "channel_id": {"acc": [0, 1], "size": [], "default": 0, "range": [0, 1], "hidden": 1, "auto": 0, "comment": "0: SDR/HDR_Main, 1: HDR_Sub", "dependency": "common", "target_conf": ["itp_blc.dither_pmask"]}, "offset_out": {"acc": [0, 8, 6], "size": [], "default": 16.0, "range": [0.0, 255.984375], "hidden": 1, "auto": 0, "dependency": "common", "comment": "offset out", "target_conf": []}, "sbl_corr_enable": {"acc": [0, 1], "size": [], "default": 0, "range": [0, 1], "hidden": 0, "auto": 0, "comment": "sbl corr enable", "dependency": " ", "target_conf": ["itp_blc.sbl_corr_enable"]}, "sbl_val": {"acc": [0, 8, 10], "size": [4], "default": [16.0, 16.0, 16.0, 16.0], "range": [0.0, 255.9990234375], "hidden": 0, "auto": 1, "comment": "auto param, interpolation based on exposure time and gain", "dependency": " ", "target_conf": ["itp_blc.sbl_val"]}, "gbl_corr_enable": {"acc": [0, 1], "size": [], "default": 0, "range": [0, 1], "hidden": 0, "auto": 0, "comment": "gbl corr enable", "dependency": " ", "target_conf": ["itp_blc.gbl_corr_enable"]}, "gbl_val": {"acc": [1, 7, 10], "size": [4], "default": [0.0, 0.0, 0.0, 0.0], "range": [-128.0, 127.9990234375], "hidden": 0, "auto": 1, "comment": "auto param, interpolation based on exposure time and gain", "dependency": " ", "target_conf": ["itp_blc.gbl_val"]}, "sensor_bl": {"acc": [0, 8, 10], "size": [], "default": 16.0, "range": [0.0, 255.9990234375], "hidden": 1, "auto": 0, "comment": "sensor black level, used to calculate dgain. When the difference with sbl_val is large, the calculated dgain will be inaccurate", "dependency": "common", "target_conf": ["itp_blc.dgain"]}, "lin_enable": {"acc": [0, 1], "size": [], "default": 0, "range": [0, 1], "hidden": 0, "auto": 0, "comment": "0: disable, 1: enable", "dependency": " ", "target_conf": ["itp_blc.lin_enable"]}, "lin_lut_x": {"acc": [0, 8, 4], "size": [16], "default": [16.0, 32.0, 48.0, 64.0, 80.0, 96.0, 112.0, 128.0, 144.0, 160.0, 176.0, 192.0, 208.0, 224.0, 240.0, 255.9375], "range": [1.0, 255.9375], "hidden": 0, "auto": 0, "comment": "x coordinate of lin lut, assume there is a leading zero point by default", "dependency": " ", "target_conf": ["itp_blc.lin_lut_x", "itp_blc.lin_lut_slope"]}, "lin_lut_y": {"acc": [0, 8, 6], "size": [16, 4], "default": [[16.0, 16.0, 16.0, 16.0], [32.0, 32.0, 32.0, 32.0], [48.0, 48.0, 48.0, 48.0], [64.0, 64.0, 64.0, 64.0], [80.0, 80.0, 80.0, 80.0], [96.0, 96.0, 96.0, 96.0], [112.0, 112.0, 112.0, 112.0], [128.0, 128.0, 128.0, 128.0], [144.0, 144.0, 144.0, 144.0], [160.0, 160.0, 160.0, 160.0], [176.0, 176.0, 176.0, 176.0], [192.0, 192.0, 192.0, 192.0], [208.0, 208.0, 208.0, 208.0], [224.0, 224.0, 224.0, 224.0], [240.0, 240.0, 240.0, 240.0], [255.984375, 255.984375, 255.984375, 255.984375]], "range": [1.0, 255.984375], "hidden": 0, "auto": 0, "comment": "y coordinate of lin lut, assume there is a leading zero point by default", "dependency": " ", "target_conf": ["itp_blc.lin_lut_y", "itp_blc.lin_lut_slope"]}, "dither_seed_enable": {"acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "0: use stat dither_seed, 1: use conf dither_seed. set 1 for the 1st frame, then set 0 from the 2nd frame onwards.", "hidden": 1, "auto": 0, "target_conf": ["itp_blc.dither_seed_enable"], "dependency": "common"}, "det_enable": {"acc": [0, 1], "size": [], "default": 1, "range": [0, 1], "hidden": 0, "auto": 0, "comment": "det enable, 0: disable, 1: enable", "dependency": " ", "target_conf": ["bl_det.det_enable"]}, "bpc_det_th": {"acc": [0, 8, 10], "size": [], "default": 128.0, "range": [0.0, 255.9990234375], "hidden": 0, "auto": 1, "dependency": " ", "comment": "threshold for quickly checking whether the current point is a bad point", "target_conf": ["bl_det.bpc_det_th"]}, "det_window": {"acc": [0, 14], "size": [4], "default": "[0, 0, img.height, img.width]", "range": [0, 16383], "hidden": 1, "auto": 0, "comment": "det window, in [y, x, height, width] order", "dependency": "common", "target_conf": ["bl_det.det_window"]}, "active_region": {"acc": [0, 14], "size": [4], "default": "[0, 0, img.height, img.width]", "range": [0, 16383], "hidden": 1, "auto": 0, "comment": "active region area, in [y, x, height, width] order", "dependency": "common", "target_conf": ["bl_det.active_region"]}}, "submodules": {"setup": {"params": ["offset_out", "channel_id", "active_region", "det_window"], "configs": ["itp_blc.enable", "itp_blc.dither_pmask", "bl_det.enable"]}, "common": {"params": ["enable", "sensor_bl"], "configs": []}, "lin": {"params": ["lin_enable", "lin_lut_x", "lin_lut_y"], "configs": []}, "sbl": {"params": ["sbl_corr_enable", "sbl_val"], "configs": []}, "gbl": {"params": ["gbl_corr_enable", "gbl_val"], "configs": []}, "dither": {"params": ["dither_seed_enable"], "configs": ["itp_blc.dither_seed"]}, "det": {"params": ["det_enable", "bpc_det_th"], "configs": []}}, "target_module": {"mc20l": {"itp_blc": {"id": 1300, "method": 0}, "bl_det": {"id": 1100, "method": 0}}}, "structs": {}, "configs": {"itp_blc": {"enable": {"acc": [0, 1], "size": [], "description": "bypass / enable, 0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "lin_enable": {"acc": [0, 1], "size": [], "description": "lin enable, 0: disable, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "lin_lut_x": {"acc": [0, 8, 4], "size": [15], "description": "lin lut anchor in x_coordinate", "usage": "", "constraints": "lin_lut_x[n] <= lin_lut_x[n+1]", "type": "AX_U16", "partition": "-"}, "lin_lut_y": {"acc": [0, 8, 6], "size": [15, 4], "description": "four channel lin lut anchor in y_coordinate", "usage": "", "constraints": "lin_lut_y[n][m] <= lin_lut_y[n+1][m]", "type": "AX_U16", "partition": "-"}, "lin_lut_slope": {"acc": [0, 12, 8], "size": [16, 4], "description": "four channel lin lut slope ", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "sbl_corr_enable": {"acc": [0, 1], "size": [], "description": "sbl enable, 0: disable, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sbl_val": {"acc": [0, 8, 10], "size": [4], "description": "sbl value, channel split, 0: R, 1: Gb, 2: Gr, 3: B", "usage": "commonly, around sensor bl", "constraints": "all", "type": "AX_U32", "partition": "-"}, "gbl_corr_enable": {"acc": [0, 1], "size": [], "description": "gbl enable, 0: disable, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "gbl_val": {"acc": [1, 7, 10], "size": [4], "description": "gbl value, channel split, 0: R, 1: Gb, 2: Gr, 3: B", "usage": "", "constraints": "", "type": "AX_S32", "partition": "-"}, "dgain": {"acc": [0, 1, 8], "size": [], "description": "support affine data", "usage": "white_level - 16.0(offset_out) / white_level - black_level(min(sbl_val))", "constraints": "all", "type": "AX_U16", "partition": "-"}, "dither_enable": {"acc": [0, 1], "size": [], "description": "0: disable, 1: enable", "usage": "set 1 as default", "constraints": "", "type": "AX_U8", "partition": "-"}, "dither_seed_enable": {"acc": [0, 1], "size": [], "description": "0: use stat dither_seed, 1: use conf dither_seed", "usage": "set 1 for the 1st frame, then set 0 from the 2nd frame onwards", "constraints": "", "type": "AX_U8", "partition": "-"}, "dither_seed": {"acc": [0, 16], "size": [2], "description": "value of dither seed", "usage": "set non 0, when dither enable", "constraints": "[0,2^16), [0,2^15)", "type": "AX_U16", "partition": "-"}, "dither_pmask": {"acc": [0, 16], "size": [2], "description": "dither pmask", "usage": "should set properly, pMask are picked from https://git-ext.axera-tech.com/isp/xyclops/lfsr/-/tree/master/pMask 16.txt,15.txt", "constraints": "[0,2^16), [0,2^15)", "type": "AX_U16", "partition": "-"}, "offset_out": {"acc": [0, 8, 6], "size": [], "description": "offset out", "usage": "16.0", "constraints": "all", "type": "AX_U16", "partition": "-"}}, "bl_det": {"enable": {"acc": [0, 1], "size": [], "description": "bypass / enable, 0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "active_region": {"acc": [0, 14], "size": [4], "description": "active region area, in [y, x, height, width] order", "usage": "", "constraints": "<=(image_height, image_width)", "type": "AX_U16", "partition": "-"}, "det_enable": {"acc": [0, 1], "size": [], "description": "det enable, 0: disable, 1: enable", "usage": "set 1 as default", "constraints": "", "type": "AX_U8", "partition": "-"}, "bpc_det_th": {"acc": [0, 8, 10], "size": [], "description": "threshold for quickly checking whether the current point is a bad point", "usage": "", "constraints": "<=white_level", "type": "AX_U32", "partition": "-"}, "det_window": {"acc": [0, 14], "size": [4], "description": "det window, in [y, x, height, width] order", "usage": "", "constraints": "<= image_size", "type": "AX_U16", "partition": "-"}}}, "partition_configs": []}