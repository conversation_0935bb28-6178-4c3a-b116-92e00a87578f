h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| hcg_mode |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 1 | None | hidden | 'ainr.hcg_mode' | hcg_mode | common |
| hdr_ratio |  | u9.8 | AX_U32 | [\] |  [0, 131071\] | [0.0, 511.99609375\] | 256 | 1.0 | hidden |  | hdr ratio | common |
| hdrc_input_max_value |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1048575 | 16383.984375 | hidden |  | hdrc input max value | common |
| hdrc_output_max_value |  | u10.6 | AX_U16 | [\] |  [0, 65535\] | [0.0, 1023.984375\] | 65535 | 1023.984375 | hidden |  | hdrc output max value | common |
| hdrc_offset_in |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1024 | 16.0 | hidden | 'hdrc.offset_in' | hdrc offset in | common |
| hdrc_offset_out |  | u10.6 | AX_U16 | [\] |  [0, 65535\] | [0.0, 1023.984375\] | 4096 | 64.0 | hidden | 'hdrc.offset_out' | hdrc offset out | common |
| hdrc_dither_enable |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 1 | None | hidden | 'hdrc.dither_enable' | hdrc dither enable | common |
| hdrc_dither_seed_enable |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 1 | None | hidden | 'hdrc.dither_seed_enable' | hdrc dither seed enable | common |
| hidrc_offset_in |  | u10.6 | AX_U16 | [\] |  [0, 65535\] | [0.0, 1023.984375\] | 4096 | 64.0 | hidden | 'hidrc.offset_in' | hidrc offset in | common |
| hidrc_offset_out |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1024 | 16.0 | hidden | 'hidrc.offset_out' | hidrc offset out | common |
| hidrc_input_max_value |  | u10.6 | AX_U16 | [\] |  [0, 65535\] | [0.0, 1023.984375\] | 65535 | 1023.984375 | hidden |  | hidrc input max value | common |
| hidrc_output_max_value |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1048575 | 16383.984375 | hidden |  | hidrc output max value | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'ainr.enable' | SW enable | user | None | None | None |
| model_list_num | model_list_num | u8 | AX_U8 | [\] | [0, 6\] | [None, None\] | 6 | None | open | 'ainr.model_list_num' | model_list_num | user | None | None | None |
| model_list_path | model_list_path | acc_unknown | AX_CHAR | [6, 128\] | [None, None\] | [None, None\] | None | None | open | 'ainr.model_list_path' | model_list_path | user | None | None | None |
| curr_model_path | curr_model_path | acc_unknown | AX_CHAR | [128\] | [None, None\] | [None, None\] | None | None | open | 'ainr.curr_model_path' | curr_model_path | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['nearest', 'nearest'\] |
| curr_temporal_nr_name | curr_temporal_nr_name | acc_unknown | AX_CHAR | [128\] | [None, None\] | [None, None\] | None | None | open | 'ainr.curr_temporal_nr_name' | curr_temporal_nr_name | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['nearest', 'nearest'\] |
| curr_spatial_nr_name | curr_spatial_nr_name | acc_unknown | AX_CHAR | [128\] | [None, None\] | [None, None\] | None | None | open | 'ainr.curr_spatial_nr_name' | curr_spatial_nr_name | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['nearest', 'nearest'\] |
| bias_out | bias_out | s9.6 | AX_S16 | [4\] | [-32768, 32767\] | [-512.0, 511.984375\] | [0, 0, 0, 0\] | [0.0, 0.0, 0.0, 0.0\] | open |  | bias_out | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['nearest', 'nearest'\] |
| offset_2d | offset_2d | u16 | AX_U16 | [4\] | [0, 65535\] | [None, None\] | [4096, 4096, 4096, 4096\] | None | open | 'ainr.offset_2d' | offset_2d | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['nearest', 'nearest'\] |
| offset_3d | offset_3d | u16 | AX_U16 | [4\] | [0, 65535\] | [None, None\] | [4096, 4096, 4096, 4096\] | None | open | 'ainr.offset_3d' | offset_3d | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['nearest', 'nearest'\] |
| lut_temporal_blend | lut_temporal_blend | u8 | AX_U8 | [17, 2\] | [0, 255\] | [None, None\] | [[0, 0\], [16, 16\], [32, 32\], [48, 48\], [64, 64\], [80, 80\], [96, 96\], [112, 112\], [128, 128\], [143, 143\], [159, 159\], [175, 175\], [191, 191\], [207, 207\], [223, 223\], [239, 239\], [255, 255\]\] | None | open | 'ainr.lut_temporal_blend', 'ainr.position_temporal_blend', 'ainr.step_temporal_blend' | lut_temporal_blend | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| lut_spatial_guidance | lut_spatial_guidance | u8 | AX_U8 | [17, 2\] | [0, 255\] | [None, None\] | [[0, 0\], [16, 16\], [32, 32\], [48, 48\], [64, 64\], [80, 80\], [96, 96\], [112, 112\], [128, 128\], [143, 143\], [159, 159\], [175, 175\], [191, 191\], [207, 207\], [223, 223\], [239, 239\], [255, 255\]\] | None | open | 'ainr.lut_spatial_guidance', 'ainr.position_spatial_guidance', 'ainr.step_spatial_guidance' | lut_spatial_guidance | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| lut_spatial_blend | lut_spatial_blend | u8 | AX_U8 | [17, 2\] | [0, 255\] | [None, None\] | [[0, 0\], [16, 16\], [32, 32\], [48, 48\], [64, 64\], [80, 80\], [96, 96\], [112, 112\], [128, 128\], [143, 143\], [159, 159\], [175, 175\], [191, 191\], [207, 207\], [223, 223\], [239, 239\], [255, 255\]\] | None | open | 'ainr.lut_spatial_blend', 'ainr.position_spatial_blend', 'ainr.step_spatial_blend' | lut_spatial_blend | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| lut_hdr_mask | lut_hdr_mask | u8 | AX_U8 | [17, 2\] | [0, 255\] | [None, None\] | [[0, 0\], [16, 16\], [32, 32\], [48, 48\], [64, 64\], [80, 80\], [96, 96\], [112, 112\], [128, 128\], [143, 143\], [159, 159\], [175, 175\], [191, 191\], [207, 207\], [223, 223\], [239, 239\], [255, 255\]\] | None | open | 'ainr.lut_hdr_mask', 'ainr.position_hdr_mask', 'ainr.step_hdr_mask' | lut_hdr_mask | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| lut_ext_temporal | lut_ext_temporal | u8 | AX_U8 | [17, 2\] | [0, 255\] | [None, None\] | [[0, 0\], [16, 16\], [32, 32\], [48, 48\], [64, 64\], [80, 80\], [96, 96\], [112, 112\], [128, 128\], [143, 143\], [159, 159\], [175, 175\], [191, 191\], [207, 207\], [223, 223\], [239, 239\], [255, 255\]\] | None | open | 'ainr.lut_ext_temporal', 'ainr.position_ext_temporal', 'ainr.step_ext_temporal' | lut_ext_temporal | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| lut_high_frequency | lut_high_frequency | u8 | AX_U8 | [17, 2\] | [0, 255\] | [None, None\] | [[0, 0\], [16, 16\], [32, 32\], [48, 48\], [64, 64\], [80, 80\], [96, 96\], [112, 112\], [128, 128\], [143, 143\], [159, 159\], [175, 175\], [191, 191\], [207, 207\], [223, 223\], [239, 239\], [255, 255\]\] | None | open | 'ainr.lut_high_frequency', 'ainr.position_high_frequency', 'ainr.step_high_frequency' | lut_high_frequency | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| nr_level_mode | nr_level_mode | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open |  | 0: lut fusion, 1: only use nr level | user | None | None | None |
| spatial_nr_level | spatial_nr_level | s7 | AX_S8 | [\] | [-127, 127\] | [None, None\] | 0 | None | open |  | spatial_nr_level | user | None | None | None |
| temporal_nr_level | temporal_nr_level | s7 | AX_S8 | [\] | [-127, 127\] | [None, None\] | 0 | None | open |  | temporal_nr_level | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |