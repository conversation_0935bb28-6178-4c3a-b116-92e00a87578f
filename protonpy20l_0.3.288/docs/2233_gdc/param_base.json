{"partition_configs": ["yuv_gdc.mesh_start", "yuv_gdc.pos_start", "yuv_gdc.internal_start", "yuv_gdc.partition_start"], "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "yuv_gdc.is 0x2233"}, "com_matrix": {"size": [2, 3], "type": "AX_F64", "comment": "common matrix", "default": 1.0}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "params": {"partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "target_conf": ["yuv_gdc.mesh_start", "yuv_gdc.pos_start", "yuv_gdc.internal_start", "yuv_gdc.partition_start", "yuv_gdc.mesh_length", "yuv_gdc.inv_mesh_length"], "display": "yuv_gdc partition information", "comment": "yuv_gdc partition information", "hidden": 1, "dependency": "common", "auto": 0}, "com_matrix": {"display": "common matrix", "acc": [1, 14, 10], "size": [3, 3], "hidden": 1, "auto": 0, "dependency": "user", "comment": "common matrix", "target_conf": []}, "ldc_enable": {"display": "ldc_enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 0, "comment": "ldc enable", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "ldc_intrinsic_matrix": {"display": "ldc intrinsic matrix", "acc": [1, 7, 8], "size": [3, 3], "hidden": 0, "auto": 0, "dependency": "user", "comment": "ldc intrinsic matrix", "target_conf": []}, "ldc_distortion_coeff": {"display": "ldc distortion coeff", "acc": [1, 7, 8], "size": [8], "hidden": 0, "auto": 0, "dependency": "user", "comment": "ldc distortion coeff", "target_conf": []}, "dis_enable": {"display": "dis enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 0, "comment": "dis enable", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "dis_sad_thres": {"display": "dis sad thres", "acc": [0, 8, 8], "size": [], "hidden": 0, "auto": 0, "dependency": "user", "comment": "dis sad thres", "target_conf": []}, "dis_frame_pos_weight": {"display": "dis frame pos weight", "acc": [0, 8, 8], "size": [16], "hidden": 0, "auto": 0, "dependency": "user", "comment": "dis frame pos weight", "target_conf": []}, "rotate_enable": {"display": "rotate enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 0, "comment": "rotate enable", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "rotate_angle_mode": {"display": "rotate angle mode", "acc": [0, 2], "size": [], "hidden": 0, "auto": 0, "dependency": "user", "comment": "4 kind of rotation angle 0/90/180/270, 0: 0°, 1: 90° 2: 180° 3: 270°", "target_conf": []}, "flip_enable": {"display": "flip enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 0, "comment": "flip enable", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "mirror_enable": {"display": "mirror enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 0, "comment": "mirror enable", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "crop_enable": {"display": "crop enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 0, "comment": "crop enable", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "crop_start": {"display": "crop start", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop start coordinates", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "crop_size": {"display": "crop size", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop size", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "scale_enable": {"display": "scale enable", "acc": [0, 1], "range": [0, 1], "type": "AX_U8", "size": [], "default": 0, "comment": "scale enable", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "scale_step": {"display": "scale step", "acc": [0, 6, 10], "size": [2], "comment": "Scalar factor", "default": [1, 1], "range": [0.0, 63.9990234375], "hidden": 0, "auto": 0, "target_conf": ["yuv_gdc.output_size"], "dependency": "user"}, "enable": {"display": "enable", "acc": [0, 1], "size": [], "default": 1, "range": [0, 1], "hidden": 0, "auto": 0, "comment": "soft enable switch for the entire module", "dependency": "user", "target_conf": ["yuv_gdc.enable"]}, "undistort_enable": {"display": "undistort enable", "acc": [0, 1], "size": [], "hidden": 0, "auto": 0, "dependency": "user", "comment": "1: undistort process, 0: bypass undistort process", "target_conf": ["yuv_gdc.undistort_enable"]}, "input_format": {"display": "input format", "acc": [0, 1], "size": [], "hidden": 1, "auto": 0, "dependency": "user", "comment": "1: 420 input, 0: 422 input", "target_conf": ["yuv_gdc.input_mode", "yuv_gdc.input_bit_mode", "yuv_gdc.process_mode"]}, "mesh_wise_mode": {"display": "mesh wise mode", "acc": [0, 1], "size": [], "hidden": 1, "auto": 0, "dependency": "user", "comment": "0: pixel wise, 1: mesh_wise", "target_conf": ["yuv_gdc.mesh_wise_mode"]}, "pix_padding_mode": {"display": "pix padding mode", "acc": [0, 1], "size": [], "hidden": 0, "auto": 0, "dependency": "user", "comment": "pixel padding is: 1: CONSTANT 0: MIRROR_ABA", "target_conf": ["yuv_gdc.pix_padding_mode"]}, "const_Y": {"display": "const Y", "acc": [0, 8, 2], "size": [], "hidden": 0, "auto": 0, "dependency": "user", "comment": "const value Y for padding pixel", "target_conf": ["yuv_gdc.const_Y"]}, "const_UV": {"display": "const UV", "acc": [1, 7, 2], "size": [2], "hidden": 0, "auto": 0, "dependency": "user", "comment": "const value UV for pading pixel", "target_conf": ["yuv_gdc.const_UV"]}, "mesh_padding_mode": {"display": "mesh padding mode", "acc": [0, 1], "size": [], "hidden": 0, "auto": 0, "dependency": "user", "comment": "0: mesh is cycle padding, 1: mesh is constant padding", "target_conf": ["yuv_gdc.mesh_padding_mode"]}, "const_dx": {"display": "const x", "acc": [1, 14, 10], "size": [], "hidden": 0, "auto": 0, "dependency": "user", "comment": "constant padding mesh value for dx", "target_conf": ["yuv_gdc.const_dx"]}, "const_dy": {"display": "const y", "acc": [1, 14, 10], "size": [], "hidden": 0, "auto": 0, "dependency": "user", "comment": "constant padding mesh value for dy", "target_conf": ["yuv_gdc.const_dy"]}, "mesh_value_mode": {"display": "mesh value mode", "acc": [0, 1], "size": [], "hidden": 0, "auto": 0, "dependency": "user", "comment": "0: mesh is absolute displacement, 1: mesh is relative displacement", "target_conf": ["yuv_gdc.mesh_value_mode"]}, "mesh_height": {"display": "mesh height", "acc": [0, 14], "size": [], "hidden": 1, "auto": 0, "dependency": "user", "comment": "mesh height", "target_conf": ["yuv_gdc.mesh_height"]}, "mesh_width": {"display": "mesh width", "acc": [0, 14], "size": [], "hidden": 1, "auto": 0, "dependency": "user", "comment": "mesh width", "target_conf": ["yuv_gdc.mesh_width"]}}, "submodules": {"setup": {"params": ["enable", "input_format", "const_Y", "const_UV", "const_dx", "const_dy"], "configs": ["yuv_gdc.input_size", "yuv_gdc.rotation_en", "yuv_gdc.clockwise_on", "yuv_gdc.mesh_width", "yuv_gdc.mesh_height"]}, "ptn_related": {"params": ["partition_info"], "configs": []}, "ldc": {"params": ["ldc_enable", "ldc_intrinsic_matrix", "ldc_distortion_coeff", "com_matrix"], "configs": ["yuv_gdc.mesh_x", "yuv_gdc.mesh_y"]}, "dis": {"params": ["dis_enable", "dis_sad_thres", "dis_frame_pos_weight"], "configs": []}, "rotate_flip": {"params": ["rotate_enable", "rotate_angle_mode", "flip_enable", "mirror_enable"], "configs": []}, "crop_resize": {"params": ["crop_enable", "crop_start", "crop_size", "scale_enable", "scale_step"], "configs": []}, "undistortion": {"params": ["undistort_enable", "mesh_wise_mode", "mesh_padding_mode", "mesh_value_mode", "pix_padding_mode"], "configs": []}}, "target_module": {"laguna": {"yuv_gdc": {"id": 7700, "method": 0}}}, "configs": {"yuv_gdc": {"enable": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "output_size": {"acc": [0, 14], "size": [2], "description": "undistorted image output size", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "input_size": {"acc": [0, 14], "size": [2], "description": "input image size", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "input_bit_mode": {"acc": [0, 1], "size": [], "description": "0: 10bit input, 1: 8bit input", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "undistort_enable": {"acc": [0, 1], "size": [], "description": "1: undistort process, 0: bypass undistort process", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "input_mode": {"acc": [0, 1], "size": [], "description": "1: 420 input, 0: 422 input", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "process_mode": {"acc": [0, 2], "size": [], "description": "0: process Y, 1: process YUV, 2: process UV", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "rotation_en": {"acc": [0, 1], "size": [], "description": "1: do rotation, 1: bypass rotate", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "clockwise_on": {"acc": [0, 1], "size": [], "description": "if clockwisely rotate when rotation is on", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "mesh_wise_mode": {"acc": [0, 1], "size": [], "description": "0: pixel wise, 1: mesh_wise", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "pix_padding_mode": {"acc": [0, 1], "size": [], "description": "pixel padding is: 1: CONSTANT 0: MIRROR_ABA", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "const_Y": {"acc": [0, 8, 2], "size": [], "description": "const value Y for padding pixel", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "const_UV": {"acc": [1, 7, 2], "size": [2], "description": "const value UV for pading pixel", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "mesh_padding_mode": {"acc": [0, 1], "size": [], "description": "0: mesh is cycle padding, 1: mesh is constant padding", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "const_dx": {"acc": [1, 14, 10], "size": [], "description": "constant padding mesh value for dx", "usage": "", "constraints": "", "type": "AX_S32", "partition": "-"}, "const_dy": {"acc": [1, 14, 10], "size": [], "description": "constant padding mesh value for dy", "usage": "", "constraints": "", "type": "AX_S32", "partition": "-"}, "mesh_start": {"acc": [1, 8], "size": [2], "description": "image left-top startpoint starts in mesh coordinate", "usage": "", "constraints": "", "type": "AX_S16", "partition": "support"}, "internal_start": {"acc": [0, 10], "size": [2], "description": "image left-top startpoint starts in internel mesh block", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "pos_start": {"acc": [0, 0, 18], "size": [2], "description": "image left-top startpoint's position in internel mesh block", "usage": "", "constraints": "", "type": "AX_U32", "partition": "support"}, "mesh_value_mode": {"acc": [0, 1], "size": [], "description": "0: mesh is absolute displacement, 1: mesh is relative displacement", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "mesh_length": {"acc": [0, 10], "size": [2], "description": "mesh length", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "inv_mesh_length": {"acc": [0, 0, 18], "size": [2], "description": "inv mesh length", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "mesh_height": {"acc": [0, 14], "size": [], "description": "mesh height", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "mesh_width": {"acc": [0, 14], "size": [], "description": "mesh width", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "partition_start": {"acc": [0, 14], "size": [2], "description": "partition start", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "mesh_x": {"acc": [1, 14, 10], "size": [33, 33], "description": "mesh x", "usage": "", "constraints": "", "type": "AX_S32", "partition": "-"}, "mesh_y": {"acc": [1, 14, 10], "size": [33, 33], "description": "mesh y", "usage": "", "constraints": "", "type": "AX_S32", "partition": "-"}}}}