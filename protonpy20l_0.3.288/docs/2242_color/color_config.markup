h2. Conf list
h3. clc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | clc enable, 0: bypass, 1: enable |  |
| mode | u1 | [\] | - |  | mode=0 : Low power mode. Do not do matrix interpolation． mode=1 : Default configuration for mc20 |  |
| matrix_list | s3.8 | [16, 6\] | - |  | ccm matrices corresponding to 16 different color phase |  |
| hr_lpf | s1.7 | [5\] | - |  | low pass filter |  |
| sat_mit_enable | u1 | [\] | - |  | sat mitigation enable |  |
| sat_mit_ratio_lower_lut | u1.7 | [8\] | - |  | sat mit ratio lower lut |  |
| sat_mit_ratio_upper_lut | u1.7 | [8\] | - |  | sat mit ratio upper lut |  |
| sat_mit_lower_lut_x | s8.4 | [8\] | - |  | sat mit lower lut x index |  |
| sat_mit_lower_lut_y | s8.4 | [8\] | - |  | sat mit lower lut y value |  |
| sat_mit_lower_lut_slope | u1.7 | [9\] | - |  | sat mit lower lut slope |  |
| sat_mit_upper_lut_x | u9.4 | [8\] | - |  | sat mit upper lut x index |  |
| sat_mit_upper_lut_y | u8.4 | [8\] | - |  | sat mit upper lut y value |  |
| sat_mit_upper_lut_slope | u1.7 | [9\] | - |  | sat mit upper lut slope |  |
| offset_in | u8.4 | [\] | - |  |  |  |
| offset_out | u8.4 | [\] | - |  |  |  |

