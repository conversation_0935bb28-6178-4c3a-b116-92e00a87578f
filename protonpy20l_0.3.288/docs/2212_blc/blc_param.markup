h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| channel_id |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'itp_blc.dither_pmask' | 0: SDR/HDR_Main, 1: HDR_Sub | common |
| offset_out |  | u8.6 | AX_U16 | [\] |  [0, 16383\] | [0.0, 255.984375\] | 1024 | 16.0 | hidden |  | offset out | common |
| sensor_bl |  | u8.10 | AX_U32 | [\] |  [0, 262143\] | [0.0, 255.9990234375\] | 16384 | 16.0 | hidden | 'itp_blc.dgain' | sensor black level, used to calculate dgain. When the difference with sbl_val is large, the calculated dgain will be inaccurate | common |
| dither_seed_enable |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 1 | None | hidden | 'itp_blc.dither_seed_enable' | 0: use stat dither_seed, 1: use conf dither_seed. set 1 for the 1st frame, then set 0 from the 2nd frame onwards. | common |
| det_window |  | u14 | AX_U16 | [4\] |  [0, 16383\] | [None, None\] | [0, 0, img.height, img.width\] | None | hidden | 'bl_det.det_window' | det window, in [y, x, height, width\] order | common |
| active_region |  | u14 | AX_U16 | [4\] |  [0, 16383\] | [None, None\] | [0, 0, img.height, img.width\] | None | hidden | 'bl_det.active_region' | active region area, in [y, x, height, width\] order | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'itp_blc.dgain', 'itp_blc.dither_enable', 'itp_blc.offset_out' | soft enable switch for the entire module |   | None | None | None |
| sbl_corr_enable | sbl corr enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'itp_blc.sbl_corr_enable' | sbl corr enable |   | None | None | None |
| sbl_val | sbl val | u8.10 | AX_U32 | [4\] | [0, 262143\] | [0.0, 255.9990234375\] | [16384, 16384, 16384, 16384\] | [16.0, 16.0, 16.0, 16.0\] | open | 'itp_blc.sbl_val' | auto param, interpolation based on exposure time and gain |   | ['again', 'exp_time'\] | [16, 10\] | ['linear', 'linear'\] |
| gbl_corr_enable | gbl corr enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'itp_blc.gbl_corr_enable' | gbl corr enable |   | None | None | None |
| gbl_val | gbl val | s7.10 | AX_S32 | [4\] | [-131072, 131071\] | [-128.0, 127.9990234375\] | [0, 0, 0, 0\] | [0.0, 0.0, 0.0, 0.0\] | open | 'itp_blc.gbl_val' | auto param, interpolation based on exposure time and gain |   | ['again', 'exp_time'\] | [16, 10\] | ['linear', 'linear'\] |
| lin_enable | lin enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'itp_blc.lin_enable' | 0: disable, 1: enable |   | None | None | None |
| lin_lut_x | lin lut x | u8.4 | AX_U16 | [16\] | [16, 4095\] | [1.0, 255.9375\] | [256, 512, 768, 1024, 1280, 1536, 1792, 2048, 2304, 2560, 2816, 3072, 3328, 3584, 3840, 4095\] | [16.0, 32.0, 48.0, 64.0, 80.0, 96.0, 112.0, 128.0, 144.0, 160.0, 176.0, 192.0, 208.0, 224.0, 240.0, 255.9375\] | open | 'itp_blc.lin_lut_x', 'itp_blc.lin_lut_slope' | x coordinate of lin lut, assume there is a leading zero point by default |   | None | None | None |
| lin_lut_y | lin lut y | u8.6 | AX_U16 | [16, 4\] | [64, 16383\] | [1.0, 255.984375\] | [[1024, 1024, 1024, 1024\], [2048, 2048, 2048, 2048\], [3072, 3072, 3072, 3072\], [4096, 4096, 4096, 4096\], [5120, 5120, 5120, 5120\], [6144, 6144, 6144, 6144\], [7168, 7168, 7168, 7168\], [8192, 8192, 8192, 8192\], [9216, 9216, 9216, 9216\], [10240, 10240, 10240, 10240\], [11264, 11264, 11264, 11264\], [12288, 12288, 12288, 12288\], [13312, 13312, 13312, 13312\], [14336, 14336, 14336, 14336\], [15360, 15360, 15360, 15360\], [16383, 16383, 16383, 16383\]\] | [[16.0, 16.0, 16.0, 16.0\], [32.0, 32.0, 32.0, 32.0\], [48.0, 48.0, 48.0, 48.0\], [64.0, 64.0, 64.0, 64.0\], [80.0, 80.0, 80.0, 80.0\], [96.0, 96.0, 96.0, 96.0\], [112.0, 112.0, 112.0, 112.0\], [128.0, 128.0, 128.0, 128.0\], [144.0, 144.0, 144.0, 144.0\], [160.0, 160.0, 160.0, 160.0\], [176.0, 176.0, 176.0, 176.0\], [192.0, 192.0, 192.0, 192.0\], [208.0, 208.0, 208.0, 208.0\], [224.0, 224.0, 224.0, 224.0\], [240.0, 240.0, 240.0, 240.0\], [255.984375, 255.984375, 255.984375, 255.984375\]\] | open | 'itp_blc.lin_lut_y', 'itp_blc.lin_lut_slope' | y coordinate of lin lut, assume there is a leading zero point by default |   | None | None | None |
| det_enable | det enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'bl_det.det_enable' | det enable, 0: disable, 1: enable |   | None | None | None |
| bpc_det_th | bpc det th | u8.10 | AX_U32 | [\] | [0, 262143\] | [0.0, 255.9990234375\] | 131072 | 128.0 | open | 'bl_det.bpc_det_th' | threshold for quickly checking whether the current point is a bad point |   | ['again', 'exp_time'\] | [16, 10\] | ['linear', 'linear'\] |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |