h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| use_isp_hdr |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'hdr.enable' | From ACP. Default value depends on ACP. 0 - HW disable; 1 - HW enable | common |
| offset_in |  | u8.6 | AX_U16 | [2\] |  [0, 16383\] | [0.0, 255.984375\] | [0, 0\] | [0.0, 0.0\] | hidden | 'hdr.offset_in', 'hdr.d_gain', 'hdr.wb_gain', 'hdr.dgst_exp_wb_gain', 'hdr.mot_sat_x' | From ACP. Default value depends on ACP. offset in. | common |
| offset_out |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 0 | 0.0 | hidden | 'hdr.offset_out', 'hdr.inv_wb_gain' | From ACP. Default value depends on ACP. offset out. | common |
| wb_gain |  | u4.12 | AX_U16 | [4\] |  [0, 65535\] | [0.0, 15.999755859375\] | [0, 0, 0, 0\] | [0.0, 0.0, 0.0, 0.0\] | hidden | 'hdr.wb_gain', 'hdr.inv_wb_gain', 'hdr.dgst_exp_wb_gain' | From ACP. Default value depends on ACP. wb gain from AWB / WBC | common |
| exposure_ratio |  | u9.8 | AX_U32 | [\] |  [0, 131071\] | [0.0, 511.99609375\] | 0 | 0.0 | hidden | 'hdr.dgst_clip_level', 'hdr.dgst_exp_wb_gain', 'hdr.fus_clip_level', 'hdr.fus_exp_gain', 'hdr.fus_exp_ratio', 'hdr.inv_wb_gain', 'hdr.mot_k_ratio', 'hdr.mot_sat_x' | From ACP. Default value depends on ACP. hdr ratio from AE | common |
| internal_lf_indicator |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'hdr.dgst_clip_level', 'hdr.dgst_exp_wb_gain', 'hdr.fus_exp_gain', 'hdr.fus_exp_ratio', 'hdr.mot_sat_x', 'hdr.internal_lf_indicator', 'hdr.tnr_mask_base_fid', 'hdr.fus_base_fid', 'hdr.debug_show_mask' | From ACP. Default value depends on ACP. 0 - input0(after swap_frame) is LF; 1 - input1(after swap_frame) is LF | common |
| a_gain |  | u22.10 | AX_U32 | [\] |  [0, 4294967295\] | [0.0, 4194303.9990234375\] | 0 | 0.0 | hidden | 'hdr.noise_profile', 'hdr.inv_noise_profile' | From ACP. Default value depends on ACP. SF a_gain | common |
| shot_noise_coef |  | s0.31 | AX_S32 | [4, 2\] |  [-2147483648, 2147483647\] | [-1.0, 0.9999999995343387\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | hidden | 'hdr.noise_profile', 'hdr.inv_noise_profile' | From ACP. Default value depends on ACP. shot noise coefficients | common |
| read_noise_coef |  | s0.31 | AX_S32 | [4, 3\] |  [-2147483648, 2147483647\] | [-1.0, 0.9999999995343387\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | hidden | 'hdr.noise_profile', 'hdr.inv_noise_profile' | From ACP. Default value depends on ACP. read-out noise coefficients | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | Enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open |  | 0 - Disable; 1 - Enable |   | None | None | None |
| debug_show_mask | Debug Mode | u3 | AX_U8 | [\] | [0, 4\] | [None, None\] | 0 | None | open | 'hdr.debug_show_mask' | 0 - normal; 1 - general mask; 2 - motion mask; 3 - exposure mask sf; 4 - exposure mask lf |   | None | None | None |
| noise_lut_scale | Noise Lut Scale | u4.12 | AX_U16 | [\] | [0, 65535\] | [0.0, 15.999755859375\] | 4096 | 1.0 | open | 'hdr.noise_profile', 'hdr.inv_noise_profile' | Noise profile will be multiplied by this param. |   | ['gain/lux'\] | [16\] | ['linear'\] |
| fus_dark_prot_en | Dark Protection Enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'hdr.fus_dark_prot_en' | 0 - Disable dark protection feature for normal fusion; 1 - Enable dark protection feature for normal fusion |   | None | None | None |
| fus_over_exp_prot_thre | Over-exposure Protection Threshold | u8.8 | AX_U16 | [2, 2\] | [0, 65535\] | [0.0, 255.99609375\] | [[64256, 65024\], [64256, 65024\]\] | [[251.0, 254.0\], [251.0, 254.0\]\] | open | 'hdr.fus_prot_slope', 'hdr.fus_prot_thre' | fus_over_exp_prot_thre[x\] for input frame x; For Luma value from fus_over_exp_prot_thre[x\][0\] to fus_over_exp_prot_thre[x\][1\], protection weight will drop from 1 to 0. When weight == 0, corresponding frame will not be used in normal fusion(area that satisfies hdr_ratio). Example: fus_over_exp_prot_thre = [[251, 254\], [247, 249\]\], Luma value larger than 254 in frame0 will NOT be used in fusion and 251~254 is transition range; Luma value larger than 249 in frame1 will NOT be used in fusion and 247~249 is transition range. |   | None | None | None |
| fus_prot_wb_ratio | Over-exposure Protection WB Ratio | u1.8 | AX_U16 | [2\] | [0, 256\] | [0.0, 1.0\] | [511, 511\] | [1.99609375, 1.99609375\] | open | 'hdr.fus_prot_wb_ratio' | fus_prot_wb_ratio[x\] for input frame x. This param controls how much WB gain is considered when calculating Luma value used for over-exposure protection. |   | None | None | None |
| dgst_base_fid | De-ghost Base Fid | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'hdr.dgst_base_fid' | Specify which frame to be used as dgst fusion result when both exp mask is 0 for a pixel. 0 - SF; 1 - LF |   | ['gain/lux'\] | [16\] | ['linear'\] |
| dgst_enable | De-ghost Enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'hdr.dgst_enable' | 0 - dgst disable; 1 - dgst enable |   | None | None | None |
| dgst_stren_limit | De-ghost Stren Limit | u1.8 | AX_U16 | [2\] | [0, 256\] | [0.0, 1.0\] | [0, 256\] | [0.0, 1.0\] | open | 'hdr.dgst_stren_limit' | Forcibly set motion mask used in dgst to be from dgst_stren_limit[0\] to dgst_stren_limit[1\] |   | None | None | None |
| exp_weight_gain | Exp Weight Gain | u1.8 | AX_U16 | [2\] | [0, 256\] | [0.0, 1.0\] | [0, 256\] | [0.0, 1.0\] | open | 'hdr.exp_weight_gain' | Gain applied on exposure mask before exp mask normalization. |   | ['gain/lux'\] | [16\] | ['linear'\] |
| exp_weight_lut_luma_low | Exposure Weight Lut Dark | u1.15 | AX_U16 | [2, 33\] | [0, 32768\] | [0.0, 1.0\] | [32768, 32768, ... , 32768\] | [1.0, 1.0, ... , 1.0\] | open | 'hdr.exp_weight_lut_luma_low' | Weight lut of exposure mask for dark area. It's used to generate exposure weight based on pixel luma value. |   | ['gain/lux'\] | [16\] | ['linear'\] |
| exp_weight_lut_luma_high | Exposure Weight Lut Bright | u1.15 | AX_U16 | [2, 33\] | [0, 32768\] | [0.0, 1.0\] | [32768, 32768, ... , 32768\] | [1.0, 1.0, ... , 1.0\] | open | 'hdr.exp_weight_lut_luma_high' | Weight lut of exposure mask for bright area. It's used to generate exposure weight based on pixel luma value. |   | ['gain/lux'\] | [16\] | ['linear'\] |
| exp_y_ratio | Exp Y Ratio | u1.8 | AX_U16 | [2\] | [0, 256\] | [0.0, 1.0\] | [256, 256\] | [1.0, 1.0\] | open | 'hdr.exp_y_ratio' | This param controls how much WB gain is considered when calculating Luma value used for exp mask generation. |   | None | None | None |
| motion_mask_enhance | Motion Mask Enhance | u8.8 | AX_U16 | [\] | [0, 65535\] | [0.0, 255.99609375\] | 256 | 1.0 | open | 'hdr.mot_coring_slope' | This param will enhance motion mask. |   | ['gain/lux'\] | [16\] | ['linear'\] |
| motion_mask_noise_level | Motion Level Control | u1.11 | AX_U16 | [\] | [0, 4095\] | [0.0, 1.99951171875\] | 0 | 0.0 | open | 'hdr.mot_coring_thre' | This parameter acts like motion mask threshold, cutting the motion mask. |   | ['gain/lux'\] | [16\] | ['linear'\] |
| motion_mask_continuity | motion mask continuity control | u1.8 | AX_U16 | [\] | [0, 256\] | [0.0, 1.0\] | 256 | 1.0 | open | 'hdr.mot_mask_max_blend_ratio' | The higher this parameter is, the more continuous(slightly) motion mask can be. Can be helpful for some scenes. |   | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |