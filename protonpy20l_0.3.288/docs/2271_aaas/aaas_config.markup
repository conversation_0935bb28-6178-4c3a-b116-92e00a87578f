h2. Conf list
h3. aestat
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| grid_enable | u1 | [2\] | support |  | 0: bypass, 1: enable |  |
| grid_hori_region_num | u8 | [2\] | support |  | grid horizontal region num |  |
| grid_vert_region_num | u16 | [2\] | support |  | grid vertical region num |  |
| grid_region_height | u10 | [2\] | support |  | grid region height |  |
| grid_region_width | u13 | [2\] | support |  | grid region width |  |
| grid_4ch_enable | u1 | [2\] | - |  | grid 4ch enable, 0: 1ch (Y) ,  1: 4ch (RGGB) |  |
| grid_sat_thr | u14.6 | [2, 4\] | - |  | saturation threshold (R(orY)/Gr/Gb/B) for (grid0, grid1) |  |
| grid_roi | u16 | [2, 4\] | support |  | ROI  : (x, y, w, h)  for (grid0, grid1) |  |
| grid_start_pos | u13 | [2, 2\] | support |  | offset in 1st grid: (x, y)  for (grid0, grid1) |  |
| grid_ycoeff | u0.12 | [4\] | - |  | grid Y coefficient, calc Y for grid |  |
| item_enable | u1 | [2\] | support |  | item ROI enable |  |
| item_roi | u16 | [2, 4\] | support |  | ROI  : (x, y, w, h)  for various item |  |
| hist_log_enable | u1 | [\] | support |  | 0: bypass, 1: enable |  |
| hist_linear_enable | u1 | [\] | support |  | 0: bypass, 1: enable |  |
| hist_mode | u2 | [\] | - |  | hist mode, 0: Y, 1:YRGB, 2:RGGB |  |
| hist_roi_config | u16 | [4\] | support |  | hist ROI setting (x, y, w, h) |  |
| hist_linear_bins | u2 | [\] | - |  | hist linear bin num, 0: 256, 1: 512, 2:1024 |  |
| hist_linear_scale | s4 | [\] | - |  | hist linear scale |  |
| hist_log_scale | s4 | [\] | - |  | hist log scale |  |
| hist_weight | u8 | [256\] | - |  | hist weight (16x16grid) |  |
| hist_block_start_pos | u10 | [2\] | support |  | hist weight block pos offset in 1st grid (x, y) |  |
| hist_block_size | u10 | [2\] | support |  | hist weight block size (x, y) |  |
| hist_ycoeff | u0.12 | [4\] | - |  | Y coefficient, calc Y for hist |  |
| hist_wl_gain | u1.8 | [\] | - |  | white level gain for hist |  |
| dgain | u10.8 | [4\] | - |  | dgain for AEstat input (R, Gr, Gb, B) |  |
| offset_in | u14.6 | [\] | - |  |  |  |

h3. awbstat
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| grid_enable | u1 | [\] | support |  | enable for grid |  |
| grid_mode | u2 | [\] | - |  | grid_mode, 0: RGB, 1:RGGB, 2:RGGBxLuma4ch 3: RGGBxLuma2ch |  |
| grid_hori_region_num | u8 | [\] | support |  | region num(H) |  |
| grid_vert_region_num | u8 | [\] | support |  | region num(V) |  |
| grid_region_height | u10 | [\] | support |  | region height |  |
| grid_region_width | u10 | [\] | support |  | region width |  |
| grid_rgb_thr | s14.6 | [8\] | - |  | RGB thr for valid sum and count |  |
| grid_y_thr | s14.6 | [2\] | - |  | Y thr for valid sum and count |  |
| grid_rgb_sat_thr | u14.6 | [4\] | - |  | RGB thr for sat sum and count |  |
| grid_y_sat_thr | u14.6 | [\] | - |  | Y thr for sat sum and count |  |
| grid_roi | u16 | [4\] | support |  | grid ROI |  |
| grid_start_pos | u10 | [2\] | support |  | grid start position |  |
| grid_luma_slice_thr | s14.6 | [3\] | - |  | thr for luma slice (grid) |  |
| grid_ycoeff | u0.12 | [4\] | - |  | Y coefficient for grid |  |
| item_enable | u1 | [2\] | support |  | item ROI enable |  |
| item_roi | u16 | [2, 4\] | support |  | ROI  : (x, y, w, h)  for various item |  |
| dgain | u10.8 | [4\] | - |  | dgain for AWBstat input (R, Gr, Gb, B) |  |
| offset_in | u14.6 | [\] | - |  |  |  |

h3. afstat
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| fv_mode | u1 | [4\] | - |  | af stat mode, 0: sum mode, 1: peak mode |  |
| wb_gain | u4.8 | [4\] | - | all | white balance gain [0\]:R,[1\]:Gr,[2\]:Gb,[3\]:B |  |
| fir_enable | u1 | [2\] | - |  | fir stat enable |  |
| grid_vert_region_num | u8 | [\] | support |  | grid vertical region number |  |
| grid_hori_region_num | u8 | [\] | support |  | grid horizontal region number |  |
| grid_region_height | u10 | [\] | support |  | grid region height |  |
| grid_region_width | u10 | [\] | support |  | grid region width |  |
| coring_thr | u8.10 | [4\] | - |  | coring threshold |  |
| coring_gain | u5.7 | [4\] | - |  | coring gain |  |
| coring_lut | u5 | [4, 16\] | - |  | coring adjust lut |  |
| coring_pix_sum_mode | u1 | [4\] | - |  | af coring pix cnt mode, 1: sum all pix, 0: count only > threshold |  |
| coring_pix_cnt_mode | u1 | [4\] | - |  | af coring pix cnt mode, 1: count all pix, 0: count only > threshold |  |
| scale_ratio | u3 | [\] | - |  | scale down ratio |  |
| ycoeff | u0.12 | [4\] | - |  | coeff for compute Y |  |
| vert_enable | u1 | [2\] | support |  | vertical enable for v0, v1 |  |
| v_iir_coeff | s2.12 | [2, 10\] | - |  | vertical iir coeff |  |
| hori_enable | u1 | [2\] | support |  | horizontal enable for v0, v1 |  |
| h_iir_coeff | s2.12 | [2, 10\] | - |  | horizontal iir coeff |  |
| h_fir_coeff | s6 | [2, 13\] | - |  | horizontal fir coeff |  |
| weight | u1.7 | [8\] | - |  | downsample image weight |  |
| ldg_thr | u8.4 | [4\] | - |  | luma depend gain thr |  |
| ldg_slope | u0.8 | [4\] | - |  | luma depend gain slope |  |
| ldg_lim | u1.7 | [4\] | - |  | luma depend gain limit |  |
| ldg_range | u2 | [2\] | - |  | luma depend gain pix range |  |
| high_luma_cnt_thr | u8.4 | [4\] | - |  | high luma count thr |  |
| grid_roi | u16 | [4\] | support |  | grid ROI |  |
| grid_start_pos | u10 | [2\] | support |  | grid start position |  |
| drc_enable | u1 | [\] | - |  | drc enable |  |
| drc_lut | u8.4 | [15\] | - |  | drc lut |  |
| dgain | u10.8 | [4\] | - |  | dgain for AFstat input (R, Gr, Gb, B) |  |
| offset_in | u14.6 | [\] | - |  |  |  |

