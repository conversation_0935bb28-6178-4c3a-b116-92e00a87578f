h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||

| None | | | | | | | | | | | | |

h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 1 | None | open | 'ive.enable' | ive enable control bit | user | None | None | None |
| mode | mode | u8 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'ive.mode' | choose operator. | user | None | None | None |
| sub_mode | sub mode | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 4 | None | open | 'ive.sub_mode' | choose the sub mode of operater. | user | None | None | None |
| var_threshold | var threshold | u4.4 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [np.float64(0.0), np.float64(15.9375)\] | 100 | 6.25 | open | 'ive.var_threshold' | var_threshold multiplies model's var combined with background ratio to determine the standard used to judge if the pixel is background or not. | user | None | None | None |
| var_threshold_check | var threshold | u4.4 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [np.float64(0.0), np.float64(15.9375)\] | 0 | 0.0 | open | 'ive.var_threshold_check' | var_threshold multiplies model's var to determine the standard used to judge if the model is a hit or not. | user | None | None | None |
| var0 | var0 | u14.4 | AX_U32 | [\] | [np.int64(0), np.int64(262143)\] | [np.float64(0.0), np.float64(16383.9375)\] | 9600 | 600.0 | open | 'ive.var0' | The initial variance setting of new model if the weakest one needs replacing. | user | None | None | None |
| min_var | min var | u14.4 | AX_U32 | [\] | [np.int64(0), np.int64(262143)\] | [np.float64(0.0), np.float64(16383.9375)\] | 3200 | 200.0 | open | 'ive.min_var' | The smallest value of variance for updating Gaussian models. | user | None | None | None |
| max_var | max var | u14.4 | AX_U32 | [\] | [np.int64(0), np.int64(262143)\] | [np.float64(0.0), np.float64(16383.9375)\] | 160000 | 10000.0 | open | 'ive.max_var' | The largest value of variance for updating Gaussian models. | user | None | None | None |
| lr | lr | u1.7 | AX_U8 | [\] | [np.int64(0), np.int64(128)\] | [np.float64(0.0), np.float64(1.0)\] | 1 | 0.0078125 | open | 'ive.lr' | Variable that determines model parameters updating speed. | user | None | None | None |
| background_ratio | background ratio | u1.7 | AX_U8 | [\] | [np.int64(0), np.int64(128)\] | [np.float64(0.0), np.float64(1.0)\] | 38 | 0.296875 | open | 'ive.background_ratio' | Variable used to determine the model weight(sum) boundary for background and foreground. Since all Gaussian models are in descending order, the higher this value is, more Gaussian model will be treated as background. | user | None | None | None |
| ct | ct | s1.7 | AX_S16 | [\] | [np.int64(-128), np.int64(128)\] | [np.float64(-1.0), np.float64(1.0)\] | -6 | -0.046875 | open | 'ive.ct' | The largest value of variance for updating Gaussian models | user | None | None | None |
| set_thresh | set thresh | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 1 | None | open | 'ive.set_thresh' | The largest value of variance for updating Gaussian models | user | None | None | None |
| filter_weight | filter weight | s8.10 | AX_S32 | [5, 5\] | [np.int64(-262144), np.int64(262143)\] | [np.float64(-256.0), np.float64(255.9990234375)\] | [[-2048, -1024, 0, 1024, 2048\], [-2048, -1024, 0, 1024, 2048\], [-4096, -2048, 0, 2048, 4096\], [-2048, -1024, 0, 1024, 2048\], [-2048, -1024, 0, 1024, 2048\]\] | [[np.float64(-2.0), np.float64(-1.0), np.float64(0.0), np.float64(1.0), np.float64(2.0)\], [np.float64(-2.0), np.float64(-1.0), np.float64(0.0), np.float64(1.0), np.float64(2.0)\], [np.float64(-4.0), np.float64(-2.0), np.float64(0.0), np.float64(2.0), np.float64(4.0)\], [np.float64(-2.0), np.float64(-1.0), np.float64(0.0), np.float64(1.0), np.float64(2.0)\], [np.float64(-2.0), np.float64(-1.0), np.float64(0.0), np.float64(1.0), np.float64(2.0)\]\] | open | 'ive.filter_weight' | Set proper filter template | user | None | None | None |
| filter_stride | filter stride | u5 | AX_U8 | [2\] | [np.int64(0), np.int64(31)\] | [None, None\] | [1, 1\] | None | open | 'ive.filter_stride' | For normal usage, it's default to all 1; For special usage e.g. SAD, it should be set to 4 since in mc20e ive only 4x4 SAD is supported. From the cmodel point of view, there is no constraint about it. For example, if the stride is set to 2, cmodel will still work, but HW will fail probably. | user | None | None | None |
| filter_init | filter init | u11 | AX_U16 | [2\] | [np.int64(0), np.int64(2047)\] | [None, None\] | [0, 0\] | None | open | 'ive.filter_init' | For normal usage, it's default to all 1; For special usage e.g. SAD, it should be set to 4 since in mc20e ive only 4x4 SAD is supported. From the cmodel point of view, there is no constraint about it. For example, if the stride is set to 2, cmodel will still work, but HW will fail probably. | user | None | None | None |
| nms_low_threshold | nms low threshold | u11 | AX_U16 | [\] | [np.int64(0), np.int64(2047)\] | [None, None\] | 300 | None | open | 'ive.nms_low_threshold' | Weak edge lower gradient limit | user | None | None | None |
| nms_high_threshold | nms high threshold | u11 | AX_U16 | [\] | [np.int64(0), np.int64(2047)\] | [None, None\] | 800 | None | open | 'ive.nms_high_threshold' | Strong edge lower gradient limit | user | None | None | None |
| thresh_low_thr | lower threshold | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 100 | None | open | 'ive.thresh_low_thr' | Lower threshold | user | None | None | None |
| thresh_high_thr | upper threshold | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 200 | None | open | 'ive.thresh_high_thr' | Upper threshold | user | None | None | None |
| thresh_min_val | thresh min val | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 3 | None | open | 'ive.thresh_min_val' | Filling value of pixel level 0(pixels lower than lower threshold) when used | user | None | None | None |
| thresh_mid_val | thresh mid val | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 145 | None | open | 'ive.thresh_mid_val' | Filling value of pixel level 1(pixels between lower threshold and upper threshold) when used | user | None | None | None |
| thresh_max_val | thresh max val | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 231 | None | open | 'ive.thresh_max_val' | Filling value of pixel level 2(pixels greater than lower threshold) when used | user | None | None | None |
| conv_16to8_gain | conv 16to8 gain | s1.14 | AX_S16 | [\] | [np.int64(-32768), np.int64(32767)\] | [np.float64(-2.0), np.float64(1.99993896484375)\] | 41 | 0.00250244140625 | open | 'ive.conv_16to8_gain' | Replace 'a/b' value used in above formula. | user | None | None | None |
| conv_16to8_bias | conv 16to8 bias | s15 | AX_S16 | [\] | [np.int64(-32768), np.int64(32767)\] | [None, None\] | -10 | None | open | 'ive.conv_16to8_bias' | Replace 'bias' value used in above formula. | user | None | None | None |
| weight_pic_0 | weight pic 0 | s7.8 | AX_S16 | [\] | [np.int64(-32768), np.int64(32767)\] | [np.float64(-128.0), np.float64(127.99609375)\] | 128 | 0.5 | open | 'ive.weight_pic_0' | Used in ADD mode to determine picture 0 weight. | user | None | None | None |
| weight_pic_1 | weight pic 0 | s7.8 | AX_S16 | [\] | [np.int64(-32768), np.int64(32767)\] | [np.float64(-128.0), np.float64(127.99609375)\] | 128 | 0.5 | open | 'ive.weight_pic_1' | Used in ADD mode to determine picture 1 weight. | user | None | None | None |
| dma_h_gap | dma h gap | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 3 | None | open | 'ive.dma_h_gap' | Horizontal gap | user | None | None | None |
| dma_v_gap | dma v gap | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 4 | None | open | 'ive.dma_v_gap' | Vertical gap | user | None | None | None |
| dma_byte_num | dma byte num | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 2 | None | open | 'ive.dma_byte_num' | Choose how many bytes to read in each segment | user | None | None | None |
| dma_out_height | dma out height | u11 | AX_U16 | [\] | [np.int64(0), np.int64(2047)\] | [None, None\] | 8 | None | open | 'ive.dma_out_height' | Output matrix height | user | None | None | None |
| dma_out_width | dma out width | u11 | AX_U16 | [\] | [np.int64(0), np.int64(2047)\] | [None, None\] | 20 | None | open | 'ive.dma_out_width' | Output matrix width | user | None | None | None |
| dma_y0 | dma y0 | u11 | AX_U16 | [\] | [np.int64(0), np.int64(2047)\] | [None, None\] | 2 | None | open | 'ive.dma_y0' | Starting point y coordinate for Direct copy | user | None | None | None |
| dma_x0 | dma x0 | u11 | AX_U16 | [\] | [np.int64(0), np.int64(2047)\] | [None, None\] | 2 | None | open | 'ive.dma_x0' | Starting point x coordinate for Direct copy | user | None | None | None |
| dma_y1 | dma y1 | u11 | AX_U16 | [\] | [np.int64(0), np.int64(2047)\] | [None, None\] | 22 | None | open | 'ive.dma_y1' | End point y coordinate for Direct copy | user | None | None | None |
| dma_x1 | dma x1 | u11 | AX_U16 | [\] | [np.int64(0), np.int64(2047)\] | [None, None\] | 25 | None | open | 'ive.dma_x1' | End point x coordinate for Direct copy | user | None | None | None |
| dma_fill_data0 | dma fill data0 | u16 | AX_U16 | [\] | [np.int64(0), np.int64(65535)\] | [None, None\] | 45637 | None | open | 'ive.dma_fill_data0' | Bytes0 in U64 which is to be filled into matrix | user | None | None | None |
| dma_fill_data1 | dma fill data1 | u16 | AX_U16 | [\] | [np.int64(0), np.int64(65535)\] | [None, None\] | 22538 | None | open | 'ive.dma_fill_data1' | Bytes1 in U64 which is to be filled into matrix | user | None | None | None |
| dma_fill_data2 | dma fill data2 | u16 | AX_U16 | [\] | [np.int64(0), np.int64(65535)\] | [None, None\] | 61262 | None | open | 'ive.dma_fill_data2' | Bytes2 in U64 which is to be filled into matrix | user | None | None | None |
| dma_fill_data3 | dma fill data3 | u16 | AX_U16 | [\] | [np.int64(0), np.int64(65535)\] | [None, None\] | 16472 | None | open | 'ive.dma_fill_data3' | Bytes3 in U64 which is to be filled into matrix | user | None | None | None |
| lbp_thresh | lbp thresh | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 0 | None | open | 'ive.lbp_thresh' | LBP Thresh value | user | None | None | None |
| bernsen_cmin | bernsen cmin | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 15 | None | open | 'ive.bernsen_cmin' | Bernsen local contrast threshold | user | None | None | None |
| bernsen_thresh2 | bernsen thresh2 | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 128 | None | open | 'ive.bernsen_thresh2' | The threshold value to judge corner based on corner response | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |