h2. Conf list
h3. gam
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| mode | u1 | [\] | - |  | Lut Mode for Gam. 0: linear(gblk_num = 1), 1: log(gblk_num = 8) | set 0 as default |
| range_adjust_enable | u1 | [\] | - |  | map input range from 240 to 256. | set 1 as default |
| dither_enable | u1 | [\] | - |  | 0: disable, 1: enable | set 1 as default |
| dither_seed_enable | u1 | [\] | - |  | 0: use stat dither_seed, 1: use conf dither_seed | set 1 for the 1st frame, then set 0 from the 2nd frame onwards |
| dither_seed | u16 | [2\] | - | [0,2^16), [0,2^15) | value of dither seed | set non 0, when dither enable |
| dither_pmask | u16 | [2\] | - | [0,2^16), [0,2^15) | dither pmask | should set properly, pMask are picked from https://git-core.megvii-inc.com/chenjiahui/LFSR/tree/master/pMask 16.txt,15.txt |
| lut | u8.4 | [129\] | - | 0 <= lut[n+1\]-lut[n\] < 32.0, lut[n\]<lut[n+1\] for n=0,1,..,128 | gamma lut |  |
| offset_in | u8.4 | [\] | - |  | offset for input | Set 16 |
| offset_out | u8.2 | [\] | - |  | offset for out | Set 16 |

