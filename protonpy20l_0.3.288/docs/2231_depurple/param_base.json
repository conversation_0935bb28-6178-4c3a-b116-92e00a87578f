{"partition_configs": ["pfd.mesh_start", "pfd.internal_start", "pfd.pos_start"], "context": {"AN_ID": {"size": [], "acc": [0, 16, 0], "comment": "DEPURPLE is 0x2231", "type": "AX_U16"}}, "params": {"offset_in": {"acc": [0, 8, 4], "type": "AX_U16", "size": [], "range": [0.0, 256.0], "default": 16.0, "comment": "offset_in", "hidden": 1, "auto": 0, "target_conf": ["pfd.offset_in"], "dependency": "common", "display": "pfd offset in"}, "partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "hidden": 1, "auto": 0, "target_conf": ["pfd.mesh_start", "pfd.internal_start", "pfd.pos_start", "pfd.mesh_length", "pfd.inv_mesh_length"], "display": "partition information", "comment": "partition information", "dependency": "common"}, "det_enable": {"display": "det enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 1, "comment": "software enable for pfd", "hidden": 0, "auto": 0, "target_conf": ["pfd.mask_coef"], "dependency": "user"}, "det_c_ratio": {"display": "color ratio", "acc": [1, 1, 6], "type": "AX_S8", "size": [4], "range": [-1.0, 1.0], "default": [-0.5, 0, 0, 0.5], "comment": "weight for computr color channel using bayer", "hidden": 0, "auto": 0, "target_conf": ["pfd.c_ratio"], "dependency": "user"}, "det_adj_y_gam": {"display": "pfd adj y channel with gamma", "acc": [0, 3, 5], "type": "AX_U8", "size": [], "range": [0.0, 7.0], "default": 2.1875, "comment": "for gamma correction to meet with decent gam", "hidden": 0, "auto": 0, "target_conf": ["pfd.gamma_lut"], "dependency": "user"}, "det_edge_c_enable": {"display": "det c enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "for color channel edge offset", "hidden": 0, "auto": 0, "target_conf": ["pfd.detedge_slope", "pfd.detedge_offset"], "dependency": "user"}, "det_edge_slope_y": {"display": "edge slope y", "acc": [0, 1, 6], "type": "AX_U8", "size": [], "range": [0.0, 1.0], "default": 0.3125, "comment": "for luma channel edge gain", "hidden": 0, "auto": 0, "target_conf": ["pfd.detedge_slope"], "dependency": "user"}, "det_edge_offset_y": {"display": "edge offset y", "acc": [1, 1, 6], "type": "AX_S8", "size": [], "range": [-1.0, 1.0], "default": -0.78125, "comment": "for luma channel edge offset", "hidden": 0, "auto": 0, "target_conf": ["pfd.detedge_offset"], "dependency": "user"}, "det_edge_slope_c": {"display": "edge slope c", "acc": [0, 1, 6], "type": "AX_U8", "size": [], "range": [0.0, 1.0], "default": 0.3125, "comment": "for color channel edge gain", "hidden": 0, "auto": 0, "target_conf": ["pfd.detedge_slope"], "dependency": "user"}, "det_edge_offset_c": {"display": "edge offset c", "acc": [1, 1, 6], "type": "AX_S8", "size": [], "range": [-1.0, 1.0], "default": -0.78125, "comment": "for color channel edge offset", "hidden": 0, "auto": 0, "target_conf": ["pfd.detedge_offset"], "dependency": "user"}, "det_seledge_slope_y": {"display": "edge y slope for compensate luma", "acc": [0, 1, 8], "type": "AX_U16", "size": [], "range": [0.0, 1.0], "default": 0.078125, "comment": "for compensate luma with slope for", "hidden": 0, "auto": 0, "target_conf": ["pfd.seledge_slope_y"], "dependency": "user"}, "det_seledge_thr_y": {"display": "edge y thr for compensate luma", "acc": [0, 8, 6], "type": "AX_U16", "size": [], "range": [0.0, 255.984375], "default": 150, "comment": "for luma compensation when detect edge, used to compute for mesh-wisely", "hidden": 0, "auto": 1, "target_conf": ["pfd.seledge_thr_y_mesh"], "dependency": "user"}, "det_seledge_thr_y_mesh": {"display": "location y_thr mesh", "acc": [0, 1, 7], "type": "AX_U8", "size": [17, 17], "range": [0.0, 1.0], "default": [[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]], "comment": "location wise strength adjust for edge compensation", "hidden": 0, "auto": 0, "target_conf": ["pfd.mesh"], "dependency": "user"}, "det_seledge_weight": {"display": "edge detection weight", "acc": [0, 1, 6], "type": "AX_U8", "size": [7], "range": [0.0, 1.0], "default": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "comment": "edge detection weight", "hidden": 0, "auto": 0, "target_conf": ["pfd.seledge_dist_weight"], "dependency": "user"}, "det_mask_strength": {"display": "det strength", "acc": [0, 3, 6], "type": "AX_U16", "size": [], "range": [0.0, 7.984375], "default": 1.0, "comment": "strength adjust for mask", "hidden": 0, "auto": 1, "target_conf": ["pfd.mask_coef"], "dependency": "user"}, "det_mask_dialate_enable": {"display": "det mask dialate enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "dialate mask enable", "hidden": 0, "auto": 0, "target_conf": ["pfd.mask_dialate_enable"], "dependency": "user"}, "det_mask_dialate_weight": {"display": "det mask dialate weight", "acc": [0, 1, 6], "type": "AX_U8", "size": [7], "range": [0.0, 1.0], "default": [64, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "comment": "dialate weight for mask", "hidden": 0, "auto": 0, "target_conf": ["pfd.mask_dist_weight"], "dependency": "user"}, "det_adj_mesh_enable": {"display": "adjust mesh enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "for enable locationwise strength adjustment", "hidden": 0, "auto": 0, "target_conf": ["pfd.mesh_adj_mask_enable"], "dependency": "user"}, "det_adj_mesh": {"display": "location strength adj", "acc": [0, 2, 6], "type": "AX_U8", "size": [17, 17], "range": [0.0, 3.984375], "default": [[1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]], "comment": "mesh for adjust depurple strength", "hidden": 0, "auto": 0, "target_conf": ["pfd.mesh"], "dependency": "user"}, "desat_enable": {"display": "desat enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 1, "comment": "desat_enable", "hidden": 0, "auto": 0, "target_conf": ["lce.desat_enable"], "dependency": "user"}, "desat_strength": {"display": "desat strength", "acc": [0, 4, 4], "type": "AX_U8", "size": [], "range": [0.0, 15.0], "default": 2.0, "comment": "desat_strength", "hidden": 0, "auto": 1, "target_conf": ["lce.desat_strength"], "dependency": "user"}, "desat_luma_lut": {"display": "desaturation luma ratio lut", "acc": [0, 1, 7], "type": "AX_U8", "size": [8], "range": [0.0, 1.0], "default": [0.0, 0.0, 0.0, 0.046875, 0.78125, 0.8984375, 1.0, 1.0], "comment": "desat_luma_lut", "hidden": 0, "auto": 0, "target_conf": ["lce.desat_luma_lut"], "dependency": "user"}, "desat_sat_lut": {"display": "desaturation saturation ratio lut", "acc": [0, 1, 7], "type": "AX_U8", "size": [6], "range": [0.0, 1.0], "default": [0.0, 0.1953125, 0.5, 0.78125, 1.0, 1.0], "comment": "desat_sat_lut", "hidden": 0, "auto": 0, "target_conf": ["lce.desat_sat_lut"], "dependency": "user"}, "desat_angle_ratio_lut": {"display": "desaturation angle ratio lut", "acc": [0, 1, 7], "type": "AX_U8", "size": [16], "range": [0.0, 1.0], "default": [1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0], "comment": "calculate 16 phase ratio", "hidden": 0, "auto": 1, "target_conf": ["lce.desat_angle_ratio_lut"], "dependency": "user"}, "desat_uv_val": {"display": "desaturation uv value", "acc": [1, 7, 2], "type": "AX_S16", "size": [2], "range": [-127.75, 127.75], "default": [0, 0], "comment": "depurple aera fill color with UV", "hidden": 0, "auto": 0, "target_conf": ["lce.desat_uv_val"], "dependency": "user"}, "desat_debug_enable": {"display": "debug enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "desat debug enable", "hidden": 0, "auto": 0, "target_conf": ["lce.desat_debug_enable"], "dependency": "user"}, "desat_debug_thr": {"display": "debug thr", "acc": [0, 1, 4], "type": "AX_U8", "size": [], "range": [0.0, 1.0], "default": 0.0, "comment": "debug depurple threshold for showing debug info", "hidden": 0, "auto": 0, "target_conf": ["lce.desat_debug_thr"], "dependency": "user"}, "desat_debug_color": {"display": "debug color", "acc": [1, 8, 2], "type": "AX_S16", "size": [3], "range": [-255.75, 255.75], "default": [0.0, 0.0, 0.0], "comment": "debug mode's color fill for depurple aera with YUV", "hidden": 0, "auto": 0, "target_conf": ["lce.desat_debug_color"], "dependency": "user"}}, "submodules": {"setup": {"params": ["offset_in"], "configs": ["pfd.enable", "pfd.y_ratio", "pfd.mesh_mode"]}, "ptn_related": {"params": ["partition_info"], "configs": []}, "det": {"params": ["det_enable", "det_c_ratio", "det_adj_y_gam", "det_edge_c_enable", "det_edge_slope_y", "det_edge_offset_y", "det_edge_slope_c", "det_edge_offset_c", "det_seledge_slope_y", "det_seledge_thr_y", "det_seledge_thr_y_mesh", "det_seledge_weight", "det_mask_strength", "det_mask_dialate_enable", "det_mask_dialate_weight", "det_adj_mesh_enable", "det_adj_mesh"], "configs": []}, "desat": {"params": ["desat_enable", "desat_strength", "desat_luma_lut", "desat_sat_lut", "desat_angle_ratio_lut", "desat_uv_val", "desat_debug_enable", "desat_debug_thr", "desat_debug_color"], "configs": []}}, "target_module": {"mc20l": {"lce": {"id": 5400, "method": 0}, "pfd": {"id": 8600, "method": 0}}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "configs": {"lce": {"enable": {"acc": [0, 1], "size": [], "description": "lce enable, 0: disable, 1: enable", "usage": "set to 1 normally and use each submodule's own enable flag", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_enable": {"acc": [0, 1], "size": [], "description": "sharpen enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_debug_mode": {"acc": [0, 5], "size": [], "description": "sharpen debug mode, 0: normal mode, others: visualized intermediate result for debugging", "usage": "", "constraints": "{0, 3 ~ 18}", "type": "AX_U8", "partition": "-"}, "shp_filter_ud": {"acc": [1, 0, 12], "size": [3, 3], "description": "undirectional filter coeffs", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_filter_dir_0": {"acc": [1, 0, 12], "size": [9], "description": "0 directional filter coeffs", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_filter_dir_22": {"acc": [1, 0, 12], "size": [13], "description": "22 directional filter coeffs", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_filter_dir_45": {"acc": [1, 0, 12], "size": [9], "description": "45 directional filter coeffs", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_ud_texture_shift": {"acc": [0, 3], "size": [], "description": "undirectional texture gain lut x-axis shift bits", "usage": "smaller value means more texture gain control points in flat or weak texture regions", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_dir_texture_shift": {"acc": [0, 3], "size": [], "description": "directional texture gain lut x-axis shift bits", "usage": "smaller value means more texture gain control points in flat or weak edge regions", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_ud_gain_lut": {"acc": [0, 8, 4], "size": [33], "description": "undirectional texture gain lut", "usage": "set undirectional sharpen gain w.r.t texture strength", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_dir_gain_lut": {"acc": [0, 8, 4], "size": [33], "description": "directional texture gain lut", "usage": "set directional sharpen gain w.r.t texture strength", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_ud_scale": {"acc": [0, 0, 4], "size": [], "description": "scaling factor for ud filters", "usage": "larger value means to use more undirectional filter result", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_dir_scale": {"acc": [0, 0, 4], "size": [], "description": "scaling factor for dir filters", "usage": "larger value means to use more directional filter result", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_edge_sad_noise": {"acc": [0, 8, 4], "size": [], "description": "edge noise level", "usage": "larger value means stronger noise level and reduce directional filter weight", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_nr_ud_texture_scale": {"acc": [0, 0, 8], "size": [], "description": "scaling factor for undirectional nr's texture map", "usage": "larger value means more like a texture pixel (not noise pixel), and reduces undirectional nr strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_nr_ud_texture_offset": {"acc": [0, 0, 8], "size": [], "description": "coring offset for undirectional nr's texture map", "usage": "larger value means stronger coring on texutre map, and increases undirectional nr strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_nr_ud_limit": {"acc": [0, 8, 2], "size": [], "description": "undirectional noise limit", "usage": "larger value means less limitation for calculated undirectional noise", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_nr_dir_texture_scale": {"acc": [0, 0, 8], "size": [], "description": "scaling factor for directional nr's texture map", "usage": "larger value means more like a edge pixel (not noise pixel), and reduces directional nr strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_nr_dir_texture_offset": {"acc": [0, 4, 4], "size": [], "description": "coring offset for directional nr's texture map", "usage": "larger value means stronger coring on texutre map, and increases directional nr strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_nr_dir_limit": {"acc": [0, 8, 2], "size": [], "description": "directional noise limit", "usage": "larger value means less limitation for calculated directional noise", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_nr_level": {"acc": [0, 1, 5], "size": [], "description": "noise reduction level", "usage": "larger value means stronger noise reduction", "constraints": "shp_nr_level <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_coring_ud_level": {"acc": [0, 3, 3], "size": [], "description": "undirectional sharpen coring level", "usage": "larger value means stronger coring, and reduces undirectional sharpen strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_coring_dir_level": {"acc": [0, 3, 3], "size": [], "description": "directional sharpen coring level", "usage": "larger value means stronger coring, and reduces directional sharpen strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_shoot_ref_ratio": {"acc": [0, 1, 5], "size": [], "description": "the blending ratio of original pixel and local min max to generate shoot reference", "usage": "larger value means to use more local min max value for shoot reference, and leads to stronger shoot", "constraints": "shp_shoot_ref_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_overshoot": {"acc": [0, 0, 7], "size": [], "description": "global overshoot level", "usage": "larger value means stronger overshoot", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_undershoot": {"acc": [0, 0, 7], "size": [], "description": "global undershoot level", "usage": "larger value means stronger undershoot", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_motion_mask_enable": {"acc": [0, 1], "size": [], "description": "motion mask enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_motion_mask_lut": {"acc": [0, 0, 8], "size": [9], "description": "motion mask lut", "usage": "used to clip, inverse, or remap the original motion mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_motion_ud_gain_lut": {"acc": [0, 8, 4], "size": [33], "description": "undirectional texture gain lut for motion region", "usage": "set undirectional sharpen gain w.r.t texture strength for motion region", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_motion_dir_gain_lut": {"acc": [0, 8, 4], "size": [33], "description": "directional texture gain lut for motion region", "usage": "set directional sharpen gain w.r.t texture strength for motion region", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_motion_overshoot": {"acc": [0, 0, 7], "size": [], "description": "overshoot level for motion region", "usage": "larger value means stronger overshoot for motion region", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_motion_undershoot": {"acc": [0, 0, 7], "size": [], "description": "undershoot level for motion region", "usage": "larger value means stronger undershoot for motion region", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_luma_mask_enable": {"acc": [0, 1], "size": [], "description": "luma mask enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_luma_ref_select": {"acc": [0, 1], "size": [], "description": "luma mask reference selection, 0: blurred Y, 1: denoised Y", "usage": "usually set to 0", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_luma_gain_lut": {"acc": [0, 1, 5], "size": [2, 33], "description": "luma gain lut, [0]: negative, [1]: positive", "usage": "usually set lower gain for dark region to avoid enhancing noises", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_detail_adj_enable": {"acc": [0, 1], "size": [], "description": "detail enhancement enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_detail_texture_shift": {"acc": [0, 3], "size": [], "description": "texture map shift bits for detail region", "usage": "smaller value means detail region will have more flat or weak texture pixels", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_detail_overshoot_slope": {"acc": [1, 0, 9], "size": [], "description": "detail overshoot slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_detail_overshoot_offset": {"acc": [1, 4, 7], "size": [], "description": "detail overshoot offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_detail_overshoot_limit": {"acc": [0, 0, 7], "size": [2], "description": "detail overshoot limit", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_detail_undershoot_slope": {"acc": [1, 0, 9], "size": [], "description": "detail undershoot slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_detail_undershoot_offset": {"acc": [1, 4, 7], "size": [], "description": "detail undershoot offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_detail_undershoot_limit": {"acc": [0, 0, 7], "size": [2], "description": "detail undershoot limit", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_enable": {"acc": [0, 1], "size": [], "description": "shoot suppression enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_blend_ratio": {"acc": [0, 1, 4], "size": [], "description": "the blending ratio of two shoot control strategy", "usage": "larger value means less shoot control for texture region while keeping shoot control for strong edges", "constraints": "shp_shoot_sup_blend_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_var_min_slope": {"acc": [0, 0, 9], "size": [], "description": "shoot control (by min variance) curve slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_shoot_sup_var_min_offset": {"acc": [1, 4, 7], "size": [], "description": "shoot control (by min variance) curve offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_shoot_sup_var_min_limit": {"acc": [0, 0, 7], "size": [2], "description": "shoot control (by min variance) curve limit", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_var_diff_slope": {"acc": [1, 0, 9], "size": [], "description": "shoot control (by variance difference) curve slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_shoot_sup_var_diff_offset": {"acc": [0, 4, 7], "size": [], "description": "shoot control (by variance difference) curve offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_shoot_sup_var_diff_limit": {"acc": [0, 0, 7], "size": [2], "description": "shoot control (by variance difference) curve limit", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_limit_sup_enable": {"acc": [0, 1], "size": [], "description": "adaptive limit control enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_limit_sup_max_over_change": {"acc": [0, 8, 2], "size": [], "description": "adaptive limit control max over change", "usage": "larger value means less limitation for pixel value over change", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_limit_sup_max_under_change": {"acc": [0, 8, 2], "size": [], "description": "adaptive limit control max under change", "usage": "larger value means less limitation for pixel value under change", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_limit_sup_max_over_gain": {"acc": [0, 5, 5], "size": [], "description": "adaptive limit control over change gain", "usage": "larger value means less limitation for pixel value over change", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_limit_sup_max_under_gain": {"acc": [0, 5, 5], "size": [], "description": "adaptive limit control under change gain", "usage": "larger value means less limitation for pixel value under change", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_enable": {"acc": [0, 1], "size": [], "description": "rgb color sharpen gain control enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_red_var5_shift": {"acc": [0, 3], "size": [], "description": "texture map shift bits for red color mask", "usage": "smaller value means more red edge pixels will not be affected by red sharpen gain", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_red_center": {"acc": [1, 7, 2], "size": [2], "description": "red color center uv", "usage": "set as uv value of red color", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_red_radius": {"acc": [0, 8, 2], "size": [2], "description": "red color radius", "usage": "larger value means more pixels will be selected in red color mask", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_red_dist_mode": {"acc": [0, 1], "size": [], "description": "red color distance calculation mode, 0: diamond, 1: rectangle", "usage": "set to 0 as default", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_red_dist_weight": {"acc": [0, 0, 3], "size": [4], "description": "red color distance weights of 4 directions", "usage": "larger value means less pixels will be selected in red color mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_red_gain": {"acc": [0, 1, 5], "size": [], "description": "red color sharpen gain", "usage": "larger value means stronger sharpen for red color", "constraints": "shp_rgb_ctrl_red_gain <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_red_gain_slope": {"acc": [0, 0, 9], "size": [], "description": "red color gain slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_red_gain_offset": {"acc": [1, 4, 5], "size": [], "description": "red color gain offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_blue_var5_shift": {"acc": [0, 3], "size": [], "description": "texture map shift bits for blue color mask", "usage": "smaller value means more blue edge pixels will not be affected by blue sharpen gain", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_blue_center": {"acc": [1, 7, 2], "size": [2], "description": "blue color center uv", "usage": "set as uv value of blue color", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_blue_radius": {"acc": [0, 8, 2], "size": [2], "description": "blue color radius", "usage": "larger value means more pixels will be selected in blue color mask", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_blue_dist_mode": {"acc": [0, 1], "size": [], "description": "blue color distance calculation mode, 0: diamond, 1: rectangle", "usage": "set to 0 as default", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_blue_dist_weight": {"acc": [0, 0, 3], "size": [4], "description": "blue color distance weights of 4 directions", "usage": "larger value means less pixels will be selected in blue color mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_blue_gain": {"acc": [0, 1, 5], "size": [], "description": "blue color sharpen gain", "usage": "larger value means stronger sharpen for blue color", "constraints": "shp_rgb_ctrl_blue_gain <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_blue_gain_slope": {"acc": [0, 0, 9], "size": [], "description": "blue color gain slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_blue_gain_offset": {"acc": [1, 4, 5], "size": [], "description": "blue color gain offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_green_center": {"acc": [1, 7, 2], "size": [2], "description": "green color center uv", "usage": "set as uv value of green color", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_green_radius": {"acc": [0, 8, 2], "size": [2], "description": "green color radius", "usage": "larger value means more pixels will be selected in green color mask", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_green_dist_mode": {"acc": [0, 1], "size": [], "description": "green color distance calculation mode, 0: diamond, 1: rectangle", "usage": "set to 0 as default", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_green_dist_weight": {"acc": [0, 0, 3], "size": [4], "description": "green color distance weights of 4 directions", "usage": "larger value means less pixels will be selected in green color mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_green_gain": {"acc": [0, 3, 5], "size": [], "description": "green color sharpen gain", "usage": "larger value means stronger sharpen for green color", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_green_gain_slope": {"acc": [1, 3, 9], "size": [], "description": "green color gain slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_green_gain_offset": {"acc": [1, 4, 5], "size": [], "description": "green color gain offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_skin_ctrl_enable": {"acc": [0, 1], "size": [], "description": "skin color sharpen gain control enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_skin_ctrl_color_center": {"acc": [1, 7, 2], "size": [2], "description": "skin color center uv", "usage": "set as uv value of skin color", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_skin_ctrl_color_radius": {"acc": [0, 8, 2], "size": [2], "description": "skin color radius", "usage": "larger value means more pixels will be selected in skin color mask", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_skin_ctrl_color_dist_mode": {"acc": [0, 1], "size": [], "description": "red color distance calculation mode, 0: diamond, 1: rectangle", "usage": "set to 1 as default", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_skin_ctrl_color_dist_weight": {"acc": [0, 0, 3], "size": [4], "description": "skin color distance weights of 4 directions", "usage": "larger value means less pixels will be selected in skin color mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_skin_ctrl_color_slope": {"acc": [0, 2, 9], "size": [], "description": "skin color gain slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_skin_ctrl_var5_shift": {"acc": [0, 3], "size": [], "description": "texture map shift bits for skin color mask", "usage": "smaller value means more skin edge pixels will not be affected by skin sharpen gain", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_skin_ctrl_var5_gain_slope": {"acc": [1, 0, 9], "size": [], "description": "skin color edge gain slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_skin_ctrl_var5_gain_offset": {"acc": [0, 4, 5], "size": [], "description": "skin color edge gain offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_skin_ctrl_var5_gain_limit": {"acc": [0, 1, 5], "size": [2], "description": "skin color edge gain limit", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U8", "partition": "-"}, "yadj_enable": {"acc": [0, 1], "size": [], "description": "yadj enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "yadj_lut": {"acc": [0, 8, 2], "size": [33], "description": "yadj lut, used to adjust brightness/contrast", "usage": "normally calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "yclip_enable": {"acc": [0, 1], "size": [], "description": "yclip enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "yclip_clip": {"acc": [0, 8, 0], "size": [2], "description": "yclip clip limit, [0]: lower limit, [1]: upper limit", "usage": "normally calculated in algo logic", "constraints": "yclip_clip[0] <= yclip_clip[1]", "type": "AX_U8", "partition": "-"}, "hsvc_enable": {"acc": [0, 1], "size": [], "description": "hsvc enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "hsvc_h_lut": {"acc": [1, 8, 7], "size": [25, 17], "description": "hsvc hue lut", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "hsvc_s_lut": {"acc": [0, 1, 15], "size": [25, 17], "description": "hsvc saturation lut", "usage": "", "constraints": "all", "type": "AX_U16", "partition": "-"}, "hsvc_yuv2rgb_matrix": {"acc": [1, 2, 8], "size": [3, 3], "description": "hsvc yuv2rgb matrix", "usage": "set as the same as inverted csc matrix", "constraints": "all", "type": "AX_S16", "partition": "-"}, "hsvc_rgb2yuv_matrix": {"acc": [1, 2, 8], "size": [3, 3], "description": "hsvc rgb2yuv matrix", "usage": "set as the same as csc matrix", "constraints": "all", "type": "AX_S16", "partition": "-"}, "hsvc_yuv2rgb_offset": {"acc": [1, 8, 2], "size": [2, 3], "description": "hsvc yuv2rgb offset", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "hsvc_rgb2yuv_offset": {"acc": [1, 8, 2], "size": [2, 3], "description": "hsvc rgb2yuv offset", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "ccmp_enable": {"acc": [0, 1], "size": [], "description": "ccmp enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "ccmp_y_lut": {"acc": [0, 1, 9], "size": [29], "description": "ccmp y gain lut", "usage": "smaller value means stronger saturation compression", "constraints": "all", "type": "AX_U16", "partition": "-"}, "ccmp_sat_lut": {"acc": [0, 1, 9], "size": [23], "description": "ccmp saturation gain lut", "usage": "smaller value means stronger saturation compression", "constraints": "all", "type": "AX_U16", "partition": "-"}, "cset_enable": {"acc": [0, 1], "size": [], "description": "cset enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "cset_io_flag": {"acc": [0, 1], "size": [], "description": "cset color target inverse selection flag, 0: inverse selection, 1: normal selection", "usage": "set to 1 normally", "constraints": "all", "type": "AX_U8", "partition": "-"}, "cset_color": {"acc": [1, 7, 2], "size": [2], "description": "cset color target new color uv value", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "cset_center_y": {"acc": [0, 8, 2], "size": [], "description": "cset color target y value", "usage": "set as y value of the specified color target", "constraints": "all", "type": "AX_U16", "partition": "-"}, "cset_center_uv": {"acc": [1, 7, 2], "size": [2], "description": "cset color target uv value", "usage": "set as uv value of the specified color target", "constraints": "all", "type": "AX_S16", "partition": "-"}, "cset_radius": {"acc": [0, 7, 2], "size": [3], "description": "cset color target radius", "usage": "larger value means more pixels will be selected in the color mask", "constraints": "all", "type": "AX_U16", "partition": "-"}, "cset_t_grad": {"acc": [0, 4], "size": [3], "description": "cset color mask transition gradient", "usage": "larger value means smoother/wider transition band in the color mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "cclip_enable": {"acc": [0, 1], "size": [], "description": "cclip enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "cclip_cmtx": {"acc": [1, 2, 5], "size": [2, 2], "description": "cclip color matrix", "usage": "normally calculated in algo logic", "constraints": "all", "type": "AX_S8", "partition": "-"}, "cclip_clip": {"acc": [1, 7, 0], "size": [2], "description": "cclip clip limit, [0]: lower limit, [1]: upper limit", "usage": "normally calculated in algo logic", "constraints": "cclip_clip[0] <= cclip_clip[1]", "type": "AX_S8", "partition": "-"}, "desat_enable": {"acc": [0, 1], "size": [], "description": "desat enable, 0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_strength": {"acc": [0, 4, 4], "size": [], "description": "desat strength, increase or decrease strength of desat", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_luma_lut": {"acc": [0, 1, 7], "size": [8], "description": "the luma ratio table for desat", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_sat_lut": {"acc": [0, 1, 7], "size": [6], "description": "the saturation ratio table for desat", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_angle_ratio_lut": {"acc": [0, 1, 7], "size": [16], "description": "the angle ratio table for desat", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_uv_val": {"acc": [1, 7, 2], "size": [2], "description": "desat uv color", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "desat_debug_enable": {"acc": [0, 1], "size": [], "description": "debug desat mask display enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_debug_thr": {"acc": [0, 1, 4], "size": [], "description": "debug threshold for desat mask", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_debug_color": {"acc": [1, 8, 2], "size": [3], "description": "debug desat color fill", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}}, "pfd": {"enable": {"acc": [0, 1], "size": [], "description": "0:bypass, 1:enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "y_ratio": {"acc": [0, 1, 6], "size": [4], "type": "AX_U8", "partition": "-"}, "c_ratio": {"acc": [1, 1, 6], "size": [4], "type": "AX_S8", "partition": "-"}, "gamma_lut": {"acc": [0, 8, 6], "size": [8], "type": "AX_U16", "partition": "-"}, "detedge_slope": {"acc": [0, 1, 6], "size": [2], "description": "detect edge slope", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "detedge_offset": {"acc": [1, 1, 6], "size": [2], "description": "detect edge offset", "usage": "", "constraints": "", "type": "AX_S8", "partition": "-"}, "seledge_thr_y_mesh": {"acc": [0, 8, 6], "size": [17, 17], "description": "select edge threshold for Y", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "seledge_slope_y": {"acc": [0, 1, 8], "size": [], "description": "select edge slope for Y", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "seledge_dist_weight": {"acc": [0, 1, 6], "size": [7], "description": "select distance weight", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "mask_coef": {"acc": [0, 3, 6], "size": [], "description": "mask coefficent", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "mesh_adj_mask_enable": {"acc": [0, 1], "size": [], "description": "enable location wise adjust for mask", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "mesh_mode": {"acc": [0, 1], "size": [], "description": "mesh using mode, 0: directly use; 1: use as 1/4", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "mesh": {"acc": [0, 2, 6], "size": [17, 17], "description": "strength control mesh", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "mesh_start": {"acc": [0, 4], "size": [2], "description": "start mesh position", "usage": "", "constraints": "", "type": "AX_U8", "partition": "support"}, "internal_start": {"acc": [0, 10], "size": [2], "description": "internal start among first mesh", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "pos_start": {"acc": [0, 0, 16], "size": [2], "description": "positiion start among first mesh", "usage": "", "constraints": "", "type": "AX_U16", "partition": "support"}, "mesh_length": {"acc": [0, 10], "size": [2], "description": "one unit mesh acoording to origin resolution", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "inv_mesh_length": {"acc": [0, 0, 16], "size": [2], "description": "inverse value of mesh length", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "mask_dialate_enable": {"acc": [0, 1], "size": [], "description": "enable of mask dialate functionality", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "mask_dist_weight": {"acc": [0, 1, 6], "size": [7], "description": "dialate distance weight", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "offset_in": {"acc": [0, 8, 4], "size": [], "description": "offset_in for input", "usage": "offset_in from RLTM", "constraints": "", "type": "AX_U16", "partition": "-"}}}}