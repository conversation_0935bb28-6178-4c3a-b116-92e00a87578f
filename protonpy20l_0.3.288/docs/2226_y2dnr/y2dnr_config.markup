h2. Conf list
h3. y_2dnr
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | y_2dnr bypass/enable, 0: bypass, 1: enable |  |
| debug_mode | u4 | [\] | - | [0~8\] | debug mode, 0: normal, 1: motion mask, 2: bp mask, 3:flat weight, 4:detail weight, 5:edge weight, 6:var, 7:nlm out, 8:bf out |  |
| debug_gain | u3 | [\] | - |  | debug bit shift for data visualization |  |
| motion_mask_enable | u1 | [\] | - |  | y_2dnr motion mask, 0: disable, 1: enable |  |
| dither_enable | u1 | [\] | - |  | enable dither or not, 0:disable 1:enable |  |
| dither_pmask | u16 | [2\] | - |  | dither mask |  |
| dither_seed | u16 | [2\] | - |  | dither seed |  |
| dither_seed_enable | u1 | [\] | - |  | dither seed enable |  |
| dir_det_ud_scale | u0.4 | [\] | - |  | non dir scale for dir detection |  |
| dir_det_dir_scale | u0.4 | [\] | - |  | dir scale for dir detection |  |
| dir_det_edge_noise | u8.4 | [\] | - |  | edge base for dir detection |  |
| bp_det_mode | u1 | [\] | - |  | two loacl dif method for bad pixel detection, 0:max-min mode,1:max2-min2 mode | the larger the value, more bad pixels will be detected |
| bp_det_m1 | u4.4 | [\] | - |  | local diff gain for bad pixel detection | the larger the value, less bad pixels will be detected |
| bp_det_m2 | u4.8 | [\] | - |  | bad pixel probability gain, the larger the bad pixel probability | the larger the value, the larger the bad pixel probability and more pixel will be corrected |
| bp_noise_strength | u8 | [9\] | - |  | noise str for bad pixel detection |  |
| bp_detail_lut | u0.8 | [9\] | - |  | bad pixel modify str lut |  |
| dir_det_var_w_thr | u8.2 | [2\] | - |  | thr for flat region dir modify curve |  |
| dir_det_var_w_slope | u2.8 | [2\] | - |  | slope for flat region dir modify curve |  |
| detail_w_thr | u8.2 | [2\] | - |  | thr for detail detection curve |  |
| detail_w_slope | u2.8 | [2\] | - |  | slope for detail detection curve |  |
| bpc_en | u1 | [\] | - |  | replace center bp pixel for nlm and bf filter, 0:disable, 1:enable |  |
| nlm_w_thr | u8.2 | [3\] | - |  | nlm thr for dif weight curve, [0-2\] represents edge, detail flat separately |  |
| nlm_w_slope | u2.8 | [3\] | - |  | nlm slope for dif weight curve, [0-2\] represents edge, detail flat separately |  |
| nlm_dis_coef | u1.8 | [6\] | - | [0\] + 4\*[1\] + 4\*[2\] + 4\*[3\] + 8\*[4\] + 4\*[5\] == 1.0 | nlm dis weight |  |
| bf_w_thr | u8.2 | [3\] | - |  | bilateral thr for dif weight curve, [0-2\] represents edge, detail flat separately |  |
| bf_w_slope | u2.8 | [3\] | - |  | bilateral slope for dif weight curve, [0-2\] represents edge, detail flat separately |  |
| bf_dis_coef | u1.8 | [10\] | - | [0\] + 4\*[1\] + 4\*[2\] + 4\*[3\] + 8\*[4\] + 4\*[5\] + 4\*[6\] +8\*[7\] + 8\*[8\] + 4\*[9\]== 1.0 | bilateral dis weight |  |
| nlm_bf_w | u0.8 | [6\] | - |  | nlm and bilateral blending weight, [0-2\] represents foreground edge, detail flat separately, [3-5\] represents background edge, detail flat separately |  |
| nlm_dir_str_scale | u1.4 | [\] | - | [0.0, 1.0\] | nlm dir str |  |
| nlm_str | u0.8 | [17\] | - |  | nlm global str lut |  |
| bf_str | u0.8 | [17\] | - |  | bilateral global str lut |  |
| luma_thr_bias_lut | s8.2 | [2, 17\] | - |  | luma thr bias for nr, 0: foreground, 1: background |  |
| luma_slope_bias_lut | s2.8 | [2, 17\] | - |  | luma slope bias for nr, 0: foreground, 1: background |  |
| motion_lut | u0.8 | [9\] | - |  | motion lut |  |

