{"enable": {"api": "nAinrEn", "display": "enable", "comments": "SW enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "model_list_num": {"api": "nModelListNum", "display": "modelListNum", "comments": "model_list_num", "hint": "Accuracy: U8.0 Range: [0, 6]"}, "model_list_path": {"api": "szModelListPath", "display": "modelListPath", "comments": "model_list_path", "hint": "Accuracy: S7.0 Range: [-128, 127]"}, "curr_model_path": {"api": "szCurrModelPath", "display": "currModelPath", "comments": "curr_model_path", "hint": "Accuracy: S7.0 Range: [-128, 127]"}, "curr_temporal_nr_name": {"api": "szCurrTemporalNrName", "display": "currTemporalNrName", "comments": "curr_temporal_nr_name", "hint": "Accuracy: S7.0 Range: [-128, 127]"}, "curr_spatial_nr_name": {"api": "szCurrSpatialNrName", "display": "currSpatialNrName", "comments": "curr_spatial_nr_name", "hint": "Accuracy: S7.0 Range: [-128, 127]"}, "nr_level_mode": {"api": "nNrLevelMode", "display": "nrLevelMode", "comments": "0: lut fusion, 1: only use nr level", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "temporal_nr_level": {"api": "nTemporalNrLevel", "display": "temporalNrLevel", "comments": "temporal_nr_level", "hint": "Accuracy: S7.0 Range: [-127, 127]"}, "spatial_nr_level": {"api": "nSpatialNrLevel", "display": "spatialNrLevel", "comments": "spatial_nr_level", "hint": "Accuracy: S7.0 Range: [-127, 127]"}, "bias_out": {"api": "nBiasOut", "display": "biasOut", "comments": "bias_out", "hint": "Accuracy: S9.6 Range: [-32768, 32767]"}, "offset_2d": {"api": "nOffset2d", "display": "offset2d", "comments": "offset_2d", "hint": "Accuracy: U16.0 Range: [0, 65535]"}, "offset_3d": {"api": "nOffset3d", "display": "offset3d", "comments": "offset_3d", "hint": "Accuracy: U16.0 Range: [0, 65535]"}, "lut_temporal_blend": {"api": "nLutTemporalBlend", "display": "lutTemporalBlend", "comments": "lut_temporal_blend", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "lut_spatial_blend": {"api": "nLutSpatialBlend", "display": "lutSpatialBlend", "comments": "lut_spatial_blend", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "lut_spatial_guidance": {"api": "nLutSpatialGuidance", "display": "lutSpatialGuidance", "comments": "lut_spatial_guidance", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "lut_hdr_mask": {"api": "nLutHdrMask", "display": "lutHdrMask", "comments": "lut_hdr_mask", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "lut_ext_temporal": {"api": "nLutExtTemporal", "display": "lutExtTemporal", "comments": "lut_ext_temporal", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "lut_high_frequency": {"api": "nLutHighFreq", "display": "lutHighFreq", "comments": "lut_high_frequency", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "extra_info": {"AinrCapability": {"attr": "RO", "comment": "RO: only support get api, RW: support set and get api, REF: directly use,  NULL: only support struct", "params": {"pIspAinrCapability": {"type": "AX_ISP_IQ_AINR_CAPABILITY_T", "size": [], "api": "pIspAinrCapability", "member": {"nValidNum": {"type": "AX_U8", "size": [], "api": "nValidNum", "display": "", "comments": "valid_num", "hint": "Accuracy: U8.0 Range: [0, 6]"}, "tModelCapList": {"type": "AX_ISP_IQ_AINR_CAP_TABLE_T", "size": [6], "member": {"szModelPath": {"type": "AX_CHAR", "size": [128], "api": "szModelPath", "display": "", "comments": "szModelPath", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "nSuppBiasOut": {"type": "AX_U8", "size": [], "api": "nSuppBiasOut", "display": "", "comments": "nSuppBiasOut", "hint": "Accuracy: U1.0 Range: [0, 1]", "dependency": "bias_out"}, "nSuppOffset3D": {"type": "AX_U8", "size": [], "api": "nSuppOffset3D", "display": "", "comments": "nSuppOffset3D", "hint": "Accuracy: U1.0 Range: [0, 1]", "dependency": "offset_3d"}, "nSuppOffset2D": {"type": "AX_U8", "size": [], "api": "nSuppOffset2D", "display": "", "comments": "nSuppOffset2D", "hint": "Accuracy: U1.0 Range: [0, 1]", "dependency": "offset_2d"}, "nSuppLutTemporalBlend": {"type": "AX_U8", "size": [], "api": "nSuppLutTemporalBlend", "display": "", "comments": "nSuppLutTemporalBlend", "hint": "Accuracy: U1.0 Range: [0, 1]", "dependency": "lut_temporal_blend"}, "nSuppLutSpatialBlend": {"type": "AX_U8", "size": [], "api": "nSuppLutSpatialBlend", "display": "", "comments": "nSuppLutSpatialBlend", "hint": "Accuracy: U1.0 Range: [0, 1]", "dependency": "lut_spatial_blend"}, "nSuppLutSpatialGuidance": {"type": "AX_U8", "size": [], "api": "nSuppLutSpatialGuidance", "display": "", "comments": "nSuppLutSpatialGuidance", "hint": "Accuracy: U1.0 Range: [0, 1]", "dependency": "lut_spatial_guidance"}, "nSuppLutHdrMask": {"type": "AX_U8", "size": [], "api": "nSuppLutHdrMask", "display": "", "comments": "nSuppLutHdrMask", "hint": "Accuracy: U1.0 Range: [0, 1]", "dependency": "lut_hdr_mask"}, "nSuppLutExtTemporal": {"type": "AX_U8", "size": [], "api": "nSuppLutExtTemporal", "display": "", "comments": "nSuppLutExtTemporal", "hint": "Accuracy: U1.0 Range: [0, 1]", "dependency": "lut_ext_temporal"}, "nSuppLutHighFreq": {"type": "AX_U8", "size": [], "api": "nSuppLutHighFreq", "display": "", "comments": "nSuppLutHighFreq", "hint": "Accuracy: U1.0 Range: [0, 1]", "dependency": "lut_high_frequency"}, "nTemporalBaseNrValidNum": {"type": "AX_U8", "size": [], "api": "nTemporalBaseNrValidNum", "display": "", "comments": "nTemporalBaseNrValidNum", "hint": "Accuracy: U8.0 Range: [0, 12]"}, "szTemporalBaseNrList": {"type": "AX_CHAR", "size": [12, 128], "api": "szTemporalBaseNrList", "display": "", "comments": "szTemporalBaseNrList", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "nSpatialBaseNrValidNum": {"type": "AX_U8", "size": [], "api": "nSpatialBaseNrValidNum", "display": "", "comments": "nSpatialBaseNrValidNum", "hint": "Accuracy: U8.0 Range: [0, 12]"}, "szSpatialBaseNrList": {"type": "AX_CHAR", "size": [12, 128], "api": "szSpatialBaseNrList", "display": "", "comments": "szSpatialBaseNrList", "hint": "Accuracy: U8.0 Range: [0, 255]"}}}}}}}}}