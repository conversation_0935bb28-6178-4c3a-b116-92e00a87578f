h2. Conf list
h3. dehaze
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| coef_set_en | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| coef_set_a | s5.7 | [\] | - |  | coefficient set a |  |
| coef_set_b | s4.8 | [\] | - |  | coefficient set b |  |
| offset_in | u8.2 | [\] | - | all | offset of input | Set 16.0 |
| offset_out | u8.2 | [\] | - | all | offset of output | Set 16.0 |
| a | u8.8 | [\] | - | [0, 255.99609375\] | Atmospheric reflectance | I = (I - A)/T + A |
| locat_offset | u14 | [2, 2\] | support | (locat_offset[0\][1\] % 2 == 0 && locat_offset[1\][1\] % 2 == 0; locat_offset[0\][1\] < zoom_ratio && locat_offset[1\][1\] < zoom_ratio; inv_start_offset[0\] - inv_zoom_ratio \* locat_offset[0\][1\] >= 0; inv_start_offset[0\] + inv_zoom_ratio \* (zoom_ratio - 1 - locat_offset[0\][1\]) <= 1; inv_start_offset[1\] - inv_zoom_ratio \* locat_offset[1\][1\] >= 0; inv_start_offset[1\] + inv_zoom_ratio \* (zoom_ratio - 1 - locat_offset[1\][1\]) <= 1 | coeff spatial offset | partition related |
| inv_start_offset | u0.14 | [2\] | support | inv_start_offset[0\] - inv_zoom_ratio \* locat_offset[0\][1\] >= 0; inv_start_offset[0\] + inv_zoom_ratio \* (zoom_ratio - 1 - locat_offset[0\][1\]) <=1; inv_start_offset[1\] - inv_zoom_ratio \* locat_offset[1\][1\] >= 0; inv_start_offset[1\] + inv_zoom_ratio \* (zoom_ratio - 1 - locat_offset[1\][1\]) <=1 | Bilinear interpolation weights | partition related |
| gray_weight | u1.8 | [3\] | - | gray_weight[0\] + conf.gray_weight[1\] + conf.gray_weight[2\] == 1 | weight of R,G,B channel | calculate gray |
| tx | u1.8 | [\] | - | 1>=tx>=0 | Lower limit of t |  |
| zoom_ratio | u7 | [\] | - | 32,64; =1/inv_zoom_ratio | ratio of a,b zoom | zoom_ratio |
| inv_zoom_ratio | u0.6 | [\] | - | =1/zoom_ratio | inverse of zoom_ratio |  |
| ptn_offset_h_w | u14 | [2\] | support | ptn_offset_h_w[0\] % 2 == 0; ptn_offset_h_w[0\] + ptn_h_w[0\] <= pic_h_w[0\]; ptn_offset_h_w[1\] % 2 == 0; ptn_offset_h_w[1\] + ptn_h_w[1\] <= pic_h_w[1\] | start offset of the partition | partition related |
| ptn_roi_t_b_l_r | u14 | [4\] | support | 0<=ptn_roi_t_b_l_r<=(pic.h,pic.w); ptn_roi_t_b_l_r % 2 == 0; ptn_roi_t_b_l_r[0\] < ptn_roi_t_b_l_r[1\]; ptn_roi_t_b_l_r[2\] < ptn_roi_t_b_l_r[3\] | roi zone | partition related |
| pic_h_w | u14 | [2\] | - | pic_h_w == [pic.h,pic.w\] | pic_h_w |  |
| ptn_h_w | u14 | [2\] | support | ptn_h_w ==[partition.h,partition.w\] | ptn_h_w | partition related |
| graystat_binratio | u1 | [\] | - | all | Gray's shift | gray = gray / (1 << (graystat_binratio \* 2)) |
| norm_ratio | u1.20 | [3\] | - | (0, 1\] | calculate bottom_right, top_right, bottom_left block's normalize ratio |  |

h3. ifa
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | N/A | enable control bit | 0 - disable; 1 - enable |
| mode | u4 | [\] | - | N/A | Select ifa operator to run | 0 - guided filter |
| sub_mode | u4 | [\] | - | N/A | Select sub_mode inside ifa operator | If selected ifa operator has serval sub-mode, use this register to select it. |
| output_hor_crop_enable | u1 | [\] | - | N/A | output horizontal crop enable control bit | 0 - disable; 1 - enable |
| output_hor_crop_st | u12 | [\] | support | N/A | output picture's horizontal crop start coordinates | out_hor_crop_st[n\] for pics[n\] |
| output_hor_crop_width | u12 | [\] | support | N/A | output picture's horizontal crop width | out_hor_crop_width[n\] for pics[n\] |
| guided_enable | u1 | [\] | - | N/A | guided filter enable control bit | 0 - disable; 1 - enable |
| guided_dehaze_dc_adj_enable | u1 | [\] | - | N/A | When used for dehaze, this bit controls whether adjust dark channel using coeff k before calculating guided filter.. | If enabled, dc = clip(1 - dc \* k, 0, 1.0) |
| guided_dehaze_dc_k | s4.12 | [\] | - | N/A | k value in dehaze logic | clip(b - dc \* k, 0, 1.0) |
| guided_dehaze_dc_b | u1.12 | [\] | - | N/A | b value in dehaze logic | clip(b - dc \* k, 0, 1.0) |
| guided_win_r | u3 | [\] | - | N/A | 2 \* guided_win_r + 1 == window size | Example: 5x5 --> 2 \* 2 + 1 |
| guided_epsilon | u16 | [\] | - | N/A | It's u16 form for various acc epsilon; For rltm, it's u0.16; For dehaze, it's u12.4; For UVNR, it's u0.16; The guided_epsilon value in guided filter formula. | coeff_a = cov_ip / (var_i + guided_epsilon \* guided_epsilon) |
| blur_enable | u1 | [\] | - | N/A | guided filter enable control bit | 0 - disable; 1 - enable |
| blur_weight | s5.10 | [3, 3\] | - |  | 3 x 3 filter weight; Symmetric parameter; Effective 5 x 5 filter weight. | Use upper-left corner 3x3 parameter to init internal 5x5 filter weight symmetrically. |

