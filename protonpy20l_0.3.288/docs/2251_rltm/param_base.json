{"configs": {"raw_ltm": {"enable": {"acc": [0, 1], "size": [], "description": "bypass/enable", "usage": "0: bypass, 1: enable", "constraints": "0, 1", "type": "AX_U8", "partition": "-"}, "dither_mode": {"acc": [0, 1], "size": [], "description": "disable/enable", "usage": "0: bypass, 1: enable, set 0 as default", "constraints": "0, 1", "type": "AX_U8", "partition": "-"}, "dither_scale": {"acc": [0, 8, 4], "size": [], "description": "dither strength", "usage": "set non 0, when dither enable", "constraints": "dither_scale = [0, 255.9375]", "type": "AX_U16", "partition": "-"}, "dither_seed_enable": {"acc": [0, 1, 0], "size": [], "description": "0: use stat dither_seed, 1: use conf dither_seed", "usage": "set 1 for the 1st frame, then set 0 from the 2nd frame onwards", "constraints": "0, 1", "type": "AX_U8", "partition": "-"}, "dither_seed": {"acc": [0, 16, 0], "size": [2], "description": "value of dither seed", "usage": "set non 0, when dither enable", "constraints": "dither_seed[0]: (0, 65536), dither_seed[10]: (0, 32768)", "type": "AX_U16", "partition": "-"}, "luma_weight": {"acc": [0, 1, 7], "size": [2], "description": "weight of luma_max and luma_rgb for luma and luma_detail calculate", "usage": "normal set [1, 0]", "constraints": "[0, 1]", "type": "AX_U8", "partition": "-"}, "win_size": {"acc": [0, 12, 0], "size": [], "description": "raw_ltm window size", "usage": "", "constraints": "win_size = 128, 256, 512, 1024, see more in Appendix", "type": "AX_U16", "partition": "-"}, "offset_h_w": {"acc": [0, 15, 0], "size": [2], "description": "current partition's offset of height and width", "usage": "for no partition case, set [0, 0]", "constraints": "offset_tile_h_w[0] < pic_h_w[0], offset_tile_h_w[1] < pic_h_w[1], see more in Appendix", "type": "AX_U16", "partition": "support"}, "ptn_h_w": {"acc": [0, 15, 0], "size": [2], "description": "the size of partition", "usage": "for no partition case, set as the full picture resolution", "constraints": "the height and width of the partition need to be smaller than the full picture size considering offset", "type": "AX_U16", "partition": "support"}, "ptn_hist_roi_t_b_l_r": {"acc": [0, 15, 0], "size": [4], "description": "the coordinates (top, bottom, left, right) of the histogram area in the current partition", "usage": "for no partition case, set as the [0, pic_h_w[0], 0, pic_h_w[1]]", "constraints": "the coordinates should not outside full picture", "type": "AX_U16", "partition": "support"}, "curve": {"acc": [0, 8, 4], "size": [104, 257], "description": "ltm tone LUT calculate by algo logic inside the hist roi area", "usage": "", "constraints": "the lut size should keep right with full picture resolution and window size", "type": "AX_U16", "partition": "-"}, "log10_lut": {"acc": [0, 3, 13], "size": [257], "description": "log10 lut calculate by 3a++", "usage": "", "constraints": "log10_lut[i-1] <= log10_lut[i], log10_lut[-1] < 6.3", "type": "AX_U16", "partition": "-"}, "gtm_lut": {"acc": [0, 8, 4], "size": [129], "description": "gtm lut calculate by algo logic", "usage": "", "constraints": "gtm_lut[i-1] <= gtm_lut[i]", "type": "AX_U16", "partition": "-"}, "pmask_16bit": {"acc": [0, 16, 0], "size": [], "description": "16 bit dither mask", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "pmask_15bit": {"acc": [0, 15, 0], "size": [], "description": "15 bit dither mask", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "dgain": {"acc": [0, 10, 6], "size": [], "description": "digital gain for input picture", "usage": "", "constraints": "make input picture white level up to U14.6, [1, 1023.984375]", "type": "AX_U16", "partition": "-"}, "pic_h_w": {"acc": [0, 15, 0], "size": [2], "description": "full input picture size", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "max_gain": {"acc": [0, 12, 16], "size": [], "description": "the max gain of raw_ltm module", "usage": "", "constraints": "[1, 4095.9999847412]", "type": "AX_U32", "partition": "-"}, "limit_luma_in": {"acc": [0, 14, 6], "size": [], "description": "limit the minimum value of input luma", "usage": "", "constraints": "[0.0625, 16383.984375]", "type": "AX_U32", "partition": "-"}, "limit_luma_out": {"acc": [0, 8, 4], "size": [], "description": "limit the minimum value of output luma", "usage": "", "constraints": "[0.0, 255.9375]", "type": "AX_U16", "partition": "-"}, "kernel_dis": {"acc": [0, 1, 11], "size": [3, 3], "description": "spatial filter coefficient of high frequency detail filter", "usage": "", "constraints": "[0, 1], kernel_dis.sum() == 1", "type": "AX_U16", "partition": "-"}, "sigma_val_lut": {"acc": [0, 1, 11], "size": [33], "description": "range filter coefficient lut for high frequency detail filter", "usage": "", "constraints": "[0, 1]", "type": "AX_U16", "partition": "-"}, "detail_coring_pos": {"acc": [0, 3, 13], "size": [], "description": "coring value of positive high frequency detail enhancement", "usage": "", "constraints": "[0, 7.99987793]", "type": "AX_U16", "partition": "-"}, "detail_coring_neg": {"acc": [0, 3, 13], "size": [], "description": "coring value of negative high frequency detail enhancement", "usage": "", "constraints": "[0, 7.99987793]", "type": "AX_U16", "partition": "-"}, "detail_gain_pos": {"acc": [0, 3, 5], "size": [], "description": "gain value of positive high frequency detail enhancement", "usage": "", "constraints": "[0, 7.96875]", "type": "AX_U8", "partition": "-"}, "detail_gain_neg": {"acc": [0, 3, 5], "size": [], "description": "gain value of negative high frequency detail enhancement", "usage": "", "constraints": "[0, 7.96875]", "type": "AX_U8", "partition": "-"}, "detail_limit_pos": {"acc": [0, 3, 13], "size": [], "description": "limit value of positive high frequency detail enhancement", "usage": "", "constraints": "[0, 7.99987793]", "type": "AX_U16", "partition": "-"}, "detail_limit_neg": {"acc": [0, 3, 13], "size": [], "description": "limit value of negative high frequency detail enhancement", "usage": "", "constraints": "[0, 7.99987793]", "type": "AX_U16", "partition": "-"}, "detail_extra_strength_pos": {"acc": [0, 4, 4], "size": [], "description": "extra strength of positive high frequency detail enhancement", "usage": "", "constraints": "[0, 15.9375]", "type": "AX_U8", "partition": "-"}, "detail_extra_strength_neg": {"acc": [0, 4, 4], "size": [], "description": "extra strength of negative high frequency detail enhancement", "usage": "", "constraints": "[0, 15.9375]", "type": "AX_U8", "partition": "-"}, "detail_strength_lut": {"acc": [0, 4, 4], "size": [33], "description": "lut of high frequency detail strength", "usage": "", "constraints": "[0, 15.9375]", "type": "AX_U8", "partition": "-"}, "detail_strength_lut_pos_low": {"acc": [0, 1, 7], "size": [33], "description": "lut of pos low frequency detail strength", "usage": "", "constraints": "[0, 1.9921875]", "type": "AX_U8", "partition": "-"}, "detail_strength_lut_neg_low": {"acc": [0, 1, 7], "size": [33], "description": "lut of neg low frequency detail strength", "usage": "", "constraints": "[0, 1.9921875]", "type": "AX_U8", "partition": "-"}, "exp10_lut_pos": {"acc": [0, 14, 14], "size": [65], "description": "positive exp10 lut", "usage": "", "constraints": "[1, 16383.99993896]", "type": "AX_U32", "partition": "-"}, "exp10_lut_neg": {"acc": [0, 1, 14], "size": [65], "description": "negative exp10 lut", "usage": "", "constraints": "[0, 1]", "type": "AX_U16", "partition": "-"}, "downsample_ratio": {"acc": [0, 1], "size": [], "description": "downsample ratio for log luma calculate", "usage": "", "constraints": "0, 1", "type": "AX_U8", "partition": "-"}, "detail_low_enable": {"acc": [0, 1], "size": [], "description": "low frequency enhancement bypass/enable control", "usage": "", "constraints": "0, 1", "type": "AX_U8", "partition": "-"}, "detail_low_reset_coeffab": {"acc": [0, 1], "size": [], "description": "low frequency enhancement coeff a and b reset to 1 and 0", "usage": "", "constraints": "0, 1", "type": "AX_U8", "partition": "-"}, "detail_gain_pos_low": {"acc": [0, 3, 5], "size": [], "description": "gain value of positive low frequency enhancement", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "detail_gain_neg_low": {"acc": [0, 3, 5], "size": [], "description": "gain value of negative low frequency enhancement", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "detail_limit_pos_low": {"acc": [0, 3, 13], "size": [], "description": "limit value of positive low frequency enhancement", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "detail_limit_neg_low": {"acc": [0, 3, 13], "size": [], "description": "limit value of negative low frequency enhancement", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "offset_in": {"acc": [0, 14, 6], "size": [], "description": "offset of pipeline input", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "offset_out": {"acc": [0, 8, 4], "size": [], "description": "offset of pipeline output", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}}, "raw_ltm_pst": {"enable": {"acc": [0, 1], "size": [], "description": "bypass/enable", "usage": "0: bypass, 1: enable", "constraints": "0, 1", "type": "AX_U8", "partition": "-"}, "mode_pst": {"acc": [0, 2], "size": [], "description": "raw_ltm work mode", "usage": "0: first partition, 1: last partition, 2: other partition", "constraints": "", "type": "AX_U8", "partition": "support"}, "kernel_dis_pst": {"acc": [0, 1, 11], "size": [3, 3], "description": "Bilateral filter's distance kernel.", "usage": "Effective kernel size is 5x5. 3x3 register will be used to initialize 5x5 kernel by using mirror copy.", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "sigma_val_lut_pst": {"acc": [0, 1, 11], "size": [33], "description": "This LUT is used to convert pixel value difference to weight in bilateral filter.", "usage": "", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "downsample_ratio": {"acc": [0, 1], "size": [], "description": "downsample ratio for log luma calculate", "usage": "0: output 1/32*1/32 full picture size log_luma, 1: output 1/64*1/64 full picture size log_luma", "constraints": "0, 1", "type": "AX_U8", "partition": "-"}, "norm_ratio_lut": {"acc": [0, 1, 15], "size": [3], "description": "calculate bottom_right, top_right, bottom_left block's normalize ratio", "usage": "", "constraints": "(0, 1]", "type": "AX_U16", "partition": "-"}}, "ifa": {"enable": {"acc": [0, 1], "size": [], "description": "enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "mode": {"acc": [0, 4], "size": [], "description": "Select ifa operator to run", "usage": "0 - guided filter", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "sub_mode": {"acc": [0, 4], "size": [], "description": "Select sub_mode inside ifa operator", "usage": "If selected ifa operator has serval sub-mode, use this register to select it.", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "output_hor_crop_enable": {"acc": [0, 1], "size": [], "description": "output horizontal crop enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "output_hor_crop_st": {"acc": [0, 12], "size": [], "description": "output picture's horizontal crop start coordinates", "usage": "out_hor_crop_st[n] for pics[n]", "constraints": "N/A", "type": "AX_U16", "partition": "support"}, "output_hor_crop_width": {"acc": [0, 12], "size": [], "description": "output picture's horizontal crop width", "usage": "out_hor_crop_width[n] for pics[n]", "constraints": "N/A", "type": "AX_U16", "partition": "support"}, "guided_enable": {"acc": [0, 1], "size": [], "description": "guided filter enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "guided_dehaze_dc_adj_enable": {"acc": [0, 1], "size": [], "description": "When used for dehaze, this bit controls whether adjust dark channel using coeff k before calculating guided filter..", "usage": "If enabled, dc = clip(1 - dc * k, 0, 1.0)", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "guided_dehaze_dc_k": {"acc": [1, 4, 12], "size": [], "description": "k value in dehaze logic", "usage": "clip(b - dc * k, 0, 1.0)", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "guided_dehaze_dc_b": {"acc": [0, 1, 12], "size": [], "description": "b value in dehaze logic", "usage": "clip(b - dc * k, 0, 1.0)", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "guided_win_r": {"acc": [0, 3], "size": [], "description": "2 * guided_win_r + 1 == window size", "usage": "Example: 5x5 --> 2 * 2 + 1", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "guided_epsilon": {"acc": [0, 16], "size": [], "description": "It's u16 form for various acc epsilon; For rltm, it's u0.16; For dehaze, it's u12.4; For UVNR, it's u0.16; The guided_epsilon value in guided filter formula.", "usage": "coeff_a = cov_ip / (var_i + guided_epsilon * guided_epsilon)", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "blur_enable": {"acc": [0, 1], "size": [], "description": "guided filter enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "blur_weight": {"acc": [1, 5, 10], "size": [3, 3], "description": "3 x 3 filter weight; Symmetric parameter; Effective 5 x 5 filter weight.", "usage": "Use upper-left corner 3x3 parameter to init internal 5x5 filter weight symmetrically.", "constraibnts": "N/A", "type": "AX_S16", "partition": "-"}}}, "partition_configs": ["raw_ltm.ptn_h_w", "raw_ltm.offset_h_w", "raw_ltm.ptn_hist_roi_t_b_l_r", "raw_ltm_pst.mode_pst", "ifa.output_hor_crop_st", "ifa.output_hor_crop_width"], "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "Rltm is 0x2251", "type": "AX_U16"}}, "params": {"enable": {"display": "enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 1, "comment": "SW enable", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "offset_in": {"acc": [0, 14, 6], "auto": 0, "comment": "offset_in", "default": 16.0, "display": "offset in", "hidden": 1, "range": [0.0, 16383.984375], "size": [], "target_conf": ["raw_ltm.offset_in"], "type": "AX_U32", "dependency": "common"}, "offset_out": {"acc": [0, 8, 4], "auto": 0, "comment": "offset_out", "default": 16.0, "display": "offset out", "hidden": 1, "range": [0.0, 255.9375], "size": [], "target_conf": ["raw_ltm.offset_out"], "type": "AX_U16", "dependency": "common"}, "input_max_value": {"acc": [0, 14, 6], "auto": 0, "comment": "input_max_value", "default": 256.0, "display": "input_max_value", "hidden": 1, "range": [0.0, 16383.984375], "size": [], "target_conf": ["raw_ltm.dgain"], "type": "AX_U32", "dependency": "common"}, "dither_mode": {"display": "dither_mode", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 1, "comment": "dither enable", "hidden": 0, "auto": 0, "target_conf": ["raw_ltm.dither_mode", "raw_ltm.dither_scale", "raw_ltm.dither_seed"], "dependency": "user"}, "dither_seed_enable": {"display": "dither_seed_enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 1, "comment": "dither seed enable", "hidden": 1, "auto": 0, "target_conf": ["raw_ltm.dither_seed_enable", "raw_ltm.dither_seed"], "dependency": "common"}, "dither_scale": {"display": "dither_scale", "acc": [0, 8, 4], "type": "AX_U16", "size": [], "range": [0.0, 255.9375], "default": 0.0, "comment": "dither strength", "hidden": 0, "auto": 0, "target_conf": ["raw_ltm.dither_mode", "raw_ltm.dither_scale", "raw_ltm.dither_seed"], "dependency": "user"}, "luma_weight": {"display": "luma_weight", "acc": [0, 1, 7], "type": "AX_U8", "size": [2], "range": [0.0, 1.0], "default": [0.5, 0.0], "comment": "weight of luma_max and luma_rgb for luma and luma_detail calculate", "hidden": 0, "auto": 0, "target_conf": ["raw_ltm.luma_weight"], "dependency": "user"}, "highlight_sup": {"display": "highlight_sup", "acc": [0, 5, 3], "type": "AX_U8", "size": [], "range": [0.0, 31.875], "default": 0.0, "comment": "highlight suppression", "hidden": 0, "auto": 2, "target_conf": [], "dependency": "user"}, "s_curve": {"display": "s_curve", "acc": [0, 1, 15], "type": "AX_U16", "size": [1025], "range": [0.0, 1.0], "default": "(np.arange(1025)/1024.0).tolist()", "comment": "s-curve", "hidden": 0, "auto": 1, "target_conf": [], "dependency": "user"}, "base_curve": {"display": "base_curve", "acc": [0, 1, 15], "type": "AX_U16", "size": [513], "range": [0.0, 1.0], "default": "(np.arange(513)/512.0).tolist()", "comment": "base-curve", "hidden": 0, "auto": 1, "target_conf": [], "dependency": "user"}, "gtm_sw_dgain_enable": {"display": "gtm_sw_dgain_enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "enable dgain mode", "hidden": 0, "auto": 0, "target_conf": ["raw_ltm.curve"], "dependency": "user"}, "gtm_sw_dgain": {"display": "gtm_sw_dgain", "acc": [0, 8, 8], "type": "AX_U16", "size": [], "range": [1.0, 255.99609375], "default": 1.0, "comment": "dgain when sw gain is on", "hidden": 0, "auto": 0, "target_conf": ["raw_ltm.curve"], "dependency": "user"}, "local_factor": {"display": "local_factor", "acc": [0, 1, 7], "type": "AX_U8", "size": [], "range": [0.0, 1.0], "default": 0.6953125, "comment": "factor for local hist and global hist", "hidden": 0, "auto": 2, "target_conf": ["raw_ltm.curve"], "dependency": "user"}, "rltm_strength": {"display": "rltm_strength", "acc": [0, 1, 7], "type": "AX_U8", "size": [], "range": [0.0, 1.0], "default": 1.0, "comment": "rltm strength", "hidden": 0, "auto": 2, "target_conf": ["raw_ltm.curve"], "dependency": "user"}, "base_curve_strength": {"display": "base_curve_strength", "acc": [0, 1, 7], "type": "AX_U8", "size": [], "range": [0.0, 1.0], "default": 0.0, "comment": "rltm base curve strength", "hidden": 0, "auto": 2, "target_conf": ["raw_ltm.curve"], "dependency": "user"}, "contrast_strength": {"display": "contrast_strength", "acc": [0, 1, 7], "type": "AX_U8", "size": [], "range": [0.0078125, 1.9921875], "default": 0.328125, "comment": "contrast strength", "hidden": 0, "auto": 2, "target_conf": ["raw_ltm.curve"], "dependency": "user"}, "pst_gamma": {"display": "pst_gamma", "acc": [0, 3, 5], "type": "AX_U8", "size": [], "range": [1.0, 7.96875], "default": 1.8125, "comment": "pst gamma", "hidden": 0, "auto": 2, "target_conf": [], "dependency": "user"}, "dark_contrast_enhance": {"display": "dark_contrast_enhance", "acc": [0, 0, 8], "type": "AX_U8", "size": [2], "range": [0.0, 0.99609375], "default": [0.0, 0.0], "comment": "Max Luma Value & Pixel Num for Contrast Enhance", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.curve"], "dependency": "user"}, "hdr_ratio": {"display": "hdr_ratio", "acc": [0, 9, 8], "type": "AX_U32", "size": [], "range": [0.0, 511.99609375], "default": 1.0, "comment": "hdr ratio", "hidden": 1, "auto": 0, "target_conf": ["raw_ltm.curve"], "dependency": "common"}, "k_max": {"display": "k_max", "acc": [0, 12, 4], "type": "AX_U16", "size": [], "range": [1.0, 4095.9375], "default": 4095.9375, "comment": "max gain for rltm", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.curve"], "dependency": "user"}, "w_win_hist": {"display": "w_win_hist", "acc": [0, 19], "type": "AX_U32", "size": [84, 64], "range": [0, 524287], "default": "np.zeros((84, 64)).tolist()", "comment": "histogram of each hist window", "hidden": 1, "auto": 0, "target_conf": ["raw_ltm.curve"], "dependency": "user"}, "detail_sigma_dis": {"display": "detail_sigma_dis", "acc": [0, 3, 13], "type": "AX_U16", "size": [], "range": [0.0001220703125, 7.9998779296875], "default": 1.0986328125, "comment": "bilateral filter sigma distance", "hidden": 0, "auto": 2, "target_conf": ["raw_ltm.kernel_dis"], "dependency": "user"}, "detail_sigma_val": {"display": "detail_sigma_val", "acc": [0, 3, 13], "type": "AX_U16", "size": [], "range": [0.0001220703125, 7.9998779296875], "default": 0.3662109375, "comment": "bilateral filter sigma value", "hidden": 0, "auto": 2, "target_conf": ["raw_ltm.sigma_val_lut"], "dependency": "user"}, "detail_coring_pos": {"display": "detail_coring_pos", "acc": [0, 3, 13], "type": "AX_U16", "size": [], "range": [0.0, 7.9998779296875], "default": 0.0048828125, "comment": "the coring for high freq pos detail", "hidden": 0, "auto": 2, "target_conf": ["raw_ltm.detail_coring_pos"], "dependency": "user"}, "detail_coring_neg": {"display": "detail_coring_neg", "acc": [0, 3, 13], "type": "AX_U16", "size": [], "range": [0.0, 7.9998779296875], "default": 0.0048828125, "comment": "the coring for high freq neg detail", "hidden": 0, "auto": 2, "target_conf": ["raw_ltm.detail_coring_neg"], "dependency": "user"}, "detail_gain_pos": {"display": "detail_gain_pos", "acc": [0, 3, 5], "type": "AX_U8", "size": [], "range": [0.0, 7.96875], "default": 1.0, "comment": "the gain for high freq pos detail", "hidden": 0, "auto": 2, "target_conf": ["raw_ltm.detail_gain_pos"], "dependency": "user"}, "detail_gain_neg": {"display": "detail_gain_neg", "acc": [0, 3, 5], "type": "AX_U8", "size": [], "range": [0.0, 7.96875], "default": 1.0, "comment": "the gain for high freq neg detail", "hidden": 0, "auto": 2, "target_conf": ["raw_ltm.detail_gain_neg"], "dependency": "user"}, "detail_limit_pos": {"display": "detail_limit_pos", "acc": [0, 3, 13], "type": "AX_U16", "size": [], "range": [0.0, 7.9998779296875], "default": 7.9998779296875, "comment": "the limit for high freq pos detail", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.detail_limit_pos"], "dependency": "user"}, "detail_limit_neg": {"display": "detail_limit_neg", "acc": [0, 3, 13], "type": "AX_U16", "size": [], "range": [0.0, 7.9998779296875], "default": 7.9998779296875, "comment": "the limit for high freq neg detail", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.detail_limit_neg"], "dependency": "user"}, "detail_extra_strength_pos": {"display": "detail_extra_strength_pos", "acc": [0, 4, 4], "type": "AX_U8", "size": [], "range": [0.0, 15.9375], "default": 0, "comment": "the global strength for high freq pos detail", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.detail_extra_strength_pos"], "dependency": "user"}, "detail_extra_strength_neg": {"display": "detail_extra_strength_neg", "acc": [0, 4, 4], "type": "AX_U8", "size": [], "range": [0.0, 15.9375], "default": 0, "comment": "the global strength for high freq neg detail", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.detail_extra_strength_neg"], "dependency": "user"}, "detail_strength_lut": {"display": "detail_strength_lut", "acc": [0, 4, 4], "type": "AX_U8", "size": [32], "range": [0.0, 15.9375], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "comment": "the strength for high freq detail by detail", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.detail_strength_lut"], "dependency": "user"}, "detail_strength_lut_pos_low": {"display": "detail_strength_lut_pos_low", "acc": [0, 1, 7], "type": "AX_U8", "size": [33], "range": [0.0, 1.9921875], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "comment": "the strength for low freq pos detail by luma", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.detail_strength_lut_pos_low"], "dependency": "user"}, "detail_strength_lut_neg_low": {"display": "detail_strength_lut_neg_low", "acc": [0, 1, 7], "type": "AX_U8", "size": [33], "range": [0.0, 1.9921875], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "comment": "the strength for low freq neg detail by luma", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.detail_strength_lut_neg_low"], "dependency": "user"}, "downsample_ratio": {"display": "downsample_ratio", "acc": [0, 8], "type": "AX_U8", "size": [], "range": [0, 1], "default": 1, "comment": "the downsample ratio of low freq luma", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "detail_low_enable": {"display": "detail_low_enable", "acc": [0, 8], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "low freq enhance enable", "hidden": 0, "auto": 0, "target_conf": ["raw_ltm.detail_low_enable"], "dependency": "user"}, "detail_low_reset_coeffab": {"display": "detail_low_reset_coeffab", "acc": [0, 8], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "reset the guided filter coeff a&b to 1&0", "hidden": 1, "auto": 0, "target_conf": ["raw_ltm.detail_low_reset_coeffab"], "dependency": "common"}, "detail_gain_pos_low": {"display": "detail_gain_pos_low", "acc": [0, 3, 5], "type": "AX_U8", "size": [], "range": [0.0, 7.96875], "default": 1.0, "comment": "the gain for low freq pos detail", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.detail_gain_pos_low"], "dependency": "user"}, "detail_gain_neg_low": {"display": "detail_gain_neg_low", "acc": [0, 3, 5], "type": "AX_U8", "size": [], "range": [0.0, 7.96875], "default": 1.0, "comment": "the gain for low freq neg detail", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.detail_gain_neg_low"], "dependency": "user"}, "detail_limit_pos_low": {"display": "detail_limit_pos_low", "acc": [0, 3, 13], "type": "AX_U16", "size": [], "range": [0.0, 7.9998779296875], "default": 7.9998779296875, "comment": "the limit for low freq pos detail", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.detail_limit_pos_low"], "dependency": "user"}, "detail_limit_neg_low": {"display": "detail_limit_neg_low", "acc": [0, 3, 13], "type": "AX_U16", "size": [], "range": [0.0, 7.9998779296875], "default": 7.9998779296875, "comment": "the limit for low freq neg detail", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm.detail_limit_neg_low"], "dependency": "user"}, "alpha": {"display": "alpha", "acc": [0, 1, 15], "type": "AX_U16", "size": [], "range": [0.0, 1.0], "default": 0.25, "comment": "temporal filter strength", "hidden": 0, "auto": 0, "target_conf": ["raw_ltm.curve"], "dependency": "user"}, "reset": {"display": "reset", "acc": [0, 2], "type": "AX_U8", "size": [], "range": [0, 2], "default": 2, "comment": "the num frame that use current rltm curve only", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "stop_updating": {"display": "stop_updating", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "stop updating curve", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "sigma_dis_pst": {"display": "sigma_dis_pst", "acc": [0, 3, 13], "type": "AX_U16", "size": [], "range": [0.0001220703125, 7.9998779296875], "default": 1.0986328125, "comment": "spatial domain weight low freq", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm_pst.kernel_dis_pst"], "dependency": "user"}, "sigma_val_pst": {"display": "sigma_val_pst", "acc": [0, 3, 13], "type": "AX_U16", "size": [], "range": [0.0001220703125, 0.25], "default": 0.1220703125, "comment": "intensity domain weight low freq", "hidden": 0, "auto": 1, "target_conf": ["raw_ltm_pst.sigma_val_lut_pst"], "dependency": "user"}, "coeff_win_r": {"display": "win_r", "acc": [0, 3], "type": "AX_U8", "size": [], "range": [0, 5], "default": 2, "comment": "guided filter win_radius", "hidden": 0, "auto": 0, "target_conf": ["ifa.guided_win_r"], "dependency": "user"}, "coeff_eps": {"display": "eps", "acc": [0, 16], "type": "AX_U16", "size": [], "range": [0, 65535], "default": 200, "comment": "guided filter eps", "hidden": 0, "auto": 0, "target_conf": ["ifa.guided_epsilon"], "dependency": "user"}, "sigma_dis_blur": {"display": "sigma_dis_blur", "acc": [0, 3, 13], "type": "AX_U16", "size": [], "range": [0.0001220703125, 7.9998779296875], "default": 1.0986328125, "comment": "blur coeff a and b", "hidden": 0, "auto": 0, "target_conf": ["ifa.blur_weight"], "dependency": "user"}, "pic_h_w": {"display": "pic_h_w", "acc": [0, 15, 0], "size": [2], "range": [128.0, 32767.0], "default": [1520.0, 2688.0], "comment": "full input picture size", "hidden": 1, "auto": 0, "type": "AX_U16", "target_conf": [], "dependency": "common"}, "partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "target_conf": ["raw_ltm.pic_h_w", "raw_ltm.offset_h_w", "raw_ltm.ptn_h_w", "raw_ltm.ptn_hist_roi_t_b_l_r", "raw_ltm.win_size", "raw_ltm_pst.mode_pst"], "dependency": "common", "display": "raw_ltm partition info", "range": [], "default": [], "comment": "raw_ltm partition info", "hidden": 1, "auto": 0}, "ifa_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "target_conf": ["ifa.output_hor_crop_enable", "ifa.output_hor_crop_st", "ifa.output_hor_crop_width"], "dependency": "common", "comment": "ifa partition", "hidden": 1, "auto": 0}}, "submodules": {"setup": {"configs": ["raw_ltm.enable", "raw_ltm_pst.enable", "raw_ltm.pmask_16bit", "raw_ltm.pmask_15bit", "raw_ltm.exp10_lut_pos", "raw_ltm.exp10_lut_neg", "raw_ltm.limit_luma_in", "raw_ltm.limit_luma_out"], "params": ["offset_in", "offset_out", "input_max_value"]}, "win_size": {"configs": ["raw_ltm.win_size"], "params": ["pic_h_w"]}, "dither": {"configs": [], "params": ["dither_mode", "dither_scale", "dither_seed_enable"]}, "reserved": {"configs": ["raw_ltm.gtm_lut", "raw_ltm.max_gain"], "params": []}, "common": {"configs": ["raw_ltm.log10_lut"], "params": ["luma_weight", "highlight_sup", "pst_gamma", "s_curve", "base_curve"]}, "info": {"configs": [], "params": ["partition_info"]}, "curve": {"configs": [], "params": ["gtm_sw_dgain_enable", "gtm_sw_dgain", "local_factor", "rltm_strength", "base_curve_strength", "contrast_strength", "dark_contrast_enhance", "hdr_ratio", "k_max", "alpha", "enable", "w_win_hist"]}, "detail": {"configs": [], "params": ["detail_sigma_dis", "detail_sigma_val", "detail_coring_pos", "detail_coring_neg", "detail_gain_pos", "detail_gain_neg", "detail_limit_pos", "detail_limit_neg", "detail_extra_strength_pos", "detail_extra_strength_neg", "detail_strength_lut"]}, "low_freq": {"configs": ["raw_ltm.downsample_ratio", "raw_ltm_pst.downsample_ratio", "raw_ltm_pst.norm_ratio_lut"], "params": ["downsample_ratio", "detail_low_enable", "detail_low_reset_coeffab", "detail_gain_pos_low", "detail_gain_neg_low", "detail_limit_pos_low", "detail_limit_neg_low", "sigma_dis_pst", "sigma_val_pst", "detail_strength_lut_pos_low", "detail_strength_lut_neg_low"]}, "temporal_filter": {"configs": [], "params": ["reset", "stop_updating"]}, "coeff": {"configs": ["ifa.enable", "ifa.mode", "ifa.sub_mode", "ifa.guided_enable", "ifa.guided_dehaze_dc_adj_enable", "ifa.guided_dehaze_dc_k", "ifa.guided_dehaze_dc_b", "ifa.guided_win_r", "ifa.guided_epsilon", "ifa.blur_enable", "ifa.blur_weight"], "params": ["coeff_win_r", "coeff_eps", "sigma_dis_blur"]}, "coeff_part": {"configs": [], "params": ["ifa_partition_info", "pic_h_w", "downsample_ratio"]}}, "structs": {}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [12], "ref_interp_method": ["linear"]}, "2": {"ref_mode": ["gain/lux", "hdr_ratio"], "ref_group_num": [12, 4], "ref_interp_method": ["linear", "linear"]}}, "target_module": {"mc20l": {"raw_ltm": {"id": 2800, "method": 0}, "raw_ltm_pst": {"id": 2801, "method": 0}, "ifa": {"id": 6500, "method": 0}}}, "constraints": ["s_curve[i] <= s_curve[i+1]", "base_curve[i] <= base_curve[i+1]", "ceil(max(pic_h_w)/(32 * downsample_ratio + 32))<=255"]}