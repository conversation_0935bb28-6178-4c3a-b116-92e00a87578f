h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| debug_mode |  | u5 | AX_U8 | [\] |  [0, 31\] | [None, None\] | 0 | None | hidden | 'lce.shp_debug_mode' | sharpen debug mode | common |
| disable_for_prev_debug |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden |  | disable sharpen when using yuv3dnr debug mode | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | Enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'lce.shp_limit_sup_max_over_change', 'lce.shp_limit_sup_max_under_change' | sharpen enable |   | None | None | None |
| ud_gain_lut | UD Gain | u8.4 | AX_U16 | [33\] | [0, 4095\] | [0.0, 255.9375\] | [128, 128, ... , 128\] | [8.0, 8.0, ... , 8.0\] | open | 'lce.shp_ud_gain_lut' | set undirectional gain w.r.t. texture strength |   | ['gain/lux'\] | [12\] | ['linear'\] |
| ud_gain_lut_anchor | UD Lut Anchor | u3 | AX_U8 | [\] | [0, 7\] | [None, None\] | 1 | None | open | 'lce.shp_ud_texture_shift' | smaller value means more anchor points in flat/weak texture regions |   | ['gain/lux'\] | [12\] | ['linear'\] |
| dir_gain_lut | Dir Gain | u8.4 | AX_U16 | [33\] | [0, 4095\] | [0.0, 255.9375\] | [128, 128, ... , 128\] | [8.0, 8.0, ... , 8.0\] | open | 'lce.shp_dir_gain_lut' | set directional gain w.r.t. edge strength |   | ['gain/lux'\] | [12\] | ['linear'\] |
| dir_gain_lut_anchor | Dir Lut Anchor | u3 | AX_U8 | [\] | [0, 7\] | [None, None\] | 1 | None | open | 'lce.shp_dir_texture_shift' | smaller value means more anchor points in flat/weak texture regions |   | ['gain/lux'\] | [12\] | ['linear'\] |
| ud_freq | UD Frequency | u5.5 | AX_U16 | [\] | [0, 1023\] | [0.0, 31.96875\] | 32 | 1.0 | open | 'lce.shp_filter_ud', 'lce.shp_ud_gain_lut' | larger value means higher undirectional frequecny will be enhanced |   | ['gain/lux'\] | [12\] | ['linear'\] |
| dir_freq | Dir Frequency | u5.5 | AX_U16 | [\] | [0, 1023\] | [0.0, 31.96875\] | 32 | 1.0 | open | 'lce.shp_filter_dir_0', 'lce.shp_filter_dir_22', 'lce.shp_filter_dir_45', 'lce.shp_dir_gain_lut' | larger value means higher directional frequecny will be enhanced |   | ['gain/lux'\] | [12\] | ['linear'\] |
| ud_coring | UD Coring | u3.3 | AX_U8 | [\] | [0, 63\] | [0.0, 7.875\] | 0 | 0.0 | open | 'lce.shp_coring_ud_level' | larger value means stronger coring for undirectional details |   | ['gain/lux'\] | [12\] | ['linear'\] |
| dir_coring | Dir Coring | u3.3 | AX_U8 | [\] | [0, 63\] | [0.0, 7.875\] | 0 | 0.0 | open | 'lce.shp_coring_dir_level' | larger value means stronger coring for directional details |   | ['gain/lux'\] | [12\] | ['linear'\] |
| edge_strength | Edge Strength | u4 | AX_U8 | [\] | [0, 15\] | [None, None\] | 8 | None | open | 'lce.shp_ud_scale', 'lce.shp_dir_scale' | larger value means to use more directional filter's result |   | ['gain/lux'\] | [12\] | ['linear'\] |
| edge_threshold | Edge Threshold | u8.4 | AX_U16 | [\] | [0, 4095\] | [0.0, 255.9375\] | 256 | 16.0 | open | 'lce.shp_edge_sad_noise' | larger value means less pixels will be considered as edge pixels |   | ['gain/lux'\] | [12\] | ['linear'\] |
| edge_smooth_thin_ratio | Edge Smooth Thin Ratio | u4 | AX_U8 | [\] | [0, 15\] | [None, None\] | 0 | None | open | 'lce.shp_filter_dir_0', 'lce.shp_filter_dir_22', 'lce.shp_filter_dir_45' | larger value means to make the edge much thinner |   | ['gain/lux'\] | [12\] | ['linear'\] |
| overshoot | Overshoot | u0.7 | AX_U8 | [\] | [0, 127\] | [0.0, 0.9921875\] | 80 | 0.625 | open | 'lce.shp_overshoot' | larger value means stronger overshoot |   | ['gain/lux'\] | [12\] | ['linear'\] |
| undershoot | Undershoot | u0.7 | AX_U8 | [\] | [0, 127\] | [0.0, 0.9921875\] | 90 | 0.703125 | open | 'lce.shp_undershoot' | larger value means stronger undershoot |   | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_region_threshold | Detail Threshold | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 20 | None | open | 'lce.shp_detail_overshoot_slope', 'lce.shp_detail_overshoot_offset', 'lce.shp_detail_overshoot_limit', 'lce.shp_detail_undershoot_slope', 'lce.shp_detail_undershoot_offset', 'lce.shp_detail_undershoot_limit' | larger value means more pixels will be considered as detail region |   | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_overshoot_adjust | Detail Overshoot Adjust | s0.7 | AX_S8 | [\] | [-127, 127\] | [-0.9921875, 0.9921875\] | 0 | 0.0 | open | 'lce.shp_detail_adj_enable', 'lce.shp_detail_overshoot_slope', 'lce.shp_detail_overshoot_offset', 'lce.shp_detail_overshoot_limit' | larger value means stronger overshoot for detail region |   | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_undershoot_adjust | Detail Undershoot Adjust | s0.7 | AX_S8 | [\] | [-127, 127\] | [-0.9921875, 0.9921875\] | 0 | 0.0 | open | 'lce.shp_detail_adj_enable', 'lce.shp_detail_undershoot_slope', 'lce.shp_detail_undershoot_offset', 'lce.shp_detail_undershoot_limit' | larger value means stronger undershoot for detail region |   | ['gain/lux'\] | [12\] | ['linear'\] |
| shoot_release_ratio | Shoot Release Ratio | u1.4 | AX_U8 | [\] | [0, 16\] | [0.0, 1.0\] | 0 | 0.0 | open | 'lce.shp_shoot_sup_enable', 'lce.shp_shoot_sup_blend_ratio' | larger value means shoot suppression tends to ignore more weak texture/detail region |   | ['gain/lux'\] | [12\] | ['linear'\] |
| shoot_sup_range | Shoot Suppression Range | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 0 | None | open | 'lce.shp_shoot_sup_enable', 'lce.shp_shoot_sup_var_min_slope', 'lce.shp_shoot_sup_var_min_offset', 'lce.shp_shoot_sup_var_min_limit' | larger value means shoot suppression tends to control more edges |   | ['gain/lux'\] | [12\] | ['linear'\] |
| shoot_sup_strength | Shoot Suppression Strength | u0.7 | AX_U8 | [\] | [0, 127\] | [0.0, 0.9921875\] | 96 | 0.75 | open | 'lce.shp_shoot_sup_enable', 'lce.shp_shoot_sup_var_min_slope', 'lce.shp_shoot_sup_var_min_offset', 'lce.shp_shoot_sup_var_min_limit' | larger value means stronger shoot suppression on edges |   | ['gain/lux'\] | [12\] | ['linear'\] |
| shp_extra_gain | Sharpen Extra Gain | u4.4 | AX_U8 | [\] | [16, 255\] | [1.0, 15.9375\] | 16 | 1.0 | open | 'lce.shp_ud_gain_lut', 'lce.shp_dir_gain_lut' | larger value means more extra gain applied to ud/dir gain lut |   | ['gain/lux'\] | [12\] | ['linear'\] |
| shp_limit | Sharpen Limit | u5.5 | AX_U16 | [\] | [0, 1023\] | [0.0, 31.96875\] | 1023 | 31.96875 | open | 'lce.shp_limit_sup_max_over_gain', 'lce.shp_limit_sup_max_under_gain' | smaller value means more restriction on the pixel value change, and may reduce the number of white/black points |   | ['gain/lux'\] | [12\] | ['linear'\] |
| luma_gain_lut_negative | Luma Negative Gain | u1.5 | AX_U8 | [33\] | [0, 63\] | [0.0, 1.96875\] | [32, 32, ... , 32\] | [1.0, 1.0, ... , 1.0\] | open | 'lce.shp_luma_mask_enable', 'lce.shp_luma_gain_lut' | adjust sharpen gain for negative hf based on image brightness |   | ['gain/lux'\] | [12\] | ['linear'\] |
| luma_gain_lut_positive | Luma Positive Gain | u1.5 | AX_U8 | [33\] | [0, 63\] | [0.0, 1.96875\] | [32, 32, ... , 32\] | [1.0, 1.0, ... , 1.0\] | open | 'lce.shp_luma_mask_enable', 'lce.shp_luma_gain_lut' | adjust sharpen gain for positive hf based on image brightness |   | ['gain/lux'\] | [12\] | ['linear'\] |
| red_gain | Red Gain | u1.5 | AX_U8 | [\] | [0, 32\] | [0.0, 1.0\] | 32 | 1.0 | open | 'lce.shp_rgb_ctrl_enable', 'lce.shp_rgb_ctrl_red_gain', 'lce.shp_rgb_ctrl_red_gain_slope', 'lce.shp_rgb_ctrl_red_gain_offset' | smaller value means less sharpen for red region |   | ['gain/lux'\] | [12\] | ['linear'\] |
| green_gain | Green Gain | u3.5 | AX_U8 | [\] | [0, 255\] | [0.0, 7.96875\] | 32 | 1.0 | open | 'lce.shp_rgb_ctrl_enable', 'lce.shp_rgb_ctrl_green_gain', 'lce.shp_rgb_ctrl_green_gain_slope', 'lce.shp_rgb_ctrl_green_gain_offset' | smaller value means less sharpen for green region |   | ['gain/lux'\] | [12\] | ['linear'\] |
| blue_gain | Blue Gain | u1.5 | AX_U8 | [\] | [0, 32\] | [0.0, 1.0\] | 32 | 1.0 | open | 'lce.shp_rgb_ctrl_enable', 'lce.shp_rgb_ctrl_blue_gain', 'lce.shp_rgb_ctrl_blue_gain_slope', 'lce.shp_rgb_ctrl_blue_gain_offset' | smaller value means less sharpen for blue region |   | ['gain/lux'\] | [12\] | ['linear'\] |
| skin_gain | Skin Gain | u1.5 | AX_U8 | [\] | [0, 32\] | [0.0, 1.0\] | 32 | 1.0 | open | 'lce.shp_skin_ctrl_enable', 'lce.shp_skin_ctrl_var5_gain_slope', 'lce.shp_skin_ctrl_var5_gain_offset', 'lce.shp_skin_ctrl_var5_gain_limit' | smaller value means less sharpen for skin region |   | ['gain/lux'\] | [12\] | ['linear'\] |
| skin_center | Skin Color Center | s7.2 | AX_S16 | [2\] | [-512, 511\] | [-128.0, 127.75\] | [-66, 114\] | [-16.5, 28.5\] | open | 'lce.shp_skin_ctrl_color_center' | skin color center |   | None | None | None |
| skin_radius | Skin Color Radius | u8.2 | AX_U16 | [2\] | [0, 1023\] | [0.0, 255.75\] | [45, 65\] | [11.25, 16.25\] | open | 'lce.shp_skin_ctrl_color_radius' | skin color radius |   | None | None | None |
| nr_strength | NR Strength | u1.5 | AX_U8 | [\] | [0, 32\] | [0.0, 1.0\] | 0 | 0.0 | open | 'lce.shp_nr_level' | larger value means stronger noise reduction |   | ['gain/lux'\] | [12\] | ['linear'\] |
| nr_ud_texture_sensitivity | UD Texture Sensitivity | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 64 | 0.25 | open | 'lce.shp_nr_ud_texture_scale' | larger value means image has more undirectional textures and apply less noise reduction |   | ['gain/lux'\] | [12\] | ['linear'\] |
| nr_ud_texture_threshold | UD Texture Threshold | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 64 | 0.25 | open | 'lce.shp_nr_ud_texture_offset' | larger value means less pixels will be considered as real texture and apply more noise reduction |   | ['gain/lux'\] | [12\] | ['linear'\] |
| nr_ud_limit | UD Noise Limit | u8.2 | AX_U16 | [\] | [0, 1023\] | [0.0, 255.75\] | 256 | 64.0 | open | 'lce.shp_nr_ud_limit' | larger value means less restriction on undirectional noise reduction |   | ['gain/lux'\] | [12\] | ['linear'\] |
| nr_dir_edge_sensitivity | Dir Edge Sensitivity | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 64 | 0.25 | open | 'lce.shp_nr_dir_texture_scale' | larger value means image has more directional edges and apply less noise reduction |   | ['gain/lux'\] | [12\] | ['linear'\] |
| nr_dir_edge_threshold | Dir Edge Threshold | u4.4 | AX_U8 | [\] | [0, 255\] | [0.0, 15.9375\] | 64 | 4.0 | open | 'lce.shp_nr_dir_texture_offset' | larger value means less pixels will be considered as real edge and apply more noise reduction |   | ['gain/lux'\] | [12\] | ['linear'\] |
| nr_dir_limit | Dir Noise Limit | u8.2 | AX_U16 | [\] | [0, 1023\] | [0.0, 255.75\] | 256 | 64.0 | open | 'lce.shp_nr_dir_limit' | larger value means less restriction on directional noise reduction |   | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_mask_enable | Motion Mask Enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'lce.shp_motion_mask_enable' | motion mask enable |   | None | None | None |
| motion_overshoot | Motion Overshoot | u0.7 | AX_U8 | [\] | [0, 127\] | [0.0, 0.9921875\] | 80 | 0.625 | open | 'lce.shp_motion_overshoot' | larger value means stronger overshoot |   | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_undershoot | Motion Undershoot | u0.7 | AX_U8 | [\] | [0, 127\] | [0.0, 0.9921875\] | 90 | 0.703125 | open | 'lce.shp_motion_undershoot' | larger value means stronger undershoot |   | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_ud_gain_lut | Motion UD Gain | u8.4 | AX_U16 | [33\] | [0, 4095\] | [0.0, 255.9375\] | [128, 128, ... , 128\] | [8.0, 8.0, ... , 8.0\] | open | 'lce.shp_motion_ud_gain_lut' | set undirectional gain w.r.t. texture strength |   | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_dir_gain_lut | Motion Dir Gain | u8.4 | AX_U16 | [33\] | [0, 4095\] | [0.0, 255.9375\] | [128, 128, ... , 128\] | [8.0, 8.0, ... , 8.0\] | open | 'lce.shp_motion_dir_gain_lut' | set directional gain w.r.t. edge strength |   | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_mask_lut | Motion Mask Lut | u0.8 | AX_U8 | [9\] | [0, 255\] | [0.0, 0.99609375\] | [0, 32, 64, 96, 128, 160, 192, 224, 255\] | [0.0, 0.125, 0.25, 0.375, 0.5, 0.625, 0.75, 0.875, 0.99609375\] | open | 'lce.shp_motion_mask_lut' | used to threshold/reverse the input motion lut |   | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |