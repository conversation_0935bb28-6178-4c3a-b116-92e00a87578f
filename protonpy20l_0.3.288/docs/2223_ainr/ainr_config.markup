h2. Conf list
h3. ainr
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | 0, 1 | bypass/enable | 0: bypass, 1: enable |
| hcg_mode | u1 | [\] | - | 0, 1 | hcg_mode |  |
| model_list_num | u8 | [\] | - | [0, 6\] | model_list_num |  |
| model_list_path | acc_unknown | [6, 128\] | - |  | model_list_path |  |
| curr_model_path | acc_unknown | [128\] | - |  | curr_model_path |  |
| curr_temporal_nr_name | acc_unknown | [128\] | - |  | curr_temporal_nr_name |  |
| curr_spatial_nr_name | acc_unknown | [128\] | - |  | curr_spatial_nr_name |  |
| offset_2d | u16 | [4\] | - | [0, 65535\] | offset_2d | normal set [4096, 4096, 4096, 4096\] |
| offset_3d | u16 | [4\] | - | [0, 65535\] | offset_3d | normal set [4096, 4096, 4096, 4096\] |
| lut_temporal_blend | u16 | [257\] | - | [0, 65535\] | lut_temporal_blend | normal set x==y |
| lut_spatial_guidance | u16 | [257\] | - | [0, 65535\] | lut_spatial_guidance | normal set x==y |
| lut_spatial_blend | u16 | [257\] | - | [0, 65535\] | lut_spatial_blend | normal set x==y |
| lut_hdr_mask | u16 | [257\] | - | [0, 65535\] | lut_hdr_mask | normal set x==y |
| lut_vst_inv | u16 | [257\] | - | [0, 65535\] | lut_vst_inv | normal set x==y |
| lut_ext_temporal | u16 | [257\] | - | [0, 65535\] | lut_ext_temporal | normal set x==y |
| lut_high_frequency | u16 | [257\] | - | [0, 65535\] | lut_high_frequency | normal set x==y |
| position_temporal_blend | u16 | [16\] | - | [0, 65535\] | position_temporal_blend | normal set x==y |
| step_temporal_blend | u32 | [16\] | - | [0, 4294967295\] | step_temporal_blend | normal set x==y |
| position_spatial_guidance | u16 | [16\] | - | [0, 65535\] | position_spatial_guidance | normal set x==y |
| step_spatial_guidance | u32 | [16\] | - | [0, 4294967295\] | step_spatial_guidance | normal set x==y |
| position_spatial_blend | u16 | [16\] | - | [0, 65535\] | position_spatial_blend | normal set x==y |
| step_spatial_blend | u32 | [16\] | - | [0, 4294967295\] | step_spatial_blend | normal set x==y |
| position_hdr_mask | u16 | [16\] | - | [0, 65535\] | position_hdr_mask | normal set x==y |
| step_hdr_mask | u32 | [16\] | - | [0, 4294967295\] | step_hdr_mask | normal set x==y |
| position_vst_inv | u16 | [16\] | - | [0, 65535\] | position_vst_inv | normal set x==y |
| step_vst_inv | u32 | [16\] | - | [0, 4294967295\] | step_vst_inv | normal set x==y |
| position_ext_temporal | u16 | [16\] | - | [0, 65535\] | position_ext_temporal | normal set x==y |
| step_ext_temporal | u32 | [16\] | - | [0, 4294967295\] | step_ext_temporal | normal set x==y |
| position_high_frequency | u16 | [16\] | - | [0, 65535\] | position_high_frequency | normal set x==y |
| step_high_frequency | u32 | [16\] | - | [0, 4294967295\] | step_high_frequency | normal set x==y |

h3. hdrc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  |  |  |
| affine_k | u7.9 | [\] | - |  |  |  |
| lut | u10.6 | [257\] | - |  |  |  |
| clip_level | u10.6 | [\] | - |  |  |  |
| offset_in | u14.6 | [4\] | - |  |  |  |
| offset_out | u10.6 | [\] | - |  |  |  |
| dither_enable | u1 | [\] | - |  |  |  |
| dither_seed_enable | u1.0 | [\] | - |  |  |  |
| dither_seed | u16.0 | [2\] | - |  |  |  |
| dither_pmask_16bit | u16.0 | [\] | - |  |  |  |
| dither_pmask_15bit | u16.0 | [\] | - |  |  |  |

h3. hidrc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  |  |  |
| affine_k | u1.11 | [\] | - |  |  |  |
| lut | u14.6 | [513\] | - |  |  |  |
| lut_inv_mode | u1 | [\] | - |  |  |  |
| offset_in | u10.6 | [\] | - |  |  |  |
| offset_out | u14.6 | [4\] | - |  |  |  |

