h2. Conf list
h3. hdr
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | N/A | hdr enable control bit | 0 - disable; 1 - enable |
| swap_frame | u1 | [\] | - | N/A | input frame swap bit | 0 - not swap; 1 - swap input frames |
| internal_lf_indicator | u1 | [\] | - | N/A | Internal LF indication bit. | 0 - LF is input 0; 1 - LF is input 1 |
| tnr_mask_base_fid | u1 | [\] | - | N/A | 0 - tnr mask represents input0's ratio in fusion output; 1 - tnr mask represents input1's ratio in fusion output |  |
| wb_gain | u4.12 | [4\] | - | N/A | Full range wb gain. It should consider dgain. | AWB/WBC's wbgain \* dgain = full range wbgain |
| inv_wb_gain | u1.15 | [4\] | - | N/A | 1 / wb_gain | 1 / wb_gain |
| d_gain | u4.12 | [\] | - | N/A | dgain should be set so that image \* dgain = full range. | Set to proper value |
| noise_profile | u6.10 | [33\] | - | N/A | noise profile of SF | Set to proper value. |
| inv_noise_profile | u10.10 | [33\] | - | N/A | inverse noise profile of SF | Set to proper value. |
| dgst_enable | u1 | [\] | - | N/A | dgst enable control bit | 0 - disable; 1 - enable |
| mot_sat_x | u8.10 | [2\] | - | N/A | Motion judgement coefficient which would be calculated in algo logic. | Set to proper value. |
| mot_k_ratio | u1.16 | [\] | - | N/A | Motion judgement coefficient which would be calculated in algo logic. | Set to proper value. |
| mot_coring_thre | u1.11 | [\] | - | N/A | Coring threshold for current motion mask. Motion level lower than this will be set to 0. | Set to proper value. |
| mot_coring_slope | u8.8 | [\] | - | N/A | Coring slope for current motion mask. Motion level larger than this will be multiplied by this to generate a new motion level. | Set to proper value. |
| mot_mask_max_blend_ratio | u1.8 | [\] | - | N/A | The blending ratio of 'max motion mask'. 'max motion mask' is the mask generated by taking the maximum motion mask value of 4 channels(e.g. RGGB) | mot mask = (1 - ratio) \* mot_mask + ratio \* mot_max_mask |
| exp_y_ratio | u1.8 | [2\] | - | N/A | When calculate luma value to generate exposure mask, it will use wbgain luma and dgain luma. This ratio controls the blending ratio of wbgain luma generated weight. | Set to proper value. |
| exp_weight_lut_luma_low | u1.15 | [2, 33\] | - | N/A | Exposure weight lut used in generating exposure mask based on luma value. For u8.4, when luma value is in between 0 ~ 32, this lut is effective. | Set to proper value. |
| exp_weight_lut_luma_mid | u1.15 | [2, 33\] | - | N/A | Exposure weight lut used in generating exposure mask based on luma value. For u8.4, when luma value is in between 32 ~ 224, this lut is effective. | Set to proper value. |
| exp_weight_lut_luma_high | u1.15 | [2, 33\] | - | N/A | Exposure weight lut used in generating exposure mask based on luma value. For u8.4, when luma value is in between 224 ~ 256, this lut is effective. | Set to proper value. |
| exp_weight_gain | u1.8 | [2\] | - | N/A | After exposure mask generation, exposure mask will be multiplied by this value to get exp mask used in dgst process. Note that at this point, exp masks have not yet been normalized. | Set to proper value. |
| fus_dark_prot_en | u1 | [2\] | - | N/A | If enabled, dark area of image will be protected using the similiar logic as over-exposure protection in fusion. | 0 - disable; 1 - enable |
| fus_prot_thre | u8.8 | [2, 2\] | - | N/A | Normal fusion protection threshold. Pixel value which is between fus_prot_thre[i\][0\] ~ fus_prot_thre[i\][1\] will be considered valid for fusion, where i is the frame index. | Set to proper value. |
| fus_prot_slope | u10.10 | [2, 2\] | - | N/A | Normal fusion protection slope. Pixel value which is between fus_prot_thre[i\][0\] ~ fus_prot_thre[i\][1\] will be considered valid for fusion, where i is the frame index. fus_prot_slope[i\][0\] and fus_prot_slope[i\][1\] are slope for transition(smoothing). | Set to proper value. |
| fus_weight_lut | u1.16 | [17\] | - | N/A | Fusion weight lut used to make transition area of LF and SF more smooth. | Set to proper value. |
| fus_weight_min_blend_ratio | u1.8 | [2\] | - | N/A | The blending ratio of 'min weight'. 'min_weight' is the minimum weight of 4 surrounding channels(e.g. RGGB). | fus prot weight = (1 - ratio) \* normal_weight + ratio \* min_weight |
| fus_prot_wb_ratio | u1.8 | [2\] | - | N/A | When calculating luma to judge if pixel value is over-exposed, both wbgain luma and dgain luma is calculated. This register controls the ratio of wbgain luma in final luma. | Set to proper value. |
| fus_base_fid | u1 | [\] | - | N/A | If all pixel values are considered over-exposed, this register choose which pixel value to use. | src[fus_base_fid\][y, x\] \* fus_exp_gain = normal fusion res |
| fus_clip_level | u14.6 | [\] | - | N/A | Normal fusion result clip level. | Set to proper value. |
| fus_exp_ratio | u1.16 | [2\] | - | N/A | HDR ratio | Set to proper value. |
| fus_exp_gain | u9.8 | [\] | - | N/A | Gain used when both pixel values are considered over-exposed. | Set to proper value. |
| dgst_stren_limit | u1.8 | [2\] | - | N/A | Motion mask used in dgst will be forcibly set in this range. | Set to proper value. |
| dgst_clip_level | u14.6 | [2\] | - | N/A | Clip level used when generating dgst fusion result whose ratio in final HDR result is controlled by motion mask. | Set to proper value. |
| dgst_exp_wb_gain | u12.16 | [2, 4\] | - | N/A | wbgain used when generating dgst fusion result. It should include HDR ratio. | Set to proper value. |
| dgst_base_fid | u1 | [\] | - | N/A | When both exp mask is 0, this register specifies which frame to be used in dgst fusion result. | Set to proper value. |
| debug_show_mask | u3 | [\] | - | N/A | HDR debug mode control. | 0 - normal mode; 1 - general mask; 2 - motion mask; 3 - exposure mask 0; 4 - exposure mask 1 |
| offset_in | u8.6 | [2\] | - | N/A | offset in inputs | offset_in for inputs of HDR. Since it's gone through BLC, pre-dpc inputs should have the same offset as pst-dpc inputs. |
| offset_out | u14.6 | [\] | - | N/A | offset out | Set to proper value. |

