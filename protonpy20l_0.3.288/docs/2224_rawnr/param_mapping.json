{"enable": {"api": "nRaw2dnrEn", "display": "enable", "comments": "soft enable switch for the entire module", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "mask_thre": {"api": "nMaskThre", "display": "maskThre", "comments": "mask threshold for coring", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "mask_limit": {"api": "nMaskLimit", "display": "maskLimit", "comments": "mask limit for coring", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "edge_preserve_ratio": {"api": "nEdgePreserveRatio", "display": "edgePreserveRatio", "comments": "edge_preserve_ratio for freq separation", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "inter_g_strength": {"api": "nInterGStr", "display": "interGStr", "comments": "inter_g_strength for freq separation", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "hf_nr_enable": {"api": "nHfNrEnable", "display": "hfNrEnable", "comments": "hf noise reduction enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "hf_nr_stren": {"api": "nHfNrStren", "display": "hfNrStren", "comments": "hf noise reduction strength", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "hf_nr_stren_rgb": {"api": "nHfNrStrenRgb", "display": "hfNrStrenRgb", "comments": "hf noise reduction strength for rgb channel", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "wiener_factor": {"api": "nWienerFactor", "display": "wienerFactor", "comments": "hf noise reduction wiener factor for different brightness", "hint": "Accuracy: U6.4 Range: [0, 1023]"}, "mf_nr_enable": {"api": "nMfNrEnable", "display": "mfNrEnable", "comments": "mf noise reduction enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "mf_nr_stren": {"api": "nMfNrStren", "display": "mfNrStren", "comments": "mf noise reduction strength", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "mf_nr_stren_rgb": {"api": "nMfNrStrenRgb", "display": "mfNrStrenRgb", "comments": "mf noise reduction strength for rgb channel", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "nlm_factor": {"api": "nNlmFactor", "display": "nlmFactor", "comments": "mf noise reduction nlm factor for different brightness", "hint": "Accuracy: U4.6 Range: [0, 1023]"}, "inter_channel_strength": {"api": "nInterChannelStr", "display": "interChannelStr", "comments": "inter_channel_strength for mf nlm", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "lut_mode": {"api": "nLutMode", "display": "lutMode", "comments": "lut_mode for noise_profile. 0: SDR 256, 1: HDR 512, 2: HDR 1024, 3: HDR 2048, 4: HDR 4096, 5: HDR 8192, 6: HDR 16384, 255: AUTO", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "lut_type": {"api": "nLutType", "display": "lutType", "comments": "lut_type for noise_profile, 0: Linear, 1: Log/Exponential", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "noise_profile_factor": {"api": "nNoiseProfileFactor", "display": "noiseProfileFactor", "comments": "noise_profile_factor for noise_profile", "hint": "Accuracy: U3.7 Range: [0, 1023]"}}