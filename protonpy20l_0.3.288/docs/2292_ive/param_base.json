{"context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "ive is 0x2292", "type": "AX_U16"}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "params": {"enable": {"display": "enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 1, "comment": "ive enable control bit", "hidden": 0, "auto": 0, "target_conf": ["ive.enable"], "dependency": "user"}, "mode": {"acc": [0, 8], "auto": 0, "comment": "choose operator.", "default": 0, "display": "mode", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["ive.mode"], "type": "AX_U8", "dependency": "user"}, "sub_mode": {"acc": [0, 8], "auto": 0, "comment": "choose the sub mode of operater.", "default": 4.25, "display": "sub mode", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.sub_mode"], "type": "AX_U8", "dependency": "user"}, "var_threshold": {"acc": [0, 4, 4], "auto": 0, "comment": "var_threshold multiplies model's var combined with background ratio to determine the standard used to judge if the pixel is background or not.", "default": 6.25, "display": "var threshold", "hidden": 0, "range": [0.0, 15.9375], "size": [], "target_conf": ["ive.var_threshold"], "type": "AX_U8", "dependency": "user"}, "var_threshold_check": {"acc": [0, 4, 4], "auto": 0, "comment": "var_threshold multiplies model's var to determine the standard used to judge if the model is a hit or not.", "default": 0, "display": "var threshold", "hidden": 0, "range": [0.0, 15.9375], "size": [], "target_conf": ["ive.var_threshold_check"], "type": "AX_U8", "dependency": "user"}, "var0": {"acc": [0, 14, 4], "auto": 0, "comment": "The initial variance setting of new model if the weakest one needs replacing.", "default": 600.0, "display": "var0", "hidden": 0, "range": [0.0, 16383.9375], "size": [], "target_conf": ["ive.var0"], "type": "AX_U32", "dependency": "user"}, "min_var": {"acc": [0, 14, 4], "auto": 0, "comment": "The smallest value of variance for updating Gaussian models.", "default": 200.0, "display": "min var", "hidden": 0, "range": [0.0, 16383.9375], "size": [], "target_conf": ["ive.min_var"], "type": "AX_U32", "dependency": "user"}, "max_var": {"acc": [0, 14, 4], "auto": 0, "comment": "The largest value of variance for updating Gaussian models.", "default": 10000.0, "display": "max var", "hidden": 0, "range": [0.0, 16383.9375], "size": [], "target_conf": ["ive.max_var"], "type": "AX_U32", "dependency": "user"}, "lr": {"acc": [0, 1, 7], "auto": 0, "comment": "Variable that determines model parameters updating speed.", "default": 0.01, "display": "lr", "hidden": 0, "range": [0.0, 1.0], "size": [], "target_conf": ["ive.lr"], "type": "AX_U8", "dependency": "user"}, "background_ratio": {"acc": [0, 1, 7], "auto": 0, "comment": "Variable used to determine the model weight(sum) boundary for background and foreground. Since all Gaussian models are in descending order, the higher this value is, more Gaussian model will be treated as background.", "default": 0.3, "display": "background ratio", "hidden": 0, "range": [0.0, 1.0], "size": [], "target_conf": ["ive.background_ratio"], "type": "AX_U8", "dependency": "user"}, "ct": {"acc": [1, 1, 7], "auto": 0, "comment": "The largest value of variance for updating Gaussian models", "default": -0.05, "display": "ct", "hidden": 0, "range": [-1.0, 1.0], "size": [], "target_conf": ["ive.ct"], "type": "AX_S8", "dependency": "user"}, "set_thresh": {"acc": [0, 8], "auto": 0, "comment": "The largest value of variance for updating Gaussian models", "default": 1, "display": "set thresh", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.set_thresh"], "type": "AX_U8", "dependency": "user"}, "filter_weight": {"acc": [1, 8, 10], "auto": 0, "comment": "Set proper filter template", "default": [[-2.0, -1.0, 0.0, 1.0, 2.0], [-2.0, -1.0, 0.0, 1.0, 2.0], [-4.0, -2.0, 0.0, 2.0, 4.0], [-2.0, -1.0, 0.0, 1.0, 2.0], [-2.0, -1.0, 0.0, 1.0, 2.0]], "display": "filter weight", "hidden": 0, "range": [-256.0, 255.9990234375], "size": [5, 5], "target_conf": ["ive.filter_weight"], "type": "AX_S32", "dependency": "user"}, "filter_stride": {"acc": [0, 5], "auto": 0, "comment": "For normal usage, it's default to all 1; For special usage e.g. SAD, it should be set to 4 since in mc20e ive only 4x4 SAD is supported. From the cmodel point of view, there is no constraint about it. For example, if the stride is set to 2, cmodel will still work, but HW will fail probably.", "default": [1, 1], "display": "filter stride", "hidden": 0, "range": [0, 31], "size": [2], "target_conf": ["ive.filter_stride"], "type": "AX_U8", "dependency": "user"}, "filter_init": {"acc": [0, 11], "auto": 0, "comment": "For normal usage, it's default to all 1; For special usage e.g. SAD, it should be set to 4 since in mc20e ive only 4x4 SAD is supported. From the cmodel point of view, there is no constraint about it. For example, if the stride is set to 2, cmodel will still work, but HW will fail probably.", "default": [0, 0], "display": "filter init", "hidden": 0, "range": [0, 2047], "size": [2], "target_conf": ["ive.filter_init"], "type": "AX_U8", "dependency": "user"}, "nms_low_threshold": {"acc": [0, 11], "auto": 0, "comment": "Weak edge lower gradient limit", "default": 300, "display": "nms low threshold", "hidden": 0, "range": [0, 2047], "size": [], "target_conf": ["ive.nms_low_threshold"], "type": "AX_U16", "dependency": "user"}, "nms_high_threshold": {"acc": [0, 11], "auto": 0, "comment": "Strong edge lower gradient limit", "default": 800, "display": "nms high threshold", "hidden": 0, "range": [0, 2047], "size": [], "target_conf": ["ive.nms_high_threshold"], "type": "AX_U16", "dependency": "user"}, "thresh_low_thr": {"acc": [0, 8], "auto": 0, "comment": "Lower threshold", "default": 100, "display": "lower threshold", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.thresh_low_thr"], "type": "AX_U8", "dependency": "user"}, "thresh_high_thr": {"acc": [0, 8], "auto": 0, "comment": "Upper threshold", "default": 200, "display": "upper threshold", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.thresh_high_thr"], "type": "AX_U8", "dependency": "user"}, "thresh_min_val": {"acc": [0, 8], "auto": 0, "comment": "Filling value of pixel level 0(pixels lower than lower threshold) when used", "default": 3, "display": "thresh min val", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.thresh_min_val"], "type": "AX_U8", "dependency": "user"}, "thresh_mid_val": {"acc": [0, 8], "auto": 0, "comment": "Filling value of pixel level 1(pixels between lower threshold and upper threshold) when used", "default": 145, "display": "thresh mid val", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.thresh_mid_val"], "type": "AX_U8", "dependency": "user"}, "thresh_max_val": {"acc": [0, 8], "auto": 0, "comment": "Filling value of pixel level 2(pixels greater than lower threshold) when used", "default": 231, "display": "thresh max val", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.thresh_max_val"], "type": "AX_U8", "dependency": "user"}, "conv_16to8_gain": {"acc": [1, 1, 14], "auto": 0, "comment": "Replace 'a/b' value used in above formula.", "default": 0.0025, "display": "conv 16to8 gain", "hidden": 0, "range": [-2.0, 1.99993896484375], "size": [], "target_conf": ["ive.conv_16to8_gain"], "type": "AX_S16", "dependency": "user"}, "conv_16to8_bias": {"acc": [1, 15], "auto": 0, "comment": "Replace 'bias' value used in above formula.", "default": -10, "display": "conv 16to8 bias", "hidden": 0, "range": [-32768, 32767], "size": [], "target_conf": ["ive.conv_16to8_bias"], "type": "AX_S16", "dependency": "user"}, "weight_pic_0": {"acc": [1, 7, 8], "auto": 0, "comment": "Used in ADD mode to determine picture 0 weight.", "default": 0.5, "display": "weight pic 0", "hidden": 0, "range": [-128.0, 127.99609375], "size": [], "target_conf": ["ive.weight_pic_0"], "type": "AX_S16", "dependency": "user"}, "weight_pic_1": {"acc": [1, 7, 8], "auto": 0, "comment": "Used in ADD mode to determine picture 1 weight.", "default": 0.5, "display": "weight pic 0", "hidden": 0, "range": [-128.0, 127.99609375], "size": [], "target_conf": ["ive.weight_pic_1"], "type": "AX_S16", "dependency": "user"}, "dma_h_gap": {"acc": [0, 8], "auto": 0, "comment": "Horizontal gap", "default": 3, "display": "dma h gap", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.dma_h_gap"], "type": "AX_U8", "dependency": "user"}, "dma_v_gap": {"acc": [0, 8], "auto": 0, "comment": "Vertical gap", "default": 4, "display": "dma v gap", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.dma_v_gap"], "type": "AX_U8", "dependency": "user"}, "dma_byte_num": {"acc": [0, 8], "auto": 0, "comment": "Choose how many bytes to read in each segment", "default": 2, "display": "dma byte num", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.dma_byte_num"], "type": "AX_U8", "dependency": "user"}, "dma_out_height": {"acc": [0, 11], "auto": 0, "comment": "Output matrix height", "default": 8, "display": "dma out height", "hidden": 0, "range": [0, 2047], "size": [], "target_conf": ["ive.dma_out_height"], "type": "AX_U16", "dependency": "user"}, "dma_out_width": {"acc": [0, 11], "auto": 0, "comment": "Output matrix width", "default": 20, "display": "dma out width", "hidden": 0, "range": [0, 2047], "size": [], "target_conf": ["ive.dma_out_width"], "type": "AX_U16", "dependency": "user"}, "dma_y0": {"acc": [0, 11], "auto": 0, "comment": "Starting point y coordinate for Direct copy", "default": 2, "display": "dma y0", "hidden": 0, "range": [0, 2047], "size": [], "target_conf": ["ive.dma_y0"], "type": "AX_U16", "dependency": "user"}, "dma_x0": {"acc": [0, 11], "auto": 0, "comment": "Starting point x coordinate for Direct copy", "default": 2, "display": "dma x0", "hidden": 0, "range": [0, 2047], "size": [], "target_conf": ["ive.dma_x0"], "type": "AX_U16", "dependency": "user"}, "dma_y1": {"acc": [0, 11], "auto": 0, "comment": "End point y coordinate for Direct copy", "default": 22, "display": "dma y1", "hidden": 0, "range": [0, 2047], "size": [], "target_conf": ["ive.dma_y1"], "type": "AX_U16", "dependency": "user"}, "dma_x1": {"acc": [0, 11], "auto": 0, "comment": "End point x coordinate for Direct copy", "default": 25, "display": "dma x1", "hidden": 0, "range": [0, 2047], "size": [], "target_conf": ["ive.dma_x1"], "type": "AX_U16", "dependency": "user"}, "dma_fill_data0": {"acc": [0, 16], "auto": 0, "comment": "Bytes0 in U64 which is to be filled into matrix", "default": 45637, "display": "dma fill data0", "hidden": 0, "range": [0, 65535], "size": [], "target_conf": ["ive.dma_fill_data0"], "type": "AX_U16", "dependency": "user"}, "dma_fill_data1": {"acc": [0, 16], "auto": 0, "comment": "Bytes1 in U64 which is to be filled into matrix", "default": 22538, "display": "dma fill data1", "hidden": 0, "range": [0, 65535], "size": [], "target_conf": ["ive.dma_fill_data1"], "type": "AX_U16", "dependency": "user"}, "dma_fill_data2": {"acc": [0, 16], "auto": 0, "comment": "Bytes2 in U64 which is to be filled into matrix", "default": 61262, "display": "dma fill data2", "hidden": 0, "range": [0, 65535], "size": [], "target_conf": ["ive.dma_fill_data2"], "type": "AX_U16", "dependency": "user"}, "dma_fill_data3": {"acc": [0, 16], "auto": 0, "comment": "Bytes3 in U64 which is to be filled into matrix", "default": 16472, "display": "dma fill data3", "hidden": 0, "range": [0, 65535], "size": [], "target_conf": ["ive.dma_fill_data3"], "type": "AX_U16", "dependency": "user"}, "lbp_thresh": {"acc": [0, 8], "auto": 0, "comment": "LBP Thresh value", "default": 0, "display": "lbp thresh", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.lbp_thresh"], "type": "AX_U8", "dependency": "user"}, "bernsen_cmin": {"acc": [0, 8], "auto": 0, "comment": "Bernsen local contrast threshold", "default": 15, "display": "bernsen cmin", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.be<PERSON>en_cmin"], "type": "AX_U8", "dependency": "user"}, "bernsen_thresh2": {"acc": [0, 8], "auto": 0, "comment": "The threshold value to judge corner based on corner response", "default": 128, "display": "bernsen thresh2", "hidden": 0, "range": [0, 255], "size": [], "target_conf": ["ive.be<PERSON>en_thresh2"], "type": "AX_U8", "dependency": "user"}}, "submodules": {"setup": {"params": ["enable", "mode"], "configs": ["ive.inverse_size", "ive.hist_normal_value"]}, "gmm2": {"params": ["sub_mode", "var_threshold", "var_threshold_check", "var0", "min_var", "max_var", "lr", "background_ratio", "ct", "set_thresh"], "configs": []}, "5x5filter": {"params": ["filter_weight", "filter_stride", "filter_init"], "configs": []}, "nms": {"params": ["nms_low_threshold", "nms_high_threshold"], "configs": []}, "canny_post": {"params": ["set_thresh"], "configs": []}, "thres": {"params": ["sub_mode", "thresh_low_thr", "thresh_high_thr", "thresh_min_val", "thresh_mid_val", "thresh_max_val"], "configs": []}, "conv16to8": {"params": ["sub_mode", "conv_16to8_gain", "conv_16to8_bias"], "configs": []}, "two_pics_op": {"params": ["sub_mode", "weight_pic_0", "weight_pic_1"], "configs": []}, "hist_integ": {"params": ["sub_mode"], "configs": []}, "dilation_erosion": {"params": ["sub_mode", "filter_weight"], "configs": []}, "ccl": {"params": ["sub_mode", "lbp_thresh"], "configs": []}, "dma": {"params": ["sub_mode", "dma_h_gap", "dma_v_gap", "dma_byte_num", "dma_out_height", "dma_out_width", "dma_y0", "dma_x0", "dma_y1", "dma_x1", "dma_fill_data0", "dma_fill_data1", "dma_fill_data2", "dma_fill_data3"], "configs": []}, "map": {"params": ["sub_mode"], "configs": []}, "lbp": {"params": ["sub_mode", "lbp_thresh"], "configs": []}, "sad": {"params": ["filter_weight", "filter_stride", "filter_init"], "configs": []}, "ordstat_bernsen": {"params": ["sub_mode", "berns<PERSON>_cmin", "bernsen_thresh2", "set_thresh"], "configs": []}}, "target_module": {"mc20l": {"ive": {"id": 6400, "method": 0}}}, "configs": {"ive": {"enable": {"acc": [0, 1], "size": [], "type": "AX_U8", "partition": "-"}, "mode": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "sub_mode": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "var_threshold": {"acc": [0, 4, 4], "size": [], "type": "AX_U8", "partition": "-"}, "var_threshold_check": {"acc": [0, 4, 4], "size": [], "type": "AX_U8", "partition": "-"}, "var0": {"acc": [0, 14, 4], "size": [], "type": "AX_U32", "partition": "-"}, "min_var": {"acc": [0, 14, 4], "size": [], "type": "AX_U32", "partition": "-"}, "max_var": {"acc": [0, 14, 4], "size": [], "type": "AX_U32", "partition": "-"}, "lr": {"acc": [0, 1, 7], "size": [], "type": "AX_U8", "partition": "-"}, "background_ratio": {"acc": [0, 1, 7], "size": [], "type": "AX_U8", "partition": "-"}, "ct": {"acc": [1, 1, 7], "size": [], "type": "AX_S16", "partition": "-"}, "set_thresh": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "filter_weight": {"acc": [1, 8, 10], "size": [5, 5], "type": "AX_S32", "partition": "-"}, "filter_stride": {"acc": [0, 5], "size": [2], "type": "AX_U8", "partition": "-"}, "filter_init": {"acc": [0, 11], "size": [2], "type": "AX_U16", "partition": "-"}, "nms_low_threshold": {"acc": [0, 11], "size": [], "type": "AX_U16", "partition": "-"}, "nms_high_threshold": {"acc": [0, 11], "size": [], "type": "AX_U16", "partition": "-"}, "thresh_low_thr": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "thresh_high_thr": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "thresh_min_val": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "thresh_mid_val": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "thresh_max_val": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "conv_16to8_gain": {"acc": [1, 1, 14], "size": [], "type": "AX_S16", "partition": "-"}, "conv_16to8_bias": {"acc": [1, 15, 0], "size": [], "type": "AX_S16", "partition": "-"}, "inverse_size": {"acc": [0, 1, 15], "size": [], "type": "AX_U16", "partition": "-"}, "weight_pic_0": {"acc": [1, 7, 8], "size": [], "type": "AX_S16", "partition": "-"}, "weight_pic_1": {"acc": [1, 7, 8], "size": [], "type": "AX_S16", "partition": "-"}, "hist_normal_value": {"acc": [0, 0, 20], "size": [], "type": "AX_U32", "partition": "-"}, "dma_h_gap": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "dma_v_gap": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "dma_byte_num": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "dma_out_height": {"acc": [0, 11], "size": [], "type": "AX_U16", "partition": "-"}, "dma_out_width": {"acc": [0, 11], "size": [], "type": "AX_U16", "partition": "-"}, "dma_y0": {"acc": [0, 11], "size": [], "type": "AX_U16", "partition": "-"}, "dma_x0": {"acc": [0, 11], "size": [], "type": "AX_U16", "partition": "-"}, "dma_y1": {"acc": [0, 11], "size": [], "type": "AX_U16", "partition": "-"}, "dma_x1": {"acc": [0, 11], "size": [], "type": "AX_U16", "partition": "-"}, "dma_fill_data0": {"acc": [0, 16], "size": [], "type": "AX_U16", "partition": "-"}, "dma_fill_data1": {"acc": [0, 16], "size": [], "type": "AX_U16", "partition": "-"}, "dma_fill_data2": {"acc": [0, 16], "size": [], "type": "AX_U16", "partition": "-"}, "dma_fill_data3": {"acc": [0, 16], "size": [], "type": "AX_U16", "partition": "-"}, "lbp_thresh": {"acc": [1, 8], "size": [], "type": "AX_S16", "partition": "-"}, "bernsen_cmin": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "bernsen_thresh2": {"acc": [0, 8], "size": [], "type": "AX_U8", "partition": "-"}, "harris_mat_scale": {"acc": [0, 5], "size": [], "type": "AX_U8", "partition": "-"}, "harris_k": {"acc": [0, 1, 15], "size": [], "type": "AX_U16", "partition": "-"}, "corner_max_num": {"acc": [0, 16], "size": [], "type": "AX_U16", "partition": "-"}, "corner_thresh": {"acc": [1, 15], "size": [], "type": "AX_S16", "partition": "-"}, "corner_response_shift": {"acc": [0, 7], "size": [], "type": "AX_U8", "partition": "-"}, "corner_distance": {"acc": [0, 11], "size": [2], "type": "AX_U16", "partition": "-"}, "ofl_pyra_scale": {"acc": [0, 3, 5], "size": [], "type": "AX_U8", "partition": "-"}, "ofl_win_size": {"acc": [0, 5], "size": [2], "type": "AX_U8", "partition": "-"}, "ofl_mat_scale": {"acc": [0, 6], "size": [], "type": "AX_U8", "partition": "-"}, "ofl_grad_compensation": {"acc": [0, 8, 6], "size": [], "type": "AX_U16", "partition": "-"}, "ofl_flt_g_epsilon": {"acc": [0, 7, 12], "size": [], "type": "AX_U32", "partition": "-"}, "ofl_iter_epsilon": {"acc": [0, 0, 16], "size": [], "type": "AX_U16", "partition": "-"}, "ofl_max_iter": {"acc": [0, 4], "size": [], "type": "AX_U8", "partition": "-"}, "sfd_coring_thresh": {"acc": [0, 12], "size": [], "type": "AX_U16", "partition": "-"}, "sfd_coring_slope": {"acc": [0, 8, 8], "size": [], "type": "AX_U16", "partition": "-"}, "sfd_damping_coef": {"acc": [0, 1, 15], "size": [2], "type": "AX_U16", "partition": "-"}}}, "partition_configs": []}