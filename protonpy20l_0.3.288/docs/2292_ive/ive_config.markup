h2. Conf list
h3. ive
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  |  |  |
| mode | u8 | [\] | - |  |  |  |
| sub_mode | u8 | [\] | - |  |  |  |
| var_threshold | u4.4 | [\] | - |  |  |  |
| var_threshold_check | u4.4 | [\] | - |  |  |  |
| var0 | u14.4 | [\] | - |  |  |  |
| min_var | u14.4 | [\] | - |  |  |  |
| max_var | u14.4 | [\] | - |  |  |  |
| lr | u1.7 | [\] | - |  |  |  |
| background_ratio | u1.7 | [\] | - |  |  |  |
| ct | s1.7 | [\] | - |  |  |  |
| set_thresh | u8 | [\] | - |  |  |  |
| filter_weight | s8.10 | [5, 5\] | - |  |  |  |
| filter_stride | u5 | [2\] | - |  |  |  |
| filter_init | u11 | [2\] | - |  |  |  |
| nms_low_threshold | u11 | [\] | - |  |  |  |
| nms_high_threshold | u11 | [\] | - |  |  |  |
| thresh_low_thr | u8 | [\] | - |  |  |  |
| thresh_high_thr | u8 | [\] | - |  |  |  |
| thresh_min_val | u8 | [\] | - |  |  |  |
| thresh_mid_val | u8 | [\] | - |  |  |  |
| thresh_max_val | u8 | [\] | - |  |  |  |
| conv_16to8_gain | s1.14 | [\] | - |  |  |  |
| conv_16to8_bias | s15.0 | [\] | - |  |  |  |
| inverse_size | u1.15 | [\] | - |  |  |  |
| weight_pic_0 | s7.8 | [\] | - |  |  |  |
| weight_pic_1 | s7.8 | [\] | - |  |  |  |
| hist_normal_value | u0.20 | [\] | - |  |  |  |
| dma_h_gap | u8 | [\] | - |  |  |  |
| dma_v_gap | u8 | [\] | - |  |  |  |
| dma_byte_num | u8 | [\] | - |  |  |  |
| dma_out_height | u11 | [\] | - |  |  |  |
| dma_out_width | u11 | [\] | - |  |  |  |
| dma_y0 | u11 | [\] | - |  |  |  |
| dma_x0 | u11 | [\] | - |  |  |  |
| dma_y1 | u11 | [\] | - |  |  |  |
| dma_x1 | u11 | [\] | - |  |  |  |
| dma_fill_data0 | u16 | [\] | - |  |  |  |
| dma_fill_data1 | u16 | [\] | - |  |  |  |
| dma_fill_data2 | u16 | [\] | - |  |  |  |
| dma_fill_data3 | u16 | [\] | - |  |  |  |
| lbp_thresh | s8 | [\] | - |  |  |  |
| bernsen_cmin | u8 | [\] | - |  |  |  |
| bernsen_thresh2 | u8 | [\] | - |  |  |  |
| harris_mat_scale | u5 | [\] | - |  |  |  |
| harris_k | u1.15 | [\] | - |  |  |  |
| corner_max_num | u16 | [\] | - |  |  |  |
| corner_thresh | s15 | [\] | - |  |  |  |
| corner_response_shift | u7 | [\] | - |  |  |  |
| corner_distance | u11 | [2\] | - |  |  |  |
| ofl_pyra_scale | u3.5 | [\] | - |  |  |  |
| ofl_win_size | u5 | [2\] | - |  |  |  |
| ofl_mat_scale | u6 | [\] | - |  |  |  |
| ofl_grad_compensation | u8.6 | [\] | - |  |  |  |
| ofl_flt_g_epsilon | u7.12 | [\] | - |  |  |  |
| ofl_iter_epsilon | u0.16 | [\] | - |  |  |  |
| ofl_max_iter | u4 | [\] | - |  |  |  |
| sfd_coring_thresh | u12 | [\] | - |  |  |  |
| sfd_coring_slope | u8.8 | [\] | - |  |  |  |
| sfd_damping_coef | u1.15 | [2\] | - |  |  |  |

