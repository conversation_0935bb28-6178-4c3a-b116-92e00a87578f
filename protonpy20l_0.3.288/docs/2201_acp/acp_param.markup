h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| hcg_lcg_mode |  | u2 | AX_U8 | [\] |  [0, 3\] | [None, None\] | 0 | None | hidden |  | 0: hcg, 1: lcg, 2: not supported (use lcg) |   |
| sen_black_level |  | u8.6 | AX_U16 | [\] |  [0, 16383\] | [0.0, 255.984375\] | 0 | 0.0 | hidden |  | sen black level |   |
| again |  | u22.10 | AX_U32 | [\] |  [0, 4294967295\] | [0.0, 4194303.9990234375\] | 0 | 0.0 | hidden |  | again |   |
| dgain |  | u10.8 | AX_U32 | [\] |  [0, 262143\] | [0.0, 1023.99609375\] | 0 | 0.0 | hidden |  | dgain |   |
| wb_gain |  | u4.8 | AX_U16 | [4\] |  [0, 4095\] | [0.0, 15.99609375\] | [0, 0, 0, 0\] | [np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0)\] | hidden |  | wb gain |   |
| exposure_ratio |  | u8.8 | AX_U16 | [\] |  [0, 65535\] | [0.0, 255.99609375\] | 0 | 0.0 | hidden |  | exposure ratio |   |
| use_isp_hdr |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden |  | use isp hdr |   |
| lf_indicator |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'hdr.internal_lf_indicator' | lf indicator |   |
| img_partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden |  | img partition info |   |
| ifa_partition_edge_width |  | u16 | AX_U16 | [\] |  [0, 65535\] | [None, None\] | 0 | None | hidden |  | ifa partition edge width |   |
| ifa_naked_partition_width |  | u16 | AX_U16 | [\] |  [0, 65535\] | [None, None\] | 0 | None | hidden |  | ifa naked partition width |   |
| is_first_frame |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden |  | is first frame |   |
| blc_det_window |  | u14 | AX_U16 | [4\] |  [0, 16383\] | [None, None\] | [0, 0, 0, 0\] | None | hidden | 'blc.det_window' | bl_det window, in [y, x, height, width\] order |   |
| blc_active_region |  | u14 | AX_U16 | [4\] |  [0, 16383\] | [None, None\] | [0, 0, 0, 0\] | None | hidden | 'blc.active_region' | bl_det active region area, in [y, x, height, width\] order |   |
| blc_channel_id |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'blc.channel_id' | blc channel_id, 0: SDR/HDR_Main, 1: HDR_Sub |   |
| clc_sat_strength |  | acc_unknown | AX_F32 | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'clc.sat_strength' | clc sat strength |   |
| yuv3dnr_debug_mode |  | u4 | AX_U8 | [\] |  [0, 15\] | [None, None\] | 0 | None | hidden |  | yuv3dnr debug mode |   |
| yuv3dnr_debug_gain |  | u3 | AX_U8 | [\] |  [0, 7\] | [None, None\] | 0 | None | hidden | 'yuv3dnr.debug_gain' | yuv3dnr debug gain |   |
| sharpen_debug_mode |  | u5 | AX_U8 | [\] |  [0, 31\] | [None, None\] | 0 | None | hidden |  | sharpen debug mode |   |
| y2dnr_debug_mode |  | u4 | AX_U8 | [\] |  [0, 15\] | [None, None\] | 0 | None | hidden |  | y2dnr debug mode |   |
| y2dnr_debug_gain |  | u3 | AX_U8 | [\] |  [0, 7\] | [None, None\] | 0 | None | hidden | 'y2dnr.debug_gain' | y2dnr debug gain |   |
| day_night_mode |  | u1 | AX_U8 | [\] |  [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | hidden |  | 0: day (color) mode, 1: night (gray) mode |   |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| hcg_shot_noise_coef | Hcg Shot Noise Coeffs | s0.31 | AX_S32 | [4, 2\] | [np.int64(-2147483648), np.int64(2147483647)\] | [np.float64(-1.0), np.float64(0.9999999995343387)\] | np.zeros((4, 2)).tolist() | np.zeros((4, 2)).tolist() | open |  | Hcg Calibration Params |   | None | None | None |
| hcg_read_noise_coef | Hcg Read Noise Coeffs | s0.31 | AX_S32 | [4, 3\] | [np.int64(-2147483648), np.int64(2147483647)\] | [np.float64(-1.0), np.float64(0.9999999995343387)\] | np.zeros((4, 3)).tolist() | np.zeros((4, 3)).tolist() | open |  | Hcg Calibration Params |   | None | None | None |
| lcg_shot_noise_coef | Lcg Shot Noise Coeffs | s0.31 | AX_S32 | [4, 2\] | [np.int64(-2147483648), np.int64(2147483647)\] | [np.float64(-1.0), np.float64(0.9999999995343387)\] | np.zeros((4, 2)).tolist() | np.zeros((4, 2)).tolist() | open |  | Lcg Calibration Params |   | None | None | None |
| lcg_read_noise_coef | Lcg Read Noise Coeffs | s0.31 | AX_S32 | [4, 3\] | [np.int64(-2147483648), np.int64(2147483647)\] | [np.float64(-1.0), np.float64(0.9999999995343387)\] | np.zeros((4, 3)).tolist() | np.zeros((4, 3)).tolist() | open |  | Lcg Calibration Params |   | None | None | None |
| aestat0_input_mux | aestat0 input mux | u2 | AX_U8 | [\] | [np.int64(0), np.int64(2)\] | [None, None\] | 1 | None | open | 'aaas.aes0_offset_in', 'aaas.aes0_hist_wl_gain', 'aaas.aes0_is_transposed' | aestat0 access point, 0: RawScl0, 1: DPC0, 2: WBC, NO GUI |   | None | None | None |
| aestat1_input_mux | aestat1 input mux | u2 | AX_U8 | [\] | [np.int64(0), np.int64(2)\] | [None, None\] | 2 | None | open | 'aaas.aes1_offset_in', 'aaas.aes1_hist_wl_gain', 'aaas.aes1_is_transposed' | aestat1 access point, 0: RawScl1, 1: DPC1, 2: WBC, NO GUI |   | None | None | None |
| awbstat_input_mux | awbstat input mux | u3 | AX_U8 | [\] | [np.int64(0), np.int64(4)\] | [None, None\] | 4 | None | open | 'aaas.awbs_offset_in', 'aaas.awbs_is_transposed' | awbstat access point, 0: RawScl0, 1: RawScl1, 2: DPC0, 3: Raw2DNR, 4: LSC, NO GUI |   | None | None | None |
| afstat_input_mux | afstat input mux | u2 | AX_U8 | [\] | [np.int64(0), np.int64(2)\] | [None, None\] | 1 | None | open | 'aaas.afs_offset_in', 'aaas.afs_is_transposed' | afstat access point, 0: RawScl0, 1: DPC0, 2: WBC, NO GUI |   | None | None | None |
| afstat_drc_mode | afstat drc mode | u8 | AX_U8 | [\] | [np.int64(0), np.int64(7)\] | [None, None\] | 0 | None | open | 'aaas.afs_drc_mode' | afstat drc mode, set w.r.t input bitwidth and af access point |   | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |