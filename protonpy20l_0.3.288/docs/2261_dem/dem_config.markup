h2. Conf list
h3. dem
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | dem enable, 0: bypass (COPY2GRAY mode), 1: enable |  |
| wb_stat_enable | u1 | [\] | - |  | enable wb stat or not, 0:disable, 1:enable |  |
| wb_stat_roi_t_b_l_r | u14 | [4\] | support | 0<=roi_t_b_l_r<=(pic.h,pic.w); roi_t_b_l_r % 2 == 0; roi_t_b_l_r[0\] < roi_t_b_l_r[1\]; roi_t_b_l_r[2\] < roi_t_b_l_r[3\] | roi zone, [0\]: top, [1\]: bottom, [2\]: left, [3\]: right |  |
| wb_stat_mesh_start | u3 | [2\] | support |  | mesh start index correspond to current partition's roi start position, [index_y, index_x\] |  |
| wb_stat_mesh_pos_start | u14 | [2\] | support | 0=<wb_stat_mesh_pos_start<=wb_stat_mesh_shape | pos in wb_stat_mesh_start correspond to current partition's roi start position, [index_y, index_x\] |  |
| wb_stat_slice_level | u8.4 | [\] | - |  | slice level for saturation; max setting means slice disable |  |
| wb_stat_slice_sat_type | u1 | [\] | - |  | saturation calculation method. 0: sum; 1: max |  |
| wb_stat_mesh_shape | u11 | [2\] | - | both<=1024, MC20L's max support width is 8192 | mesh shape when do wbc stat calculate, based on whole size raw, [index_y, index_x\] | mesh_h_shape, mesh_w_shape |
| wbc_enable | u1 | [\] | - |  | local wbc enable, 0: disable, 1: enable |  |
| wbc_gain_lut_r | u1.7 | [8, 8\] | - | [0.25, 1.0\] | local wbc R channel gain lut | wbc_gain_lut_r(i, j) = (local_wbc_gain(i, j)[r\] / global_wbc_gain[r\]) / max(local_wbc_gain(i, j)[:\] / global_wbc_gain[:\]) |
| wbc_gain_lut_g | u1.7 | [8, 8\] | - | [0.25, 1.0\] | local wbc G channel gain lut | wbc_gain_lut_g(i, j) = (local_wbc_gain(i, j)[g\] / global_wbc_gain[g\]) / max(local_wbc_gain(i, j)[:\] / global_wbc_gain[:\]) |
| wbc_gain_lut_b | u1.7 | [8, 8\] | - | [0.25, 1.0\] | local wbc B channel gain lut | wbc_gain_lut_b(i, j) = (local_wbc_gain(i, j)[b\] / global_wbc_gain[b\]) / max(local_wbc_gain(i, j)[:\] / global_wbc_gain[:\]) |
| wbc_mesh_start | u4 | [2\] | support |  | start index of padding gain lut (10 \*10) response to partition img start position, basicly same to wb_stat_block_num, [index_y, index_x\] |  |
| wbc_mesh_phase_start | u0.15 | [2\] | support |  | local wbc gain lut mesh initial phase, [index_y, index_x\] | for first partition, it is 0.5 + wbc_mesh_step / 2 |
| wbc_mesh_pos_start | u14 | [2\] | support | 0=<wbc_mesh_pos_start<=wb_stat_mesh_shape | local wbc gain lut mesh initial position in block, (include overlap area), [index_y, index_x\] |  |
| wbc_mesh_step | u0.14 | [2\] | - |  | local wbc gain lut mesh step, [0\]: y step, [1\]: x step | wbc_mesh_step[0\] = 8 / partition_height, wbc_mesh_step[1\] = 8 / partition_width |
| pre_gamma_enable | u1 | [\] | - |  | pre-gamma enable, 0: disable, 1: enable | normally set 1 |
| mode | u4 | [\] | - | mode == 1 or mode == 2 or mode == 4 | demosaic mode | 0: diff ratio(not supported), 1: copy to gray, 2: copy to green, 3: RGGB2RGB(not supported), 4: divider ratio |
| gic_enable | u1 | [\] | - |  | green imbalance correction enable, 0: disable, 1: enable |  |
| gic_strength_lut | u1.7 | [9\] | - |  | gic strength lut, [0\]:0, [1\]:32, ..., [8\]:256 | for gamma domain, the maximum input is 256 - offset_out |
| gic_ct_lut | u8.4 | [2, 9\] | - |  | gic crosstalk level for R/B channel, column index [0\]:0, [1\]:32, ..., [8\]:256, row[0\]: R channel crosstalk level w.r.t. R value, row[1\]: B channel crosstalk level w.r.t. B value |  |
| gic_limit_lut | u8.4 | [9\] | - |  | gic limit lut: [0\]:0, [1\]:32, ..., [8\]:256 | set gic limit w.r.t. GB/GR value |
| grad_h_enable | u1 | [\] | - |  | enable color diff for high frequency gradient, 0: disable, 1: enable | set enable to take into account high frequency gradient when calculating color difference |
| gradf_ratio | u1.4 | [\] | - | [0.0, 1.0\] | high and low frequency gradient filter's blending ratio | larger value means that more high frequency gradient will be taken into account |
| grad_hh_level | u2.2 | [\] | - |  | level for adjusting high frequency gradient | increase this value to enhance horizontal and vertical high frequency texture |
| inv_noise_lut | u4.8 | [9\] | - |  | 1/noise lut, [0\]:0, [1\]:32, ..., [8\]:256 | for gamma domain, the maximum input is 256 - offset_out |
| ginterp_hv_hf_ratio | u1.5 | [\] | - | ginterp_hv_hf_ratio <= 1.0 | hf ratio of h&v direction in green interpolation | larger value means adding more high frequency info of h&v direction for interpolated G pixels |
| ginterp_pn_hf_ratio | u1.5 | [\] | - | ginterp_pn_hf_ratio <= 1.0 | hf ratio of diagonal direction in green interpolation | larger value means adding more high frequency info of diagonal direction for interpolated G pixels |
| ginterp_x_hf_ratio | u1.5 | [\] | - | ginterp_x_hf_ratio <= 1.0 | hf ratio of x direction in green interpolation | larger value means adding more high frequency info of x direction for interpolated G pixels |
| gclip_level | u0.4 | [\] | - |  | green pixel clip level | larger value means more restricted range for interpolated G pixels |
| edge_enhance_enable | u1 | [\] | - |  | edge enhance enable, 0: disable, 1: enable |  |
| edge_enhance_ghv_level | u2.4 | [\] | - |  | edge enhance horizontal and vertical direction strength | larger value means stronger enhancement for horizontal and vertical direction |
| edge_enhance_ghv_corner_min | u0.8 | [\] | - |  | edge enhance horizontal/vertical direction corner minimum coring strength | increase this value to reduce artifacts on corner pixels |
| edge_enhance_ghv_corner_slope | u2.4 | [\] | - |  | edge enhance horizontal/vertical direction corner coring slope | increase this value to reduce artifacts on corner pixels |
| edge_enhance_ghv_corner_offset | s0.8 | [\] | - |  | edge enhance horizontal/vertical direction corner coring offset | increase this value to reduce artifacts on corner pixels |
| edge_enhance_gpn_level | u2.4 | [\] | - |  | edge enhance diagonal direction strength | larger value means stronger enhancement for diagonal direction |
| edge_enhance_level_lut | u1.4 | [2, 9\] | - | [0.0, 1.0\] | edge enhance strength lut with g channel, row[0\]: noise reduction strength lut, row[1\]: sharpen strength lut | larger values means stronger nr and sharpen level |
| fc_cor_enable | u1 | [\] | - |  | false color correction enable, 0: disable, 1: enable |  |
| fc_cor_limit | u8.4 | [\] | - |  | false color correction limit | larger value means more pixels will be considered as false color pixels |
| fc_cor_gain_safe | u2.2 | [\] | - |  | false color correction gain for safe uv | larger value means stronger fcc strength, makes the corrected color more grayish |
| fc_cor_gain | u4.4 | [\] | - |  | false color correction gain | larger value means stronger fcc strength, makes the corrected color more grayish |
| fc_cor_th | u4.4 | [\] | - |  | false color correction threshold | larger value means less pixels will be considered as false color pixels |
| fc_cor_sat_lut | u8.4 | [9\] | - |  | false color correction saturation lut (linear lut with g input), controls the blending weight of original color and corrected color | larger value means current pixel is more like an unsaturated gray pixel and more corrected color will be used in the final output |
| fc_cor_inv_base_sat_level | u0.8 | [\] | - |  | false color correction inverse base saturation level | smaller value means larger downscaling of the saturation level and more corrected color will be used in the final output |
| fc_cor_ratio_limit | u1.8 | [2\] | - | [0.0, 1.0\] | false color correction ratio limit, [0\]: lower limit, [1\]: upper limit | normally set [0.0, 1.0\] |
| post_gamma_enable | u1 | [\] | - |  | post-gamma enable, 0: disable, 1: enable | normally set 1 |
| offset_in | u8.4 | [\] | - |  | offset in | set to 16.0 in usual |
| offset_out | u8.4 | [\] | - |  | offset out | set to 16.0 in usual |

