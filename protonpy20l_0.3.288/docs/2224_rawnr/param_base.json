{"partition_configs": [], "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "RAWNR is 0x2224"}, "enable": {"size": [], "acc": [0, 1], "default": 1}}, "params": {"enable": {"acc": [0, 1], "size": [], "default": 1, "range": [0, 1], "hidden": 0, "auto": 0, "comment": "soft enable switch for the entire module", "dependency": " ", "target_conf": []}, "offset_in": {"acc": [0, 14, 6], "size": [], "default": 16.0, "range": [0.0, 16383.984375], "comment": "Offset In", "hidden": 1, "auto": 0, "target_conf": ["raw_2dnr.offset_in"], "dependency": "common"}, "offset_out": {"acc": [0, 14, 6], "size": [], "range": [0.0, 16383.984375], "default": 16.0, "comment": "Offset Out", "hidden": 1, "auto": 0, "target_conf": ["raw_2dnr.offset_out"], "dependency": "common"}, "mask_thre": {"acc": [0, 0, 8], "size": [2], "range": [0.0, 0.99609375], "default": [0, 255], "comment": "mask threshold for coring", "hidden": 0, "auto": 1, "target_conf": ["raw_2dnr.mask_coring_thre", "raw_2dnr.mask_coring_slope"], "dependency": " "}, "mask_limit": {"acc": [0, 0, 8], "size": [2], "range": [0.0, 0.99609375], "default": [0, 255], "comment": "mask limit for coring", "hidden": 0, "auto": 1, "target_conf": ["raw_2dnr.mask_coring_limit", "raw_2dnr.mask_coring_slope"], "dependency": " "}, "edge_preserve_ratio": {"display": "edge_preserve_ratio", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.5, "comment": "edge_preserve_ratio for freq separation", "hidden": 0, "auto": 1, "target_conf": ["raw_2dnr.freqsep_edge_preserve"], "dependency": " "}, "inter_g_strength": {"acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.5, "comment": "inter_g_strength for freq separation", "hidden": 0, "auto": 1, "target_conf": ["raw_2dnr.freqsep_inter_g_strength"], "dependency": " "}, "hf_nr_enable": {"display": "hf_nr_enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "hf noise reduction enable", "hidden": 0, "auto": 0, "target_conf": ["raw_2dnr.wiener_org_blend"], "dependency": " "}, "hf_nr_stren": {"acc": [0, 0, 8], "size": [2], "range": [0.0, 0.99609375], "default": [0.5, 0.5], "comment": "hf noise reduction strength", "hidden": 0, "auto": 1, "ref_group": 0, "target_conf": ["raw_2dnr.wiener_org_blend"], "dependency": " "}, "hf_nr_stren_rgb": {"acc": [0, 0, 8], "size": [2, 3], "range": [0.0, 0.99609375], "default": 0.99609375, "comment": "hf noise reduction strength for rgb channel", "hidden": 0, "auto": 1, "ref_group": 0, "target_conf": ["raw_2dnr.wiener_org_blend"], "dependency": " "}, "wiener_factor": {"acc": [0, 6, 4], "size": [2, 17], "range": [0.0, 63.9375], "default": 1.0, "comment": "hf noise reduction wiener factor for different brightness", "hidden": 0, "auto": 1, "target_conf": ["raw_2dnr.wiener_factor"], "dependency": " "}, "mf_nr_enable": {"acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "mf noise reduction enable", "hidden": 0, "auto": 0, "target_conf": ["raw_2dnr.nlm_org_blend"], "dependency": " "}, "mf_nr_stren": {"acc": [0, 0, 8], "size": [2], "range": [0.0, 0.99609375], "default": [0.5, 0.5], "comment": "mf noise reduction strength", "hidden": 0, "auto": 1, "target_conf": ["raw_2dnr.nlm_org_blend"], "dependency": " "}, "mf_nr_stren_rgb": {"acc": [0, 0, 8], "size": [2, 3], "range": [0.0, 0.99609375], "default": 0.99609375, "comment": "mf noise reduction strength for rgb channel", "hidden": 0, "auto": 1, "target_conf": ["raw_2dnr.nlm_org_blend"], "dependency": " "}, "nlm_factor": {"acc": [0, 4, 6], "size": [2, 17], "range": [0.0, 15.984375], "default": 0.5, "comment": "mf noise reduction nlm factor for different brightness", "hidden": 0, "auto": 1, "ref_group": 0, "target_conf": ["raw_2dnr.nlm_thre_factor", "raw_2dnr.nlm_inv_thre_factor"], "dependency": " "}, "inter_channel_strength": {"acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.99609375, "comment": "inter_channel_strength for mf nlm", "hidden": 0, "auto": 1, "target_conf": ["raw_2dnr.nlm_inter_g_strength"], "dependency": " "}, "again": {"acc": [0, 8, 8], "size": [], "range": [0.0, 255.99609375], "default": 16.0, "comment": "again for noise_profile", "hidden": 1, "auto": 0, "target_conf": ["raw_2dnr.noise_profile_bayer_lut", "raw_2dnr.inv_noise_profile_bayer_lut", "raw_2dnr.noise_profile_y_lut", "raw_2dnr.inv_noise_profile_y_lut"], "dependency": "common"}, "hdr_ratio": {"acc": [0, 9, 8], "size": [], "range": [0.0, 511.99609375], "default": 1.0, "comment": "hdr_ratio", "hidden": 1, "auto": 0, "target_conf": ["raw_2dnr.noise_profile_bayer_lut", "raw_2dnr.inv_noise_profile_bayer_lut", "raw_2dnr.noise_profile_y_lut", "raw_2dnr.inv_noise_profile_y_lut"], "dependency": "common"}, "lut_mode": {"acc": [0, 8], "size": [], "range": [0, 255], "default": 255, "comment": "lut_mode for noise_profile. 0: SDR 256, 1: HDR 512, 2: HDR 1024, 3: HDR 2048, 4: HDR 4096, 5: HDR 8192, 6: HDR 16384, 255: AUTO", "hidden": 0, "auto": 0, "target_conf": ["raw_2dnr.lut_affine_k"], "dependency": " "}, "lut_type": {"acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "lut_type for noise_profile, 0: Linear, 1: Log/Exponential", "hidden": 0, "auto": 0, "target_conf": ["raw_2dnr.lut_type"], "dependency": " "}, "noise_profile_factor": {"acc": [0, 3, 7], "size": [], "range": [0.0, 7.9921875], "default": 1.0, "comment": "noise_profile_factor for noise_profile", "hidden": 0, "auto": 1, "target_conf": ["raw_2dnr.noise_profile_bayer_lut", "raw_2dnr.inv_noise_profile_bayer_lut", "raw_2dnr.noise_profile_y_lut", "raw_2dnr.inv_noise_profile_y_lut"], "dependency": " "}, "read_noise_coeffs": {"acc": [1, 0, 31], "size": [4, 3], "range": [-1.0, 0.9999999995343387], "default": [[0.02, 0.02, 0.0], [0.02, 0.02, 0.0], [0.02, 0.02, 0.0], [0.02, 0.02, 0.0]], "comment": "read_noise_coeffs", "hidden": 1, "auto": 0, "target_conf": ["raw_2dnr.noise_profile_bayer_lut", "raw_2dnr.inv_noise_profile_bayer_lut", "raw_2dnr.noise_profile_y_lut", "raw_2dnr.inv_noise_profile_y_lut"], "dependency": "common"}, "shot_noise_coeffs": {"acc": [1, 0, 31], "size": [4, 2], "range": [-1.0, 0.9999999995343387], "default": [[0.5, 0.05], [0.5, 0.05], [0.5, 0.05], [0.5, 0.05]], "comment": "shot_noise_coeffs", "hidden": 1, "auto": 0, "target_conf": ["raw_2dnr.noise_profile_bayer_lut", "raw_2dnr.inv_noise_profile_bayer_lut", "raw_2dnr.noise_profile_y_lut", "raw_2dnr.inv_noise_profile_y_lut"], "dependency": "common"}}, "submodules": {"setup": {"params": ["offset_in", "offset_out"], "configs": ["raw_2dnr.enable", "raw_2dnr.mask_en", "raw_2dnr.mask_step"]}, "common": {"params": ["enable"], "configs": []}, "mask": {"params": ["mask_thre", "mask_limit"], "configs": []}, "fs": {"params": ["edge_preserve_ratio", "inter_g_strength"], "configs": []}, "hfnr": {"params": ["hf_nr_enable", "hf_nr_stren", "hf_nr_stren_rgb", "wiener_factor"], "configs": []}, "mfnr": {"params": ["mf_nr_enable", "mf_nr_stren", "mf_nr_stren_rgb", "nlm_factor", "inter_channel_strength"], "configs": []}, "np": {"params": ["again", "hdr_ratio", "lut_mode", "lut_type", "noise_profile_factor", "read_noise_coeffs", "shot_noise_coeffs"], "configs": []}}, "target_module": {"mc20l": {"raw_2dnr": {"id": 2200, "method": 0}}}, "structs": {}, "autos": {"1": {"ref_mode": ["gain/lux", "hdr_ratio"], "ref_group_num": [12, 4], "ref_interp_method": ["linear", "linear"]}}, "configs": {"raw_2dnr": {"enable": {"acc": [0, 1], "size": [], "description": "Module enable", "usage": "0: disable, 1: enable", "constraints": "", "type": "AX_U8", "partition": "-"}, "lut_type": {"acc": [0, 1], "size": [], "description": "Lut type for noise profile and noise reduction factor", "usage": "0: linear lut, 1: log2 lut", "constraints": "", "type": "AX_U8", "partition": "-"}, "lut_affine_k": {"acc": [0, 8, 4], "size": [], "description": "Lut affine_k for different white level", "usage": "256: 64.0, 4096: 4.0, 16384: 1.0", "constraints": "[1.0, 64.0]", "type": "AX_U16", "partition": "-"}, "mask_en": {"acc": [0, 1], "size": [], "description": "External mask enable", "usage": "0: disable, 1: enable", "constraints": "", "type": "AX_U8", "partition": "-"}, "mask_step": {"acc": [0, 1, 4], "size": [], "description": "External mask upscale step", "usage": "1.0: no upscale, 0.5: 2x upscale, 0.25: 4x upscale, 0.125: 8x upscale, 0.0625: 16x upscale.", "constraints": "This register is hard-coded to 0.25.", "type": "AX_U8", "partition": "-"}, "mask_coring_thre": {"acc": [0, 0, 8], "size": [], "description": "External mask coring threshold", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "mask_coring_slope": {"acc": [0, 8, 4], "size": [], "description": "External mask coring slope", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "mask_coring_limit": {"acc": [0, 0, 8], "size": [2], "description": "External mask coring limit", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "noise_profile_bayer_lut": {"acc": [0, 12, 4], "size": [4, 17], "description": "Bayer channel noise profile", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "inv_noise_profile_bayer_lut": {"acc": [0, 6, 12], "size": [4, 17], "description": "Bayer channel inverse noise profile", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "noise_profile_y_lut": {"acc": [0, 12, 4], "size": [17], "description": "Y Image noise profile", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "inv_noise_profile_y_lut": {"acc": [0, 6, 12], "size": [17], "description": "Y Image inverse noise profile", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "freqsep_edge_preserve": {"acc": [0, 0, 8], "size": [], "description": "Edge preserving strength for frequency separation", "usage": "0: for non edge preserving, bilateral filter behaves like a gaussian filter, 1: for full edge preserving, bilateral filter behaves like an all pass filter", "constraints": "", "type": "AX_U8", "partition": "-"}, "freqsep_inter_g_strength": {"acc": [0, 0, 8], "size": [], "description": "Inter-channel strength for frequency separation", "usage": "0: gb / gr will only use its own channel for frequency separation, 1: gb / gr will fully use each other for frequency separation", "constraints": "", "type": "AX_U8", "partition": "-"}, "nlm_thre_factor": {"acc": [0, 6, 6], "size": [2, 17], "description": "NLM threshold lut, controlled by pixel brightness", "usage": "nlm_thre_factor[0][x] for areas where Mask Image is 1, nlm_thre_factor[1][x] for areas where Mask Image is 0. for where Mask Image is 0.5, blend ratio will be the interpolation of nlm_thre_factor[0] and nlm_thre_factor[1]", "constraints": "", "type": "AX_U16", "partition": "-"}, "nlm_inv_thre_factor": {"acc": [0, 6, 6], "size": [2, 17], "description": "NLM inverse threshold, controlled by pixel brightness", "usage": "nlm_inv_thre_factor[0][x] for areas where Mask Image is 1, nlm_inv_thre_factor[1][x] for areas where Mask Image is 0. for where Mask Image is 0.5, blend ratio will be the interpolation of nlm_inv_thre_factor[0] and nlm_inv_thre_facto[1]", "constraints": "", "type": "AX_U16", "partition": "-"}, "nlm_inter_g_strength": {"acc": [0, 0, 8], "size": [], "description": "Inter-channel noise reduction strength", "usage": "0: non inter-channel noise reduction (gb / gr will only use its own channel), 1: full inter-channel noise reduction (gb / gr will fully use each other for noise reduction)", "constraints": "", "type": "AX_U8", "partition": "-"}, "wiener_factor": {"acc": [0, 12, 4], "size": [2, 17], "description": "Wiener filter factor lut (controlled by pixel brightness)", "usage": "0: no noise reduction in high frequency part. MAX : stronger noise reduction in high frequency part. factor[0][x] for areas where Mask Image is 1. factor[1][x] for areas where Mask Image is 0 (for where Mask Image is 0.5, factor will be the interpolation of factor[0]  and factor[1] )", "constraints": "", "type": "AX_U16", "partition": "-"}, "nlm_org_blend": {"acc": [0, 1, 7], "size": [2, 4], "description": "nlm original blend ratio for Bayer channel", "usage": "0: use no input image for blending (full noise reduction), 1: use full input image for blending (no noise reduction). org_blend[0][x] for areas where Mask Image is 1. org_blend[1][x] for areas where Mask Image is 0. (for where Mask Image is 0.5, blend ratio will be the interpolation of org_blend[0][x] and org_blend[1][x] )", "constraints": "[0.0, 1.0]", "type": "AX_U8", "partition": "-"}, "wiener_org_blend": {"acc": [0, 1, 7], "size": [2, 4], "description": "wiener original blend ratio for Bayer channel", "usage": "0: use no input image for blending (full noise reduction), 1: use full input image for blending (no noise reduction). org_blend[0][x] for areas where Mask Image is 1. org_blend[1][x] for areas where Mask Image is 0. (for where Mask Image is 0.5, blend ratio will be the interpolation of org_blend[0][x] and org_blend[1][x] )", "constraints": "[0.0, 1.0]", "type": "AX_U8", "partition": "-"}, "offset_in": {"acc": [0, 14, 6], "size": [], "description": "Input offset", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}, "offset_out": {"acc": [0, 14, 6], "size": [], "description": "Output offset", "usage": "", "constraints": "", "type": "AX_U32", "partition": "-"}}}}