h2. Conf list
h3. raw_2dnr
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | Module enable | 0: disable, 1: enable |
| lut_type | u1 | [\] | - |  | Lut type for noise profile and noise reduction factor | 0: linear lut, 1: log2 lut |
| lut_affine_k | u8.4 | [\] | - | [1.0, 64.0\] | Lut affine_k for different white level | 256: 64.0, 4096: 4.0, 16384: 1.0 |
| mask_en | u1 | [\] | - |  | External mask enable | 0: disable, 1: enable |
| mask_step | u1.4 | [\] | - | This register is hard-coded to 0.25. | External mask upscale step | 1.0: no upscale, 0.5: 2x upscale, 0.25: 4x upscale, 0.125: 8x upscale, 0.0625: 16x upscale. |
| mask_coring_thre | u0.8 | [\] | - |  | External mask coring threshold |  |
| mask_coring_slope | u8.4 | [\] | - |  | External mask coring slope |  |
| mask_coring_limit | u0.8 | [2\] | - |  | External mask coring limit |  |
| noise_profile_bayer_lut | u12.4 | [4, 17\] | - |  | Bayer channel noise profile |  |
| inv_noise_profile_bayer_lut | u6.12 | [4, 17\] | - |  | Bayer channel inverse noise profile |  |
| noise_profile_y_lut | u12.4 | [17\] | - |  | Y Image noise profile |  |
| inv_noise_profile_y_lut | u6.12 | [17\] | - |  | Y Image inverse noise profile |  |
| freqsep_edge_preserve | u0.8 | [\] | - |  | Edge preserving strength for frequency separation | 0: for non edge preserving, bilateral filter behaves like a gaussian filter, 1: for full edge preserving, bilateral filter behaves like an all pass filter |
| freqsep_inter_g_strength | u0.8 | [\] | - |  | Inter-channel strength for frequency separation | 0: gb / gr will only use its own channel for frequency separation, 1: gb / gr will fully use each other for frequency separation |
| nlm_thre_factor | u6.6 | [2, 17\] | - |  | NLM threshold lut, controlled by pixel brightness | nlm_thre_factor[0\][x\] for areas where Mask Image is 1, nlm_thre_factor[1\][x\] for areas where Mask Image is 0. for where Mask Image is 0.5, blend ratio will be the interpolation of nlm_thre_factor[0\] and nlm_thre_factor[1\] |
| nlm_inv_thre_factor | u6.6 | [2, 17\] | - |  | NLM inverse threshold, controlled by pixel brightness | nlm_inv_thre_factor[0\][x\] for areas where Mask Image is 1, nlm_inv_thre_factor[1\][x\] for areas where Mask Image is 0. for where Mask Image is 0.5, blend ratio will be the interpolation of nlm_inv_thre_factor[0\] and nlm_inv_thre_facto[1\] |
| nlm_inter_g_strength | u0.8 | [\] | - |  | Inter-channel noise reduction strength | 0: non inter-channel noise reduction (gb / gr will only use its own channel), 1: full inter-channel noise reduction (gb / gr will fully use each other for noise reduction) |
| wiener_factor | u12.4 | [2, 17\] | - |  | Wiener filter factor lut (controlled by pixel brightness) | 0: no noise reduction in high frequency part. MAX : stronger noise reduction in high frequency part. factor[0\][x\] for areas where Mask Image is 1. factor[1\][x\] for areas where Mask Image is 0 (for where Mask Image is 0.5, factor will be the interpolation of factor[0\]  and factor[1\] ) |
| nlm_org_blend | u1.7 | [2, 4\] | - | [0.0, 1.0\] | nlm original blend ratio for Bayer channel | 0: use no input image for blending (full noise reduction), 1: use full input image for blending (no noise reduction). org_blend[0\][x\] for areas where Mask Image is 1. org_blend[1\][x\] for areas where Mask Image is 0. (for where Mask Image is 0.5, blend ratio will be the interpolation of org_blend[0\][x\] and org_blend[1\][x\] ) |
| wiener_org_blend | u1.7 | [2, 4\] | - | [0.0, 1.0\] | wiener original blend ratio for Bayer channel | 0: use no input image for blending (full noise reduction), 1: use full input image for blending (no noise reduction). org_blend[0\][x\] for areas where Mask Image is 1. org_blend[1\][x\] for areas where Mask Image is 0. (for where Mask Image is 0.5, blend ratio will be the interpolation of org_blend[0\][x\] and org_blend[1\][x\] ) |
| offset_in | u14.6 | [\] | - |  | Input offset |  |
| offset_out | u14.6 | [\] | - |  | Output offset |  |

