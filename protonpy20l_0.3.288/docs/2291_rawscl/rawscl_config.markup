h2. Conf list
h3. raw_scl
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  |  |  |
| crop_enable | u1 | [\] | - |  | select roi function enable |  |
| roi_size | u16 | [2\] | - | >=128 | roi size | [height, width\] |
| roi_start | u16 | [2\] | - | >=0 | roi location | [y, x\] of top_left_point |
| norm_value | u0.6 | [2\] | - | norm_val[\*\] = 1 / ((scale_ratio[\*\] + 1) ) | sclae normalize value [0\]: h [1\]: w |  |
| scale_enable | u1 | [\] | - |  | scale_enable |  |
| scale_ratio | u6 | [2\] | - | >0; (roi_size / (scale_ratio + 1)) % 2 == 0 | scale ratio [0\]: h; [1\]: w |  |
| scale_mode | u1 | [\] | - | 0 or 1 | choose scale mode | 0: Average; 1: Decimation |
| output_acc_mode | u2 | [\] | - | 0 or 1 or 2 | choose ouput acc | 0: u8.4; 1: u8.2; 2: u8.0 |

