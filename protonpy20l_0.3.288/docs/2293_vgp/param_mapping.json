{"v0_format": {"api": "nV0Format", "display": "v0Format", "comments": "v0 format", "hint": "Accuracy: U6.0 Range: [0, 63]"}, "v0_sp_alpha_enable": {"api": "bSpecialAlphaEnable", "display": "specialAlphaEnable", "comments": "Special alpha channel enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "v0_global_alpha_enable": {"api": "bGlobalAlphaEnable", "display": "globalAlphaEnable", "comments": "v0 global alpha enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "v0_global_alpha": {"api": "nGlobalAlpha", "display": "globalAlpha", "comments": "v0's global alpha value. If global alpha is used or there is no alpha channel in input v0, this value will be the global alpha for v0. BITMAP's alpha channel will be generated by using color palette if global alpha is not enabled", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "v0_global_chroma": {"api": "nGlobalChroma", "display": "globalChroma", "comments": "v0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "quad_v0_enable": {"api": "bQuadEnable", "display": "quadEnable", "comments": "Quad enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "quad_v0_in_x_coords": {"api": "nInXCoords", "display": "inXCoords", "comments": "x coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed.", "hint": "Accuracy: S13.0 Range: [-8192, 8191]"}, "quad_v0_in_y_coords": {"api": "nInYCoords", "display": "inYCoords", "comments": "y coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed.", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "quad_v0_out_x_coords": {"api": "nOutXCoords", "display": "outXCoords", "comments": "x coordinates of intersection points between outer quad's edges, and image rectangle which is constrained by y_st and y_ed.", "hint": "Accuracy: S13.0 Range: [-8192, 8191]"}, "quad_v0_out_y_coords": {"api": "nOutYCoords", "display": "outYCoords", "comments": "y coordinates of intersection points between outer quad's edges, and image rectangle which is constrained by y_st and y_ed.", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "quad_v0_mode": {"api": "nQuadMode", "display": "quadMode", "comments": "Quad feature mode control bit. 0 - mosaic; 1 - fill", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "quad_v0_mosaic_step_size": {"api": "nMosaicStepSize", "display": "mosaicStepSize", "comments": "Mosaic step size", "hint": "Accuracy: U7.0 Range: [0, 127]"}, "quad_v0_fill_out_luma": {"api": "nFillOutLuma", "display": "fillOutLuma", "comments": "Outer quad luma", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "quad_v0_fill_out_chroma": {"api": "nFillOutChroma", "display": "fillOutChroma", "comments": "Outer quad chroma(with 128 bias)", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "quad_v0_out_alpha": {"api": "nOutAlpha", "display": "outAlpha", "comments": "Outer quad blending alpha", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "roi_luma_sum_tl_coords": {"api": "nRoiLumaSumTlCoords", "display": "roiLumaSumTlCoords", "comments": "top-left corner coordinates of 8 roi areas", "hint": "Accuracy: U12.0 Range: [0, 4095]"}, "roi_luma_sum_br_coords": {"api": "nRoiLumaSumBrCoords", "display": "roiLumaSumBrCoords", "comments": "bottom-right corner coordinates of 8 roi areas", "hint": "Accuracy: U12.0 Range: [0, 4095]"}, "g0_format": {"api": "nG0Format", "display": "g0Format", "comments": "g0 format", "hint": "Accuracy: U6.0 Range: [0, 63]"}, "g0_global_alpha_enable": {"api": "bGlobalAlphaEnable", "display": "globalAlphaEnable", "comments": "g0 global alpha enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "g0_global_alpha": {"api": "nGlobalAlpha", "display": "globalAlpha", "comments": "g0's global alpha value. If global alpha is used or there is no alpha channel in input v0, this value will be the global alpha for v0. BITMAP's alpha channel will be generated by using color palette if global alpha is not enabled", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "g0_global_chroma": {"api": "nGlobalChroma", "display": "globalChroma", "comments": "g0's global alpha value. If global alpha is used or there is no alpha channel in input g0, this value will be the global alpha for g0. BITMAP's alpha channel will be generated by using color palette if global alpha is not enabled", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "blending_enable": {"api": "bBlendingEnable", "display": "blendingEnable", "comments": "Blending unit enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "blending_coord": {"api": "nCoord", "display": "coord", "comments": "Blending unit enable control bit", "hint": "Accuracy: U16.0 Range: [0, 65535]"}, "blending_alpha0_out_mode": {"api": "nAlpha0OutMode", "display": "alpha0OutMode", "comments": "0 - v0 alpha not changed; 1 - v0 alpha inversed; Result is intermediate.", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "blending_alpha1_out_mode": {"api": "nAlpha1OutMode", "display": "alpha1OutMode", "comments": "0 - gx alpha not changed; 1 - gx alpha inversed; Result is intermediate.", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "blending_alpha_out_weight": {"api": "nAlphaOutWeight", "display": "alphaOutWeight", "comments": "v0_alpha * (1 - weight) + gx_alpha * weight", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "blending_color_key_enable": {"api": "bColorKeyEnable", "display": "colorKeyEnable", "comments": "blending color key enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "blending_alpha_select": {"api": "nAlphaSelect", "display": "alphaSelect", "comments": "Select alpha channel to be used in blending", "hint": "Accuracy: U2.0 Range: [0, 3]"}, "blending_color_key_val": {"api": "nColorKeyVal", "display": "colorKeyVal", "comments": "Select alpha channel to be used in blending", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "blending_color_key_inv": {"api": "nColorKeyInv", "display": "colorKeyInv", "comments": "If enabled, pix whose color is outside of the color-key range will do the color-key process, rather than the one within the color range", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "ddr_write_format": {"api": "nDdrWriteFormat", "display": "ddrWriteFormat", "comments": "DDR dump format", "hint": "Accuracy: U6.0 Range: [0, 63]"}, "dither_enable": {"api": "bD<PERSON>er<PERSON>nable", "display": "ditherEnable", "comments": "dither enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}}