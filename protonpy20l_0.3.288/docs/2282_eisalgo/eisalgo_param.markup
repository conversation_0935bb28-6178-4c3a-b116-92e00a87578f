h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| img_height |  | u16 | AX_U16 | [\] |  [0, 65535\] | [None, None\] | 65535 | None | hidden |  | height of img which send to gdc | user |
| img_width |  | u16 | AX_U16 | [\] |  [0, 65535\] | [None, None\] | 65535 | None | hidden |  | width of img which send to gdc | user |
| offset_vh |  | s7 | AX_S8 | [256\] |  [-128, 127\] | [None, None\] | np.zeros(256).tolist() | None | hidden |  | Vertical offset | user |
| proj_vh |  | u16 | AX_U16 | [256\] |  [0, 65535\] | [None, None\] | np.zeros(256).tolist() | None | hidden |  | Vertical Projection | user |
| sad_vh |  | u32 | AX_U32 | [256\] |  [0, 65535\] | [None, None\] | np.zeros(256).tolist() | None | hidden |  | Vertical Projection | user |
| proj_vh_num |  | u16 | AX_U16 | [2\] |  [0, 65535\] | [None, None\] | [65535, 65535\] | None | hidden |  | Vertical Projection NUM | user |
| proj_vh_size |  | u16 | AX_U16 | [2\] |  [0, 65535\] | [None, None\] | [65535, 65535\] | None | hidden |  | Vertical Projection Size | user |
| sad_vh_num |  | u16 | AX_U16 | [2\] |  [0, 65535\] | [None, None\] | [65535, 65535\] | None | hidden |  | sad_v and offset_v valid num | user |
| sad_vh_size |  | u16 | AX_U16 | [2\] |  [0, 65535\] | [None, None\] | [65535, 65535\] | None | hidden |  | sad_v size | user |
| offset_scale_ratio |  | u8 | AX_U8 | [\] |  [0, 2\] | [None, None\] | 0 | None | hidden |  | offset scale = (1 << scale ratio) | user |
| rotation |  | u8 | AX_U8 | [\] |  [0, 3\] | [None, None\] | 0 | None | hidden |  | 0: 0, 1: 90, 2: 180, 3: 270 | user |
| mirror |  | u8 | AX_U8 | [\] |  [0, 255\] | [None, None\] | 0 | None | hidden |  | 0: No, 1: Yes | user |
| flip |  | u8 | AX_U8 | [\] |  [0, 255\] | [None, None\] | 0 | None | hidden |  | 0: No, 1: Yes | user |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open |  | 0: DIS Disable  1: DIS Enable | user | None | None | None |
| sw_calc_enable | sw calc enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open |  | 0: use HW offset 1: use projection calculate offset | user | None | None | None |
| delay_frame_num | delay frame num | u8 | AX_U8 | [\] | [0, 15\] | [None, None\] | 0 | None | open |  | delay num, the more, the smoother. defalut 0. Accuracy: U8 Range: [0, 15\] | user | None | None | None |
| history_frame_num | history frame num | u8 | AX_U8 | [\] | [1, 16\] | [None, None\] | 0 | None | open |  | history frame, the more, the smoother | user | None | None | None |
| crop_ratio | crop ratio | u8 | AX_U8 | [\] | [127, 255\] | [None, None\] | 205 | None | open |  | crop_ratio for warped image | user | None | None | None |
| sad_threshold | sad threshold | u32 | AX_U32 | [\] | [0, 65535\] | [None, None\] | 65535 | None | open |  | threshord for valid offset | user | None | None | None |
| frame_pos_weights | frame pos weights | u8 | AX_U8 | [16\] | [0, 255\] | [None, None\] | [0, 32, 64, 96, 128, 160, 192, 224, 255, 255, 255, 255, 255, 255, 255, 255\] | None | open |  | the weight of history frame position for weighted sum | user | None | None | None |
| scale_ratio | scale ratio | u8 | AX_U8 | [\] | [0, 16\] | [None, None\] | 0 | None | open |  | quant scale = (1 << scale ratio) | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |