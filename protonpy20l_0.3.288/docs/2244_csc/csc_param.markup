h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| csc_mode |  | u2 | AX_U8 | [\] |  [0, 2\] | [None, None\] | 2 | None | hidden | 'csc.input_type', 'csc.output_type' | csc mode, 0:YUV422->YUV420, 1:RGB->YUV422, 2:RGB->YUV420 | common |
| csc_offset_in |  | s8.2 | AX_S16 | [\] |  [-1024, 1023\] | [-256.0, 255.75\] | 64 | 16.0 | hidden | 'csc.offset_in' | csc input offset, need not tune | common |
| csc_offset_out |  | s8.2 | AX_S16 | [\] |  [-1024, 1023\] | [-256.0, 255.75\] | 64 | 16.0 | hidden | 'csc.offset_out' | csc out offset, need not tune | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| csc_conv_mode | csc_conv_mode | u4 | AX_U8 | [\] | [0, 3\] | [None, None\] | 1 | None | open | 'csc.matrix' | csc conversion mode, 0:BT601, 1:BT709, 2:BT2020, 3:user define |   | None | None | None |
| csc_matrix | csc_matrix | s2.10 | AX_S16 | [3, 3\] | [-4096, 4095\] | [-4.0, 3.9990234375\] | [[306, 601, 117\], [-173, -339, 512\], [512, -429, -83\]\] | [[0.298828125, 0.5869140625, 0.1142578125\], [-0.1689453125, -0.3310546875, 0.5\], [0.5, -0.4189453125, -0.0810546875\]\] | open | 'csc.matrix' | csc conversion matrix, valid only for csc_conv_mode = 3 |   | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |