# controls

![](../assets/imgs/controls.jpg)

大多数模块的auto control参数就是ref_mode和auto_mode两者，但是也存在一些模块，需要参考其他的control参数。脚本支持自定义该类控制参数（**如果不需要特殊指定control，这个域可以缺省**）

在controls中可添加对应的ref group的控制参数。

![](../assets/imgs/controls_refgroup.png)

属性|说明|是否可缺省
---|---|---
display	UI|显示名称|可
acc|参数精度，标识方法同xxx_m0_base.json|不可
type|参数类型|可
size|参数尺寸，标识方法同xxx_m0_base.json|不可
range|参数范围，可支持整形或浮点型|可
default|参数默认值，可支持整形或浮点型|可
comment|参数说明|可
hidden|参数是否为open参数，若缺省则为open|可

## 生成结果

![](../assets/imgs/controls_result.jpg)

生成结果会反映在WIKI文档上

**目前control参数暂不支持复杂的结构体**

