{"partition_configs": [], "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "GAM is 0x2243"}}, "params": {"enable": {"display": "Enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "0: linear lut, 1: set lut in params", "hidden": 0, "auto": 0, "target_conf": ["gam.lut"], "dependency": " "}, "offset_in": {"display": "Offset In", "acc": [0, 8, 4], "size": [], "default": 16.0, "comment": "only support set to 16.0", "hidden": 1, "auto": 0, "target_conf": ["gam.offset_in"], "dependency": "common"}, "offset_out": {"display": "Offset Out", "acc": [0, 8, 2], "size": [], "default": 16.0, "comment": "only support set to 16.0", "hidden": 1, "auto": 0, "target_conf": ["gam.offset_out"], "dependency": "common"}, "mode": {"display": "Mode", "acc": [0, 8], "size": [], "range": [0, 6], "default": 3, "comment": "0: linear, 1: BT709, 2: srgb, 3: AX gamma0(default gamma) 4: AX gamma1, 5: AX gamma2, 6: customer.", "hidden": 0, "auto": 0, "target_conf": ["gam.lut"], "dependency": " ", "enum_field": {"0": "LINEAR", "1": "BT709", "2": "SRGB", "3": "AX_GAM0", "4": "AX_GAM1", "5": "AX_GAM2", "6": "MODE_CUSTOMER"}}, "custom_lut": {"display": "Custom Lut", "acc": [0, 8, 4], "size": [129], "default": [0.0, 6.5625, 13.1875, 19.8125, 26.4375, 33.0625, 39.6875, 46.3125, 52.9375, 59.25, 65.0625, 70.4375, 75.4375, 80.0625, 84.5, 88.6875, 92.625, 96.375, 100.0, 103.4375, 106.75, 109.9375, 113.0, 116.0, 118.875, 121.625, 124.3125, 126.9375, 129.4375, 131.9375, 134.3125, 136.625, 138.9375, 141.1875, 143.3125, 145.4375, 147.5625, 149.5625, 151.5625, 153.5, 155.4375, 157.3125, 159.125, 160.9375, 162.6875, 164.4375, 166.1875, 167.875, 169.5, 171.125, 172.75, 174.3125, 175.875, 177.4375, 178.9375, 180.4375, 181.875, 183.3125, 184.75, 186.1875, 187.5625, 188.9375, 190.3125, 191.625, 192.9375, 194.25, 195.5625, 196.8125, 198.0625, 199.3125, 200.5625, 201.75, 203.0, 204.1875, 205.375, 206.5, 207.6875, 208.8125, 209.9375, 211.0625, 212.1875, 213.25, 214.375, 215.4375, 216.5, 217.5625, 218.625, 219.6875, 220.6875, 221.6875, 222.75, 223.75, 224.75, 225.6875, 226.6875, 227.6875, 228.625, 229.5625, 230.5, 231.4375, 232.375, 233.3125, 234.25, 235.125, 236.0625, 236.9375, 237.8125, 238.75, 239.625, 240.5, 241.3125, 242.1875, 243.0625, 243.875, 244.75, 245.5625, 246.375, 247.25, 248.0625, 248.875, 249.6875, 250.5, 251.25, 252.0625, 252.875, 253.625, 254.4375, 255.1875, 255.9375], "comment": "gamma curve. 0 <= lut[n+1]-lut[n] < 32.0. default is GAM_AX0", "hidden": 0, "auto": 1, "target_conf": ["gam.lut"], "range": [0.0, 255.9375], "dependency": " "}, "dither_seed_enable": {"acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "0: use stat dither_seed, 1: use conf dither_seed. set 1 for the 1st frame, then set 0 from the 2nd frame onwards.", "hidden": 1, "auto": 0, "target_conf": ["gam.dither_seed_enable"], "display": "dither seed enable", "dependency": "common"}}, "submodules": {"setup": {"params": ["offset_in", "offset_out"], "configs": ["gam.enable", "gam.range_adjust_enable", "gam.dither_enable", "gam.dither_pmask", "gam.mode"]}, "lut": {"params": ["enable", "mode", "custom_lut"], "configs": []}, "dither": {"params": ["dither_seed_enable"], "configs": ["gam.dither_seed"]}}, "target_module": {"mc20l": {"gam": {"id": 4000, "method": 0}}}, "structs": {}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "configs": {"gam": {"enable": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "mode": {"acc": [0, 1], "size": [], "description": "Lut Mode for Gam. 0: linear(gblk_num = 1), 1: log(gblk_num = 8)", "usage": "set 0 as default", "constraints": "", "type": "AX_U8", "partition": "-"}, "range_adjust_enable": {"acc": [0, 1], "size": [], "description": "map input range from 240 to 256.", "usage": "set 1 as default", "constraints": "", "type": "AX_U8", "partition": "-"}, "dither_enable": {"acc": [0, 1], "size": [], "description": "0: disable, 1: enable", "usage": "set 1 as default", "constraints": "", "type": "AX_U8", "partition": "-"}, "dither_seed_enable": {"acc": [0, 1], "size": [], "description": "0: use stat dither_seed, 1: use conf dither_seed", "usage": "set 1 for the 1st frame, then set 0 from the 2nd frame onwards", "constraints": "", "type": "AX_U8", "partition": "-"}, "dither_seed": {"acc": [0, 16], "size": [2], "description": "value of dither seed", "usage": "set non 0, when dither enable", "constraints": "[0,2^16), [0,2^15)", "type": "AX_U16", "partition": "-"}, "dither_pmask": {"acc": [0, 16], "size": [2], "description": "dither pmask", "usage": "should set properly, pMask are picked from https://git-core.megvii-inc.com/chenjiahui/LFSR/tree/master/pMask 16.txt,15.txt", "constraints": "[0,2^16), [0,2^15)", "type": "AX_U16", "partition": "-"}, "lut": {"acc": [0, 8, 4], "size": [129], "description": "gamma lut", "usage": "", "constraints": "0 <= lut[n+1]-lut[n] < 32.0, lut[n]<lut[n+1] for n=0,1,..,128", "type": "AX_U16", "partition": "-"}, "offset_in": {"acc": [0, 8, 4], "size": [], "description": "offset for input", "usage": "Set 16", "constraints": "", "type": "AX_U16", "partition": "-"}, "offset_out": {"acc": [0, 8, 2], "size": [], "description": "offset for out", "usage": "Set 16", "constraints": "", "type": "AX_U16", "partition": "-"}}}}