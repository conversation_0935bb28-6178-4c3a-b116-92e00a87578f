# 调用自动生成脚本之后

## 1.完成代码
![code_tbd](../assets/imgs/code_tbd.png)

`aaa_impl.c`文件，是algo logic的核心，其中包含对固定configs的赋值，以及从params映射到configs的算法逻辑。需要各模块owner自行完成。

`ctest/aaa/test_aaa.cpp`，是algo logic的c函数的测试文件，**主要目的是通过对aaa模块的api的调用，验证aaa模块从params到configs的映射关系是否符合预期**（对返回的configs要进行数值的check，以确保结果符合预期），模版会生成部分代码，具体测试的内容，需要各模块owner自行完成。

`pytests/test_aaa.py`，是algo logic的python wrapper函数的测试文件，**主要目的是通过对aaa模块的api的python wrapper调用，验证aaa模块从params到configs的映射关系是否符合预期，python wrapper是否工作正常（对返回的configs要进行数值的check，以确保结果符合预期）**，具体测试的内容，需要各模块owner自行完成。

`samples/aaa/__main__.py`，`samples/aaa/logic.py`, `samples/aaa/data_utils.py`是algo logic的主观测试代码，**主要目的是通过对aaa模块的api的调用，验证aaa模块在某套params的设置下，其对图像效果的影响是否符合预期**（主观结果需要在lovelive上确认），代码需要各模块owner自行完成。

## 2.完成wiki文档
在wiki页面的右上角，点击`编辑`按钮
![wiki_doc1](../assets/imgs/wiki_doc1.png)

点击`+`，在下拉菜单中选择`Wiki标记`
![wiki_doc2](../assets/imgs/wiki_doc2.png)

在弹出的对话框中，粘贴自动生成的`*.markup`文件中的内容，点击`确定`
![wiki_doc3](../assets/imgs/wiki_doc3.png)