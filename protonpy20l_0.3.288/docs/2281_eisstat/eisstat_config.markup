h2. Conf list
h3. eis_pre
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  |  |  |
| scale_ratio | u2 | [\] | - |  |  |  |
| lut_enable | u1 | [\] | - |  |  |  |
| lut | u8.2 | [33\] | - |  |  |  |
| proj_v_region_len | u14 | [2\] | - |  |  |  |
| proj_v_start_pos | u14 | [2\] | support |  |  |  |
| proj_v_start_index | u5 | [\] | support |  |  |  |
| proj_v_roi | u14 | [4\] | support |  |  |  |
| proj_v_shiftbit | u3 | [\] | - |  |  |  |
| proj_h_region_len | u14 | [2\] | - |  |  |  |
| proj_h_start_pos | u14 | [2\] | support |  |  |  |
| proj_h_start_index | u5 | [\] | support |  |  |  |
| proj_h_roi | u14 | [4\] | support |  |  |  |
| proj_h_shiftbit | u3 | [\] | - |  |  |  |
| offset_in | u8.2 | [\] | - |  |  |  |

h3. eis_post
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| offset_calc_enable | u1 | [\] | support |  |  |  |
| scale_ratio | u2 | [\] | - |  |  |  |
| proj_v_region_num | u5 | [2\] | - |  |  |  |
| proj_v_region_len | u14 | [2\] | - |  |  |  |
| proj_h_region_num | u5 | [2\] | - |  |  |  |
| proj_h_region_len | u14 | [2\] | - |  |  |  |
| search_range | u9 | [2\] | - |  |  |  |

