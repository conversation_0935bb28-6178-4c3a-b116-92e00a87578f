{"ext_mask_mode": {"api": "nExtMaskMode", "display": "extMask.mode", "comments": "ext mask mode, 0:ai, 1:hdr", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "ext_mask_thre": {"api": "nExtMaskThr", "display": "extMask.thr", "comments": "The starting point for remapping ext mask, below which the probability will be mapped to 0", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "ext_mask_slope": {"api": "nExtMaskGain", "display": "extMask.gain", "comments": "The slope of ext mask remapping, the larger the value, the easier it is to map the mask to 1.0", "hint": "Accuracy: U8.8 Range: [0, 65535]"}, "ext_mask_ratio": {"api": "nExtMaskRatio", "display": "extMask.ratio", "comments": "Only effective in AI mode. The reference level of AI mask. The larger the size, the more likely it is to use an AI mask as the motion mask", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "motion_small_sel_ratio": {"api": "nMotSmallSel", "display": "detMotion.smallSel", "comments": "Applied to the feature of motion judgement, the larger the value, the larger the detection window used for motion judgement, which improves the noise resistance and smoothness of motion detection while losing the ability to detect texture details.", "hint": "Accuracy: U9.0 Range: [0, 256]"}, "motion_large_sel_ratio": {"api": "nMotLargeSel", "display": "detMotion.largeSel", "comments": "The greater the values of features applied to time-domain fusion coefficient filtering, the larger the detection window used for motion judgement. This improves the noise resistance and smoothness of motion detection while losing texture detail detection ability.", "hint": "Accuracy: U9.0 Range: [0, 256]"}, "motion_feature_gauss_str": {"api": "nMotSmoothStr", "display": "detMotion.smoothStr", "comments": "The pre smoothing degree of the motion judgement features, the larger the value, the smoother the motion judgement features.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "motion_sad_sd_th": {"api": "nMotSensThr", "display": "detMotion.sensThr", "comments": "Representing the difference between two types of motion judgement features, used to map the fusion ratio of texture sensitive motion judgement features. The larger the value, the less texture sensitive motion judgement features are used, and the probability of misjudgment caused by noise decreases. However, it may lead to some areas being misjudged as stationary.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "motion_sad_weight_slope": {"api": "nMotSensGain", "display": "detMotion.sensGain", "comments": "Based on the fusion mapping of texture sensitive motion judgement features. The larger the value, the faster the texture sensitive motion judgement features are used, and the probability of misjudgment caused by noise increases, resulting in more accurate motion judgement.", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "motion_sad_weight_th": {"api": "nMotSensRatio", "display": "detMotion.sensRatio", "sublabel": ["low", "high"], "comments": "The upper and lower limits of the fusion ratio of texture sensitive motion judgement features. The larger the value, the more texture sensitive motion judgement features are used, and the probability of misjudgment caused by noise increases, resulting in more accurate motion judgement", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "motion_highlight_diff_th": {"api": "nMotShpDiff", "display": "detMotion.shpDiff", "comments": "For highlight areas, the frame difference judgment threshold for motion sharpening is used. If the frame difference is higher than this threshold, sharpening will occur, and the larger the value, the less effective motion sharpening will be.", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "motion_highlight_luma_th": {"api": "nMotShpLumaThr", "display": "detMotion.shpLumaThr", "comments": "For highlight areas, the brightness judgment threshold for motion sharpening is used. Only when the brightness is higher than this threshold will it sharpen. The larger the value, the less effective the motion sharpening will be.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "motion_highlight_luma_slope": {"api": "nMotShpLumaGain", "display": "detMotion.shpLumaGain", "comments": "For the highlight area, the brightness judgment gain using motion sharpening is used. The larger the value, the faster the transition from motion sharpening in the dark area to brightness.", "hint": "Accuracy: U2.8 Range: [0, 1023]"}, "motion_highlight_var_th": {"api": "nMotShpVarThr", "display": "detMotion.shpVarThr", "comments": "For highlight areas, the variance judgment threshold for motion sharpening is used. Only when the variance is higher than this threshold will the sharpening be effective. The larger the value, the less effective the motion sharpening will be.", "hint": "Accuracy: U7.0 Range: [0, 127]"}, "motion_highlight_var_slope": {"api": "nMotShpVarGain", "display": "detMotion.shpVarGain", "comments": "For the highlight area, the variance judgment gain of motion sharpening is used. The larger the value, the faster the transition from small variance to large variance motion sharpening", "hint": "Accuracy: U2.7 Range: [0, 511]"}, "motion_sharpen_str": {"api": "nMotShpStr", "display": "detMotion.shpStr", "comments": "For highlight areas, motion sharpening intensity is used. The higher the value, the greater the intensity, making it easier to identify as motion.", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "motion_y_red_correct_str": {"api": "nMotYRedCorStr", "display": "detMotion.yRedCorStr", "comments": "The intensity of Y channel motion correction in the red area, the larger the value, the easier it is to determine as stationary. It does not take effect when the nMotSmallSel is at its maximum.", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "motion_uv_red_correct_str": {"api": "nMotCRedCorStr", "display": "detMotion.cRedCorStr", "comments": "The intensity of UV channel motion correction in the red area, the larger the value, the easier it is to determine as stationary. It does not take effect when the nMotSmallSel is at its maximum.", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "motion_y_blue_correct_str": {"api": "nMotYBlueCorStr", "display": "detMotion.yBlueCorStr", "comments": "The intensity of Y channel motion correction in the blue area, the larger the value, the easier it is to determine as stationary. It does not take effect when the nMdSmallSel is at its maximum", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "motion_uv_blue_correct_str": {"api": "nMotCBlueCorStr", "display": "detMotion.cBlueCorStr", "comments": "The intensity of UV channel motion correction in the blue area, the larger the value, the easier it is to determine as stationary. It does not take effect when the nMdSmallSel is at its maximum.", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "motion_dec_y_th": {"api": "nMotYDecThr", "display": "detMotion.yDecThr", "comments": "Y motion judgement threshold, the more the whole frame tends to be static.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "motion_dec_y_slope": {"api": "nMotYDecGain", "display": "detMotion.yDecGain", "comments": "Y determines the transition speed based on movement. The higher the value, the more inclined the whole frame is towards motion.", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "motion_m2s_dec_y_th": {"api": "nMotYM2sDecThr", "display": "detMotion.yM2sDecThr", "comments": "From dynamic to static region Y, the threshold for motion judgement. The higher the value, the more the whole frame tends to be static.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "motion_m2s_dec_y_slope": {"api": "nMotYM2sDecGain", "display": "detMotion.yM2sDecGain", "comments": "From dynamic to static zone Y, determine the transition speed. The higher the value, the more inclined the whole frame is towards motion.", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "motion_hdr_adjust_y_th": {"api": "nMotYHdrAdjThr", "display": "detMotion.yHdrAdjThr", "comments": "When applied to Y, the larger the value, the easier it is to correct the short frame area to be still.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "motion_dec_uv_th": {"api": "nMotCDecThr", "display": "detMotion.cDecThr", "comments": "UV motion judgement threshold. The higher the value, the more the whole frame tends to be static.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "motion_dec_uv_slope": {"api": "nMotCDecGain", "display": "detMotion.cDecGain", "comments": "UV motion judgement of transition speed. The higher the value, the more inclined the whole frame is towards motion.", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "motion_adj_uv_th": {"api": "nMotCAdjThr", "display": "detMotion.cAdjThr", "comments": "The UV motion judgement correction threshold will make the UV more inclined to use the current frame, based on normal motion judgement. The higher the value, the more the overall UV tends to be static.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "motion_adj_uv_slope": {"api": "nMotCAdjGain", "display": "detMotion.cAdjGain", "comments": "UV motion judgement correction excessive speed will make UV more inclined to use the current frame. The higher the value, the more the overall UV tends to move.", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "motion_hdr_adjust_uv_th": {"api": "nMotCHdrAdjThr", "display": "detMotion.cHdrAdjThr", "comments": "When applied to UV, the larger the value, the easier it is to correct the short frame area to be still. ", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "motion_noise_y_lut": {"api": "nMotYNoiseLut", "display": "detMotion.yNoiseLut", "comments": "Noise correction motion judgement Y channel feature lookup table, with brightness on the x-axis and subtraction method. The larger the value, the stronger the correction, and the easier it is to mistake real motion for stillness.", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "motion_noise_uv_lut": {"api": "nMotCNoiseLut", "display": "detMotion.cNoiseLut", "comments": "Noise correction motion judgement UV channel feature lookup table, with brightness on the x-axis and subtraction method. The larger the value, the stronger the correction, and the easier it is to mistake real motion for stillness.", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "motion_hvs_y_lut": {"api": "nMotYHvsLut", "display": "detMotion.yHvsLut", "comments": "Noise correction motion judgement Y channel feature lookup table, with brightness on the x-axis and multiplication method. The smaller the value, the stronger the correction, and the easier it is to mistake real motion for stillness.", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "motion_hvs_uv_lut": {"api": "nMotCHvsLut", "display": "detMotion.cHvsLut", "comments": "Noise correction motion judgement UV channel feature lookup table, with brightness on the x-axis and multiplication method. The smaller the value, the stronger the correction, and the easier it is to mistake real motion for stillness.", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "tweight_cur_w": {"api": "nTWtCurW", "display": "tnr.curW", "comments": "The fusion ratio of current motion judgement and historical motion information, the larger the value, the smaller the historical motion information reference, the faster the convergence speed of dynamic and static, and the higher the probability of misjudging as static.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "tweight_pre_limit": {"api": "nTWtPreLimit", "display": "tnr.preLimit", "comments": "The reference threshold for historical motion information, significant motion information greater than this value will be referenced and integrated into the current motion judgment. The larger the value, the smaller the historical motion information reference, and the faster the convergence speed of motion and stillness, increasing the probability of misjudging as stationary.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "tweight_max_ratio": {"api": "nTWtMaxStr", "display": "tnr.maxStr", "comments": "The maximum filtering strength of the time-domain fusion coefficient is greater, and the larger the value, the greater the filtering strength. The larger the proportion of using the current frame, the higher the probability of raindrop noise appearing.", "hint": "Accuracy: U10.0 Range: [0, 512]"}, "tweight_smooth_ratio": {"api": "nTWtSmoothStr", "display": "tnr.smoothStr", "comments": "The average filtering strength of the time-domain fusion coefficient is higher, and the larger the value, the greater the average filtering strength. The proportion of using the current frame is smaller, and the probability of raindrop noise appearing is reduced. ", "hint": "Accuracy: U10.0 Range: [0, 512]"}, "tweight_sf_sd_y_th": {"api": "nTWtSfThr0", "display": "tnr.sfThr0", "comments": "Guiding the selection of time-domain fusion coefficients based on regional motion judgement features, the larger the value, the more the global result is biased towards smooth filtering", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "tweight_sf_sd_y_slope": {"api": "nTWtSfGain0", "display": "tnr.sfGain0", "comments": "Guiding the selection of time-domain fusion coefficients based on regional motion judgement features, the larger the value, the steeper the transition", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "tweight_sf_sad_y_th": {"api": "nTWtSfThr1", "display": "tnr.sfThr1", "comments": "By using detail sensitive motion judgement features to guide the selection of time-domain fusion coefficients, the larger the value, the more the global result is biased towards smooth filtering", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "tweight_sf_sad_y_slope": {"api": "nTWtSfGain1", "display": "tnr.sfGain1", "comments": "Guiding the selection of time-domain fusion coefficients based on detail sensitive motion judgement features, the larger the value, the steeper the transition", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "tweight_sf_sign_y_th": {"api": "nTWtSfThr2", "display": "tnr.sfThr2", "comments": "Guiding the selection of time-domain fusion coefficients based on pixel relative relationships, the larger the value, the more the global result is biased towards smooth filtering", "hint": "Accuracy: U7.0 Range: [0, 81]"}, "tweight_sf_sign_y_slope": {"api": "nTWtSfGain2", "display": "tnr.sfGain2", "comments": "Guiding the selection of time-domain fusion coefficients through pixel relative relationships, the larger the value, the steeper the transition", "hint": "Accuracy: U1.7 Range: [0, 255]"}, "tweight_str": {"api": "nTWtStr", "display": "tnr.str", "comments": "The stronger the time-domain filtering strength, the more biased it is towards using the current frame result, and the greater the Y noise in the stationary region.", "hint": "Accuracy: U0.7 Range: [0, 127]"}, "ratio_uv_adjust_th": {"api": "nRtCAdjThr", "display": "tnr.cAdjThr", "comments": "Based on the modified UV motion judgement, the threshold for mapping the UV correction temporal fusion coefficient is determined. The smaller the value, the more inclined it is to use the current frame for global UV", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "ratio_uv_adjust_slope": {"api": "nRtCAdjGain", "display": "tnr.c<PERSON>dj<PERSON><PERSON>", "comments": "According to the modified UV motion judgement, the transition speed of the mapping UV correction temporal fusion coefficient is determined. The larger the value, the more inclined it is to use the current frame for the global UV", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "ratio_uv_adjust": {"api": "nRtCAdj", "display": "tnr.cAdj", "comments": "According to the modified UV motion judgement, the correction strength of the mapping UV correction temporal fusion coefficient is determined. The larger the value, the more inclined it is to use the current frame for the global UV", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "ratio_uv_limit": {"api": "nRtCLimit", "display": "tnr.cLimit", "comments": "The stronger the UV time-domain denoising intensity, the more the current frame is used, and the greater the UV noise in the stationary area.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "m2s_motion_cur": {"api": "nM2sCur", "display": "tnr.m2sCur", "comments": "In the determination of moving to stationary areas, the current frame needs to be stationary and the historical frames need to be moving. This corresponds to the threshold for the current frame being stationary, and the larger the value, the more likely it is to be ineffective.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "m2s_tweight_ref": {"api": "nM2sRef", "display": "tnr.m2sRef", "comments": "In the determination of moving to stationary areas, the current frame needs to be stationary, and the historical frames need to be motion. Here, the corresponding historical frames are the threshold for motion, and the smaller the value, the more likely it is not to take effect.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "m2s_var_th": {"api": "nM2sVarThr", "display": "tnr.m2sVarThr", "comments": "The variance threshold of the texture part in the transition area from motion to stillness is located, and the larger the value, the fewer textures are located", "hint": "Accuracy: U7.0 Range: [0, 127]"}, "m2s_var_slope": {"api": "nM2sVarGain", "display": "tnr.m2sVarGain", "sublabel": ["lf", "hf"], "comments": "Locate the texture overlap speed of the texture part in the transition zone from motion to stillness, with smaller values indicating less overlap, [0]: lf, [1]: hf", "hint": "Accuracy: U1.11 Range: [0, 4095]"}, "m2s_blend_ratio": {"api": "nM2sBlendRatio", "display": "tnr.m2sBlendRatio", "sublabel": ["lf", "hf"], "comments": "Locate the texture overlap ratio of the texture part in the transition area from motion to stillness, with smaller values indicating less overlap, [0]: lf, [1]: hf", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "tweight_static_lut": {"api": "nTWtStaticLut", "display": "tnr.staticLut", "comments": "Time domain fusion speed curve, the horizontal axis represents the degree to which the history has been stationary, the smaller the value, the longer the stationary time, the vertical axis represents the current mapped time domain fusion coefficient, and the smaller the value, the more reference frames are used.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_gau_y_ref_weight_th": {"api": "nSfYLfRefWtThr", "display": "ynr.lf.refWtThr", "comments": "When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_gau_y_ref_weight_slope": {"api": "nSfYLfRefWtGain", "display": "ynr.lf.refWtGain", "comments": "When providing the next reference frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "sf_gau_y_ref_ratio_limit": {"api": "nSfYLfRefRatio", "display": "ynr.lf.refRatio", "comments": "When providing the next reference frame, the larger the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally. The larger the value, the lower the clarity of the still area and the smaller the noise.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_gau_y_cur_weight_th": {"api": "nSfYLfCurWtThr", "display": "ynr.lf.curWtThr", "comments": "When provided to the output frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_gau_y_cur_weight_slope": {"api": "nSfYLfCurWtGain", "display": "ynr.lf.cur<PERSON><PERSON><PERSON>ain", "comments": "When providing the output frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "sf_gau_y_cur_ratio_limit": {"api": "nSfYLfCurRatio", "display": "ynr.lf.curRatio", "comments": "When providing the output frame, the larger the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally. The larger the value, the lower the clarity of the still area and the smaller the noise.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_gau_y_edge_th": {"api": "nSfYLfEdgeThr", "display": "ynr.lf.edgeThr", "comments": "The threshold for low-frequency edge judgment, the larger the value, the fewer areas are judged as edges.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "sf_gau_y_edge_slope": {"api": "nSfYLfEdgeGain", "display": "ynr.lf.edge<PERSON>ain", "comments": "Low frequency edge judgment transition speed, the smaller the value, the fewer areas are judged as edges.", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "sf_gau_y_detail_th": {"api": "nSfYLfDetailThr", "display": "ynr.lf.detailThr", "comments": "The threshold for low-frequency texture judgment is that the larger the value, the fewer areas are judged as texture.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "sf_gau_y_detail_slope": {"api": "nSfYLfDetailGain", "display": "ynr.lf.<PERSON><PERSON>ain", "comments": "Low frequency texture determines transition speed, and the smaller the value, the fewer areas are judged as texture.", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "sf_gau_y_sp_win_sel": {"api": "nSfYLfSpWinSel", "display": "ynr.lf.spWinSel", "sublabel": ["flat", "detail", "edge"], "comments": "The size of the low-frequency denoising window is larger, and the larger the value, the larger the window filter is used, 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "sf_gau_y_sp_sigma": {"api": "nSfYLfSpSigma", "display": "ynr.lf.spSigma", "sublabel": ["flat", "detail", "edge"], "comments": "The low-frequency denoising spatial filtering coefficient tends to use all pixels within the window as the value increases, and tends to use pixels in the center area of the window as the value decreases., 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U9.0 Range: [1, 320]"}, "sf_gau_y_thres": {"api": "nSfYLfThr", "display": "ynr.lf.thr", "sublabel": ["flat", "detail", "edge"], "comments": "Low frequency denoising threshold. The larger the value, the greater the denoising intensity, 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf_gau_y_slope": {"api": "nSfYLfGain", "display": "ynr.lf.gain", "sublabel": ["flat", "detail", "edge"], "comments": "The degree of excessive edge preservation in low-frequency denoising. The larger the value, the smaller the denoising intensity, 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U2.8 Range: [0, 1023]"}, "sf_gau_y_sad_alpha": {"api": "nSfYLfFAlpha", "display": "ynr.lf.fAlpha", "sublabel": ["flat", "detail", "edge"], "comments": "The fusion weight of bilateral and NLM filtering tends to use NLM as the weight increases, 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "sf_gau_y_hdr_adjust_th": {"api": "nSfYLfHdrAdjThr", "display": "ynr.lf.hdrAdjThr", "comments": "Short frame current frame brightness low-frequency noise reduction intensity correction, the larger the value, the greater the low-frequency noise reduction intensity of the short frame", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf_gau_y_ori_str": {"api": "nSfYLfOriStr", "display": "ynr.lf.oriStr", "comments": "The low-frequency denoising result overlaps the original noise ratio, and the larger the value, the closer it is to the original frame.", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "sf_gau_y_thres_bias_lut": {"api": "nSfYLfThrLut", "display": "ynr.lf.thrLut", "comments": "Adjust the low-frequency noise reduction threshold based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the stronger the noise reduction intensity.", "hint": "Accuracy: S6.2 Range: [-256, 255]"}, "sf_gau_y_slope_gain_lut": {"api": "nSfYLfGainLut", "display": "ynr.lf.gain<PERSON>ut", "comments": "Adjust the transition degree of denoising and edge preservation based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the smaller the denoising intensity.", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "sf_lap_y_ref_weight_th": {"api": "nSfYHfRefWtThr", "display": "ynr.hf.refWtThr", "comments": "When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_lap_y_ref_weight_slope": {"api": "nSfYHfRefWtGain", "display": "ynr.hf.refWtGain", "comments": "When providing the next reference frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "sf_lap_y_ref_ratio_limit": {"api": "nSfYHfRefRatio", "display": "ynr.hf.refRatio", "comments": "When providing the next reference frame, the higher the ratio of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally. The larger the value, the lower the clarity of the still area and the smaller the noise.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_lap_y_cur_weight_th": {"api": "nSfYHfCurWtThr", "display": "ynr.hf.curWtThr", "comments": "When provided to the output frame, the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image is smaller, and the smaller the threshold, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_lap_y_cur_weight_slope": {"api": "nSfYHfCurWtGain", "display": "ynr.hf.curW<PERSON><PERSON>ain", "comments": "When providing the output frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "sf_lap_y_cur_ratio_limit": {"api": "nSfYHfCurRatio", "display": "ynr.hf.curRatio", "comments": "When providing the output frame, the higher the ratio of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally. The larger the value, the lower the clarity of the still area and the smaller the noise.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_lap_y_edge_th": {"api": "nSfYHfEdgeThr", "display": "ynr.hf.edgeThr", "comments": "The threshold for high-frequency edge judgment is that the larger the value, the fewer areas are judged as edges.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "sf_lap_y_edge_slope": {"api": "nSfYHfEdgeGain", "display": "ynr.hf.edgeGain", "comments": "High frequency edge judgment transition speed, the smaller the value, the fewer areas are judged as edges.", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "sf_lap_y_detail_th": {"api": "nSfYHfDetailThr", "display": "ynr.hf.detailThr", "comments": "High frequency texture judgment threshold, the larger the value, the fewer areas are judged as texture.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "sf_lap_y_detail_slope": {"api": "nSfYHfDetailGain", "display": "ynr.hf.detailGain", "comments": "High frequency texture determines transition speed, and the smaller the value, the fewer areas are judged as texture.", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "sf_lap_y_sp_win_sel": {"api": "nSfYHfSpWinSel", "display": "ynr.hf.spWinSel", "sublabel": ["flat", "detail", "edge"], "comments": "The size of the high-frequency edge preservation denoising window is larger, and the larger the value, the larger the window filtering is used. 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "sf_lap_y_sp_sigma": {"api": "nSfYHfSpSigma", "display": "ynr.hf.spSigma", "sublabel": ["flat", "detail", "edge"], "comments": "The high-frequency edge preserving denoising spatial filtering coefficient tends to use all pixels within the window as the value increases, and tends to use pixels in the center area of the window as the value decreases., 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U9.0 Range: [1, 320]"}, "sf_lap_y_thres": {"api": "nSfYHfThr", "display": "ynr.hf.thr", "sublabel": ["flat", "detail", "edge"], "comments": "High frequency edge preservation denoising threshold. The larger the value, the greater the denoising intensity, 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf_lap_y_slope": {"api": "nSfYHfGain", "display": "ynr.hf.gain", "sublabel": ["flat", "detail", "edge"], "comments": "High frequency edge preservation denoising and excessive edge preservation. The larger the value, the smaller the denoising intensity, 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U2.8 Range: [0, 1023]"}, "sf_lap_y_sad_alpha": {"api": "nSfYHfFAlpha", "display": "ynr.hf.fAlpha", "sublabel": ["flat", "detail", "edge"], "comments": "The fusion weight of high-frequency edge preservation denoising bilateral and NLM filtering tends to use NLM as the weight increases, 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "sf_lap_y_gauss_win_sel": {"api": "nSfYHfSimpWinSel", "display": "ynr.hf.simpWinSel", "comments": "The window size of high-frequency non edge preserving denoising filter is larger, and the larger the value, the more pixels are used for denoising, resulting in a blurred effect, 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "sf_lap_y_gauss_sigma": {"api": "nSfYHfSimpSigma", "display": "ynr.hf.simpSigma", "comments": "High frequency non edge preserving denoising filter spatial filtering coefficient, the larger the value, the more inclined it is to use all pixels within the window, and the smaller the value, the more inclined it is to use pixels in the center area of the window, 0:flat, 1:detail, 2:edge", "hint": "Accuracy: U9.0 Range: [1, 320]"}, "sf_lap_y_gauss_ratio": {"api": "nSfYHfSimpRatio", "display": "ynr.hf.simpRatio", "sublabel": ["flat", "detail", "edge"], "comments": "The fusion coefficient of high-frequency non edge preserving denoising filtering and edge preserving filtering, the larger the value, the more the non edge preserving filtering result is used. 0:flat, 1:detail, 2:edge.", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "sf_lap_y_hdr_adjust_th": {"api": "nSfYHfHdrAdjThr", "display": "synr.hf.hdrAdjThr", "comments": "Short frame current frame brightness high-frequency noise reduction intensity correction, the larger the value, the greater the high-frequency noise reduction intensity of the short frame", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf_lap_y_ori_str": {"api": "nSfYHfOriStr", "display": "ynr.hf.oriStr", "comments": "The high-frequency denoising result overlaps the original noise ratio, and the larger the value, the closer it is to the original frame.", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "sf_lap_y_thres_bias_lut": {"api": "nSfYHfThrLut", "display": "ynr.hf.thrLut", "comments": "Adjust the high-frequency edge preservation and noise reduction threshold based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the stronger the noise reduction intensity.", "hint": "Accuracy: S6.2 Range: [-256, 255]"}, "sf_lap_y_slope_gain_lut": {"api": "nSfYHfGainLut", "display": "ynr.hf.gainLut", "comments": "Adjust the transition degree of denoising and edge preservation based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the smaller the denoising intensity.", "hint": "Accuracy: U3.5 Range: [0, 255]"}, "sf_gau_uv_ref_weight_th": {"api": "nSfCLfRefWtThr", "display": "cnr.lf.refWtThr", "comments": "When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_gau_uv_ref_weight_slope": {"api": "nSfCLfRefWtGain", "display": "cnr.lf.refWtGain", "comments": "When providing the next reference frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "sf_gau_uv_ref_ratio_limit": {"api": "nSfCLfRefRatio", "display": "cnr.lf.refRatio", "comments": "When providing the next reference frame, the larger the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally. The larger the value, the lower the clarity of the still area UV and the smaller the color noise.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_gau_uv_cur_weight_th": {"api": "nSfCLfCurWtThr", "display": "cnr.lf.curWtThr", "comments": "When provided to the output frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_gau_uv_cur_weight_slope": {"api": "nSfCLfCurWtGain", "display": "cnr.lf.curW<PERSON><PERSON><PERSON>", "comments": "When providing the output frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "sf_gau_uv_cur_ratio_limit": {"api": "nSfCLfCurRatio", "display": "cnr.lf.curRatio", "comments": "When provided to the output frame, the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is higher, and the larger the ratio, the more inclined it is to use the denoising result globally. The larger the value, the lower the clarity of the still area UV and the smaller the color noise.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_gau_uv_th": {"api": "nSfCLfThr", "display": "cnr.lf.thr", "comments": "The threshold for reducing saturation based on its original size, the smaller the value, the more likely it is to be globally ineffective", "hint": "Accuracy: U7.2 Range: [0, 511]"}, "sf_gau_uv_slope": {"api": "nSfCLfGain", "display": "cnr.lf.gain", "comments": "Reduce the transition speed of saturation based on its original size. The larger the value, the more likely it is to be globally ineffective", "hint": "Accuracy: U2.7 Range: [0, 511]"}, "sf_gau_uv_limit": {"api": "nSfCLfLimit", "display": "cnr.lf.limit", "comments": "According to the proportion of saturation overlaid color noise, the smaller the value, the more inclined it is to reduce saturation", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_gau_uv_hdr_adjust_th": {"api": "nSfCLfHdrAdjThr", "display": "cnr.lf.hdrAdjThr", "comments": "Short frame current frame color low-frequency noise reduction intensity correction, the larger the value, the greater the low-frequency noise reduction intensity of the short frame", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf_gau_uv_ori_str": {"api": "nSfCLfOriStr", "display": "cnr.lf.oriStr", "comments": "The ratio of low-frequency fading noise to aliasing noise, the larger the value, the closer it is to the original input", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "sf_gau_uv_thre_pre_lut": {"api": "nSfCLfThrPreLut", "display": "cnr.lf.thrPreLut", "comments": "Low frequency color first stage denoising intensity, horizontal axis brightness, the larger the value, the greater the denoising intensity", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf_gau_uv_thre_lut": {"api": "nSfCLfThrLut", "display": "cnr.lf.thrLut", "comments": "Low frequency color two-stage denoising intensity, horizontal axis brightness, the larger the value, the greater the denoising intensity", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf_gau_uv_prot_thre_lut": {"api": "nSfCLfPrThrLut", "display": "cnr.lf.prThrLut", "comments": "Low frequency color denoising protection threshold, based on the diff aliasing color noise before and after denoising, the larger the value, the more likely it is not to aliasing", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf_gau_uv_prot_slope_lut": {"api": "nSfCLfPrGainLut", "display": "cnr.lf.prGainLut", "comments": "Low frequency color denoising protection transition speed, based on the diff of color noise before and after denoising, the larger the value, the more likely it is to be overlapped", "hint": "Accuracy: U2.8 Range: [0, 1023]"}, "sf_lap_uv_ref_weight_th": {"api": "nSfCHfRefWtThr", "display": "cnr.hf.refWtThr", "comments": "When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_lap_uv_ref_weight_slope": {"api": "nSfCHfRefWtGain", "display": "cnr.hf.refWtGain", "comments": "When provided to the next reference frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "sf_lap_uv_ref_ratio_limit": {"api": "nSfCHfRefRatio", "display": "cnr.hf.refRatio", "comments": "When provided to the next reference frame, the higher the ratio of the temporal fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally. The larger the value, the lower the clarity of the still area UV and the smaller the color noise.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_lap_uv_cur_weight_th": {"api": "nSfCHfCurWtThr", "display": "cnr.hf.curWtThr", "comments": "When provided to the output frame, the lower the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_lap_uv_cur_weight_slope": {"api": "nSfCHfCurWtGain", "display": "cnr.hf.curW<PERSON><PERSON><PERSON>", "comments": "When provided to the output frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "sf_lap_uv_cur_ratio_limit": {"api": "nSfCHfCurRatio", "display": "cnr.hf.curRatio", "comments": "When provided to the output frame, the ratio of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is higher, and the larger the ratio, the more inclined it is to use the denoising result globally. The larger the value, the lower the clarity of the stationary area UV and the smaller the color noise.", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "sf_lap_uv_thre": {"api": "nSfCHfThr", "display": "cnr.hf.thr", "comments": "High frequency denoising threshold, the larger the value, the stronger the denoising intensity", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf_lap_uv_slope": {"api": "nSfCHfGain", "display": "cnr.hf.gain", "comments": "High frequency denoising transition speed, the larger the value, the weaker the denoising intensity", "hint": "Accuracy: U2.8 Range: [0, 1023]"}, "sf_lap_uv_hdr_adjust_gain": {"api": "nSfCHfHdrAdjGain", "display": "cnr.hf.hdrAdjGain", "comments": "Short frame current frame color high-frequency noise reduction intensity correction, the larger the value, the greater the high-frequency noise reduction intensity of the short frame", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "sf_lap_uv_ori_str": {"api": "nSfCHfOriStr", "display": "cnr.hf.oriStr", "comments": "High frequency color fading noise and aliasing noise, the larger the value, the closer it is to the original image", "hint": "Accuracy: U1.5 Range: [0, 32]"}, "gf_win_r": {"api": "nEhcCWin", "display": "cnr.ehc.win", "comments": "The size of the window for enhance color sf filtering, the larger the value, the greater the intensity of color noise removal", "hint": "Accuracy: U3.0 Range: [0, 5]"}, "gf_eps": {"api": "nEhcCStr", "display": "cnr.ehc.str", "comments": "The strength for enhance color sf filtering to remove color noise, the larger the value, the greater the intensity of color noise removal", "hint": "Accuracy: U8.0 Range: [1, 255]"}, "gf_uv_diff_th": {"api": "nEhcCDiffThr", "display": "cnr.ehc.diffThr", "comments": "The diff protection strength of enhance color sf filtering for color noise reduction results is weaker as the value increases, and closer to non enhance color sf filtering as the value decreases", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "gf_uv_sat_th": {"api": "nEhcCSatThr", "display": "cnr.ehc.satThr", "comments": "The results of enhance color sf filtering for removing color noise are based on the sat protection strength. The larger the value, the more inclined it is to use the enhance color sf filtering result", "hint": "Accuracy: U7.0 Range: [0, 127]"}, "gf_uv_sat_slope": {"api": "nEhcCSatGain", "display": "cnr.ehc.sat<PERSON>ain", "comments": "The enhance color sf filtering denoising result is based on the transition speed of SAT protection. The larger the value, the more inclined it is to use the original denoising result", "hint": "Accuracy: U1.8 Range: [0, 511]"}, "gf_uv_sat_ratio": {"api": "nEhcCSatRatio", "display": "cnr.ehc.satRatio", "sublabel": ["low", "high"], "comments": "The enhance color sf filtering denoising result is based on the fusion ratio between sat and the original denoising result. The larger the value, the more inclined it is to the original denoising result", "hint": "Accuracy: U1.7 Range: [0, 255]"}, "gf_uv_blend_ratio_th": {"api": "nEhcCRatioThr", "display": "cnr.ehc.ratioThr", "comments": "Determine the time-domain fusion coefficient threshold for the fusion enhance color sf filtering result of the dynamic and static regions based on the time-domain fusion coefficient. It is hoped that different fusion ratios will be used for the dynamic and static regions", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "gf_uv_blend_ratio_slope": {"api": "nEhcCRatioGain", "display": "cnr.ehc.ratioGain", "comments": "Determine the transition speed of the fusion enhance color sf filtering result for the dynamic and static regions based on the time-domain fusion coefficient. It is hoped that different fusion ratios will be used for the dynamic and static regions", "hint": "Accuracy: S7.7 Range: [-16384, 16383]"}, "gf_uv_blend_ratio": {"api": "nEhcCRatio", "display": "cnr.ehc.ratio", "sublabel": ["static", "motion"], "comments": "According to the time-domain fusion coefficient, the proportion of the fusion enhance color sf filtering results in the dynamic and static regions is determined. The first value corresponds to the static region, and the second value corresponds to the motion region. The larger the value, the more inclined it is to use the guided filtering results", "hint": "Accuracy: U1.7 Range: [0, 128]"}, "enable": {"api": "nYuv3dnrEn", "display": "enable.yuv3dnrEn", "comments": "yuv3dnr sw enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "tf_y_en": {"api": "nTfYEn", "display": "enable.tfYEn", "comments": "y tf enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "tf_uv_en": {"api": "nTfCEn", "display": "enable.tfCEn", "comments": "uv tf enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "sf_gau_y_en": {"api": "nSfYLfEn", "display": "enable.sfYLfEn", "comments": "y lf sf enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "sf_lap_y_en": {"api": "nSfYHfEn", "display": "enable.sfYHfEn", "comments": "y hf sf enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "sf_gau_uv_en": {"api": "nSfCLfEn", "display": "enable.sfCLfEn", "comments": "uv lf sf enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "sf_lap_uv_en": {"api": "nSfCHfEn", "display": "enable.sfCHfEn", "comments": "uv hf sf enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "gf_uv_en": {"api": "nEhcCEn", "display": "enable.ehcCEn", "comments": "uv gf enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "tweight_sf_en": {"api": "nTwtSfEn", "display": "enable.twtSfEn", "comments": "tweight sf enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "ext_mask_en": {"api": "nExtMaskEn", "display": "enable.extMaskEn", "comments": "ext mask enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}}