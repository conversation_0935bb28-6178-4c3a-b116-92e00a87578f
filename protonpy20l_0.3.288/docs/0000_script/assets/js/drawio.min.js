/**
 * Minified by jsDelivr using Terser v5.37.0.
 * Original file: /npm/docsify-drawio@1.0.7/drawio.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(){const n={"&":"&amp;","'":"&#x27;","`":"&#x60;",'"':"&quot;","<":"&lt;",">":"&gt;"};window.drawioConverter=function(i,t=(new Date).getTime()){let e={editable:!1,highlight:"#0000ff",nav:!1,toolbar:null,edit:null,resize:!0,lightbox:"open",xml:i};const o=JSON.stringify(e);return`<div class="drawio-viewer-index-${t}">\n          <div class="mxgraph" style="max-width: 100%; border: 1px solid transparent" data-mxgraph="${r=o,"string"!=typeof r?r:r.replace(/[&'`"<>]/g,(function(i){return n[i]}))}"></div>\n        </div>\n        `;var r};window.$docsify.plugins=[].concat((function(n){n.doneEach((n=>{try{window.GraphViewer.processElements()}catch{}}))}),$docsify.plugins)}();
//# sourceMappingURL=/sm/072b6e77a5ed328b6625eb0cc772a405ba32ed2b120c016b04d5032d9b6bcab7.map