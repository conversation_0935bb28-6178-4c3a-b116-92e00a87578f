{"hcg_shot_noise_coef": {"api": "nHcgShotNoiseCoef", "display": "hcgShotNoise<PERSON>oef", "comments": "Hcg Calibration Params", "hint": "Accuracy: S0.31 Range: [-2147483648, 2147483647]"}, "hcg_read_noise_coef": {"api": "nHcgReadNoiseCoef", "display": "hcgReadNoise<PERSON>oef", "comments": "Hcg Calibration Params", "hint": "Accuracy: S0.31 Range: [-2147483648, 2147483647]"}, "lcg_shot_noise_coef": {"api": "nLcgShotNoiseCoef", "display": "lcgShotNoiseCoef", "comments": "Lcg Calibration Params", "hint": "Accuracy: S0.31 Range: [-2147483648, 2147483647]"}, "lcg_read_noise_coef": {"api": "nLcgReadNoiseCoef", "display": "lcgReadNoiseCoef", "comments": "Lcg Calibration Params", "hint": "Accuracy: S0.31 Range: [-2147483648, 2147483647]"}, "aestat0_input_mux": {"api": "nAestat0InputMux", "display": "aestat0InputMux", "comments": "aestat0 access point, 0: RawScl0, 1: DPC0, 2: WBC, NO GUI", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "aestat1_input_mux": {"api": "nAestat1InputMux", "display": "aestat1InputMux", "comments": "aestat1 access point, 0: RawScl1, 1: DPC1, 2: W<PERSON>, NO GUI", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "awbstat_input_mux": {"api": "nAwbstatInputMux", "display": "awbstatInputMux", "comments": "awbstat access point, 0: RawScl0, 1: RawScl1, 2: DPC0, 3: Raw2DNR, 4: LSC, NO GUI", "hint": "Accuracy: U3.0 Range: [0, 4]"}, "afstat_input_mux": {"api": "nAfstatInputMux", "display": "afstatInputMux", "comments": "afstat access point, 0: RawScl0, 1: DPC0, 2: W<PERSON>, NO GUI", "hint": "Accuracy: U2.0 Range: [0, 2]"}, "afstat_drc_mode": {"api": "nAfstatDrcMode", "display": "afstatDrcMode", "comments": "afstat drc mode, set w.r.t input bitwidth and af access point", "hint": "Accuracy: U8.0 Range: [0, 7]"}}