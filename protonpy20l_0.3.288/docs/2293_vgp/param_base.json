{"context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "vgp is 0x2293", "type": "AX_U16"}, "g0_format": {"size": [], "acc": [0, 6], "comment": "g0 input`s format", "type": "AX_U8"}, "blending_enable": {"size": [], "acc": [0, 1], "comment": "Blending unit enable control bit", "type": "AX_U8"}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "params": {"v0_format": {"acc": [0, 6], "auto": 0, "comment": "v0 input`s format", "default": 0, "display": "v0 format", "hidden": 0, "range": [0, 63], "size": [], "target_conf": ["tde.v0_format"], "type": "AX_U8", "dependency": "user", "enum_field": {"0": "NV12", "1": "NV21", "2": "NV16", "3": "NV61", "4": "YUYV", "5": "YVYU", "6": "UYVY", "7": "VYUY", "8": "Y", "9": "LAB", "10": "Y_P101010", "11": "Y_P010", "12": "BIT1", "13": "BIT2", "14": "BIT4", "15": "BIT8", "16": "NV12_P101010", "17": "NV21_P101010", "18": "NV16_P101010", "19": "NV61_P101010", "20": "YUYV_P101010", "21": "YVYU_P101010", "22": "UYVY_P101010", "23": "VYUY_P101010", "24": "NV12_P010", "25": "NV21_P010", "26": "NV16_P010", "27": "NV61_P010", "28": "YUYV_P010", "29": "YVYU_P010", "30": "UYVY_P010", "31": "VYUY_P010", "32": "RGB888", "33": "BGR888", "34": "RGB565", "35": "BGR565", "48": "ARGB8888", "49": "ABGR8888", "50": "ARGB8565", "51": "ABGR8565", "52": "ARGB1555", "53": "ABGR1555", "54": "ARGB4444", "55": "ABGR4444", "56": "RGBA8888", "57": "BGRA8888", "58": "RGBA5658", "59": "BGRA5658", "60": "RGBA5551", "61": "BGRA5551", "62": "RGBA4444", "63": "BGRA4444"}}, "v0_xT422_offset_in": {"acc": [1, 8], "auto": 0, "comment": "RGB to YUV422 conversion offset in. Only effective when input is RGB, need not tune", "default": [0, 0, 0], "display": "v0 xT422 offset in", "hidden": 1, "range": [-256, 255], "size": [3], "target_conf": ["tde.v0_xT422_offset_in"], "type": "AX_S16", "dependency": "common"}, "v0_xT422_offset_out": {"acc": [1, 8], "auto": 0, "comment": "v0`s ToYuv420 csc output offset, need not tune", "default": [0, 0, 0], "display": "v0 xT422 offset out", "hidden": 1, "range": [-256, 255], "size": [3], "target_conf": ["tde.v0_xT422_offset_out"], "type": "AX_S16", "dependency": "common"}, "v0_sp_alpha_enable": {"acc": [0, 1], "auto": 0, "comment": "Special alpha channel enable control bit", "default": 1, "display": "v0 sp alpha enable", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.v0_sp_alpha_enable"], "type": "AX_U8", "dependency": "user"}, "v0_global_alpha_enable": {"acc": [0, 1], "auto": 0, "comment": "Global alpha enable control bit. It's priority is lower than special alpha", "default": 0, "display": "v0 global alpha enable", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.v0_global_alpha_enable"], "type": "AX_U8", "dependency": "user"}, "v0_global_alpha": {"acc": [0, 8], "auto": 0, "comment": "v0's global alpha value. If global alpha is used or there is no alpha channel in input v0, this value will be the global alpha for v0. BITMAP's alpha channel will be generated by using color palette if global alpha is not enabled", "default": 0, "display": "v0 sp global alpha", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.v0_global_alpha"], "type": "AX_U8", "dependency": "user"}, "v0_global_chroma": {"acc": [0, 8], "auto": 0, "comment": "v0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10", "default": [128, 128], "display": "v0 sp global chroma", "hidden": 0, "range": [0, 255], "size": [2], "target_conf": ["tde.v0_global_chroma"], "type": "AX_U8", "dependency": "user"}, "quad_v0_enable": {"display": "quad v0 enable", "acc": [0, 1], "type": "AX_U8", "size": [4], "range": [0, 1], "default": [0, 0, 0, 0], "comment": "Quad enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.quad_v0_enable"], "dependency": "user"}, "quad_v0_in_x_coords": {"display": "quad v0 in x coords", "acc": [1, 13], "type": "AX_S16", "size": [4, 4], "range": [-8192, 8191], "default": [[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "comment": "x coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed", "hidden": 0, "auto": 0, "target_conf": ["tde.quad_v0_in_x_coords", "tde.quad_v0_in_steps", "tde.quad_v0_in_directions"], "dependency": "user"}, "quad_v0_in_y_coords": {"display": "quad v0 in y coords", "acc": [1, 13], "type": "AX_S16", "size": [4, 4], "range": [-8192, 8191], "default": [[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "comment": "y coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed", "hidden": 0, "auto": 0, "target_conf": ["tde.quad_v0_in_y_coords", "tde.quad_v0_in_steps", "tde.quad_v0_in_directions"], "dependency": "user"}, "quad_v0_out_x_coords": {"display": "quad v0 out x coords", "acc": [1, 13], "type": "AX_S16", "size": [4, 4], "range": [-8192, 8191], "default": [[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "comment": "x coordinates of intersection points between outer quad's edges, and image rectangle which is constrained by y_st and y_ed", "hidden": 0, "auto": 0, "target_conf": ["tde.quad_v0_out_x_coords", "tde.quad_v0_out_steps", "tde.quad_v0_out_directions"], "dependency": "user"}, "quad_v0_out_y_coords": {"display": "quad v0 out y coords", "acc": [1, 13], "type": "AX_S16", "size": [4, 4], "range": [-8192, 8191], "default": [[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "comment": "y coordinates of intersection points between outer quad's edges, and image rectangle which is constrained by y_st and y_ed", "hidden": 0, "auto": 0, "target_conf": ["tde.quad_v0_out_y_coords", "tde.quad_v0_out_steps", "tde.quad_v0_out_directions"], "dependency": "user"}, "quad_v0_mode": {"acc": [0, 1], "auto": 0, "comment": "Quad feature mode control bit. 0 - mosaic; 1 - fill", "default": [0, 0, 0, 0], "display": "quad out mode", "hidden": 0, "range": [0, 1], "size": [4], "target_conf": ["tde.quad_v0_mode"], "type": "AX_U8", "dependency": "user"}, "quad_v0_mosaic_step_size": {"acc": [0, 7], "auto": 0, "comment": "Mosaic step size", "default": [16, 16], "display": "mosaic step size", "hidden": 0, "range": [0, 127], "size": [2], "target_conf": ["tde.quad_v0_mosaic_step_size"], "type": "AX_U8", "dependency": "user"}, "quad_v0_fill_out_luma": {"acc": [0, 8], "auto": 0, "comment": "Outer quad luma", "default": [128, 128, 128, 128], "display": "outer quad luma", "hidden": 0, "range": [0, 255], "size": [4], "target_conf": ["tde.quad_v0_fill_out_luma"], "type": "AX_U8", "dependency": "user"}, "quad_v0_fill_out_chroma": {"acc": [0, 8], "auto": 0, "comment": "Outer quad chroma(with 128 bias)", "default": [[128, 128], [128, 128], [128, 128], [128, 128]], "display": "Outer quad chroma", "hidden": 0, "range": [0, 255], "size": [4, 2], "target_conf": ["tde.quad_v0_fill_out_chroma"], "type": "AX_U8", "dependency": "user"}, "quad_v0_out_alpha": {"acc": [0, 8], "auto": 0, "comment": "outer quad blending alpha", "default": [255, 255, 255, 255], "display": "outer quad blending alpha", "hidden": 0, "range": [0, 255], "size": [4], "target_conf": ["tde.quad_v0_out_alpha"], "type": "AX_U8", "dependency": "user"}, "roi_luma_sum_tl_coords": {"acc": [0, 12], "auto": 0, "comment": "roi luma sum tl coords", "default": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "display": "top-left corner coordinates of 8 roi areas", "hidden": 0, "range": [0, 4095], "size": [8, 2], "target_conf": ["tde.roi_luma_sum_tl_coords"], "type": "AX_U16", "dependency": "user"}, "roi_luma_sum_br_coords": {"acc": [0, 12], "auto": 0, "comment": "roi luma sum br coords", "default": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "display": "bottom-right corner coordinates of 8 roi areas", "hidden": 0, "range": [0, 4095], "size": [8, 2], "target_conf": ["tde.roi_luma_sum_br_coords"], "type": "AX_U16", "dependency": "user"}, "g0_format": {"acc": [0, 6], "auto": 0, "comment": "g0 input`s format", "default": 0, "display": "g0 format", "hidden": 0, "range": [0, 63], "size": [], "target_conf": ["tde.g0_format"], "type": "AX_U8", "dependency": "user", "enum_field": {"0": "NV12", "1": "NV21", "2": "NV16", "3": "NV61", "4": "YUYV", "5": "YVYU", "6": "UYVY", "7": "VYUY", "8": "Y", "9": "LAB", "10": "Y_P101010", "11": "Y_P010", "12": "BIT1", "13": "BIT2", "14": "BIT4", "15": "BIT8", "16": "NV12_P101010", "17": "NV21_P101010", "18": "NV16_P101010", "19": "NV61_P101010", "20": "YUYV_P101010", "21": "YVYU_P101010", "22": "UYVY_P101010", "23": "VYUY_P101010", "24": "NV12_P010", "25": "NV21_P010", "26": "NV16_P010", "27": "NV61_P010", "28": "YUYV_P010", "29": "YVYU_P010", "30": "UYVY_P010", "31": "VYUY_P010", "32": "RGB888", "33": "BGR888", "34": "RGB565", "35": "BGR565", "48": "ARGB8888", "49": "ABGR8888", "50": "ARGB8565", "51": "ABGR8565", "52": "ARGB1555", "53": "ABGR1555", "54": "ARGB4444", "55": "ABGR4444", "56": "RGBA8888", "57": "BGRA8888", "58": "RGBA5658", "59": "BGRA5658", "60": "RGBA5551", "61": "BGRA5551", "62": "RGBA4444", "63": "BGRA4444"}}, "g0_xT422_offset_in": {"acc": [1, 8], "auto": 0, "comment": "RGB to YUV conversion offset in. Only effective when input format is RGB, need not tune", "default": [0, 0, 0], "display": "g0 xT422 offset in", "hidden": 1, "range": [-256, 255], "size": [3], "target_conf": ["tde.g0_xT422_offset_in"], "type": "AX_S16", "dependency": "common"}, "g0_xT422_offset_out": {"acc": [1, 8], "auto": 0, "comment": "RGB to YUV conversion offset out. Only effective when input format is RGB, need not tune", "default": [0, 0, 0], "display": "g0 xT422 offset out", "hidden": 1, "range": [-256, 255], "size": [3], "target_conf": ["tde.g0_xT422_offset_out"], "type": "AX_S16", "dependency": "common"}, "g0_global_alpha_enable": {"acc": [0, 1], "auto": 0, "comment": "Global alpha enable control bit. It's priority is lower than special alpha", "default": 0, "display": "v0 global alpha enable", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.g0_global_alpha_enable"], "type": "AX_U8", "dependency": "user"}, "g0_global_alpha": {"acc": [0, 8], "auto": 0, "comment": "g0's global alpha value. If global alpha is used or there is no alpha channel in input g0, this value will be the global alpha for g0. BITMAP's alpha channel will be generated by using color palette if global alpha is not enabled", "default": 200, "display": "g0 sp global alpha", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.g0_global_alpha"], "type": "AX_U8", "dependency": "user"}, "g0_global_chroma": {"acc": [0, 8], "auto": 0, "comment": "g0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10", "default": [128, 128], "display": "g0 sp global chroma", "hidden": 0, "range": [0, 255], "size": [2], "target_conf": ["tde.g0_global_chroma"], "type": "AX_U8", "dependency": "user"}, "blending_enable": {"acc": [0, 1], "auto": 0, "comment": "Blending unit enable control bit", "default": 0, "display": "blending enable", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.blending_enable"], "type": "AX_U8", "dependency": "user"}, "blending_coord": {"acc": [0, 16], "auto": 0, "comment": "Blending coord", "default": [0, 0], "display": "blending coord", "hidden": 0, "range": [0, 65535], "size": [2], "target_conf": ["tde.blending_coord"], "type": "AX_U16", "dependency": "user"}, "blending_alpha0_out_mode": {"acc": [0, 1], "auto": 0, "comment": "0 - v0 alpha not changed; 1 - v0 alpha inversed; Result is intermediate", "default": 0, "display": "blending alpha0 out mode", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.blending_alpha0_out_mode"], "type": "AX_U8", "dependency": "user"}, "blending_alpha1_out_mode": {"acc": [0, 1], "auto": 0, "comment": "0 - gx alpha not changed; 1 - gx alpha inversed; Result is intermediate", "default": 0, "display": "blending alpha1 out mode", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.blending_alpha1_out_mode"], "type": "AX_U8", "dependency": "user"}, "blending_alpha_out_weight": {"acc": [0, 1, 8], "auto": 0, "comment": "v0_alpha * (1 - weight) + gx_alpha * weight", "default": 0.5, "display": "blending alpha1 out mode", "hidden": 0, "range": [0.0, 1.0], "size": [], "target_conf": ["tde.blending_alpha_out_weight"], "type": "AX_U16", "dependency": "user"}, "blending_color_key_enable": {"acc": [0, 1], "auto": 0, "comment": "blending color key enable", "default": 0, "display": "blending enable", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.blending_color_key_enable"], "type": "AX_U8", "dependency": "user"}, "blending_alpha_select": {"acc": [0, 2], "auto": 0, "comment": "Select alpha channel to be used in blending", "default": 0, "display": "blending alpha select", "hidden": 0, "range": [0, 3], "size": [], "target_conf": ["tde.blending_alpha_select"], "type": "AX_U8", "dependency": "user"}, "blending_color_key_val": {"acc": [1, 8], "auto": 0, "comment": "Color key color range for YUV.", "default": [[100, 120], [30, 60], [30, 60]], "display": "blending alpha select", "hidden": 0, "range": [-255, 255], "size": [3, 2], "target_conf": ["tde.blending_color_key_val"], "type": "AX_U8", "dependency": "user"}, "blending_color_key_inv": {"acc": [0, 1], "auto": 0, "comment": "If enabled, pix whose color is outside of the color-key range will do the color-key process, rather than the one within the color range", "default": 0, "display": "blending color key inv", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.blending_color_key_inv"], "type": "AX_U8", "dependency": "user"}, "ddr_write_format": {"acc": [0, 6], "auto": 0, "comment": "DDR dump format", "default": 0, "display": "ddr write format", "hidden": 0, "range": [0, 63], "size": [], "target_conf": ["tde.ddr_write_format", "tde.ddr_write_vfilter_enable", "tde.ddr_write_adj_enable"], "type": "AX_U8", "dependency": "user", "enum_field": {"0": "NV12", "1": "NV21", "2": "NV16", "3": "NV61", "4": "YUYV", "5": "YVYU", "6": "UYVY", "7": "VYUY", "8": "Y", "9": "LAB", "10": "Y_P101010", "11": "Y_P010", "12": "BIT1", "13": "BIT2", "14": "BIT4", "15": "BIT8", "16": "NV12_P101010", "17": "NV21_P101010", "18": "NV16_P101010", "19": "NV61_P101010", "20": "YUYV_P101010", "21": "YVYU_P101010", "22": "UYVY_P101010", "23": "VYUY_P101010", "24": "NV12_P010", "25": "NV21_P010", "26": "NV16_P010", "27": "NV61_P010", "28": "YUYV_P010", "29": "YVYU_P010", "30": "UYVY_P010", "31": "VYUY_P010", "32": "RGB888", "33": "BGR888", "34": "RGB565", "35": "BGR565", "48": "ARGB8888", "49": "ABGR8888", "50": "ARGB8565", "51": "ABGR8565", "52": "ARGB1555", "53": "ABGR1555", "54": "ARGB4444", "55": "ABGR4444", "56": "RGBA8888", "57": "BGRA8888", "58": "RGBA5658", "59": "BGRA5658", "60": "RGBA5551", "61": "BGRA5551", "62": "RGBA4444", "63": "BGRA4444"}}, "ddr_write_adj_offset": {"acc": [1, 8], "auto": 0, "comment": "in && out offset for DDR write adjustment", "default": [[0, 0, 0], [0, 0, 0]], "display": "ddr write adj offset", "hidden": 1, "range": [-256, 255], "size": [2, 3], "target_conf": ["tde.ddr_write_adj_offset"], "type": "AX_S16", "dependency": "common"}, "dither_enable": {"display": "dither enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 1, "comment": "dither enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.dither_enable", "tde.dither_seed_r", "tde.dither_seed_g", "tde.dither_seed_b"], "dependency": "user"}}, "submodules": {"setup": {"params": ["v0_xT422_offset_in", "v0_xT422_offset_out", "g0_xT422_offset_in", "g0_xT422_offset_out", "ddr_write_adj_offset"], "configs": ["tde.enable", "tde.v0_xT422_decimation_hor", "tde.v0_xT422_matrix", "tde.g0_xT422_matrix", "tde.g0_xT422_decimation_hor", "tde.dither_seed_enable", "tde.dither_pmask_r", "tde.dither_pmask_g", "tde.dither_pmask_b", "tde.v0_up420T422_copy_enable", "tde.g0_up420T422_copy_enable", "tde.ddr_write_adj_matrix", "tde.ddr_write_decimation_ver", "tde.ddr_write_up422T444_copy_enable"]}, "v0_read": {"params": ["v0_format", "v0_sp_alpha_enable", "v0_global_alpha_enable", "v0_global_alpha", "v0_global_chroma"], "configs": []}, "v0_quad": {"params": ["quad_v0_enable", "quad_v0_in_x_coords", "quad_v0_in_y_coords", "quad_v0_out_x_coords", "quad_v0_out_y_coords", "quad_v0_mode", "quad_v0_mosaic_step_size", "quad_v0_fill_out_luma", "quad_v0_fill_out_chroma", "quad_v0_out_alpha"], "configs": ["tde.quad_v0_in_y_start", "tde.quad_v0_in_y_end", "tde.quad_v0_out_y_start", "tde.quad_v0_out_y_end"]}, "g0_read": {"params": ["g0_format", "g0_global_alpha_enable", "g0_global_alpha", "g0_global_chroma"], "configs": []}, "g_blend": {"params": ["blending_enable", "blending_coord", "blending_alpha0_out_mode", "blending_alpha1_out_mode", "blending_alpha_out_weight", "blending_color_key_enable", "blending_alpha_select", "blending_color_key_val", "blending_color_key_inv"], "configs": []}, "ddr_write": {"params": ["ddr_write_format"], "configs": []}, "roi_luma_sum": {"params": ["roi_luma_sum_tl_coords", "roi_luma_sum_br_coords"], "configs": []}, "dither": {"params": ["dither_enable"], "configs": []}}, "target_module": {"mc20l": {"tde": {"id": 6200, "method": 0}}}, "configs": {"tde": {"enable": {"acc": [0, 1], "size": [], "description": "DPU enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "v0_format": {"acc": [0, 6], "size": [], "description": "v0 format", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "v0_xT422_matrix": {"acc": [1, 2, 10], "size": [3, 3], "description": "RGB to YUV matrix. Only effective when input is RGB.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "v0_xT422_offset_in": {"acc": [1, 8, 0], "size": [3], "description": "RGB to YUV422 conversion offset in. Only effective when input is RGB.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "v0_xT422_offset_out": {"acc": [1, 8, 0], "size": [3], "description": "RGB to YUV422 conversion offset out. Only effective when input is RGB.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "v0_xT422_decimation_hor": {"acc": [1, 1, 5], "size": [7], "description": "YUV444 to YUV422 horizontal decimation weights.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S8", "partition": "-"}, "v0_sp_alpha_enable": {"acc": [0, 1], "size": [], "description": "Special alpha channel enable control bit.", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "v0_global_alpha_enable": {"acc": [0, 1], "size": [], "description": "Global alpha enable control bit. It's priority is lower than special alpha.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "v0_global_alpha": {"acc": [0, 8], "size": [], "description": "v0's global alpha value. If global alpha is used or there is no alpha channel in input v0, this value will be the global alpha for v0. BITMAP's alpha channel will be generated by using color palette if global alpha is not enabled.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "v0_global_chroma": {"acc": [0, 8], "size": [2], "description": "v0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "v0_up420T422_copy_enable": {"acc": [0, 1], "size": [], "description": "When input format is YUV420, this bit controls whether UV will be copied into YUV422.", "usage": "0 - use interpolation to up-sample YUV420 to YUV422; 1 - directly copy UV420 to UV422", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_v0_enable": {"acc": [0, 1], "size": [4], "description": "Quad enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_v0_in_x_coords": {"acc": [1, 13, 10], "size": [4, 4], "description": "x coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "quad_v0_in_y_coords": {"acc": [0, 13], "size": [4, 4], "description": "y coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "quad_v0_in_y_start": {"acc": [0, 13], "size": [4], "description": "y start coordinates of inner quad", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "quad_v0_in_y_end": {"acc": [0, 13], "size": [4], "description": "y end coordinates of inner quad", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "quad_v0_in_steps": {"acc": [1, 13, 10], "size": [4, 4], "description": "Inner quad step to form inner quad edges", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "quad_v0_in_directions": {"acc": [0, 2], "size": [4], "description": "This register controls direction of edge x1 and x2; 0b01 selects right side of line x1 whereas 0b00 selects left side of line x1; 0b10 selects right side of line x2 and 0b00 selects left side of line x2.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_v0_out_x_coords": {"acc": [1, 13, 10], "size": [4, 4], "description": "x coordinates of intersection points between outer quad's edges, and image rectangle which is constrained by y_st and y_ed.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "quad_v0_out_y_coords": {"acc": [0, 13], "size": [4, 4], "description": "y coordinates of intersection points between outer quad's edges, and image rectangle which is constrained by y_st and y_ed.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "quad_v0_out_y_start": {"acc": [0, 13], "size": [4], "description": "y start coordinates of outer quad", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "quad_v0_out_y_end": {"acc": [0, 13], "size": [4], "description": "y end coordinates of outer quad", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "quad_v0_out_steps": {"acc": [1, 13, 10], "size": [4, 4], "description": "Outer quad step to form outer quad edges", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "quad_v0_out_directions": {"acc": [0, 2], "size": [4], "description": "This register controls direction of edge x1 and x2; 0b01 selects right side of line x1 whereas 0b00 selects left side of line x1; 0b10 selects right side of line x2 and 0b00 selects left side of line x2.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_v0_mode": {"acc": [0, 1], "size": [4], "description": "Quad feature mode control bit.", "usage": "0 - mosaic; 1 - fill", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_v0_mosaic_step_size": {"acc": [0, 7], "size": [2], "description": "Mosaic step size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_v0_fill_out_luma": {"acc": [0, 8, 0], "size": [4], "description": "Outer quad luma", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_v0_fill_out_chroma": {"acc": [0, 8, 0], "size": [4, 2], "description": "Outer quad chroma(with 128 bias)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_v0_out_alpha": {"acc": [0, 8, 0], "size": [4], "description": "Outer quad blending alpha", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "roi_luma_sum_tl_coords": {"acc": [0, 12, 0], "size": [8, 2], "description": "top-left corner coordinates of 8 roi areas", "usage": "roi_luma_sum_tl_coords[n][0] - nth roi area's y coordinate; roi_luma_sum_tl_coords[n][1] - nth roi area's x coordinate", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "roi_luma_sum_br_coords": {"acc": [0, 12, 0], "size": [8, 2], "description": "bottom-right corner coordinates of 8 roi areas", "usage": "roi_luma_sum_br_coords[n][0] - nth roi area's y coordinate; roi_luma_sum_br_coords[n][1] - nth roi area's x coordinate", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "g0_format": {"acc": [0, 6], "size": [], "description": "input format", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "g0_xT422_matrix": {"acc": [1, 2, 10], "size": [3, 3], "description": "RGB to YUV conversion matrix. Only effective when input format is RGB.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "g0_xT422_offset_in": {"acc": [1, 8, 0], "size": [3], "description": "RGB to YUV conversion offset in. Only effective when input format is RGB.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "g0_xT422_offset_out": {"acc": [1, 8, 0], "size": [3], "description": "RGB to YUV conversion offset out. Only effective when input format is RGB.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "g0_xT422_decimation_hor": {"acc": [1, 1, 5], "size": [7], "description": "YUV444 to YUV422 horizontal decimation weights.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S8", "partition": "-"}, "g0_global_alpha_enable": {"acc": [0, 1], "size": [], "description": "Global alpha enable control bit. It's priority is lower than special alpha.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "g0_global_alpha": {"acc": [0, 8], "size": [], "description": "g0's global alpha value. If global alpha is used or there is no alpha channel in input g0, this value will be the global alpha for g0. BITMAP's alpha channel will be generated by using color palette if global alpha is not enabled.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "g0_global_chroma": {"acc": [0, 8], "size": [2], "description": "global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "g0_bitmap_color_palette": {"g0_bitmap_color_palette_alpha": {"acc": [0, 8], "size": [256], "description": "Alpha value palette for pic whose format is BITMAP.", "usage": "Effective palette array length depends on BITMAP bit-width", "constraints": "N/A"}, "g0_bitmap_color_palette_luma": {"acc": [0, 8], "size": [256], "description": "Luma value palette for pic whose format is BITMAP.", "usage": "Effective palette array length depends on BITMAP bit-width", "constraints": "N/A"}, "g0_bitmap_color_palette_chroma": {"acc": [0, 8], "size": [2, 256], "description": "Chroma value(with 128 bias) palette for pic whose format is BITMAP.", "usage": "Effective palette array length depends on BITMAP bit-width; g0_color_palette_chroma[0][x] is U in YUV; g0_color_palette_chroma[1][x] is V in YUV.", "constraints": "N/A"}}, "g0_up420T422_copy_enable": {"acc": [0, 1], "size": [], "description": "When input format is YUV420, this bit controls whether UV will be copied into YUV422.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "blending_enable": {"acc": [0, 1], "size": [], "description": "Blending unit enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "blending_coord": {"acc": [0, 16], "size": [2], "description": "Blending coord", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "blending_alpha0_out_mode": {"acc": [0, 1], "size": [], "description": "0 - v0 alpha not changed; 1 - v0 alpha inversed; Result is intermediate.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "blending_alpha1_out_mode": {"acc": [0, 1], "size": [], "description": "0 - gx alpha not changed; 1 - gx alpha inversed; Result is intermediate.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "blending_alpha_out_weight": {"acc": [0, 1, 8], "size": [], "description": "v0_alpha * (1 - weight) + gx_alpha * weight", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "blending_color_key_enable": {"acc": [0, 1], "size": [], "description": "Color key enable control bit", "usage": "0 - disable; 1 - enable", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "blending_alpha_select": {"acc": [0, 2], "size": [], "description": "Select alpha channel to be used in blending.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "blending_color_key_val": {"acc": [1, 8], "size": [3, 2], "description": "Color key color range for YUV.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "blending_color_key_inv": {"acc": [0, 1], "size": [], "description": "If enabled, pix whose color is outside of the color-key range will do the color-key process, rather than the one within the color range.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "ddr_write_adj_enable": {"acc": [0, 1], "size": [], "description": "After blending, result will be dumped into DDR. When this happens, this bit controls whether the result will be adjusted by using 3x3 matrix.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "ddr_write_format": {"acc": [0, 6], "size": [], "description": "DDR dump format", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "ddr_write_up422T444_copy_enable": {"acc": [0, 1], "size": [], "description": "To do 3x3 matrix adjustment, YUV422 pipe result should be converted into YUV444. This bit controls how to convert UV422 to UV444.", "usage": "0 - interpolation; 1 - direct copy", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "ddr_write_adj_matrix": {"acc": [1, 2, 8], "size": [3, 3], "description": "3x3 aadjustment matrix", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "ddr_write_adj_offset": {"acc": [1, 8], "size": [2, 3], "description": "in && out offset for DDR write adjustment.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "ddr_write_vfilter_enable": {"acc": [0, 1], "size": [], "description": "When output needs conversion from YUV422 to YUV420, this bit controls the down-sample approach.", "usage": "0 - direct decimation; 1 - filtering", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "ddr_write_decimation_ver": {"acc": [0, 1, 1], "size": [2], "description": "When output needs conversion from YUV422 to YUV420, this bit configs the down-sample filtering weights.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "dither_enable": {"acc": [0, 1], "size": [], "description": "dither enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "dither_seed_enable": {"acc": [0, 1], "size": [], "description": "dither seed enable bit.", "usage": "0 - use stat; 1 - use config", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "dither_seed_r": {"acc": [0, 16], "size": [2], "description": "dither seed for R channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "dither_seed_g": {"acc": [0, 16], "size": [2], "description": "dither seed for G channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "dither_seed_b": {"acc": [0, 16], "size": [2], "description": "dither seed for B channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "dither_pmask_r": {"acc": [0, 16], "size": [2], "description": "pmask for R channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "dither_pmask_g": {"acc": [0, 16], "size": [2], "description": "pmask for G channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "dither_pmask_b": {"acc": [0, 16], "size": [2], "description": "pmask for B channel", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}}}, "partition_configs": []}