{"enable": {"api": "b<PERSON><PERSON>ble", "display": "enable", "comments": "0: DIS Disable  1: DIS Enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "sw_calc_enable": {"api": "bSwCalcEnable", "display": "swCalcEnable", "comments": "0: use HW offset 1: use projection calculate offset", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "delay_frame_num": {"api": "nDelayFrameNum", "display": "delayFrameNum", "comments": "delay num, the more, the smoother. defalut 0. Accuracy: U8 Range: [0, 15]", "hint": "Accuracy: U8.0 Range: [0, 15]"}, "history_frame_num": {"api": "nHistoryFrameNum", "display": "historyFrameNum", "comments": "history frame, the more, the smoother", "hint": "Accuracy: U8.0 Range: [1, 16]"}, "crop_ratio": {"api": "nCropRatio", "display": "cropRatio", "comments": "crop_ratio for warped image", "hint": "Accuracy: U8.0 Range: [127, 255]"}, "sad_threshold": {"api": "nSadThr", "display": "sadThr", "comments": "threshord for valid offset", "hint": "Accuracy: U32.0 Range: [0, 65535]"}, "frame_pos_weights": {"api": "nFramePosWeights", "display": "framePosWeights", "comments": "the weight of history frame position for weighted sum", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "scale_ratio": {"api": "nScaleRatio", "display": "scaleRatio", "comments": "quant scale = (1 << scale ratio)", "hint": "Accuracy: U8.0 Range: [0, 16]"}}