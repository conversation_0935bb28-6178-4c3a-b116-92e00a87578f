{"enable": {"api": "b<PERSON><PERSON>ble", "display": "enable", "comments": "ive enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "mode": {"api": "nMode", "display": "mode", "comments": "Operator selection", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "sub_mode": {"api": "nSubMode", "display": "subMode", "comments": "Mode selection", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "var_threshold": {"api": "nVarThreshold", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "comments": "var_threshold multiplies model's var combined with background ratio to determine the standard used to judge if the pixel is background or not.", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "var_threshold_check": {"api": "nVarThresholdCheck", "display": "var<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comments": "var_threshold multiplies model's var to determine the standard used to judge if the model is a hit or not.", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "var0": {"api": "nVar0", "display": "var0", "comments": "The initial variance setting of new model if the weakest one needs replacing.", "hint": "Accuracy: U14.4 Range: [0, 262143]"}, "min_var": {"api": "nMinVar", "display": "minVar", "comments": "The smallest value of variance for updating Gaussian models.", "hint": "Accuracy: U14.4 Range: [0, 262143]"}, "max_var": {"api": "nMaxVar", "display": "maxVar", "comments": "The largest value of variance for updating Gaussian models.", "hint": "Accuracy: U14.4 Range: [0, 262143]"}, "lr": {"api": "nLr", "display": "lr", "comments": "Variable that determines model parameters updating speed", "hint": "Accuracy: U1.7 Range: [0, 255]"}, "background_ratio": {"api": "nBackgroundRatio", "display": "backgroundRatio", "comments": "Variable used to determine the model weight(sum) boundary for background and foreground. Since all Gaussian models are in descending order, the higher this value is, more Gaussian model will be treated as background.", "hint": "Accuracy: U1.7 Range: [0, 255]"}, "ct": {"api": "nCt", "display": "ct", "comments": "Variable used to further add recursive speed of model weight and used as a standard to delete redundant model.", "hint": "Accuracy: S1.7 Range: [-256, 255]"}, "set_thresh": {"api": "nSetThresh", "display": "<PERSON><PERSON><PERSON><PERSON>", "comments": "Set to be the thresh value.", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "filter_weight": {"api": "nFilterWeight", "display": "filterWeight", "comments": "Filter template", "hint": "Accuracy: S8.10 Range: [-262144, 262143]"}, "filter_stride": {"api": "nFilterStride", "display": "filterStride", "comments": "Filter stride / step", "hint": "Accuracy: U5.0 Range: [0, 31]"}, "filter_init": {"api": "nFilterInit", "display": "filterInit", "comments": "Filter initial coordinates", "hint": "Accuracy: U11.0 Range: [0, 2047]"}, "nms_low_threshold": {"api": "nNmsLowThreshold", "display": "nmsLowThreshold", "comments": "Weak edge lower gradient limit", "hint": "Accuracy: U11.0 Range: [0, 2047]"}, "nms_high_threshold": {"api": "nNmsHighThreshold", "display": "nmsHighThreshold", "comments": "Strong edge lower gradient limit", "hint": "Accuracy: U11.0 Range: [0, 2047]"}, "thresh_low_thr": {"api": "nThreshLowThr", "display": "threshLowThr", "comments": "Lower threshold", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "thresh_high_thr": {"api": "nThreshHighThr", "display": "threshHighThr", "comments": "Upper threshold", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "thresh_min_val": {"api": "nThreshMinVal", "display": "threshMinVal", "comments": "Filling value of pixel level 0(pixels lower than lower threshold) when used", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "thresh_mid_val": {"api": "nThreshMidVal", "display": "threshMidVal", "comments": "Filling value of pixel level 1(pixels between lower threshold and upper threshold) when used", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "thresh_max_val": {"api": "nThreshMaxVal", "display": "threshMaxVal", "comments": "Filling value of pixel level 2(pixels greater than lower threshold) when used", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "conv_16to8_gain": {"api": "nConv16to8Gain", "display": "conv16to8Gain", "comments": "Replace 'a/b' value used in above formula.", "hint": "Accuracy: S1.14 Range: [-32768, 32767]"}, "conv_16to8_bias": {"api": "nConv16To8Bias", "display": "conv16To8Bias", "comments": "Replace 'bias' value used in above formula.", "hint": "Accuracy: S15.0 Range: [-32768, 32767]"}, "weight_pic_0": {"api": "nWeightPic0", "display": "weightPic0", "comments": "Used in ADD mode to determine picture 0 weight.", "hint": "Accuracy: S7.8 Range: [-32768, 32767]"}, "weight_pic_1": {"api": "nWeightPic1", "display": "weightPic1", "comments": "Used in ADD mode to determine picture 1 weight.", "hint": "Accuracy: S7.8 Range: [-32768, 32767]"}, "lbp_thresh": {"api": "nLbpT<PERSON>esh", "display": "lbpThr<PERSON>", "comments": "Lbp thresh value", "hint": "Accuracy: S8.0 Range: [-256, 255]"}, "dma_h_gap": {"api": "nHGap", "display": "hGap", "comments": "Horizontal gap", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "dma_v_gap": {"api": "nVGap", "display": "vGap", "comments": "Vertical gap", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "dma_byte_num": {"api": "nByteNum", "display": "byteNum", "comments": "Choose how many bytes to read in each segment", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "dma_out_height": {"api": "nOutHeight", "display": "outHeight", "comments": "Output matrix height", "hint": "Accuracy: U11.0 Range: [0, 2047]"}, "dma_out_width": {"api": "nOutWidth", "display": "dmaVGap", "comments": "Output matrix width", "hint": "Accuracy: U11.0 Range: [0, 2047]"}, "dma_y0": {"api": "nY0", "display": "y0", "comments": "Starting point y coordinate for Direct copy", "hint": "Accuracy: U11.0 Range: [0, 2047]"}, "dma_x0": {"api": "nX0", "display": "x0", "comments": "Starting point x coordinate for Direct copy", "hint": "Accuracy: U11.0 Range: [0, 2047]"}, "dma_y1": {"api": "nY1", "display": "y1", "comments": "End point y coordinate for Direct copy", "hint": "Accuracy: U11.0 Range: [0, 2047]"}, "dma_x1": {"api": "nX1", "display": "x1", "comments": "End point x coordinate for Direct copy", "hint": "Accuracy: U11.0 Range: [0, 2047]"}, "dma_fill_data0": {"api": "nFillData0", "display": "fillData0", "comments": "Bytes0 in U64 which is to be filled into matrix", "hint": "Accuracy: U16.0 Range: [0, 65535]"}, "dma_fill_data1": {"api": "nFillData1", "display": "fillData1", "comments": "Bytes1 in U64 which is to be filled into matrix", "hint": "Accuracy: U16.0 Range: [0, 65535]"}, "dma_fill_data2": {"api": "nFillData2", "display": "fillData2", "comments": "Bytes2 in U64 which is to be filled into matrix", "hint": "Accuracy: U16.0 Range: [0, 65535]"}, "dma_fill_data3": {"api": "nFillData3", "display": "fillData3", "comments": "Bytes3 in U64 which is to be filled into matrix", "hint": "Accuracy: U16.0 Range: [0, 65535]"}, "bernsen_cmin": {"api": "nBernsenCMin", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "comments": "Bernsen local contrast threshold", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "bernsen_thresh2": {"api": "nBernsenCMin", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "comments": "The threshold value to judge corner based on corner response", "hint": "Accuracy: U8.0 Range: [0, 255]"}}