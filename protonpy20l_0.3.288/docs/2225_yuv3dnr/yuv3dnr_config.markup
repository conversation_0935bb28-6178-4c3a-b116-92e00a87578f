h2. Conf list
h3. yuv_3dnr
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| tf_y_en | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| tf_uv_en | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| sf_gau_y_en | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| sf_gau_uv_en | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| sf_lap_y_en | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| sf_lap_uv_en | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| gf_uv_en | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| debug_mode | u4 | [\] | - | [0, 13\] |  |  |
| debug_gain | u3 | [\] | - |  | debug bit shift for data visualization |  |
| init_ref | u1 | [\] | - |  | 0: no, 1: yes |  |
| init_tweight | u1 | [\] | - |  | 0: no, 1: yes |  |
| init_coef_ab | u1 | [\] | - |  | 0: no, 1: yes |  |
| ext_mask_en | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| ext_mask_mode | u1 | [\] | - |  | 0: motion, 1: hdr |  |
| ext_mask_thre | u1.7 | [\] | - | ext_mask_thre <= 1.0 | adjust by ext_mask |  |
| ext_mask_slope | u8.8 | [\] | - |  | adjust by ext_mask |  |
| ext_mask_ratio | u1.7 | [\] | - |  | ratio adjust by ext_mask |  |
| motion_small_win_sel | u1 | [\] | - | motion_large_win_sel >= motion_small_sel_ratio | motion feature small win sel, 0:3_7, 1:7_11 |  |
| motion_small_sel_ratio | u1.7 | [\] | - | motion_small_sel_ratio <= 1.0 | motion feature small select ratio |  |
| motion_large_win_sel | u1 | [\] | - | motion_large_win_sel >= motion_small_sel_ratio | motion feature large win sel, 0:3_7, 1:7_11 |  |
| motion_large_sel_ratio | u1.7 | [\] | - | motion_large_sel_ratio <= 1.0 | motion feature large select ratio |  |
| motion_feature_gauss_str | u1.7 | [\] | - | motion_feature_gauss_str <= 1.0 | motion feature gauss str |  |
| motion_sad_weight_th | u1.7 | [2\] | - | motion_sad_weight_th[1\] >= motion_sad_weight_th[0\], motion_sad_weight_th <= 1.0 | sad weight | linearClamp((sad-sd).clip(min=0), motion_sad_sd_th, motion_sad_weight_slope, [motion_sad_weight_th[0\], motion_sad_weight_th[1\]\]) |
| motion_sad_weight_slope | u1.8 | [\] | - |  | sad weight slope | linearClamp((sad-sd).clip(min=0), motion_sad_sd_th, motion_sad_weight_slope, [motion_sad_weight_th[0\], motion_sad_weight_th[1\]\]) |
| motion_sad_sd_th | u8 | [\] | - |  | sad sub sd th | linearClamp((sad-sd).clip(min=0), motion_sad_sd_th, motion_sad_weight_slope, [motion_sad_weight_th[0\], motion_sad_weight_th[1\]\]) |
| motion_highlight_diff_th | u8.2 | [\] | - |  | motion sharpen function diff th |  |
| motion_highlight_luma_th | u8.0 | [\] | - |  | motion sharpen function luma th |  |
| motion_highlight_luma_slope | u2.8 | [\] | - |  | motion sharpen function luma slope |  |
| motion_highlight_var_th | u7.0 | [\] | - |  | motion sharpen function var th |  |
| motion_highlight_var_slope | u2.7 | [\] | - |  | motion sharpen function var slope |  |
| motion_sharpen_str | u3.5 | [\] | - |  | motion sharpen str |  |
| motion_red_center | s7.2 | [2\] | - |  |  |  |
| motion_red_radius | u8.2 | [2\] | - |  |  |  |
| motion_red_dist_mode | u1.0 | [\] | - |  | 0: avg, 1:max |  |
| motion_red_dist_weight | u1.3 | [4\] | - |  |  |  |
| motion_red_th | u8.0 | [\] | - |  |  | linearClamp(red_dist, motion_red_th, motion_red_slope, 0.0, 1.0) |
| motion_red_slope | u1.8 | [\] | - |  |  | linearClamp(red_dist, motion_red_th, motion_red_slope, 0.0, 1.0) |
| motion_y_red_correct_str | u3.5 | [\] | - |  |  |  |
| motion_uv_red_correct_str | u3.5 | [\] | - |  |  |  |
| motion_blue_center | s7.2 | [2\] | - |  |  |  |
| motion_blue_radius | u8.2 | [2\] | - |  |  |  |
| motion_blue_dist_mode | u1.0 | [\] | - |  | 0: avg, 1:max |  |
| motion_blue_dist_weight | u1.3 | [4\] | - |  |  |  |
| motion_blue_th | u8.0 | [\] | - |  |  | linearClamp(blue_dist, motion_blue_th, motion_blue_slope, 0.0, 1.0) |
| motion_blue_slope | u1.8 | [\] | - |  |  | linearClamp(blue_dist, motion_blue_th, motion_blue_slope, 0.0, 1.0) |
| motion_y_blue_correct_str | u3.5 | [\] | - |  |  |  |
| motion_uv_blue_correct_str | u3.5 | [\] | - |  |  |  |
| motion_noise_y_lut | u8.2 | [9\] | - |  | noise in motion_y |  |
| motion_noise_uv_lut | u8.2 | [9\] | - |  | noise in motion_uv |  |
| motion_hvs_y_lut | u3.5 | [9\] | - |  | weber law in motion_y |  |
| motion_hvs_uv_lut | u3.5 | [9\] | - |  | weber law in motion_uv |  |
| motion_sad_sd_lw_adjust | u1.7 | [\] | - | motion_sad_sd_lw_adjust <= 1.0 | adjust noise in large window size |  |
| motion_dec_y_th | u8 | [\] | - |  | motion dec th in y channel | linearClamp(motion_level_y, motion_dec_y_th, motion_dec_y_slope, [0, 1\]) |
| motion_dec_y_slope | u1.8 | [\] | - |  | motion dec slope in y channel | linearClamp(motion_level_y, motion_dec_y_th, motion_dec_y_slope, [0, 1\]) |
| motion_m2s_dec_y_th | u8 | [\] | - |  | motion m2s dec th in y channel | linearClamp(motion_level_y, motion_dec_y_th, motion_dec_y_slope, [0, 1\]) |
| motion_m2s_dec_y_slope | u1.8 | [\] | - |  | motion m2s dec slope in y channel | linearClamp(motion_level_y, motion_dec_y_th, motion_dec_y_slope, [0, 1\]) |
| motion_hdr_adjust_y_th | u8.0 | [\] | - |  | motion adjust by hdr_mask |  |
| motion_dec_uv_th | u8 | [\] | - |  | motion dec th in uv channel | linearClamp(motion_level_uv, motion_dec_uv_th, motion_dec_uv_slope, [0, 1\]) |
| motion_dec_uv_slope | u1.8 | [\] | - |  | motion dec slope in uv channel | linearClamp(motion_level_uv, motion_dec_uv_th, motion_dec_uv_slope, [0, 1\]) |
| motion_adj_uv_th | u8 | [\] | - |  | motion adjust th in uv channel | linearClamp(motion_level_uv, motion_adj_uv_th, motion_adj_uv_slope, [0, 1\]) |
| motion_adj_uv_slope | u1.8 | [\] | - |  | motion adjust slope in uv channel | linearClamp(motion_level_uv, motion_adj_uv_th, motion_adj_uv_slope, [0, 1\]) |
| motion_hdr_adjust_uv_th | u8.0 | [\] | - |  | uv motion adjust by hdr_mask |  |
| ratio_uv_limit | u1.7 | [\] | - | ratio_uv_limit <= 1.0 | uv tweight limit |  |
| ratio_uv_adjust_th | u1.7 | [\] | - | uv_adjust_th <= 1.0 | uv motion th to adjust tweight |  linearClamp(motion_mask_uv_adjust, ratio_uv_adjust_th, ratio_uv_adjust_slope, [0.0, ratio_uv_adjust\]) |
| ratio_uv_adjust_slope | u4.8 | [\] | - |  | uv motion slope to adjust tweight |  linearClamp(motion_mask_uv_adjust, ratio_uv_adjust_th, ratio_uv_adjust_slope, [0.0, ratio_uv_adjust\]) |
| ratio_uv_adjust | u1.7 | [\] | - | uv_adjust_ratio <= 1.0 | uv motion to adjust tweight ratio |  linearClamp(motion_mask_uv_adjust, ratio_uv_adjust_th, ratio_uv_adjust_slope, [0.0, ratio_uv_adjust\]) |
| sf_gau_y_ref_weight_th | u1.7 | [\] | - | sf_gau_y_ref_weight_th <= 1.0 | tweight th to sf_guide_y |  linearClamp(tweight_mask, sf_gau_y_ref_weight_th, sf_gau_y_ref_weight_slope, [sf_gau_y_ref_ratio_limit, 1\]) |
| sf_gau_y_ref_weight_slope | u4.8 | [\] | - |  | tweight slope to sf_guide_y |  linearClamp(tweight_mask, sf_gau_y_ref_weight_th, sf_gau_y_ref_weight_slope, [sf_gau_y_ref_ratio_limit, 1\]) |
| sf_gau_y_ref_ratio_limit | u1.7 | [\] | - | sf_gau_y_ref_ratio_limit <= 1.0 | ratio limit to sf_guide_y |  linearClamp(tweight_mask, sf_gau_y_ref_weight_th, sf_gau_y_ref_weight_slope, [sf_gau_y_ref_ratio_limit, 1\]) |
| sf_gau_y_cur_weight_th | u1.7 | [\] | - | sf_gau_y_cur_weight_th <= 1.0 | tweight th to sf_guide_y |  linearClamp(tweight_mask, sf_gau_y_cur_weight_th, sf_gau_y_cur_weight_slope, [sf_gau_y_cur_ratio_limit, 1\]) |
| sf_gau_y_cur_weight_slope | u4.8 | [\] | - |  | tweight slope to sf_guide_y |  linearClamp(tweight_mask, sf_gau_y_cur_weight_th, sf_gau_y_cur_weight_slope, [sf_gau_y_cur_ratio_limit, 1\]) |
| sf_gau_y_cur_ratio_limit | u1.7 | [\] | - | sf_gau_y_cur_ratio_limit <= 1.0 | ratio limit to sf_guide_y |  linearClamp(tweight_mask, sf_gau_y_cur_weight_th, sf_gau_y_cur_weight_slope, [sf_gau_y_cur_ratio_limit, 1\]) |
| sf_lap_y_ref_weight_th | u1.7 | [\] | - | sf_lap_y_ref_weight_th <= 1.0 | tweight th to sf_guide_y |  linearClamp(tweight_mask, sf_lap_y_ref_weight_th, sf_lap_y_ref_weight_slope, [sf_lap_y_ref_ratio_limit, 1\]) |
| sf_lap_y_ref_weight_slope | u4.8 | [\] | - |  | tweight slope to sf_guide_y |  linearClamp(tweight_mask, sf_lap_y_ref_weight_th, sf_lap_y_ref_weight_slope, [sf_lap_y_ref_ratio_limit, 1\]) |
| sf_lap_y_ref_ratio_limit | u1.7 | [\] | - | sf_lap_y_ref_ratio_limit <= 1.0 | tweight th to sf_guide_y |  linearClamp(tweight_mask, sf_lap_y_ref_weight_th, sf_lap_y_ref_weight_slope, [sf_lap_y_ref_ratio_limit, 1\]) |
| sf_lap_y_cur_weight_th | u1.7 | [\] | - | sf_lap_y_ref_weight_th <= 1.0 | tweight th to sf_guide_y |  linearClamp(tweight_mask, sf_lap_y_cur_weight_th, sf_lap_y_cur_weight_slope, [sf_lap_y_cur_ratio_limit, 1\]) |
| sf_lap_y_cur_weight_slope | u4.8 | [\] | - |  | tweight slope to sf_guide_y |  linearClamp(tweight_mask, sf_lap_y_cur_weight_th, sf_lap_y_cur_weight_slope, [sf_lap_y_cur_ratio_limit, 1\]) |
| sf_lap_y_cur_ratio_limit | u1.7 | [\] | - | sf_lap_y_cur_ratio_limit <= 1.0 | tweight th to sf_guide_y |  linearClamp(tweight_mask, sf_lap_y_cur_weight_th, sf_lap_y_cur_weight_slope, [sf_lap_y_cur_ratio_limit, 1\]) |
| sf_gau_uv_ref_weight_th | u1.7 | [\] | - | sf_gau_uv_ref_weight_th <= 1.0 | tweight th to sf_guide_uv |  linearClamp(tweight_mask, sf_gau_uv_ref_weight_th, sf_gau_uv_ref_weight_slope, [sf_gau_uv_ref_ratio_limit, 1\]) |
| sf_gau_uv_ref_weight_slope | u4.8 | [\] | - |  | tweight slope to sf_guide_uv |  linearClamp(tweight_mask, sf_gau_uv_ref_weight_th, sf_gau_uv_ref_weight_slope, [sf_gau_uv_ref_ratio_limit, 1\]) |
| sf_gau_uv_ref_ratio_limit | u1.7 | [\] | - | sf_gau_uv_ref_ratio_limit <= 1.0 | ratio limit to sf_guide_uv |  linearClamp(tweight_mask, sf_gau_uv_ref_weight_th, sf_gau_uv_ref_weight_slope, [sf_gau_uv_ref_ratio_limit, 1\]) |
| sf_gau_uv_cur_weight_th | u1.7 | [\] | - | sf_gau_uv_ref_weight_th <= 1.0 | tweight th to sf_guide_uv |  linearClamp(tweight_mask, sf_gau_uv_cur_weight_th, sf_gau_uv_cur_weight_slope, [sf_gau_uv_cur_ratio_limit, 1\]) |
| sf_gau_uv_cur_weight_slope | u4.8 | [\] | - |  | tweight slope to sf_guide_uv |  linearClamp(tweight_mask, sf_gau_uv_cur_weight_th, sf_gau_uv_cur_weight_slope, [sf_gau_uv_cur_ratio_limit, 1\]) |
| sf_gau_uv_cur_ratio_limit | u1.7 | [\] | - | sf_gau_uv_cur_ratio_limit <= 1.0 | ratio limit to sf_guide_uv |  linearClamp(tweight_mask, sf_gau_uv_cur_weight_th, sf_gau_uv_cur_weight_slope, [sf_gau_uv_cur_ratio_limit, 1\]) |
| sf_lap_uv_ref_weight_th | u1.7 | [\] | - | sf_lap_uv_ref_weight_th <= 1.0 | tweight th to sf_guide_uv |  linearClamp(tweight_mask, sf_lap_uv_ref_weight_th, sf_lap_uv_ref_weight_slope, [sf_lap_uv_ref_ratio_limit, 1\]) |
| sf_lap_uv_ref_weight_slope | u4.8 | [\] | - |  | tweight slope to sf_guide_uv |  linearClamp(tweight_mask, sf_lap_uv_ref_weight_th, sf_lap_uv_ref_weight_slope, [sf_lap_uv_ref_ratio_limit, 1\]) |
| sf_lap_uv_ref_ratio_limit | u1.7 | [\] | - | sf_lap_uv_ref_ratio_limit <= 1.0 | ratio limit to sf_guide_uv |  linearClamp(tweight_mask, sf_lap_uv_ref_weight_th, sf_lap_uv_ref_weight_slope, [sf_lap_uv_ref_ratio_limit, 1\]) |
| sf_lap_uv_cur_weight_th | u1.7 | [\] | - | sf_lap_uv_cur_weight_th <= 1.0 | tweight th to sf_guide_uv |  linearClamp(tweight_mask, sf_lap_uv_cur_weight_th, sf_lap_uv_cur_weight_slope, [sf_lap_uv_cur_ratio_limit, 1\]) |
| sf_lap_uv_cur_weight_slope | u4.8 | [\] | - |  | tweight slope to sf_guide_uv |  linearClamp(tweight_mask, sf_lap_uv_cur_weight_th, sf_lap_uv_cur_weight_slope, [sf_lap_uv_cur_ratio_limit, 1\]) |
| sf_lap_uv_cur_ratio_limit | u1.7 | [\] | - | sf_lap_uv_cur_ratio_limit <= 1.0 | ratio limit to sf_guide_uv |  linearClamp(tweight_mask, sf_lap_uv_cur_weight_th, sf_lap_uv_cur_weight_slope, [sf_lap_uv_cur_ratio_limit, 1\]) |
| tweight_sf_en | u1 | [\] | - |  | tweight snr, 0: bypass, 1: enable |  |
| tweight_str | u0.7 | [\] | - |  | tweight str |  |
| tweight_static_lut | u1.7 | [17\] | - | tweight_static_lut <= 1.0 | ref tweight to cur tweight in static areas |  |
| m2s_motion_cur | u1.7 | [\] | - | m2s_motion_cur <= 1.0 | cur motion th of transition zone |  |
| m2s_tweight_ref | u1.7 | [\] | - | m2s_tweight_ref <= 1.0 | ref tweight th of transition zone |  |
| tweight_cur_w | u1.7 | [\] | - | tweight_cur_w <= 1.0 | tweight tnr ratio |  |
| tweight_pre_limit | u1.7 | [\] | - | tweight_pre_limit <= 1.0 | tweight tnr limit th for pre tweight |  |
| tweight_max_win_sel | u2 | [\] | - |  | tweight max filter win sel, 0:1_3, 1:3_5, 2:5_7, 3:7_9 |  |
| tweight_max_ratio | u1.7 | [\] | - | tweight_max_ratio <= 1.0 | tweight max filter ratio |  |
| tweight_smooth_win_sel | u2 | [\] | - |  | tweight smooth filter win sel, 0:1_3, 1:3_5, 2:5_7, 3:7_9 |  |
| tweight_smooth_ratio | u1.7 | [\] | - | tweight_smooth_ratio <= 1.0 | When there is a maximum filtering, it is necessary to perform secondary smoothing on the results |  |
| tweight_sf_sd_y_th | u8 | [\] | - |  | sd th for tweight max filter | linearClamp(sd, tweight_sf_sd_y_th, tweight_sf_sd_y_slope, [0.0, 1.0\]) |
| tweight_sf_sd_y_slope | u1.8 | [\] | - |  | sd slope for tweight max filter | linearClamp(sd, tweight_sf_sd_y_th, tweight_sf_sd_y_slope, [0.0, 1.0\]) |
| tweight_sf_sad_y_th | u8 | [\] | - |  | sad th for tweight max filter | linearClamp(sad, tweight_sf_sad_y_th, tweight_sf_sad_y_slope, [0.0, 1.0\]) |
| tweight_sf_sad_y_slope | u1.8 | [\] | - |  | sad slope for tweight max filter | linearClamp(sad, tweight_sf_sad_y_th, tweight_sf_sad_y_slope, [0.0, 1.0\]) |
| tweight_sf_sign_y_th | u7 | [\] | - | tweight_sf_sign_y_th <= 81 | sign th for tweight max filter | linearClamp(sign, tweight_sf_sign_y_th, tweight_sf_sign_y_slope, [0.0, 1.0\]) |
| tweight_sf_sign_y_slope | u1.7 | [\] | - |  | sign slope for tweight max filter | linearClamp(sign, tweight_sf_sign_y_th, tweight_sf_sign_y_slope, [0.0, 1.0\]) |
| tweight_map_th | u1.7 | [\] | - |  | tweight mapping th for output_mask | linearClamp(tweight_ds2, tweight_map_th, tweight_map_slope, [0.0, 1.0\]) |
| tweight_map_slope | u7.7 | [\] | - |  | tweight mapping th for output_mask | linearClamp(tweight_ds2, tweight_map_th, tweight_map_slope, [0.0, 1.0\]) |
| sf_gau_y_edge_th | u8 | [\] | - |  | sf_gau edge det th | linearClamp(edge, sf_gau_y_edge_th, sf_gau_y_edge_slope, [0.0, 1.0\]) |
| sf_gau_y_edge_slope | u1.8 | [\] | - |  | sf_gau edge det slope | linearClamp(edge, sf_gau_y_edge_th, sf_gau_y_edge_slope, [0.0, 1.0\]) |
| sf_gau_y_detail_th | u8 | [\] | - |  | sf_gau detail det th | linearClamp(detail, sf_gau_y_detail_th, sf_gau_y_detail_slope, [0.0, 1.0\]) |
| sf_gau_y_detail_slope | u1.8 | [\] | - |  | sf_gau detail det slope | linearClamp(detail, sf_gau_y_detail_th, sf_gau_y_detail_slope, [0.0, 1.0\]) |
| sf_gau_y_sp_weight | u1.8 | [3, 15\] | - | [:,0\] + 2 \* ([:,6\] + [:,9\] + [:,12\]) + 4 \* ([:,1\] + [:,2\] + [:,3\] + [:,5\] + [:,7\] + [:,8\] + [:,10\] + [:,11\] + [:,13\] + [:,14\]) + 8 \* ([:,4\]) == 1.0 | sf_gau space weight, [0\]:flat, [1\]:detail, [2\]:edge |  |
| sf_gau_y_thres | u8.2 | [3\] | - |  | sf_gau str th, [0\]:flat, [1\]:detail, [2\]:edge |  |
| sf_gau_y_slope | u2.8 | [3\] | - |  | sf_gau str slope, [0\]:flat, [1\]:detail, [2\]:edge |  |
| sf_gau_y_thres_bias_lut | s6.2 | [9\] | - |  | adjust th based on luma |  |
| sf_gau_y_slope_gain_lut | u3.5 | [9\] | - |  | adjust slope based on luma |  |
| sf_gau_y_sad_alpha | u1.5 | [3\] | - | sf_gau_y_sad_alpha <= 1.0 | sad block alpha, [0\]:flat, [1\]:detail, [2\]:edge | sad_block \* sf_gau_y_sad_alpha + sad_point \* (1 - sf_gau_y_sad_alpha) |
| sf_gau_y_hdr_adjust_th | u8.2 | [\] | - |  | sf gau hdr adjust th |  |
| sf_gau_y_ori_str | u8.4 | [\] | - |  | blend ori str |  |
| sf_lap_y_edge_th | u8 | [\] | - |  | sf_lap edge det th | linearClamp(edge, sf_lap_y_edge_th, sf_lap_y_edge_slope, [0.0, 1.0\]) |
| sf_lap_y_edge_slope | u1.8 | [\] | - |  | sf_lap edge det slope | linearClamp(edge, sf_lap_y_edge_th, sf_lap_y_edge_slope, [0.0, 1.0\]) |
| sf_lap_y_detail_th | u8 | [\] | - |  | sf_gau detail det th |  |
| sf_lap_y_detail_slope | u1.8 | [\] | - |  | sf_lap detail det slope | linearClamp(detail, [sf_lap_y_detail_th[0\], sf_lap_y_detail_th[1\]\], sf_lap_y_detail_slope, [0.0, 1.0\]) |
| sf_lap_y_sp_weight | u1.8 | [3, 6\] | - | [:,0\] + 4 \* ([:,1\] + [:,2\] + [:,3\] + [:,5\]) + 8 \* ([:,4\]) == 1.0 | sf_lap space weight, [0\]:flat, [1\]:detail, [2\]:edge |  |
| sf_lap_y_thres | u8.2 | [3\] | - |  | sf_lap str th, [0\]:flat, [1\]:detail, [2\]:edge |  |
| sf_lap_y_slope | u2.8 | [3\] | - |  | sf_lap str slope, [0\]:flat, [1\]:detail, [2\]:edge |  |
| sf_lap_y_thres_bias_lut | s6.2 | [9\] | - |  | adjust th based on luma |  |
| sf_lap_y_slope_gain_lut | u3.5 | [9\] | - |  | adjust slope based on luma |  |
| sf_lap_y_sad_alpha | u1.5 | [3\] | - | sf_lap_y_sad_alpha <= 1.0 | sad block alpha, [0\]:flat, [1\]:detail, [2\]:edge | sad_block \* sf_lap_y_sad_alpha + sad_point \* (1 - sf_lap_y_sad_alpha) |
| sf_lap_y_gauss_weight | u1.8 | [10\] | - | [0\] + 4 \* ([1\] + [2\] + [3\] + [5\] + [6\] + [9\]) + 8 \* ([4\] + [7\] + [8\]) == 1.0 | sf_lap gauss weight |  |
| sf_lap_y_gauss_ratio | u1.5 | [3\] | - | sf_lap_y_gauss_ratio <= 1.0 | fg blend gauss ratio, [0\]:flat, [1\]:detail, [2\]:edge | gauss \* sf_lap_y_gauss_ratio + output \* (1 - sf_lap_y_gauss_ratio) |
| sf_lap_y_hdr_adjust_th | u8.2 | [\] | - |  | sf lap hdr adjust th |  |
| sf_lap_y_ori_str | u8.4 | [\] | - |  | blend ori str |  |
| sf_gau_uv_th | u7.2 | [\] | - |  | sf_gau_uv str |  |
| sf_gau_uv_slope | u2.7 | [\] | - |  | sf_gau_uv slope |  |
| sf_gau_uv_limit | u1.7 | [\] | - | sf_gau_uv_limit <= 1.0 | sf_gau_uv limit |  |
| sf_gau_uv_thre_pre_lut | u8.2 | [9\] | - |  | sf_gau_uv th0 |  |
| sf_gau_uv_thre_lut | u8.2 | [9\] | - |  | sf_gau_uv th1 |  |
| sf_gau_uv_prot_thre_lut | u8.2 | [9\] | - |  | sf_gau_uv sat protect th |  |
| sf_gau_uv_prot_slope_lut | u2.8 | [9\] | - |  | sf_gau_uv sat protect slope |  |
| sf_gau_uv_hdr_adjust_th | u8.2 | [\] | - |  | sf gau uv hdr adjust th |  |
| sf_gau_uv_ori_ratio | u1.5 | [\] | - | uv_gau_ori_ratio <= 1.0 | blend ori ratio | input \* uv_gau_ori_ratio + output \* (1 - uv_gau_ori_ratio) |
| sf_lap_uv_thre | u8.2 | [\] | - |  | sf_lap_uv th |  |
| sf_lap_uv_slope | u2.8 | [\] | - |  | sf_lap_uv slope |  |
| sf_lap_uv_hdr_adjust_gain | u1.5 | [\] | - |  | sf gau uv hdr adjust gain |  |
| sf_lap_uv_ori_ratio | u1.5 | [\] | - | uv_lap_ori_ratio <= 1.0 | blend ori ratio | input \* uv_lap_ori_ratio + output \* (1 - uv_lap_ori_ratio) |
| m2s_var_th | u7 | [\] | - |  | motion to static detail recovery var th | linearClamp(var, m2s_var_th, m2s_var_slope, [0.0, m2s_blend_ratio\]) |
| m2s_var_slope | u1.11 | [2\] | - |  | motion to static detail recovery var slope, [0\]: lf, [1\]: hf | linearClamp(var, m2s_var_th, m2s_var_slope, [0.0, m2s_blend_ratio\]) |
| m2s_blend_ratio | u1.7 | [2\] | - | m2s_blend_ratio <= 1.0 | motion to static detail recovery blend ratio, [0\]: lf, [1\]: hf | linearClamp(var, m2s_var_th, m2s_var_slope, [0.0, m2s_blend_ratio\]) |
| gf_uv_diff_th | u8.0 | [\] | - |  | diff between guided filter results and normal UVNR output |  |
| gf_uv_sat_th | u7.0 | [\] | - |  | saturation th of UVNR output |  |
| gf_uv_sat_slope | u1.8 | [\] | - |  | saturation slope of UVNR output |  |
| gf_uv_sat_ratio | u1.7 | [2\] | - | gf_sat_ratio[1\] >= gf_sat_ratio[0\], gf_sat_ratio <= 1.0 | blend ratio according to saturation |  |
| gf_uv_blend_ratio_th | u1.7 | [\] | - | gf_uv_blend_ratio_th <= 1.0 | blend ratio according to ratio_uv |  |
| gf_uv_blend_ratio_slope | s7.7 | [\] | - |  | ratio slope of UVNR output |  |
| gf_uv_blend_ratio | u1.7 | [2\] | - | gf_uv_blend_ratio <= 1.0 | gf blend ratio of UVNR output |  |
| norm_ratio_8x | u1.12 | [3\] | - |  | norm ratio in 16x down sample, [0\]:right, [1\]:down, [2\]:lower right corner |  |
| ptn_roi_t_b_l_r | u14 | [4\] | support | 0<=roi_t_b_l_r<=(pic.h,pic.w); roi_t_b_l_r % 2 == 0; roi_t_b_l_r[0\] < roi_t_b_l_r[1\]; roi_t_b_l_r[2\] < roi_t_b_l_r[3\] | roi zone |  |
| pic_h_w | u14 | [2\] | - | pic_h_w[0\] % 4 == 0; pic_h_w[1\] % 4 == 0 | pic_h_w |  |
| ptn_h_w | u14 | [2\] | support | ptn_h_w <= pic_h_w; ptn_h_w[0\] % 4 == 0; ptn_h_w[1\] % 4 == 0 | partition_h_w |  |
| ptn_offset_h_w | u14 | [2\] | support | ptn_offset_h_w[0\] % 2 == 0; ptn_offset_h_w[0\] + ptn_h_w[0\] <= pic_h_w[0\]; ptn_offset_h_w[1\] % 2 == 0; ptn_offset_h_w[1\] + ptn_h_w[1\] <= pic_h_w[1\] | start offset of the partition |  |
| dither_enable | u1 | [\] | - |  | dither enable | 0: disable, 1: enable |
| dither_seed_enable | u1 | [\] | - |  | dither seed enable | 0: disable, 1: enable |
| dither_pmask_uv | u16 | [2\] | - |  | dither pmask uv | dither pmask, use fixed value |
| dither_seed_uv | u16 | [2\] | - |  | dither seed | dither seed, use fixed value |
| tf_converg_offset | u0.8 | [2\] | - | [0, 0.25\] | tf converg offset, [0\]:cur, [1\]:ref | tf merge fixed-point progress |

h3. ifa
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | N/A | enable control bit | 0 - disable; 1 - enable |
| mode | u4 | [\] | - | N/A | Select ifa operator to run | 0 - guided filter |
| sub_mode | u4 | [\] | - | N/A | Select sub_mode inside ifa operator | If selected ifa operator has serval sub-mode, use this register to select it. |
| output_hor_crop_enable | u1 | [\] | - | N/A | output horizontal crop enable control bit | 0 - disable; 1 - enable |
| output_hor_crop_st | u12 | [\] | support | N/A | output picture's horizontal crop start coordinates | out_hor_crop_st[n\] for pics[n\] |
| output_hor_crop_width | u12 | [\] | support | N/A | output picture's horizontal crop width | out_hor_crop_width[n\] for pics[n\] |
| guided_enable | u1 | [\] | - | N/A | guided filter enable control bit | 0 - disable; 1 - enable |
| guided_dehaze_dc_adj_enable | u1 | [\] | - | N/A | When used for dehaze, this bit controls whether adjust dark channel using coeff k before calculating guided filter.. | If enabled, dc = clip(1 - dc \* k, 0, 1.0) |
| guided_dehaze_dc_k | s4.12 | [\] | - | N/A | k value in dehaze logic | clip(b - dc \* k, 0, 1.0) |
| guided_dehaze_dc_b | u1.12 | [\] | - | N/A | b value in dehaze logic | clip(b - dc \* k, 0, 1.0) |
| guided_win_r | u3 | [\] | - | N/A | 2 \* guided_win_r + 1 == window size | Example: 5x5 --> 2 \* 2 + 1 |
| guided_epsilon | u16 | [\] | - | N/A | It's u16 form for various acc epsilon; For rltm, it's u0.16; For dehaze, it's u12.4; For UVNR, it's u0.16; The guided_epsilon value in guided filter formula. | coeff_a = cov_ip / (var_i + guided_epsilon \* guided_epsilon) |
| blur_enable | u1 | [\] | - | N/A | guided filter enable control bit | 0 - disable; 1 - enable |
| blur_weight | s5.10 | [3, 3\] | - |  | 3 x 3 filter weight; Symmetric parameter; Effective 5 x 5 filter weight. | Use upper-left corner 3x3 parameter to init internal 5x5 filter weight symmetrically. |

