{"context": {"csc_matrix": {"acc": [1, 2, 10], "size": [3, 3], "type": "AX_S16", "comment": "csc matrix", "default": [[306, 601, 117], [-173, -339, 512], [512, -429, -83]]}, "AN_ID": {"size": [], "acc": [0, 16], "comment": "CSC is 0x2244", "type": "AX_U16", "default": "0x2244"}}, "params": {"csc_mode": {"acc": [0, 2], "auto": 0, "comment": "csc mode, 0:YUV422->YUV420, 1:RGB->YUV422, 2:RGB->YUV420", "default": 2, "display": "csc mode", "hidden": 1, "range": [0, 2], "size": [], "target_conf": ["csc.input_type", "csc.output_type"], "type": "AX_U8", "dependency": "common"}, "csc_conv_mode": {"acc": [0, 4], "auto": 0, "comment": "csc conversion mode, 0:BT601, 1:BT709, 2:BT2020, 3:user define", "default": 1, "display": "csc_conv_mode", "hidden": 0, "range": [0, 3], "size": [], "target_conf": ["csc.matrix"], "type": "AX_U8", "dependency": " ", "enum_field": {"0": "BT601", "1": "BT709", "2": "BT2020", "3": "USER"}}, "csc_matrix": {"acc": [1, 2, 10], "auto": 0, "comment": "csc conversion matrix, valid only for csc_conv_mode = 3", "default": [[0.298828125, 0.5869140625, 0.1142578125], [-0.1689453125, -0.3310546875, 0.5], [0.5, -0.4189453125, -0.0810546875]], "display": "csc_matrix", "hidden": 0, "range": [-4.0, 3.9990234375], "size": [3, 3], "target_conf": ["csc.matrix"], "type": "AX_S16", "dependency": " "}, "csc_offset_in": {"acc": [1, 8, 2], "auto": 0, "comment": "csc input offset, need not tune", "default": 16.0, "display": "csc_offset_in", "hidden": 1, "range": [-256.0, 255.75], "size": [], "target_conf": ["csc.offset_in"], "type": "AX_S16", "dependency": "common"}, "csc_offset_out": {"acc": [1, 8, 2], "auto": 0, "comment": "csc out offset, need not tune", "default": 16.0, "display": "csc_offset_out", "hidden": 1, "range": [-256.0, 255.75], "size": [], "target_conf": ["csc.offset_out"], "type": "AX_S16", "dependency": "common"}}, "submodules": {"setup": {"params": ["csc_mode", "csc_offset_in", "csc_offset_out"], "configs": ["csc.enable", "csc.decimation_h", "csc.decimation_v", "csc.input_type", "csc.output_type"]}, "core": {"params": ["csc_conv_mode", "csc_matrix"], "configs": []}}, "target_module": {"mc20l": {"csc": {"id": 5000, "method": 0}}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "structs": {}, "configs": {"csc": {"enable": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "input_type": {"acc": [0, 1], "size": [], "description": "0: YUV422, 1: RGB", "usage": "set 1 as default", "constraints": "all", "type": "AX_U8", "partition": "-"}, "output_type": {"acc": [0, 1], "size": [], "description": "0: YUV422, 1: YUV420", "usage": "set 1 as default", "constraints": "all", "type": "AX_U8", "partition": "-"}, "matrix": {"acc": [1, 2, 10], "size": [3, 3], "description": "csc conversion matrix", "usage": "set to the calculation result of chroma matrix and color space standards such as BT601, BT709, BT2020.we can convert the range(limited or full) by this matrix. but it's not recommended", "constraints": "all", "type": "AX_S16", "partition": "-"}, "offset_in": {"acc": [1, 8, 2], "size": [3], "description": "minus offset from input", "usage": "normally set [16,16,16]", "constraints": "all", "type": "AX_S16", "partition": "-"}, "offset_out": {"acc": [1, 8, 2], "size": [3], "description": "add offset from output", "usage": "normally set [16,0,0]", "constraints": "all", "type": "AX_S16", "partition": "-"}, "decimation_h": {"acc": [1, 1, 5], "size": [7], "description": "YUV444 to YUV422 horizontal chroma subsampling filter coef", "usage": "set [0.0, 0.0, 0.0, 0.5, 0,5, 0, 0] as default", "constraints": "sum equal to 1", "type": "AX_S8", "partition": "-"}, "decimation_v": {"acc": [0, 1, 1], "size": [2], "description": "YUV422 to YUV420 vertical chroma  subsampling filter coef", "usage": "set [0.5, 0,5]", "constraints": "sum equal to 1", "type": "AX_U8", "partition": "-"}}}, "partition_configs": {}}