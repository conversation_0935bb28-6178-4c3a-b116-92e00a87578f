{"autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "configs": {"blc": {"det_window": {"acc": [0, 14], "size": [4], "description": "bl_det window, in [y, x, height, width] order", "type": "AX_U16"}, "active_region": {"acc": [0, 14], "size": [4], "description": "bl_det active region area, in [y, x, height, width] order", "type": "AX_U16"}, "channel_id": {"acc": [0, 1], "size": [], "description": "blc channel_id, 0: SDR/HDR_Main, 1: HDR_Sub", "type": "AX_U8"}, "offset_out": {"acc": [0, 8, 6], "size": [], "description": "blc offset_out", "type": "AX_U16"}, "sensor_bl": {"acc": [0, 8, 10], "size": [], "description": "blc sensor black level", "type": "AX_U32"}, "dither_seed_enable": {"acc": [0, 1], "size": [], "description": "blc dither_seed_enable", "type": "AX_U8"}}, "dpc": {"offset_in": {"acc": [0, 8, 6], "size": [], "description": "dpc offset_in", "type": "AX_U16"}, "offset_out": {"acc": [0, 8, 6], "size": [], "description": "dpc offset_out", "type": "AX_U16"}, "shot_noise_coefs": {"acc": [1, 0, 31], "size": [4, 2], "description": "dpc shot_noise_coeffs", "type": "AX_S32"}, "read_noise_coefs": {"acc": [1, 0, 31], "size": [4, 3], "description": "dpc read_noise_coeffs", "type": "AX_S32"}, "partition_info": {"type": "ax_isp_ptn_info_t", "size": [], "description": "dpc partition_info"}, "wb_gain": {"acc": [0, 4, 8], "size": [4], "description": "dpc wb_gain", "type": "AX_U16"}, "again": {"acc": [0, 22, 10], "size": [], "description": "dpc again", "type": "AX_U32"}}, "hdr": {"use_isp_hdr": {"acc": [0, 1], "size": [], "description": "hdr use_isp_hdr", "type": "AX_U8"}, "offset_in": {"acc": [0, 8, 6], "size": [2], "description": "hdr offset_in", "type": "AX_U16"}, "offset_out": {"acc": [0, 14, 6], "size": [], "description": "hdr offset_out", "type": "AX_U32"}, "wb_gain": {"acc": [0, 4, 12], "size": [4], "description": "hdr wb_gain", "type": "AX_U16"}, "exposure_ratio": {"acc": [0, 9, 8], "size": [], "description": "hdr exposure_ratio", "type": "AX_U32"}, "internal_lf_indicator": {"acc": [0, 1], "size": [], "description": "hdr internal_lf_indicator", "type": "AX_U8"}, "a_gain": {"acc": [0, 22, 10], "size": [], "description": "hdr again", "type": "AX_U32"}, "shot_noise_coef": {"acc": [1, 0, 31], "size": [4, 2], "description": "hdr shot_noise_coef", "type": "AX_S32"}, "read_noise_coef": {"acc": [1, 0, 31], "size": [4, 3], "description": "hdr read_noise_coef", "type": "AX_S32"}}, "ainr": {"hcg_mode": {"acc": [0, 1], "size": [], "description": "ainr hcg_mode, 0: lcg, 1: hcg", "type": "AX_U8"}, "hdr_ratio": {"acc": [0, 9, 8], "size": [], "description": "ainr hdr_ratio", "type": "AX_U32"}, "hdrc_input_max_value": {"acc": [0, 14, 6], "size": [], "description": "hdrc input_max_value", "type": "AX_U32"}, "hdrc_output_max_value": {"acc": [0, 10, 6], "size": [], "description": "hdrc output_max_value", "type": "AX_U16"}, "hdrc_offset_in": {"acc": [0, 14, 6], "size": [], "description": "hdrc offset_in", "type": "AX_U32"}, "hdrc_offset_out": {"acc": [0, 10, 6], "size": [], "description": "hdrc offset_out", "type": "AX_U16"}, "hdrc_dither_enable": {"acc": [0, 1], "size": [], "description": "hdrc dither_enable", "type": "AX_U8"}, "hdrc_dither_seed_enable": {"acc": [0, 1], "size": [], "description": "hdrc dither_seed_enabl", "type": "AX_U8"}, "hidrc_offset_in": {"acc": [0, 10, 6], "size": [], "description": "hidrc offset_in", "type": "AX_U16"}, "hidrc_offset_out": {"acc": [0, 14, 6], "size": [], "description": "hidrc offset_out", "type": "AX_U32"}, "hidrc_input_max_value": {"acc": [0, 10, 6], "size": [], "description": "hidrc input_max_value", "type": "AX_U16"}, "hidrc_output_max_value": {"acc": [0, 14, 6], "size": [], "description": "hidrc output_max_value", "type": "AX_U32"}}, "rawnr": {"offset_in": {"acc": [0, 14, 6], "size": [], "description": "rawnr offset_in", "type": "AX_U32"}, "offset_out": {"acc": [0, 14, 6], "size": [], "description": "rawnr offset_out", "type": "AX_U32"}, "a_gain": {"acc": [0, 8, 8], "size": [], "description": "rawnr again", "type": "AX_U16"}, "hdr_ratio": {"acc": [0, 9, 8], "size": [], "description": "rawnr hdr_ratio", "type": "AX_U32"}, "shot_noise_coeffs": {"acc": [1, 0, 31], "size": [4, 2], "description": "rawnr shot_noise_coef", "type": "AX_S32"}, "read_noise_coeffs": {"acc": [1, 0, 31], "size": [4, 3], "description": "rawnr read_noise_coef", "type": "AX_S32"}}, "lsc": {"offset_in": {"acc": [0, 14, 6], "size": [], "description": "lsc offset_in", "type": "AX_U32"}, "offset_out": {"acc": [0, 14, 6], "size": [], "description": "lsc offset_out", "type": "AX_U32"}, "partition_info": {"type": "ax_isp_ptn_info_t", "size": [], "description": "lsc par_info"}}, "wbc": {"d_gain": {"acc": [0, 10, 8], "size": [], "description": "wbc d_gain", "type": "AX_U32"}, "wbc_gain": {"acc": [0, 4, 8], "size": [4], "description": "wbc wbc_gain", "type": "AX_U16"}, "clip_level": {"acc": [0, 14, 6], "size": [], "description": "wbc clip_level", "type": "AX_U32"}, "offset_in": {"acc": [0, 14, 6], "size": [], "description": "wbc offset_in", "type": "AX_U32"}, "offset_out": {"acc": [0, 14, 6], "size": [], "description": "wbc offset_out", "type": "AX_U32"}}, "rltm": {"offset_in": {"acc": [0, 14, 6], "size": [], "description": "rltm offset_in", "type": "AX_U32"}, "offset_out": {"acc": [0, 8, 4], "size": [], "description": "rltm offset_out", "type": "AX_U16"}, "hdr_ratio": {"acc": [0, 9, 8], "size": [], "description": "rltm hdr_ratio", "type": "AX_U32"}, "input_max_value": {"acc": [0, 14, 6], "size": [], "description": "rltm input_max_value", "type": "AX_U32"}, "detail_low_reset_coeffab": {"acc": [0, 1], "size": [], "description": "rltm detail_low_reset_coeffab", "type": "AX_U8"}, "dither_seed_enable": {"acc": [0, 1], "size": [], "description": "rltm dither_seed_enable", "type": "AX_U8"}, "pic_h_w": {"acc": [0, 15], "size": [2], "description": "rltm pic_h_w", "type": "AX_U8"}, "partition_info": {"type": "ax_isp_ptn_info_t", "size": [], "description": "rltm partition_info"}, "ifa_partition_edge_width": {"type": "AX_U16", "size": [], "acc": [0, 16], "description": "rltm ifa partition edge width"}, "ifa_naked_partition_width": {"type": "AX_U16", "size": [], "acc": [0, 16], "description": "rltm ifa naked partition width"}}, "depurple": {"offset_in": {"acc": [0, 8, 4], "size": [], "description": "depurple offset_in", "type": "AX_U16"}, "partition_info": {"type": "ax_isp_ptn_info_t", "size": [], "description": "depurple partition_info"}}, "dem": {"partition_info": {"type": "ax_isp_ptn_info_t", "size": [], "description": "dem partition_info"}, "init_enable": {"acc": [0, 1], "size": [], "description": "dem init_enable", "type": "AX_U8"}, "offset_in": {"acc": [0, 8, 4], "size": [], "description": "dem offset_in", "type": "AX_U16"}, "offset_out": {"acc": [0, 8, 4], "size": [], "description": "dem offset_out", "type": "AX_U16"}, "wb_gain": {"acc": [0, 4, 8], "size": [4], "description": "dem wb_gain", "type": "AX_U16"}}, "clc": {"offset_in": {"acc": [0, 8, 4], "size": [], "description": "clc offset_out", "type": "AX_U16"}, "offset_out": {"acc": [0, 8, 4], "size": [], "description": "clc offset_out", "type": "AX_U16"}, "sat_strength": {"size": [], "description": "clc sat_strength, from awb algorithm", "type": "AX_F32"}}, "gam": {"dither_seed_enable": {"acc": [0, 1], "size": [], "description": "gamma dither_seed_enable", "type": "AX_U8"}, "offset_in": {"acc": [0, 8, 4], "size": [], "description": "gamma offset_in", "type": "AX_U16"}, "offset_out": {"acc": [0, 8, 2], "size": [], "description": "gamma offset_out", "type": "AX_U16"}}, "dehaze": {"init_enable": {"acc": [0, 1], "size": [], "description": "dehaze init_enable", "type": "AX_U8"}, "offset_in": {"acc": [0, 8, 2], "size": [], "description": "dehaze offset_in", "type": "AX_U16"}, "offset_out": {"acc": [0, 8, 2], "size": [], "description": "dehaze offset_out", "type": "AX_U16"}, "dehaze_partition_info": {"type": "ax_isp_ptn_info_t", "size": [], "description": "dehaze dehaze_partition_info"}, "ifa_partition_edge_width": {"type": "AX_U16", "size": [], "acc": [0, 16], "description": "dehaze ifa partition edge width"}, "ifa_naked_partition_width": {"type": "AX_U16", "size": [], "acc": [0, 16], "description": "dehaze ifa naked partition width"}}, "csc": {"csc_mode": {"acc": [0, 2], "size": [], "description": "csc csc_mode", "type": "AX_U8"}, "offset_in": {"acc": [1, 8, 2], "size": [], "description": "csc offset_in", "type": "AX_S16"}, "offset_out": {"acc": [1, 8, 2], "size": [], "description": "csc offset_out", "type": "AX_S16"}}, "yuv3dnr": {"debug_mode": {"acc": [0, 4], "size": [], "description": "yuv3dnr debug_mode", "type": "AX_U8"}, "debug_gain": {"acc": [0, 3], "size": [], "description": "yuv3dnr debug_gain", "type": "AX_U8"}, "init_ref": {"acc": [0, 1], "size": [], "description": "yuv3dnr init_enable", "type": "AX_U8"}, "init_tweight": {"acc": [0, 1], "size": [], "description": "yuv3dnr init_tweight", "type": "AX_U8"}, "init_coef_ab": {"acc": [0, 1], "size": [], "description": "yuv3dnr init_coef_ab", "type": "AX_U8"}, "dither_seed_enable": {"acc": [0, 1], "size": [], "description": "yuv3dnr dither_seed_enable", "type": "AX_U8"}, "yuv3dnr_partition_info": {"type": "ax_isp_ptn_info_t", "size": [], "description": "yuv3dnr yuv3dnr_partition_info"}, "ifa_partition_edge_width": {"type": "AX_U16", "size": [], "acc": [0, 16], "description": "yuv3dnr ifa partition edge width"}, "ifa_naked_partition_width": {"type": "AX_U16", "size": [], "acc": [0, 16], "description": "yuv3dnr ifa naked partition width"}}, "sharpen": {"shp_debug_mode": {"acc": [0, 5], "size": [], "description": "sharpen shp_debug_mode", "type": "AX_U8"}, "disable_for_prev_debug": {"acc": [0, 1], "size": [], "description": "sharpen disable_for_prev_debug", "type": "AX_U8"}}, "y2dnr": {"debug_mode": {"acc": [0, 4], "size": [], "description": "y2dnr debug_mode", "type": "AX_U8"}, "debug_gain": {"acc": [0, 3], "size": [], "description": "y2dnr debug_gain", "type": "AX_U8"}, "pre_debug_enable": {"acc": [0, 1], "size": [], "description": "y2dnr pre_debug_enable", "type": "AX_U8"}, "dither_seed_enable": {"acc": [0, 1], "size": [], "description": "y2dnr dither_seed_enable", "type": "AX_U8"}}, "ycproc": {"ycrt_reset_uv_for_debug": {"acc": [0, 1], "size": [], "description": "ycproc shp_debug_mode", "type": "AX_U8"}}, "aaas": {"aes0_is_transposed": {"acc": [0, 1], "type": "AX_U8", "size": [], "description": "is aes in ITP"}, "aes0_hist_wl_gain": {"acc": [0, 1, 8], "type": "AX_U16", "size": [], "description": "white level used for compute histogram maximum compensation gain"}, "aes0_dgain": {"acc": [0, 10, 8], "type": "AX_U32", "size": [4], "description": "aes dgain for statics"}, "aes0_offset_in": {"acc": [0, 14, 6], "type": "AX_U32", "size": [], "description": "aes offset in"}, "aes0_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "description": "aes partition information"}, "aes1_is_transposed": {"acc": [0, 1], "type": "AX_U8", "size": [], "description": "is aes in ITP"}, "aes1_hist_wl_gain": {"acc": [0, 1, 8], "type": "AX_U16", "size": [], "description": "white level used for compute histogram maximum compensation gain"}, "aes1_dgain": {"acc": [0, 10, 8], "type": "AX_U32", "size": [4], "description": "aes dgain for statics"}, "aes1_offset_in": {"acc": [0, 14, 6], "type": "AX_U32", "size": [], "description": "aes offset in"}, "aes1_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "description": "aes partition information"}, "awbs_is_transposed": {"acc": [0, 1], "type": "AX_U8", "size": [], "description": "is awbs in ITP"}, "awbs_dgain": {"acc": [0, 10, 8], "type": "AX_U32", "size": [4], "description": "awbs dgain for statics"}, "awbs_offset_in": {"acc": [0, 14, 6], "type": "AX_U32", "size": [], "description": "awbs offset in"}, "awbs_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "description": "awbs partition information"}, "afs_is_transposed": {"acc": [0, 1], "type": "AX_U8", "size": [], "description": "is afs in ITP"}, "afs_wb_gain": {"acc": [0, 4, 8], "type": "AX_U16", "size": [4], "description": "afs wbgain"}, "afs_drc_mode": {"acc": [0, 8], "type": "AX_U8", "size": [], "description": "afs drc mode: 0: disable, 1: 8.4 -> 8.4(do nothing), 2: 8.4 -> 8.4 (gam), 3: 8.6 -> 8.4(MSB), 4: 8.6 -> 8.4(gam), 5: 14.6 -> 8.4(MSB) 6: 14.6 -> 8.4(gam)"}, "afs_dgain": {"acc": [0, 10, 8], "type": "AX_U32", "size": [4], "description": "afs dgain for statics"}, "afs_offset_in": {"acc": [0, 14, 6], "type": "AX_U32", "size": [], "description": "afs offset in"}, "afs_partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "description": "afs partition information"}}, "eisstat": {"offset_in": {"acc": [0, 8, 2], "size": [], "description": "eisstat offset_in", "type": "AX_U16"}, "partition_info": {"type": "ax_isp_ptn_info_t", "size": [], "description": "eisstat partition_info"}}}, "params": {"hcg_shot_noise_coef": {"acc": [1, 0, 31], "size": [4, 2], "range": [-1.0, 0.9999999995343387], "default": "np.zeros((4, 2)).tolist()", "display": "Hcg Shot Noise Coeffs", "comment": "Hcg Calibration Params", "hidden": 0, "auto": 0, "target_conf": [], "type": "AX_S32"}, "hcg_read_noise_coef": {"acc": [1, 0, 31], "size": [4, 3], "range": [-1.0, 0.9999999995343387], "default": "np.zeros((4, 3)).tolist()", "display": "Hcg Read Noise Coeffs", "comment": "Hcg Calibration Params", "hidden": 0, "auto": 0, "target_conf": [], "type": "AX_S32"}, "lcg_shot_noise_coef": {"acc": [1, 0, 31], "size": [4, 2], "range": [-1.0, 0.9999999995343387], "default": "np.zeros((4, 2)).tolist()", "display": "Lcg Shot Noise Coeffs", "comment": "Lcg Calibration Params", "hidden": 0, "auto": 0, "target_conf": [], "type": "AX_S32"}, "lcg_read_noise_coef": {"acc": [1, 0, 31], "size": [4, 3], "range": [-1.0, 0.9999999995343387], "default": "np.zeros((4, 3)).tolist()", "display": "Lcg Read Noise Coeffs", "comment": "Lcg Calibration Params", "hidden": 0, "auto": 0, "target_conf": [], "type": "AX_S32"}, "hcg_lcg_mode": {"acc": [0, 2], "size": [], "comment": "0: hcg, 1: lcg, 2: not supported (use lcg)", "hidden": 1, "auto": 0, "target_conf": [], "type": "AX_U8"}, "sen_black_level": {"acc": [0, 8, 6], "size": [], "hidden": 1, "auto": 0, "type": "AX_U32", "target_conf": []}, "again": {"acc": [0, 22, 10], "size": [], "hidden": 1, "auto": 0, "target_conf": [], "type": "AX_U32"}, "dgain": {"acc": [0, 10, 8], "size": [], "hidden": 1, "auto": 0, "type": "AX_U32", "target_conf": []}, "wb_gain": {"acc": [0, 4, 8], "size": [4], "hidden": 1, "auto": 0, "type": "AX_U16", "target_conf": []}, "exposure_ratio": {"acc": [0, 8, 8], "size": [], "hidden": 1, "auto": 0, "type": "AX_U16", "target_conf": []}, "use_isp_hdr": {"acc": [0, 1], "size": [], "hidden": 1, "auto": 0, "type": "AX_U8", "target_conf": []}, "lf_indicator": {"acc": [0, 1], "size": [], "hidden": 1, "auto": 0, "type": "AX_U8", "target_conf": ["hdr.internal_lf_indicator"]}, "img_partition_info": {"type": "ax_isp_ptn_info_t", "size": [], "hidden": 1, "auto": 0, "target_conf": []}, "ifa_partition_edge_width": {"type": "AX_U16", "acc": [0, 16], "size": [], "hidden": 1, "auto": 0, "target_conf": []}, "ifa_naked_partition_width": {"type": "AX_U16", "acc": [0, 16], "size": [], "hidden": 1, "auto": 0, "target_conf": []}, "is_first_frame": {"acc": [0, 1], "size": [], "hidden": 1, "auto": 0, "type": "AX_U8", "target_conf": []}, "blc_det_window": {"acc": [0, 14], "size": [4], "hidden": 1, "auto": 0, "comment": "bl_det window, in [y, x, height, width] order", "type": "AX_U16", "target_conf": ["blc.det_window"]}, "blc_active_region": {"acc": [0, 14], "size": [4], "hidden": 1, "auto": 0, "comment": "bl_det active region area, in [y, x, height, width] order", "type": "AX_U16", "target_conf": ["blc.active_region"]}, "blc_channel_id": {"acc": [0, 1], "size": [], "hidden": 1, "auto": 0, "comment": "blc channel_id, 0: SDR/HDR_Main, 1: HDR_Sub", "type": "AX_U8", "target_conf": ["blc.channel_id"]}, "clc_sat_strength": {"size": [], "hidden": 1, "auto": 0, "type": "AX_F32", "target_conf": ["clc.sat_strength"]}, "yuv3dnr_debug_mode": {"acc": [0, 4], "size": [], "hidden": 1, "auto": 0, "type": "AX_U8", "target_conf": []}, "yuv3dnr_debug_gain": {"acc": [0, 3], "size": [], "hidden": 1, "auto": 0, "type": "AX_U8", "target_conf": ["yuv3dnr.debug_gain"]}, "sharpen_debug_mode": {"acc": [0, 5], "size": [], "hidden": 1, "auto": 0, "type": "AX_U8", "target_conf": []}, "y2dnr_debug_mode": {"acc": [0, 4], "size": [], "hidden": 1, "auto": 0, "type": "AX_U8", "target_conf": []}, "y2dnr_debug_gain": {"acc": [0, 3], "size": [], "hidden": 1, "auto": 0, "type": "AX_U8", "target_conf": ["y2dnr.debug_gain"]}, "aestat0_input_mux": {"acc": [0, 2], "size": [], "range": [0, 2], "default": 1, "hidden": 0, "auto": 0, "type": "AX_U8", "target_conf": ["aaas.aes0_offset_in", "aaas.aes0_hist_wl_gain", "aaas.aes0_is_transposed"], "comment": "aestat0 access point, 0: RawScl0, 1: DPC0, 2: WBC, NO GUI"}, "aestat1_input_mux": {"acc": [0, 2], "size": [], "range": [0, 2], "default": 2, "hidden": 0, "auto": 0, "type": "AX_U8", "target_conf": ["aaas.aes1_offset_in", "aaas.aes1_hist_wl_gain", "aaas.aes1_is_transposed"], "comment": "aestat1 access point, 0: RawScl1, 1: DPC1, 2: W<PERSON>, NO GUI"}, "awbstat_input_mux": {"acc": [0, 3], "size": [], "range": [0, 4], "default": 4, "hidden": 0, "auto": 0, "type": "AX_U8", "target_conf": ["aaas.awbs_offset_in", "aaas.awbs_is_transposed"], "comment": "awbstat access point, 0: RawScl0, 1: RawScl1, 2: DPC0, 3: Raw2DNR, 4: LSC, NO GUI"}, "afstat_input_mux": {"acc": [0, 2], "size": [], "range": [0, 2], "default": 1, "hidden": 0, "auto": 0, "type": "AX_U8", "target_conf": ["aaas.afs_offset_in", "aaas.afs_is_transposed"], "comment": "afstat access point, 0: RawScl0, 1: DPC0, 2: W<PERSON>, NO GUI"}, "afstat_drc_mode": {"acc": [0, 8], "size": [], "range": [0, 7], "hidden": 0, "default": 0, "auto": 0, "type": "AX_U8", "comment": "afstat drc mode, set w.r.t input bitwidth and af access point", "target_conf": ["aaas.afs_drc_mode"]}, "day_night_mode": {"acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "display": "Day Night Mode", "comment": "0: day (color) mode, 1: night (gray) mode", "hidden": 1, "auto": 0, "type": "AX_U8", "target_conf": []}}, "submodules": {"acp_ctx_setup": {"params": ["use_isp_hdr", "img_partition_info", "ifa_naked_partition_width", "ifa_partition_edge_width", "hcg_shot_noise_coef", "hcg_read_noise_coef", "lcg_shot_noise_coef", "lcg_read_noise_coef"], "configs": []}, "blc_setup": {"params": ["blc_det_window", "blc_active_region", "blc_channel_id"], "configs": ["blc.offset_out"]}, "dpc_setup": {"params": [], "configs": ["dpc.offset_in", "dpc.offset_out", "dpc.partition_info"]}, "hdr_setup": {"params": ["lf_indicator"], "configs": ["hdr.use_isp_hdr", "hdr.offset_in", "hdr.offset_out"]}, "ainr_setup": {"params": [], "configs": ["ainr.hdrc_offset_in", "ainr.hdrc_offset_out", "ainr.hdrc_output_max_value", "ainr.hdrc_dither_enable", "ainr.hidrc_offset_in", "ainr.hidrc_offset_out", "ainr.hidrc_input_max_value"]}, "rawnr_setup": {"params": [], "configs": ["rawnr.offset_in", "rawnr.offset_out"]}, "lsc_setup": {"params": [], "configs": ["lsc.offset_in", "lsc.offset_out", "lsc.partition_info"]}, "wbc_setup": {"params": [], "configs": ["wbc.offset_in", "wbc.offset_out"]}, "rltm_setup": {"params": [], "configs": ["rltm.offset_in", "rltm.offset_out", "rltm.pic_h_w", "rltm.partition_info"]}, "depurple_setup": {"params": [], "configs": ["depurple.offset_in", "depurple.partition_info"]}, "dem_setup": {"params": [], "configs": ["dem.offset_in", "dem.offset_out", "dem.partition_info"]}, "clc_setup": {"params": [], "configs": ["clc.offset_in", "clc.offset_out"]}, "gam_setup": {"params": [], "configs": ["gam.offset_in", "gam.offset_out"]}, "dehaze_setup": {"params": [], "configs": ["dehaze.offset_in", "dehaze.offset_out"]}, "csc_setup": {"params": [], "configs": ["csc.csc_mode", "csc.offset_in", "csc.offset_out"]}, "aaas_setup": {"params": [], "configs": ["aaas.aes0_partition_info", "aaas.aes1_partition_info", "aaas.awbs_partition_info", "aaas.afs_partition_info"]}, "eisstat_setup": {"params": [], "configs": ["eisstat.offset_in", "eisstat.partition_info"]}, "acp_ctx_dynamic": {"params": ["again", "dgain", "wb_gain", "hcg_lcg_mode", "exposure_ratio", "sen_black_level", "is_first_frame", "yuv3dnr_debug_mode", "sharpen_debug_mode", "y2dnr_debug_mode", "day_night_mode"], "configs": []}, "blc_dynamic": {"params": [], "configs": ["blc.sensor_bl", "blc.dither_seed_enable"]}, "dpc_dynamic": {"params": [], "configs": ["dpc.shot_noise_coefs", "dpc.read_noise_coefs", "dpc.wb_gain", "dpc.again"]}, "hdr_dynamic": {"params": [], "configs": ["hdr.shot_noise_coef", "hdr.read_noise_coef", "hdr.a_gain", "hdr.wb_gain", "hdr.exposure_ratio"]}, "ainr_dynamic": {"params": [], "configs": ["ainr.hcg_mode", "ainr.hdr_ratio", "ainr.hdrc_input_max_value", "ainr.hidrc_output_max_value", "ainr.hdrc_dither_seed_enable"]}, "rawnr_dynamic": {"params": [], "configs": ["rawnr.a_gain", "rawnr.hdr_ratio", "rawnr.shot_noise_coeffs", "rawnr.read_noise_coeffs"]}, "wbc_dynamic": {"params": [], "configs": ["wbc.d_gain", "wbc.wbc_gain", "wbc.clip_level"]}, "rltm_dynamic": {"params": [], "configs": ["rltm.detail_low_reset_coeffab", "rltm.dither_seed_enable", "rltm.hdr_ratio", "rltm.input_max_value", "rltm.ifa_partition_edge_width", "rltm.ifa_naked_partition_width"]}, "dem_dynamic": {"params": [], "configs": ["dem.init_enable", "dem.wb_gain"]}, "clc_dynamic": {"params": ["clc_sat_strength"], "configs": []}, "gam_dynamic": {"params": [], "configs": ["gam.dither_seed_enable"]}, "dehaze_dynamic": {"params": [], "configs": ["dehaze.init_enable", "dehaze.dehaze_partition_info", "dehaze.ifa_partition_edge_width", "dehaze.ifa_naked_partition_width"]}, "yuv3dnr_dynamic": {"params": ["yuv3dnr_debug_gain"], "configs": ["yuv3dnr.debug_mode", "yuv3dnr.init_ref", "yuv3dnr.init_tweight", "yuv3dnr.init_coef_ab", "yuv3dnr.dither_seed_enable", "yuv3dnr.yuv3dnr_partition_info", "yuv3dnr.ifa_partition_edge_width", "yuv3dnr.ifa_naked_partition_width"]}, "sharpen_dynamic": {"params": [], "configs": ["sharpen.shp_debug_mode", "sharpen.disable_for_prev_debug"]}, "y2dnr_dynamic": {"params": ["y2dnr_debug_gain"], "configs": ["y2dnr.debug_mode", "y2dnr.pre_debug_enable", "y2dnr.dither_seed_enable"]}, "ycproc_dynamic": {"params": [], "configs": ["ycproc.ycrt_reset_uv_for_debug"]}, "aes0_dynamic": {"params": ["aestat0_input_mux"], "configs": ["aaas.aes0_dgain"]}, "aes1_dynamic": {"params": ["aestat1_input_mux"], "configs": ["aaas.aes1_dgain"]}, "awbs_dynamic": {"params": ["awbstat_input_mux"], "configs": ["aaas.awbs_dgain"]}, "afs_dynamic": {"params": ["afstat_input_mux", "afstat_drc_mode"], "configs": ["aaas.afs_dgain", "aaas.afs_wb_gain"]}}, "target_module": {}, "context": {"AN_ID": {"size": [], "acc": [0, 16, 0], "comment": "ACP is 0x2201", "type": "AX_U16"}, "use_isp_hdr": {"size": [], "acc": [0, 1], "comment": "if isp hdr is enabled, 1 for isp hdr, 0 for non-isp hdr", "type": "AX_U8", "default": 0}, "img_partition_info": {"size": [], "comment": "image partition info", "type": "ax_isp_ptn_info_t"}, "ifa_partition_edge_width": {"size": [], "acc": [0, 16], "comment": "ifa partition edge width", "type": "AX_U16", "default": 208}, "ifa_naked_partition_width": {"size": [], "acc": [0, 16], "comment": "ifa naked partition width", "type": "AX_U16", "default": 208}, "hcg_shot_noise_coef": {"size": [4, 2], "acc": [1, 0, 31], "comment": "Hcg Calibration Params", "type": "AX_S32", "default": [[0, 0], [0, 0], [0, 0], [0, 0]]}, "hcg_read_noise_coef": {"size": [4, 3], "acc": [1, 0, 31], "comment": "Hcg Calibration Params", "type": "AX_S32", "default": [[0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]]}, "lcg_shot_noise_coef": {"size": [4, 2], "acc": [1, 0, 31], "comment": "Lcg Calibration Params", "type": "AX_S32", "default": [[0, 0], [0, 0], [0, 0], [0, 0]]}, "lcg_read_noise_coef": {"size": [4, 3], "acc": [1, 0, 31], "comment": "Lcg Calibration Params", "type": "AX_S32", "default": [[0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]]}, "day_night_mode": {"size": [], "acc": [0, 1], "comment": "day night mode", "type": "AX_U8", "default": 0}, "hcg_lcg_mode": {"size": [], "acc": [0, 2], "comment": "Hcg Lcg Mode", "type": "AX_U8", "default": 1}, "again": {"size": [], "acc": [0, 22, 10], "comment": "analog gain", "type": "AX_U32", "default": 1024}, "dgain": {"size": [], "acc": [0, 10, 8], "comment": "digital gain", "type": "AX_U32", "default": 256}, "wb_gain": {"size": [4], "acc": [0, 4, 8], "comment": "wb gain", "type": "AX_U16", "default": [16, 16, 16, 16]}, "exposure_ratio": {"size": [], "acc": [0, 8, 8], "comment": "exposure ratio", "type": "AX_U32", "default": 256}, "blc_white_level": {"size": [], "acc": [0, 8, 6], "comment": "blc white level, 255.984375", "type": "AX_U16", "default": 16383}, "hdr_white_level": {"size": [], "acc": [0, 14, 6], "comment": "hdr white level, 255.984375 x hdr_ratio", "type": "AX_U32", "default": 16383}, "ainr_white_level": {"size": [], "acc": [0, 10, 6], "comment": "ainr white level, 1023.984375", "type": "AX_U16", "default": 65535}, "rltm_white_level": {"size": [], "acc": [0, 8, 4], "comment": "rltm white level, 255.9375", "type": "AX_U16", "default": 4095}, "sen_black_level": {"size": [], "acc": [0, 8, 6], "comment": "sensor black level", "type": "AX_U16", "default": 1024}, "is_first_frame": {"size": [], "acc": [0, 1], "comment": "is first frame", "type": "AX_U8", "default": 1}, "yuv3dnr_debug_mode": {"size": [], "acc": [0, 8], "comment": "sharpen debug mode", "type": "AX_U8", "default": 0}, "sharpen_debug_mode": {"size": [], "acc": [0, 8], "comment": "sharpen debug mode", "type": "AX_U8", "default": 0}, "y2dnr_debug_mode": {"size": [], "acc": [0, 8], "comment": "sharpen debug mode", "type": "AX_U8", "default": 0}}}