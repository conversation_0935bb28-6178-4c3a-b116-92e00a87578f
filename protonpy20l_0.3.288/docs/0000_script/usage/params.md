# params

![params](../assets/imgs/params.png)
这个域中需要将所有的tuning接口参数都列入其中，不能有遗漏。

## params属性
|属性|说明|是否可缺省
|---|---|---
|display|UI 显示名称|可
|acc|参数精度，标识方法同xxx_m0_base.json|不可
|type|参数类型|不可`（*注1）`
|size|参数尺寸，标识方法同xxx_m0_base.json，**最大支持到4D数组**|不可
|range|参数范围，可支持整形或浮点型`（*注2）`|可
|default|参数默认值，可支持整形或浮点型`（*注2）`|可
|comment|参数说明|可
|hidden|参数是否为open参数，若缺省则为open|可
|auto|参数是否为auto参数，若缺省则为manual|可
|ref_group|参数若为auto参数，其参照组划分`（*注3）`|可
|group_num|参数若为auto参数，其auto参数的数量，若缺省则为16组|可
|target_conf|参数对应的寄存器参数|可`（*注4）`
|dependency|参数依赖关系|可

**注1：type类型如果是[AX_U8, AX_U16, AX_U32, AX_S8, AX_S16, AX_S32]，则脚本可以通过acc自行推断。如果type是其他类型，则需要指定type，不可缺省。**

**注2：range和default的类型需要与acc一致，如果acc的标识是整数，例如[0, 16]，则range和default的值也需要写整形。如果acc的标识是浮点数，例如[0, 1, 8]，则range和default的值也需要写浮点数。**

**注3：auto参数的参照源一般有Lux和Gain两种，每个auto参数都属于一个可独立配置参照源的组。组的号码通过ref_group标识。例如，有参数a,b,c。a.ref_group = 0, b.ref_group = 0, c.ref_group = 1。则，a, b共用同一参照源，c与a，b独立可使用另一个参照源。这里寄存器的写法为xxx.yyyy，其中xxx为硬件模块名，yyyy为寄存器名，这是为了对应configs中有多个硬件模块的情况。**

**注4：有些params并不映射到configs，对于此类参数可以缺省target_conf域.**

## denpendency属性

此属性的需求背景来自这里

https://soc.aixin-chip.com/t/topic/9484

各模块的Algo Owner不需要在代码上对来自Common模块的参数做特殊处理，但是需要在文档上明确标识出来。
因此在param_base.json中加入了Dependency域，这个域可缺省，缺省时默认为“ ”

## 特殊类型的params参数

除了常规类型的params参数（AX_U8, AX_U16, AX_U32, AX_S8, AX_S16, AX_S32）之外，还有一些特殊类型的params参数，脚本也支持这些特殊类型的参数。

* AX_F32：单精度浮点数
* 自定义结构体：详细见[structs](./usage/structs.md)
* partition结构体：该结构体和嵌入式软件中使用的结构体一致。详见[partition_params](./usage/partition_params.md)