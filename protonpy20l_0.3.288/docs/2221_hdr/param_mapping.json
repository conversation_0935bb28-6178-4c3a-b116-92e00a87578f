{"enable": {"api": "nHdrEn", "display": "enable", "comments": "0 - Disable; 1 - Enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "debug_show_mask": {"api": "eDebugShowMask", "display": "debugShowMask", "comments": "0 - normal; 1 - general mask; 2 - motion mask; 3 - exposure mask sf; 4 - exposure mask lf", "hint": "Accuracy: U3.0 Range: [0, 4]"}, "noise_lut_scale": {"api": "nNoiseLutScale", "display": "noiseLutScale", "comments": "Noise profile will be multiplied by this param.", "hint": "Accuracy: U4.12 Range: [0, 65535]"}, "fus_dark_prot_en": {"api": "nFusDarkProtEn", "display": "fusDarkProtEn", "comments": "0 - Disable dark protection feature for normal fusion; 1 - Enable dark protection feature for normal fusion", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "fus_over_exp_prot_thre": {"api": "nFusOverExpProtThre", "display": "fusOverExpProtThre", "comments": "fus_over_exp_prot_thre[x] for input frame x; For Luma value from fus_over_exp_prot_thre[x][0] to fus_over_exp_prot_thre[x][1], protection weight will drop from 1 to 0. When weight == 0, corresponding frame will not be used in normal fusion(area that satisfies hdr_ratio). Example: fus_over_exp_prot_thre = [[251, 254], [247, 249]], Luma value larger than 254 in frame0 will NOT be used in fusion and 251~254 is transition range; Luma value larger than 249 in frame1 will NOT be used in fusion and 247~249 is transition range.", "hint": "Accuracy: U8.8 Range: [0, 65535]"}, "fus_prot_wb_ratio": {"api": "nFusProtWbRatio", "display": "fusProtWbRatio", "comments": "fus_prot_wb_ratio[x] for input frame x. This param controls how much WB gain is considered when calculating Luma value used for over-exposure protection.", "hint": "Accuracy: U1.8 Range: [0, 256]"}, "dgst_base_fid": {"api": "eDgstBaseFid", "display": "dgstBaseFid", "comments": "Specify which frame to be used as dgst fusion result when both exp mask is 0 for a pixel. 0 - SF; 1 - LF", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "dgst_enable": {"api": "nDgstEnable", "display": "dgstEnable", "comments": "0 - dgst disable; 1 - dgst enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "dgst_stren_limit": {"api": "nDgstStrenLimit", "display": "dgstStrenLimit", "comments": "Forcibly set motion mask used in dgst to be from dgst_stren_limit[0] to dgst_stren_limit[1]", "hint": "Accuracy: U1.8 Range: [0, 256]"}, "exp_weight_gain": {"api": "nExpWeightGain", "display": "expWeightGain", "comments": "<PERSON><PERSON> applied on exposure mask before exp mask normalization.", "hint": "Accuracy: U1.8 Range: [0, 256]"}, "exp_weight_lut_luma_low": {"api": "nExpWeightLutLumaLow", "display": "expWeightLutLumaLow", "comments": "Weight lut of exposure mask for dark area. It's used to generate exposure weight based on pixel luma value.", "hint": "Accuracy: U1.15 Range: [0, 32768]"}, "exp_weight_lut_luma_high": {"api": "nExpWeightLutLumaHigh", "display": "expWeightLutLumaHigh", "comments": "Weight lut of exposure mask for bright area. It's used to generate exposure weight based on pixel luma value.", "hint": "Accuracy: U1.15 Range: [0, 32768]"}, "exp_y_ratio": {"api": "nExpYRatio", "display": "expYRatio", "comments": "This param controls how much WB gain is considered when calculating Luma value used for exp mask generation.", "hint": "Accuracy: U1.8 Range: [0, 256]"}, "motion_mask_enhance": {"api": "nMotionMaskEnhc", "display": "motionMaskEnhc", "comments": "This param will enhance motion mask.", "hint": "Accuracy: U8.8 Range: [0, 65535]"}, "motion_mask_noise_level": {"api": "nMotionMaskNoiseLevel", "display": "motionMaskNoiseLevel", "comments": "This parameter acts like motion mask threshold, cutting the motion mask.", "hint": "Accuracy: U1.11 Range: [0, 4095]"}, "motion_mask_continuity": {"api": "nMotionMaskContinuity", "display": "motionMaskContinuity", "comments": "The higher this parameter is, the more continuous(slightly) motion mask can be. Can be helpful for some scenes.", "hint": "Accuracy: U1.8 Range: [0, 256]"}}