{"configs": {"lsc": {"enable": {"acc": [0, 1], "size": [], "description": "0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "mesh_length": {"acc": [0, 10], "size": [2], "description": "The number of pixels in a coarse mesh grid", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "inv_mesh_length": {"acc": [0, 1, 22], "size": [2], "description": "inv of mesh length", "usage": "", "constraints": "0 <= 1 - mesh_length*inv_mesh_length <= 1/4096", "type": "AX_U32", "partition": "-"}, "inv_mesh_start": {"acc": [0, 1, 22], "size": [2], "description": "The coordinates of the tile starting point in the coarse grid which is scaled according to inv_mesh_length", "usage": "inv_mesh_start = (locat/2)*inv_mesh_length", "constraints": "[0.0, 1.0)", "type": "AX_U32", "partition": "support"}, "locat": {"acc": [0, 10], "size": [2], "description": "The coordinates of the upper left corner pixel of the current tile in the mesh_start grid", "usage": "", "constraints": "[0, 2*mesh_length)", "type": "AX_U16", "partition": "support"}, "mesh_start": {"acc": [0, 7], "size": [2], "description": "The coordinates of the coarse mesh grid where the upper left corner of the current tile is located", "usage": "", "constraints": "[1, mesh_shape - 3]", "type": "AX_U8", "partition": "support"}, "mesh_end": {"acc": [0, 7], "size": [], "description": "The coordinates of the coarse mesh grid where the lower right corner of the current tile is located", "usage": "", "constraints": "[mesh_start + 1, mesh_shape - 2]", "type": "AX_U8", "partition": "support"}, "rr_mesh": {"acc": [0, 4, 14], "size": [19, 15], "description": "rr channel correction table", "usage": "", "constraints": "[1.0, 17.0)", "type": "AX_U32", "partition": "-"}, "gr_mesh": {"acc": [0, 4, 14], "size": [19, 15], "description": "gr channel correction table", "usage": "", "constraints": "[1.0, 17.0)", "type": "AX_U32", "partition": "-"}, "gb_mesh": {"acc": [0, 4, 14], "size": [19, 15], "description": "gb channel correction table", "usage": "", "constraints": "[1.0, 17.0)", "type": "AX_U32", "partition": "-"}, "bb_mesh": {"acc": [0, 4, 14], "size": [19, 15], "description": "bb channel correction table", "usage": "", "constraints": "[1.0, 17.0)", "type": "AX_U32", "partition": "-"}, "offset_in": {"acc": [0, 14, 6], "size": [], "description": "offset for input", "usage": "Set according to pipe", "constraints": "", "type": "AX_U32", "partition": "-"}, "offset_out": {"acc": [0, 14, 6], "size": [], "description": "offset for output", "usage": "Set according to pipe", "constraints": "", "type": "AX_U32", "partition": "-"}}}, "partition_configs": ["lsc.inv_mesh_start", "lsc.locat", "lsc.mesh_start", "lsc.mesh_end"], "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "LSC is 0x2232", "type": "AX_U16"}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [12], "ref_interp_method": ["linear"]}, "2": {"ref_mode": ["color_temp"], "ref_group_num": [8], "ref_interp_method": ["linear"]}}, "params": {"enable": {"acc": [0, 1], "size": [], "default": 1, "comment": "0:disable, 1:enable", "hidden": 0, "auto": 0, "target_conf": ["lsc.rr_mesh", "lsc.gr_mesh", "lsc.gb_mesh", "lsc.bb_mesh"], "range": [0, 1], "dependency": " "}, "offset_in": {"acc": [0, 14, 6], "size": [], "default": 16.0, "hidden": 1, "auto": 0, "target_conf": ["lsc.offset_in"], "range": [0.0, 16383.984375], "comment": "offset in", "dependency": "common"}, "offset_out": {"acc": [0, 14, 6], "size": [], "default": 16.0, "hidden": 1, "auto": 0, "target_conf": ["lsc.offset_out"], "range": [0.0, 16383.984375], "comment": "offset out", "dependency": "common"}, "partition_info": {"size": [], "type": "ax_isp_ptn_info_t", "target_conf": ["lsc.mesh_length", "lsc.inv_mesh_length", "lsc.locat", "lsc.mesh_start", "lsc.inv_mesh_start", "lsc.mesh_end"], "comment": "partition information", "hidden": 1, "dependency": "common", "auto": 0}, "luma_ratio": {"acc": [0, 8], "size": [], "range": [0, 100], "default": 100, "hidden": 0, "auto": 1, "comment": "luma shading attenuation ratio", "target_conf": ["lsc.rr_mesh", "lsc.gr_mesh", "lsc.gb_mesh", "lsc.bb_mesh"], "dependency": " "}, "color_ratio": {"acc": [0, 8], "size": [], "range": [0, 100], "default": 100, "hidden": 0, "auto": 2, "comment": "color shading attenuation ratio", "target_conf": ["lsc.rr_mesh", "lsc.gr_mesh", "lsc.gb_mesh", "lsc.bb_mesh"], "dependency": " "}, "luma_mesh": {"acc": [0, 4, 14], "size": [15, 19], "default": 1.0, "hidden": 0, "auto": 0, "comment": "luma shading correction mesh lut", "target_conf": ["lsc.rr_mesh", "lsc.gr_mesh", "lsc.gb_mesh", "lsc.bb_mesh"], "range": [1.0, 15.99993896484375], "dependency": " "}, "color_mesh_rr": {"acc": [0, 4, 14], "size": [15, 19], "default": 1.0, "hidden": 0, "auto": 2, "comment": "color shading R channel correction mesh lut", "target_conf": ["lsc.rr_mesh"], "range": [1.0, 15.99993896484375], "dependency": " "}, "color_mesh_gr": {"acc": [0, 4, 14], "size": [15, 19], "default": 1.0, "hidden": 0, "auto": 2, "comment": "color shading GR channel correction mesh lut", "target_conf": ["lsc.gr_mesh"], "range": [1.0, 15.99993896484375], "dependency": " "}, "color_mesh_gb": {"acc": [0, 4, 14], "size": [15, 19], "default": 1.0, "hidden": 0, "auto": 2, "comment": "color shading GB channel correction mesh lut", "target_conf": ["lsc.gb_mesh"], "range": [1.0, 15.99993896484375], "dependency": " "}, "color_mesh_bb": {"acc": [0, 4, 14], "size": [15, 19], "default": 1.0, "hidden": 0, "auto": 2, "comment": "color shading B channel correction mesh lut", "target_conf": ["lsc.bb_mesh"], "range": [1.0, 15.99993896484375], "dependency": " "}}, "submodules": {"setup": {"params": ["offset_in", "offset_out"], "configs": ["lsc.enable"]}, "coord": {"params": ["partition_info"], "configs": []}, "mesh": {"params": ["enable", "luma_ratio", "color_ratio", "luma_mesh", "color_mesh_rr", "color_mesh_gr", "color_mesh_gb", "color_mesh_bb"], "configs": []}}, "target_module": {"mc20l": {"lsc": {"id": 2400, "method": 0}}}, "structs": {}}