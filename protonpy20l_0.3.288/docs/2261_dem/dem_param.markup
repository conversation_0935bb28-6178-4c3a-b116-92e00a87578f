h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'dem.wb_stat_roi_t_b_l_r', 'dem.wb_stat_mesh_start', 'dem.wb_stat_mesh_pos_start', 'dem.wbc_mesh_start', 'dem.wbc_mesh_phase_start', 'dem.wbc_mesh_pos_start' | partition information | common |
| init_enable |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 1 | None | hidden | 'dem.wbc_gain_lut_r', 'dem.wbc_gain_lut_g', 'dem.wbc_gain_lut_b' | 0: not first frame. 1: first frame | common |
| offset_in |  | u8.4 | AX_U16 | [\] |  [0, 4095\] | [0.0, 255.9375\] | 256 | 16.0 | hidden | 'dem.offset_in' | offset in | common |
| offset_out |  | u8.4 | AX_U16 | [\] |  [0, 4095\] | [0.0, 255.9375\] | 256 | 16.0 | hidden | 'dem.offset_out' | offset out | common |
| wb_gain |  | u4.8 | AX_U16 | [4\] |  [0, 4095\] | [0.0, 15.99609375\] | [256, 256, 256, 256\] | [1.0, 1.0, 1.0, 1.0\] | hidden | 'dem.gic_ct_lut' | wb_gain. [0\]: rr, [1\]: gr, [2\]: gb, [3\]: bb | common |
| wbc_stat_sum |  | s23.4 | AX_S32 | [64, 3\] |  [-134217728, 134217727\] | [-8388608.0, 8388607.9375\] | [[0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\], [0, 0, 0\]\] | [[0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\], [0.0, 0.0, 0.0\]\] | hidden | 'dem.wbc_gain_lut_r', 'dem.wbc_gain_lut_g', 'dem.wbc_gain_lut_b' | wbc stat sum, [[r_sum_00, g_sum_00, b_sum_00\],... | stat |
| wbc_stat_cnt |  | u15 | AX_U16 | [64\] |  [0, 32767\] | [None, None\] | [0, 0, ... , 0\] | None | hidden | 'dem.wbc_gain_lut_r', 'dem.wbc_gain_lut_g', 'dem.wbc_gain_lut_b' | wbc stat cnt, [[r_cnt_00, g_cnt_00, b_cnt_00\],... | stat |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'dem.mode' | dem module enable | user | None | None | None |
| hf_direction_estimation_enhance_level | hf_direction_estimation_enhance_level | u3 | AX_U8 | [\] | [0, 3\] | [None, None\] | 0 | None | open | 'dem.wbc_gain_lut_r', 'dem.wbc_gain_lut_g', 'dem.wbc_gain_lut_b' | 0: disable, 1: weak. 2: middle. 3: strong | user | None | None | None |
| mode | mode | u2 | AX_U8 | [\] | [0, 2\] | [None, None\] | 1 | None | open | 'dem.mode' | 0: normal, 1: copy to gray, 2: copy to green | user | None | None | None |
| gic_enable | gic_enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open |  | green imbalance correction enable | user | None | None | None |
| gic_strength_lut | gic_strength_lut | u1.7 | AX_U8 | [9\] | [0, 128\] | [0.0, 1.0\] | [0, 20, 48, 64, 80, 88, 104, 112, 128\] | [0.0, 0.15625, 0.375, 0.5, 0.625, 0.6875, 0.8125, 0.875, 1.0\] | open | 'dem.gic_strength_lut' | lut[0\]:0, [1\]:32, [2\]:64, ... [8\]:256 | user | None | None | None |
| gic_strength_factor | gic_strength_factor | u2.6 | AX_U8 | [\] | [0, 255\] | [0.0, 3.984375\] | 64 | 1.0 | open | 'dem.gic_strength_lut' |  | user | ['gain/lux'\] | [16\] | ['linear'\] |
| gic_ct_lut | gic_ct_lut | u8.4 | AX_U16 | [9\] | [0, 4095\] | [0.0, 255.9375\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open | 'dem.gic_ct_lut' | gic crosstalk level for R/B channel. lut[\*\][0\]:0, [\*\][1\]:32, [\*\][2\]:64, ... [\*\][8\]:256 | user | None | None | None |
| gic_limit_lut | gic_limit_lut | u8.4 | AX_U16 | [9\] | [0, 4095\] | [0.0, 255.9375\] | [256, 256, ... , 256\] | [16.0, 16.0, ... , 16.0\] | open | 'dem.gic_limit_lut' | gic limit lut, bigger value, stronger gic. lut[0\]:0, [1\]:32, [2\]:64, ... [8\]:256 | user | None | None | None |
| edge_direction_high_frequency_sensitivity | edge direction high frequency sensitivity | u1.4 | AX_U8 | [\] | [0, 16\] | [0.0, 1.0\] | 12 | 0.75 | open | 'dem.gradf_ratio' | bigger value==> more sensitivity to high freq | user | None | None | None |
| edge_direction_edge_sensitivity_lut | edge direction edge sensitivity lut | u4.8 | AX_U16 | [9\] | [0, 4095\] | [0.0, 15.99609375\] | [128, 256, 384, 448, 512, 576, 640, 704, 768\] | [0.5, 1.0, 1.5, 1.75, 2.0, 2.25, 2.5, 2.75, 3.0\] | open | 'dem.inv_noise_lut' | default inv noise lut. lut[0\]:0, [1\]:32, [2\]:64, ... [8\]:256 | user | None | None | None |
| edge_direction_hv_high_frequency_strength | edge_direction_hv_high_frequency_strength | u2.2 | AX_U8 | [\] | [0, 15\] | [0.0, 3.75\] | 5 | 1.25 | open | 'dem.grad_hh_level' | level for adjusting high frequency gradient | user | None | None | None |
| hv_interpolation_hf_coeff | hv_interpolation_hf_coeff | u1.5 | AX_U8 | [\] | [0, 32\] | [0.0, 1.0\] | 16 | 0.5 | open | 'dem.ginterp_hv_hf_ratio' | level for adjusting high frequency gradient | user | None | None | None |
| pn_interpolation_hf_coeff | pn_interpolation_hf_coeff | u1.5 | AX_U8 | [\] | [0, 32\] | [0.0, 1.0\] | 10 | 0.3125 | open | 'dem.ginterp_pn_hf_ratio' | level for adjusting high frequency gradient | user | None | None | None |
| x_interpolation_hf_coeff | x_interpolation_hf_coeff | u1.5 | AX_U8 | [\] | [0, 32\] | [0.0, 1.0\] | 0 | 0.0 | open | 'dem.ginterp_x_hf_ratio' | level for adjusting high frequency gradient | user | None | None | None |
| edge_enhance_enable | edge_enhance_enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open |  | edge enhance enable, 0: disable, 1: enable | user | None | None | None |
| edge_smoothness_strength | edge_smoothness_strength | u1.4 | AX_U8 | [9\] | [0, 16\] | [0.0, 1.0\] | [4, 4, ... , 4\] | [0.25, 0.25, ... , 0.25\] | open | 'dem.edge_enhance_level_lut' | larger values means more smooth. lut[0\]:0, [1\]:32, [2\]:64, ... [8\]:256 | user | ['gain/lux'\] | [16\] | ['linear'\] |
| edge_sharpness_strength | edge_sharpness_strength | u1.4 | AX_U8 | [9\] | [0, 16\] | [0.0, 1.0\] | [4, 4, ... , 4\] | [0.25, 0.25, ... , 0.25\] | open | 'dem.edge_enhance_level_lut' | larger values means more sharpen. lut[0\]:0, [1\]:32, [2\]:64, ... [8\]:256 | user | ['gain/lux'\] | [16\] | ['linear'\] |
| fcc_enable | fcc_enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open |  | fc cor enable | user | None | None | None |
| fcc_limit | fcc_limit | u8.4 | AX_U16 | [\] | [0, 4095\] | [0.0, 255.9375\] | 0 | 0.0 | open | 'dem.fc_cor_limit' | larger value means more pixels will be considered as false color pixels | user | None | None | None |
| fcc_sensitivity | fcc_sensitivity | u2.2 | AX_U8 | [\] | [0, 15\] | [0.0, 3.75\] | 0 | 0.0 | open | 'dem.fc_cor_gain_safe' | larger value means higher fcc sensitivity, makes the corrected color more grayish | user | ['gain/lux'\] | [16\] | ['linear'\] |
| fcc_saturation_level | fcc_saturation_level | u4.4 | AX_U8 | [\] | [0, 255\] | [0.0, 15.9375\] | 32 | 2.0 | open | 'dem.fc_cor_th' | larger value means less pixels will be considered as false color pixels | user | None | None | None |
| fcc_saturation_lut | fcc_saturation_lut | u8.4 | AX_U16 | [9\] | [0, 4095\] | [0.0, 255.9375\] | [8, 16, 32, 64, 128, 256, 256, 256, 256\] | [0.5, 1.0, 2.0, 4.0, 8.0, 16.0, 16.0, 16.0, 16.0\] | open | 'dem.fc_cor_sat_lut' | larger value means current pixel is higher possibility as false color and more need corrected color | user | None | None | None |
| fcc_strength | fcc_strength | u4.4 | AX_U8 | [\] | [0, 255\] | [0.0, 15.9375\] | 0 | 0.0 | open | 'dem.fc_cor_gain' | larger value means stronger fcc strength, makes the corrected color more grayish | user | ['gain/lux'\] | [16\] | ['linear'\] |
| fcc_sat_protect_level | fcc_sat_protect_level | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 4 | 0.015625 | open | 'dem.fc_cor_inv_base_sat_level' | smaller value means  more corrected color will be used in the final output | user | None | None | None |
| fcc_ratio_limit | fcc_ratio_limit | u1.8 | AX_U16 | [\] | [0, 256\] | [0.0, 1.0\] | 256 | 1.0 | open | 'dem.fc_cor_ratio_limit' | false color correction ratio limit of upper limit | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |