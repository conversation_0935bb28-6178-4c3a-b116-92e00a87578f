* [Home](/ "Algo Logic Generative Script")

* 背景
  - [背景说明](./background.md)

* 使用说明
  - [从0开始创建Algo Logic需要哪些文件？](./usage/file_struct.md)
  - [使用脚本前需要准备的文件```param_base.json```](./usage/param_json.md)
    - [params节点](./usage/params.md)
    - [submodules节点](./usage/submodules.md)
    - [target_module节点](./usage/target_modules.md)
    - [context节点](./usage/context.md)
    - [partition_configs节点](./usage/partition_configs.md)
    - [structs节点](./usage/structs.md)
    - [controls节点](./usage/controls.md)
  - [自动生成脚本使用方法](./usage/script_usage.md)
  - [调用自动生成脚本之后](./usage/after_usage.md)

* 开发指南
  - [文档写作](/dev/author_guide.md)

* [FAQ](/faq.md)
