# partition_configs
ISP模块的某些寄存器，需要在每个partition中配置不同的值。对于这些寄存器，脚本会自动在参数前添加两个维度，作为横纵partition的索引，用户需要再`partition_configs`中列出这些寄存器。

![partition_configs](../assets/imgs/partition_configs.png)

生成结果如下
```c
typedef struct {
    AX_U8   pfd_mesh_adj_mask_enable;   // u1
    AX_U8  (*pfd_mesh_start)[2];        // u4, support partition
    AX_U16 (*pfd_internal_start)[2];    // u10, support partition
    AX_U16 (*pfd_pos_start)[2];         // u0.16, support partition
    AX_U16  pfd_mesh_length[2];         // u10
    AX_U16  pfd_inv_mesh_length[2];     // u0.16
    AX_U8   pfd_mesh[17][17];           // u2.6
    AX_U8   pfd_mesh_mode;   
} ax_algo_depurple_cfg_locat_mesh_adj_t
```

可以看到`pfd_mesh_start`, `pfd_internal_start`, `pfd_pos_start`这三个config，从原来的一维数组（长度为2），变成了指针，指向一个数组（长度为2）。这样，这三个config就支持partition了。

更一般地说，假设一个config支持partition，其寄存器的原始定义为
```c
AX_TYPE  cfg[i][j];
```

则在algo logic的接口中，其写法会是
```c
AX_TYPE (*cfg)[i][j];
```

注意，这里的指针所指向的数组，其对应的partition顺序是从左到右，从上到下，
如下图所示
![](../assets/imgs/partition_param_order.png)