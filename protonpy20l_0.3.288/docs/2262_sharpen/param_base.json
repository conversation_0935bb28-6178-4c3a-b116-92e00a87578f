{"context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "Sharpen is 0x2262", "type": "AX_U16"}, "ud_freq": {"size": [], "type": "AX_F64", "comment": "ud_freq for motion gain lut", "default": 1.0}, "dir_freq": {"size": [], "type": "AX_F64", "comment": "dir_freq for motion gain lut", "default": 1.0}, "shp_extra_gain": {"size": [], "type": "AX_F64", "comment": "sharpen extra gain", "default": 1.0}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [12], "ref_interp_method": ["linear"]}}, "params": {"enable": {"display": "Enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "sharpen enable", "hidden": 0, "auto": 0, "target_conf": ["lce.shp_limit_sup_max_over_change", "lce.shp_limit_sup_max_under_change"]}, "debug_mode": {"display": "Debug Mode", "acc": [0, 5], "size": [], "range": [0, 31], "default": 0, "comment": "sharpen debug mode", "dependency": "common", "hidden": 1, "auto": 0, "target_conf": ["lce.shp_debug_mode"]}, "disable_for_prev_debug": {"acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "disable sharpen when using yuv3dnr debug mode", "dependency": "common", "hidden": 1, "auto": 0, "target_conf": []}, "ud_gain_lut": {"display": "<PERSON><PERSON>", "acc": [0, 8, 4], "size": [33], "range": [0.0, 255.9375], "default": [8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0], "comment": "set undirectional gain w.r.t. texture strength", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_ud_gain_lut"]}, "ud_gain_lut_anchor": {"display": "UD Lut Anchor", "acc": [0, 3], "size": [], "range": [0, 7], "default": 1, "comment": "smaller value means more anchor points in flat/weak texture regions", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_ud_texture_shift"]}, "dir_gain_lut": {"display": "<PERSON><PERSON>", "acc": [0, 8, 4], "size": [33], "range": [0.0, 255.9375], "default": [8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0], "comment": "set directional gain w.r.t. edge strength", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_dir_gain_lut"]}, "dir_gain_lut_anchor": {"display": "<PERSON><PERSON>", "acc": [0, 3], "size": [], "range": [0, 7], "default": 1, "comment": "smaller value means more anchor points in flat/weak texture regions", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_dir_texture_shift"]}, "ud_freq": {"display": "UD Frequency", "acc": [0, 5, 5], "size": [], "range": [0.0, 31.96875], "default": 1.0, "comment": "larger value means higher undirectional frequecny will be enhanced", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_filter_ud", "lce.shp_ud_gain_lut"]}, "dir_freq": {"display": "<PERSON><PERSON>", "acc": [0, 5, 5], "size": [], "range": [0.0, 31.96875], "default": 1.0, "comment": "larger value means higher directional frequecny will be enhanced", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_filter_dir_0", "lce.shp_filter_dir_22", "lce.shp_filter_dir_45", "lce.shp_dir_gain_lut"]}, "ud_coring": {"display": "UD Coring", "acc": [0, 3, 3], "size": [], "range": [0.0, 7.875], "default": 0.0, "comment": "larger value means stronger coring for undirectional details", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_coring_ud_level"]}, "dir_coring": {"display": "<PERSON><PERSON>", "acc": [0, 3, 3], "size": [], "range": [0.0, 7.875], "default": 0.0, "comment": "larger value means stronger coring for directional details", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_coring_dir_level"]}, "edge_strength": {"display": "Edge Strength", "acc": [0, 4], "size": [], "range": [0, 15], "default": 8, "comment": "larger value means to use more directional filter's result", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_ud_scale", "lce.shp_dir_scale"]}, "edge_threshold": {"display": "Edge Threshold", "acc": [0, 8, 4], "size": [], "range": [0.0, 255.9375], "default": 16.0, "comment": "larger value means less pixels will be considered as edge pixels", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_edge_sad_noise"]}, "edge_smooth_thin_ratio": {"display": "Edge Smooth Thin Ratio", "acc": [0, 4], "size": [], "range": [0, 15], "default": 0, "comment": "larger value means to make the edge much thinner", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_filter_dir_0", "lce.shp_filter_dir_22", "lce.shp_filter_dir_45"]}, "overshoot": {"display": "Overshoot", "acc": [0, 0, 7], "size": [], "range": [0.0, 0.9921875], "default": 0.625, "comment": "larger value means stronger overshoot", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_overshoot"]}, "undershoot": {"display": "Undershoot", "acc": [0, 0, 7], "size": [], "range": [0.0, 0.9921875], "default": 0.703125, "comment": "larger value means stronger undershoot", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_undershoot"]}, "detail_region_threshold": {"display": "Detail Threshold", "acc": [0, 8], "size": [], "range": [0, 255], "default": 20, "comment": "larger value means more pixels will be considered as detail region", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_detail_overshoot_slope", "lce.shp_detail_overshoot_offset", "lce.shp_detail_overshoot_limit", "lce.shp_detail_undershoot_slope", "lce.shp_detail_undershoot_offset", "lce.shp_detail_undershoot_limit"]}, "detail_overshoot_adjust": {"display": "Detail Overshoot Adjust", "acc": [1, 0, 7], "size": [], "range": [-0.9921875, 0.9921875], "default": 0.0, "comment": "larger value means stronger overshoot for detail region", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_detail_adj_enable", "lce.shp_detail_overshoot_slope", "lce.shp_detail_overshoot_offset", "lce.shp_detail_overshoot_limit"]}, "detail_undershoot_adjust": {"display": "Detail Undershoot Adjust", "acc": [1, 0, 7], "size": [], "range": [-0.9921875, 0.9921875], "default": 0.0, "comment": "larger value means stronger undershoot for detail region", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_detail_adj_enable", "lce.shp_detail_undershoot_slope", "lce.shp_detail_undershoot_offset", "lce.shp_detail_undershoot_limit"]}, "shoot_release_ratio": {"display": "Shoot Release Ratio", "acc": [0, 1, 4], "size": [], "range": [0.0, 1.0], "default": 0.0, "comment": "larger value means shoot suppression tends to ignore more weak texture/detail region", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_shoot_sup_enable", "lce.shp_shoot_sup_blend_ratio"]}, "shoot_sup_range": {"display": "Shoot Suppression Range", "acc": [0, 8], "size": [], "range": [0, 255], "default": 0, "comment": "larger value means shoot suppression tends to control more edges", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_shoot_sup_enable", "lce.shp_shoot_sup_var_min_slope", "lce.shp_shoot_sup_var_min_offset", "lce.shp_shoot_sup_var_min_limit"]}, "shoot_sup_strength": {"display": "Shoot Suppression Strength", "acc": [0, 0, 7], "size": [], "range": [0.0, 0.9921875], "default": 0.75, "comment": "larger value means stronger shoot suppression on edges", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_shoot_sup_enable", "lce.shp_shoot_sup_var_min_slope", "lce.shp_shoot_sup_var_min_offset", "lce.shp_shoot_sup_var_min_limit"]}, "shp_extra_gain": {"display": "Sharpen Extra Gain", "acc": [0, 4, 4], "size": [], "range": [1.0, 15.9375], "default": 1.0, "comment": "larger value means more extra gain applied to ud/dir gain lut", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_ud_gain_lut", "lce.shp_dir_gain_lut"]}, "shp_limit": {"display": "Sharpen <PERSON>", "acc": [0, 5, 5], "size": [], "range": [0.0, 31.96875], "default": 31.96875, "comment": "smaller value means more restriction on the pixel value change, and may reduce the number of white/black points", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_limit_sup_max_over_gain", "lce.shp_limit_sup_max_under_gain"]}, "luma_gain_lut_negative": {"display": "Luma Negative Gain", "acc": [0, 1, 5], "size": [33], "range": [0.0, 1.96875], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "comment": "adjust sharpen gain for negative hf based on image brightness", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_luma_mask_enable", "lce.shp_luma_gain_lut"]}, "luma_gain_lut_positive": {"display": "Luma Positive Gain", "acc": [0, 1, 5], "size": [33], "range": [0.0, 1.96875], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "comment": "adjust sharpen gain for positive hf based on image brightness", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_luma_mask_enable", "lce.shp_luma_gain_lut"]}, "red_gain": {"display": "<PERSON>", "acc": [0, 1, 5], "size": [], "range": [0.0, 1.0], "default": 1.0, "comment": "smaller value means less sharpen for red region", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_rgb_ctrl_enable", "lce.shp_rgb_ctrl_red_gain", "lce.shp_rgb_ctrl_red_gain_slope", "lce.shp_rgb_ctrl_red_gain_offset"]}, "green_gain": {"display": "<PERSON> Gain", "acc": [0, 3, 5], "size": [], "range": [0.0, 7.96875], "default": 1.0, "comment": "smaller value means less sharpen for green region", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_rgb_ctrl_enable", "lce.shp_rgb_ctrl_green_gain", "lce.shp_rgb_ctrl_green_gain_slope", "lce.shp_rgb_ctrl_green_gain_offset"]}, "blue_gain": {"display": "Blue Gain", "acc": [0, 1, 5], "size": [], "range": [0.0, 1.0], "default": 1.0, "comment": "smaller value means less sharpen for blue region", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_rgb_ctrl_enable", "lce.shp_rgb_ctrl_blue_gain", "lce.shp_rgb_ctrl_blue_gain_slope", "lce.shp_rgb_ctrl_blue_gain_offset"]}, "skin_gain": {"display": "<PERSON>", "acc": [0, 1, 5], "size": [], "range": [0.0, 1.0], "default": 1.0, "comment": "smaller value means less sharpen for skin region", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_skin_ctrl_enable", "lce.shp_skin_ctrl_var5_gain_slope", "lce.shp_skin_ctrl_var5_gain_offset", "lce.shp_skin_ctrl_var5_gain_limit"]}, "skin_center": {"display": "Skin Color Center", "acc": [1, 7, 2], "size": [2], "range": [-128.0, 127.75], "default": [-16.5, 28.5], "comment": "skin color center", "hidden": 0, "auto": 0, "target_conf": ["lce.shp_skin_ctrl_color_center"]}, "skin_radius": {"display": "Skin Color Radius", "acc": [0, 8, 2], "size": [2], "range": [0.0, 255.75], "default": [11.25, 16.25], "comment": "skin color radius", "hidden": 0, "auto": 0, "target_conf": ["lce.shp_skin_ctrl_color_radius"]}, "nr_strength": {"display": "NR Strength", "acc": [0, 1, 5], "size": [], "range": [0.0, 1.0], "default": 0.0, "comment": "larger value means stronger noise reduction", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_nr_level"]}, "nr_ud_texture_sensitivity": {"display": "UD Texture Sensitivity", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.25, "comment": "larger value means image has more undirectional textures and apply less noise reduction", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_nr_ud_texture_scale"]}, "nr_ud_texture_threshold": {"display": "UD Texture Threshold", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.25, "comment": "larger value means less pixels will be considered as real texture and apply more noise reduction", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_nr_ud_texture_offset"]}, "nr_ud_limit": {"display": "UD Noise Limit", "acc": [0, 8, 2], "size": [], "range": [0.0, 255.75], "default": 64.0, "comment": "larger value means less restriction on undirectional noise reduction", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_nr_ud_limit"]}, "nr_dir_edge_sensitivity": {"display": "Dir Edge Sensitivity", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.25, "comment": "larger value means image has more directional edges and apply less noise reduction", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_nr_dir_texture_scale"]}, "nr_dir_edge_threshold": {"display": "<PERSON><PERSON>eshold", "acc": [0, 4, 4], "size": [], "range": [0.0, 15.9375], "default": 4.0, "comment": "larger value means less pixels will be considered as real edge and apply more noise reduction", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_nr_dir_texture_offset"]}, "nr_dir_limit": {"display": "Dir Noise Limit", "acc": [0, 8, 2], "size": [], "range": [0.0, 255.75], "default": 64.0, "comment": "larger value means less restriction on directional noise reduction", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_nr_dir_limit"]}, "motion_mask_enable": {"display": "Motion Mask Enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "motion mask enable", "hidden": 0, "auto": 0, "target_conf": ["lce.shp_motion_mask_enable"]}, "motion_overshoot": {"display": "Motion Overshoot", "acc": [0, 0, 7], "size": [], "range": [0.0, 0.9921875], "default": 0.625, "comment": "larger value means stronger overshoot", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_motion_overshoot"]}, "motion_undershoot": {"display": "Motion Undershoot", "acc": [0, 0, 7], "size": [], "range": [0.0, 0.9921875], "default": 0.703125, "comment": "larger value means stronger undershoot", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_motion_undershoot"]}, "motion_ud_gain_lut": {"display": "Motion UD Gain", "acc": [0, 8, 4], "size": [33], "range": [0.0, 255.9375], "default": [8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0], "comment": "set undirectional gain w.r.t. texture strength", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_motion_ud_gain_lut"]}, "motion_dir_gain_lut": {"display": "Motion <PERSON><PERSON>", "acc": [0, 8, 4], "size": [33], "range": [0.0, 255.9375], "default": [8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0], "comment": "set directional gain w.r.t. edge strength", "hidden": 0, "auto": 1, "target_conf": ["lce.shp_motion_dir_gain_lut"]}, "motion_mask_lut": {"display": "Motion Mask Lut", "acc": [0, 0, 8], "size": [9], "range": [0.0, 0.99609375], "default": [0.0, 0.125, 0.25, 0.375, 0.5, 0.625, 0.75, 0.875, 1.0], "comment": "used to threshold/reverse the input motion lut", "hidden": 0, "auto": 0, "target_conf": ["lce.shp_motion_mask_lut"]}}, "submodules": {"setup": {"params": [], "configs": ["lce.enable", "lce.shp_enable", "lce.shp_luma_ref_select", "lce.shp_detail_texture_shift", "lce.shp_limit_sup_enable", "lce.shp_shoot_ref_ratio", "lce.shp_shoot_sup_var_diff_slope", "lce.shp_shoot_sup_var_diff_offset", "lce.shp_shoot_sup_var_diff_limit", "lce.shp_rgb_ctrl_red_center", "lce.shp_rgb_ctrl_red_radius", "lce.shp_rgb_ctrl_red_dist_mode", "lce.shp_rgb_ctrl_red_dist_weight", "lce.shp_rgb_ctrl_red_var5_shift", "lce.shp_rgb_ctrl_green_center", "lce.shp_rgb_ctrl_green_radius", "lce.shp_rgb_ctrl_green_dist_mode", "lce.shp_rgb_ctrl_green_dist_weight", "lce.shp_rgb_ctrl_blue_center", "lce.shp_rgb_ctrl_blue_radius", "lce.shp_rgb_ctrl_blue_dist_mode", "lce.shp_rgb_ctrl_blue_dist_weight", "lce.shp_rgb_ctrl_blue_var5_shift", "lce.shp_skin_ctrl_color_center", "lce.shp_skin_ctrl_color_radius", "lce.shp_skin_ctrl_color_dist_mode", "lce.shp_skin_ctrl_color_dist_weight", "lce.shp_skin_ctrl_color_slope", "lce.shp_skin_ctrl_var5_shift"]}, "basic_setting": {"params": ["enable", "debug_mode", "disable_for_prev_debug", "ud_gain_lut", "ud_gain_lut_anchor", "dir_gain_lut", "dir_gain_lut_anchor", "ud_freq", "dir_freq", "edge_strength", "edge_threshold", "edge_smooth_thin_ratio", "overshoot", "undershoot", "detail_region_threshold", "detail_overshoot_adjust", "detail_undershoot_adjust", "shoot_release_ratio", "shoot_sup_range", "shoot_sup_strength", "shp_extra_gain", "shp_limit"], "configs": []}, "luma_chroma_setting": {"params": ["luma_gain_lut_negative", "luma_gain_lut_positive", "red_gain", "green_gain", "blue_gain", "skin_gain", "skin_center", "skin_radius"], "configs": []}, "motion_setting": {"params": ["motion_mask_enable", "motion_overshoot", "motion_undershoot", "motion_ud_gain_lut", "motion_dir_gain_lut", "motion_mask_lut"], "configs": []}, "nr_coring_setting": {"params": ["nr_strength", "nr_ud_texture_sensitivity", "nr_ud_texture_threshold", "nr_ud_limit", "ud_coring", "nr_dir_edge_sensitivity", "nr_dir_edge_threshold", "nr_dir_limit", "dir_coring"], "configs": []}}, "target_module": {"mc20l": {"lce": {"id": 5400, "method": 0}}}, "configs": {"lce": {"enable": {"acc": [0, 1], "size": [], "description": "lce enable, 0: disable, 1: enable", "usage": "set to 1 normally and use each submodule's own enable flag", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_enable": {"acc": [0, 1], "size": [], "description": "sharpen enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_debug_mode": {"acc": [0, 5], "size": [], "description": "sharpen debug mode, 0: normal mode, others: visualized intermediate result for debugging", "usage": "", "constraints": "{0, 3 ~ 18}", "type": "AX_U8", "partition": "-"}, "shp_filter_ud": {"acc": [1, 0, 12], "size": [3, 3], "description": "undirectional filter coeffs", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_filter_dir_0": {"acc": [1, 0, 12], "size": [9], "description": "0 directional filter coeffs", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_filter_dir_22": {"acc": [1, 0, 12], "size": [13], "description": "22 directional filter coeffs", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_filter_dir_45": {"acc": [1, 0, 12], "size": [9], "description": "45 directional filter coeffs", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_ud_texture_shift": {"acc": [0, 3], "size": [], "description": "undirectional texture gain lut x-axis shift bits", "usage": "smaller value means more texture gain control points in flat or weak texture regions", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_dir_texture_shift": {"acc": [0, 3], "size": [], "description": "directional texture gain lut x-axis shift bits", "usage": "smaller value means more texture gain control points in flat or weak edge regions", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_ud_gain_lut": {"acc": [0, 8, 4], "size": [33], "description": "undirectional texture gain lut", "usage": "set undirectional sharpen gain w.r.t texture strength", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_dir_gain_lut": {"acc": [0, 8, 4], "size": [33], "description": "directional texture gain lut", "usage": "set directional sharpen gain w.r.t texture strength", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_ud_scale": {"acc": [0, 0, 4], "size": [], "description": "scaling factor for ud filters", "usage": "larger value means to use more undirectional filter result", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_dir_scale": {"acc": [0, 0, 4], "size": [], "description": "scaling factor for dir filters", "usage": "larger value means to use more directional filter result", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_edge_sad_noise": {"acc": [0, 8, 4], "size": [], "description": "edge noise level", "usage": "larger value means stronger noise level and reduce directional filter weight", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_nr_ud_texture_scale": {"acc": [0, 0, 8], "size": [], "description": "scaling factor for undirectional nr's texture map", "usage": "larger value means more like a texture pixel (not noise pixel), and reduces undirectional nr strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_nr_ud_texture_offset": {"acc": [0, 0, 8], "size": [], "description": "coring offset for undirectional nr's texture map", "usage": "larger value means stronger coring on texutre map, and increases undirectional nr strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_nr_ud_limit": {"acc": [0, 8, 2], "size": [], "description": "undirectional noise limit", "usage": "larger value means less limitation for calculated undirectional noise", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_nr_dir_texture_scale": {"acc": [0, 0, 8], "size": [], "description": "scaling factor for directional nr's texture map", "usage": "larger value means more like a edge pixel (not noise pixel), and reduces directional nr strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_nr_dir_texture_offset": {"acc": [0, 4, 4], "size": [], "description": "coring offset for directional nr's texture map", "usage": "larger value means stronger coring on texutre map, and increases directional nr strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_nr_dir_limit": {"acc": [0, 8, 2], "size": [], "description": "directional noise limit", "usage": "larger value means less limitation for calculated directional noise", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_nr_level": {"acc": [0, 1, 5], "size": [], "description": "noise reduction level", "usage": "larger value means stronger noise reduction", "constraints": "shp_nr_level <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_coring_ud_level": {"acc": [0, 3, 3], "size": [], "description": "undirectional sharpen coring level", "usage": "larger value means stronger coring, and reduces undirectional sharpen strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_coring_dir_level": {"acc": [0, 3, 3], "size": [], "description": "directional sharpen coring level", "usage": "larger value means stronger coring, and reduces directional sharpen strength", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_shoot_ref_ratio": {"acc": [0, 1, 5], "size": [], "description": "the blending ratio of original pixel and local min max to generate shoot reference", "usage": "larger value means to use more local min max value for shoot reference, and leads to stronger shoot", "constraints": "shp_shoot_ref_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_overshoot": {"acc": [0, 0, 7], "size": [], "description": "global overshoot level", "usage": "larger value means stronger overshoot", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_undershoot": {"acc": [0, 0, 7], "size": [], "description": "global undershoot level", "usage": "larger value means stronger undershoot", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_motion_mask_enable": {"acc": [0, 1], "size": [], "description": "motion mask enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_motion_mask_lut": {"acc": [0, 0, 8], "size": [9], "description": "motion mask lut", "usage": "used to clip, inverse, or remap the original motion mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_motion_ud_gain_lut": {"acc": [0, 8, 4], "size": [33], "description": "undirectional texture gain lut for motion region", "usage": "set undirectional sharpen gain w.r.t texture strength for motion region", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_motion_dir_gain_lut": {"acc": [0, 8, 4], "size": [33], "description": "directional texture gain lut for motion region", "usage": "set directional sharpen gain w.r.t texture strength for motion region", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_motion_overshoot": {"acc": [0, 0, 7], "size": [], "description": "overshoot level for motion region", "usage": "larger value means stronger overshoot for motion region", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_motion_undershoot": {"acc": [0, 0, 7], "size": [], "description": "undershoot level for motion region", "usage": "larger value means stronger undershoot for motion region", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_luma_mask_enable": {"acc": [0, 1], "size": [], "description": "luma mask enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_luma_ref_select": {"acc": [0, 1], "size": [], "description": "luma mask reference selection, 0: blurred Y, 1: denoised Y", "usage": "usually set to 0", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_luma_gain_lut": {"acc": [0, 1, 5], "size": [2, 33], "description": "luma gain lut, [0]: negative, [1]: positive", "usage": "usually set lower gain for dark region to avoid enhancing noises", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_detail_adj_enable": {"acc": [0, 1], "size": [], "description": "detail enhancement enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_detail_texture_shift": {"acc": [0, 3], "size": [], "description": "texture map shift bits for detail region", "usage": "smaller value means detail region will have more flat or weak texture pixels", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_detail_overshoot_slope": {"acc": [1, 0, 9], "size": [], "description": "detail overshoot slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_detail_overshoot_offset": {"acc": [1, 4, 7], "size": [], "description": "detail overshoot offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_detail_overshoot_limit": {"acc": [0, 0, 7], "size": [2], "description": "detail overshoot limit", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_detail_undershoot_slope": {"acc": [1, 0, 9], "size": [], "description": "detail undershoot slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_detail_undershoot_offset": {"acc": [1, 4, 7], "size": [], "description": "detail undershoot offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_detail_undershoot_limit": {"acc": [0, 0, 7], "size": [2], "description": "detail undershoot limit", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_enable": {"acc": [0, 1], "size": [], "description": "shoot suppression enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_blend_ratio": {"acc": [0, 1, 4], "size": [], "description": "the blending ratio of two shoot control strategy", "usage": "larger value means less shoot control for texture region while keeping shoot control for strong edges", "constraints": "shp_shoot_sup_blend_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_var_min_slope": {"acc": [0, 0, 9], "size": [], "description": "shoot control (by min variance) curve slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_shoot_sup_var_min_offset": {"acc": [1, 4, 7], "size": [], "description": "shoot control (by min variance) curve offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_shoot_sup_var_min_limit": {"acc": [0, 0, 7], "size": [2], "description": "shoot control (by min variance) curve limit", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_var_diff_slope": {"acc": [1, 0, 9], "size": [], "description": "shoot control (by variance difference) curve slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_shoot_sup_var_diff_offset": {"acc": [0, 4, 7], "size": [], "description": "shoot control (by variance difference) curve offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_shoot_sup_var_diff_limit": {"acc": [0, 0, 7], "size": [2], "description": "shoot control (by variance difference) curve limit", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_limit_sup_enable": {"acc": [0, 1], "size": [], "description": "adaptive limit control enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_limit_sup_max_over_change": {"acc": [0, 8, 2], "size": [], "description": "adaptive limit control max over change", "usage": "larger value means less limitation for pixel value over change", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_limit_sup_max_under_change": {"acc": [0, 8, 2], "size": [], "description": "adaptive limit control max under change", "usage": "larger value means less limitation for pixel value under change", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_limit_sup_max_over_gain": {"acc": [0, 5, 5], "size": [], "description": "adaptive limit control over change gain", "usage": "larger value means less limitation for pixel value over change", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_limit_sup_max_under_gain": {"acc": [0, 5, 5], "size": [], "description": "adaptive limit control under change gain", "usage": "larger value means less limitation for pixel value under change", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_enable": {"acc": [0, 1], "size": [], "description": "rgb color sharpen gain control enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_red_var5_shift": {"acc": [0, 3], "size": [], "description": "texture map shift bits for red color mask", "usage": "smaller value means more red edge pixels will not be affected by red sharpen gain", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_red_center": {"acc": [1, 7, 2], "size": [2], "description": "red color center uv", "usage": "set as uv value of red color", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_red_radius": {"acc": [0, 8, 2], "size": [2], "description": "red color radius", "usage": "larger value means more pixels will be selected in red color mask", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_red_dist_mode": {"acc": [0, 1], "size": [], "description": "red color distance calculation mode, 0: diamond, 1: rectangle", "usage": "set to 0 as default", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_red_dist_weight": {"acc": [0, 0, 3], "size": [4], "description": "red color distance weights of 4 directions", "usage": "larger value means less pixels will be selected in red color mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_red_gain": {"acc": [0, 1, 5], "size": [], "description": "red color sharpen gain", "usage": "larger value means stronger sharpen for red color", "constraints": "shp_rgb_ctrl_red_gain <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_red_gain_slope": {"acc": [0, 0, 9], "size": [], "description": "red color gain slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_red_gain_offset": {"acc": [1, 4, 5], "size": [], "description": "red color gain offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_blue_var5_shift": {"acc": [0, 3], "size": [], "description": "texture map shift bits for blue color mask", "usage": "smaller value means more blue edge pixels will not be affected by blue sharpen gain", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_blue_center": {"acc": [1, 7, 2], "size": [2], "description": "blue color center uv", "usage": "set as uv value of blue color", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_blue_radius": {"acc": [0, 8, 2], "size": [2], "description": "blue color radius", "usage": "larger value means more pixels will be selected in blue color mask", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_blue_dist_mode": {"acc": [0, 1], "size": [], "description": "blue color distance calculation mode, 0: diamond, 1: rectangle", "usage": "set to 0 as default", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_blue_dist_weight": {"acc": [0, 0, 3], "size": [4], "description": "blue color distance weights of 4 directions", "usage": "larger value means less pixels will be selected in blue color mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_blue_gain": {"acc": [0, 1, 5], "size": [], "description": "blue color sharpen gain", "usage": "larger value means stronger sharpen for blue color", "constraints": "shp_rgb_ctrl_blue_gain <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_blue_gain_slope": {"acc": [0, 0, 9], "size": [], "description": "blue color gain slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_blue_gain_offset": {"acc": [1, 4, 5], "size": [], "description": "blue color gain offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_green_center": {"acc": [1, 7, 2], "size": [2], "description": "green color center uv", "usage": "set as uv value of green color", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_green_radius": {"acc": [0, 8, 2], "size": [2], "description": "green color radius", "usage": "larger value means more pixels will be selected in green color mask", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_rgb_ctrl_green_dist_mode": {"acc": [0, 1], "size": [], "description": "green color distance calculation mode, 0: diamond, 1: rectangle", "usage": "set to 0 as default", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_green_dist_weight": {"acc": [0, 0, 3], "size": [4], "description": "green color distance weights of 4 directions", "usage": "larger value means less pixels will be selected in green color mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_green_gain": {"acc": [0, 3, 5], "size": [], "description": "green color sharpen gain", "usage": "larger value means stronger sharpen for green color", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_rgb_ctrl_green_gain_slope": {"acc": [1, 3, 9], "size": [], "description": "green color gain slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_rgb_ctrl_green_gain_offset": {"acc": [1, 4, 5], "size": [], "description": "green color gain offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_skin_ctrl_enable": {"acc": [0, 1], "size": [], "description": "skin color sharpen gain control enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_skin_ctrl_color_center": {"acc": [1, 7, 2], "size": [2], "description": "skin color center uv", "usage": "set as uv value of skin color", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_skin_ctrl_color_radius": {"acc": [0, 8, 2], "size": [2], "description": "skin color radius", "usage": "larger value means more pixels will be selected in skin color mask", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_skin_ctrl_color_dist_mode": {"acc": [0, 1], "size": [], "description": "red color distance calculation mode, 0: diamond, 1: rectangle", "usage": "set to 1 as default", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_skin_ctrl_color_dist_weight": {"acc": [0, 0, 3], "size": [4], "description": "skin color distance weights of 4 directions", "usage": "larger value means less pixels will be selected in skin color mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_skin_ctrl_color_slope": {"acc": [0, 2, 9], "size": [], "description": "skin color gain slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_skin_ctrl_var5_shift": {"acc": [0, 3], "size": [], "description": "texture map shift bits for skin color mask", "usage": "smaller value means more skin edge pixels will not be affected by skin sharpen gain", "constraints": "all", "type": "AX_U8", "partition": "-"}, "shp_skin_ctrl_var5_gain_slope": {"acc": [1, 0, 9], "size": [], "description": "skin color edge gain slope", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_S16", "partition": "-"}, "shp_skin_ctrl_var5_gain_offset": {"acc": [0, 4, 5], "size": [], "description": "skin color edge gain offset", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "shp_skin_ctrl_var5_gain_limit": {"acc": [0, 1, 5], "size": [2], "description": "skin color edge gain limit", "usage": "automatically calculated in algo logic", "constraints": "all", "type": "AX_U8", "partition": "-"}, "yadj_enable": {"acc": [0, 1], "size": [], "description": "yadj enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "yadj_lut": {"acc": [0, 8, 2], "size": [33], "description": "yadj lut, used to adjust brightness/contrast", "usage": "normally calculated in algo logic", "constraints": "all", "type": "AX_U16", "partition": "-"}, "yclip_enable": {"acc": [0, 1], "size": [], "description": "yclip enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "yclip_clip": {"acc": [0, 8, 0], "size": [2], "description": "yclip clip limit, [0]: lower limit, [1]: upper limit", "usage": "normally calculated in algo logic", "constraints": "yclip_clip[0] <= yclip_clip[1]", "type": "AX_U8", "partition": "-"}, "hsvc_enable": {"acc": [0, 1], "size": [], "description": "hsvc enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "hsvc_h_lut": {"acc": [1, 8, 7], "size": [25, 17], "description": "hsvc hue lut", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "hsvc_s_lut": {"acc": [0, 1, 15], "size": [25, 17], "description": "hsvc saturation lut", "usage": "", "constraints": "all", "type": "AX_U16", "partition": "-"}, "hsvc_yuv2rgb_matrix": {"acc": [1, 2, 8], "size": [3, 3], "description": "hsvc yuv2rgb matrix", "usage": "set as the same as inverted csc matrix", "constraints": "all", "type": "AX_S16", "partition": "-"}, "hsvc_rgb2yuv_matrix": {"acc": [1, 2, 8], "size": [3, 3], "description": "hsvc rgb2yuv matrix", "usage": "set as the same as csc matrix", "constraints": "all", "type": "AX_S16", "partition": "-"}, "hsvc_yuv2rgb_offset": {"acc": [1, 8, 2], "size": [2, 3], "description": "hsvc yuv2rgb offset", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "hsvc_rgb2yuv_offset": {"acc": [1, 8, 2], "size": [2, 3], "description": "hsvc rgb2yuv offset", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "ccmp_enable": {"acc": [0, 1], "size": [], "description": "ccmp enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "ccmp_y_lut": {"acc": [0, 1, 9], "size": [29], "description": "ccmp y gain lut", "usage": "smaller value means stronger saturation compression", "constraints": "all", "type": "AX_U16", "partition": "-"}, "ccmp_sat_lut": {"acc": [0, 1, 9], "size": [23], "description": "ccmp saturation gain lut", "usage": "smaller value means stronger saturation compression", "constraints": "all", "type": "AX_U16", "partition": "-"}, "cset_enable": {"acc": [0, 1], "size": [], "description": "cset enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "cset_io_flag": {"acc": [0, 1], "size": [], "description": "cset color target inverse selection flag, 0: inverse selection, 1: normal selection", "usage": "set to 1 normally", "constraints": "all", "type": "AX_U8", "partition": "-"}, "cset_color": {"acc": [1, 7, 2], "size": [2], "description": "cset color target new color uv value", "usage": "", "constraints": "all", "type": "AX_S16", "partition": "-"}, "cset_center_y": {"acc": [0, 8, 2], "size": [], "description": "cset color target y value", "usage": "set as y value of the specified color target", "constraints": "all", "type": "AX_U16", "partition": "-"}, "cset_center_uv": {"acc": [1, 7, 2], "size": [2], "description": "cset color target uv value", "usage": "set as uv value of the specified color target", "constraints": "all", "type": "AX_S16", "partition": "-"}, "cset_radius": {"acc": [0, 7, 2], "size": [3], "description": "cset color target radius", "usage": "larger value means more pixels will be selected in the color mask", "constraints": "all", "type": "AX_U16", "partition": "-"}, "cset_t_grad": {"acc": [0, 4], "size": [3], "description": "cset color mask transition gradient", "usage": "larger value means smoother/wider transition band in the color mask", "constraints": "all", "type": "AX_U8", "partition": "-"}, "cclip_enable": {"acc": [0, 1], "size": [], "description": "cclip enable, 0: disable, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "cclip_cmtx": {"acc": [1, 2, 5], "size": [2, 2], "description": "cclip color matrix", "usage": "normally calculated in algo logic", "constraints": "all", "type": "AX_S8", "partition": "-"}, "cclip_clip": {"acc": [1, 7, 0], "size": [2], "description": "cclip clip limit, [0]: lower limit, [1]: upper limit", "usage": "normally calculated in algo logic", "constraints": "cclip_clip[0] <= cclip_clip[1]", "type": "AX_S8", "partition": "-"}, "desat_enable": {"acc": [0, 1], "size": [], "description": "desat enable, 0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_strength": {"acc": [0, 4, 4], "size": [], "description": "desat strength, increase or decrease strength of desat", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_luma_lut": {"acc": [0, 1, 7], "size": [8], "description": "the luma ratio table for desat", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_sat_lut": {"acc": [0, 1, 7], "size": [6], "description": "the saturation ratio table for desat", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_angle_ratio_lut": {"acc": [0, 1, 7], "size": [16], "description": "the angle ratio table for desat", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_uv_val": {"acc": [1, 7, 2], "size": [2], "description": "desat uv color", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "desat_debug_enable": {"acc": [0, 1], "size": [], "description": "debug desat mask display enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_debug_thr": {"acc": [0, 1, 4], "size": [], "description": "debug threshold for desat mask", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "desat_debug_color": {"acc": [1, 8, 2], "size": [3], "description": "debug desat color fill", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}}}, "partition_configs": []}