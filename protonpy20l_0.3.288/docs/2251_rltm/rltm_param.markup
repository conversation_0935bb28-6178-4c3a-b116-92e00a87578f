h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| offset_in |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 1024 | 16.0 | hidden | 'raw_ltm.offset_in' | offset_in | common |
| offset_out |  | u8.4 | AX_U16 | [\] |  [0, 4095\] | [0.0, 255.9375\] | 256 | 16.0 | hidden | 'raw_ltm.offset_out' | offset_out | common |
| input_max_value |  | u14.6 | AX_U32 | [\] |  [0, 1048575\] | [0.0, 16383.984375\] | 16384 | 256.0 | hidden | 'raw_ltm.dgain' | input_max_value | common |
| dither_seed_enable |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 1 | None | hidden | 'raw_ltm.dither_seed_enable', 'raw_ltm.dither_seed' | dither seed enable | common |
| hdr_ratio |  | u9.8 | AX_U32 | [\] |  [0, 131071\] | [0.0, 511.99609375\] | 256 | 1.0 | hidden | 'raw_ltm.curve' | hdr ratio | common |
| w_win_hist |  | u19 | AX_U32 | [84, 64\] |  [0, 524287\] | [None, None\] | np.zeros((84, 64)).tolist() | None | hidden | 'raw_ltm.curve' | histogram of each hist window | user |
| detail_low_reset_coeffab |  | u8 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'raw_ltm.detail_low_reset_coeffab' | reset the guided filter coeff a&b to 1&0 | common |
| pic_h_w |  | u15.0 | AX_U16 | [2\] |  [128, 32767\] | [128.0, 32767.0\] | [1520, 2688\] | [1520.0, 2688.0\] | hidden |  | full input picture size | common |
| partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [\] | [\] | None | None | hidden | 'raw_ltm.pic_h_w', 'raw_ltm.offset_h_w', 'raw_ltm.ptn_h_w', 'raw_ltm.ptn_hist_roi_t_b_l_r', 'raw_ltm.win_size', 'raw_ltm_pst.mode_pst' | raw_ltm partition info | common |
| ifa_partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'ifa.output_hor_crop_enable', 'ifa.output_hor_crop_st', 'ifa.output_hor_crop_width' | ifa partition | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open |  | SW enable | user | None | None | None |
| dither_mode | dither_mode | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'raw_ltm.dither_mode', 'raw_ltm.dither_scale', 'raw_ltm.dither_seed' | dither enable | user | None | None | None |
| dither_scale | dither_scale | u8.4 | AX_U16 | [\] | [0, 4095\] | [0.0, 255.9375\] | 0 | 0.0 | open | 'raw_ltm.dither_mode', 'raw_ltm.dither_scale', 'raw_ltm.dither_seed' | dither strength | user | None | None | None |
| luma_weight | luma_weight | u1.7 | AX_U8 | [2\] | [0, 128\] | [0.0, 1.0\] | [64, 0\] | [0.5, 0.0\] | open | 'raw_ltm.luma_weight' | weight of luma_max and luma_rgb for luma and luma_detail calculate | user | None | None | None |
| highlight_sup | highlight_sup | u5.3 | AX_U8 | [\] | [0, 255\] | [0.0, 31.875\] | 0 | 0.0 | open |  | highlight suppression | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| s_curve | s_curve | u1.15 | AX_U16 | [1025\] | [0, 32768\] | [0.0, 1.0\] | (np.arange(1025)/1024.0).tolist() | (np.arange(1025)/1024.0).tolist() | open |  | s-curve | user | ['gain/lux'\] | [12\] | ['linear'\] |
| base_curve | base_curve | u1.15 | AX_U16 | [513\] | [0, 32768\] | [0.0, 1.0\] | (np.arange(513)/512.0).tolist() | (np.arange(513)/512.0).tolist() | open |  | base-curve | user | ['gain/lux'\] | [12\] | ['linear'\] |
| gtm_sw_dgain_enable | gtm_sw_dgain_enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'raw_ltm.curve' | enable dgain mode | user | None | None | None |
| gtm_sw_dgain | gtm_sw_dgain | u8.8 | AX_U16 | [\] | [256, 65535\] | [1.0, 255.99609375\] | 256 | 1.0 | open | 'raw_ltm.curve' | dgain when sw gain is on | user | None | None | None |
| local_factor | local_factor | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 89 | 0.6953125 | open | 'raw_ltm.curve' | factor for local hist and global hist | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| rltm_strength | rltm_strength | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 128 | 1.0 | open | 'raw_ltm.curve' | rltm strength | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| base_curve_strength | base_curve_strength | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 0 | 0.0 | open | 'raw_ltm.curve' | rltm base curve strength | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| contrast_strength | contrast_strength | u1.7 | AX_U8 | [\] | [1, 255\] | [0.0078125, 1.9921875\] | 42 | 0.328125 | open | 'raw_ltm.curve' | contrast strength | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| pst_gamma | pst_gamma | u3.5 | AX_U8 | [\] | [32, 255\] | [1.0, 7.96875\] | 58 | 1.8125 | open |  | pst gamma | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| dark_contrast_enhance | dark_contrast_enhance | u0.8 | AX_U8 | [2\] | [0, 255\] | [0.0, 0.99609375\] | [0, 0\] | [0.0, 0.0\] | open | 'raw_ltm.curve' | Max Luma Value & Pixel Num for Contrast Enhance | user | ['gain/lux'\] | [12\] | ['linear'\] |
| k_max | k_max | u12.4 | AX_U16 | [\] | [16, 65535\] | [1.0, 4095.9375\] | 65535 | 4095.9375 | open | 'raw_ltm.curve' | max gain for rltm | user | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_sigma_dis | detail_sigma_dis | u3.13 | AX_U16 | [\] | [1, 65535\] | [0.0001220703125, 7.9998779296875\] | 9000 | 1.0986328125 | open | 'raw_ltm.kernel_dis' | bilateral filter sigma distance | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| detail_sigma_val | detail_sigma_val | u3.13 | AX_U16 | [\] | [1, 65535\] | [0.0001220703125, 7.9998779296875\] | 3000 | 0.3662109375 | open | 'raw_ltm.sigma_val_lut' | bilateral filter sigma value | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| detail_coring_pos | detail_coring_pos | u3.13 | AX_U16 | [\] | [0, 65535\] | [0.0, 7.9998779296875\] | 40 | 0.0048828125 | open | 'raw_ltm.detail_coring_pos' | the coring for high freq pos detail | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| detail_coring_neg | detail_coring_neg | u3.13 | AX_U16 | [\] | [0, 65535\] | [0.0, 7.9998779296875\] | 40 | 0.0048828125 | open | 'raw_ltm.detail_coring_neg' | the coring for high freq neg detail | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| detail_gain_pos | detail_gain_pos | u3.5 | AX_U8 | [\] | [0, 255\] | [0.0, 7.96875\] | 32 | 1.0 | open | 'raw_ltm.detail_gain_pos' | the gain for high freq pos detail | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| detail_gain_neg | detail_gain_neg | u3.5 | AX_U8 | [\] | [0, 255\] | [0.0, 7.96875\] | 32 | 1.0 | open | 'raw_ltm.detail_gain_neg' | the gain for high freq neg detail | user | ['gain/lux', 'hdr_ratio'\] | [12, 4\] | ['linear', 'linear'\] |
| detail_limit_pos | detail_limit_pos | u3.13 | AX_U16 | [\] | [0, 65535\] | [0.0, 7.9998779296875\] | 65535 | 7.9998779296875 | open | 'raw_ltm.detail_limit_pos' | the limit for high freq pos detail | user | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_limit_neg | detail_limit_neg | u3.13 | AX_U16 | [\] | [0, 65535\] | [0.0, 7.9998779296875\] | 65535 | 7.9998779296875 | open | 'raw_ltm.detail_limit_neg' | the limit for high freq neg detail | user | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_extra_strength_pos | detail_extra_strength_pos | u4.4 | AX_U8 | [\] | [0, 255\] | [0.0, 15.9375\] | 0 | 0.0 | open | 'raw_ltm.detail_extra_strength_pos' | the global strength for high freq pos detail | user | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_extra_strength_neg | detail_extra_strength_neg | u4.4 | AX_U8 | [\] | [0, 255\] | [0.0, 15.9375\] | 0 | 0.0 | open | 'raw_ltm.detail_extra_strength_neg' | the global strength for high freq neg detail | user | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_strength_lut | detail_strength_lut | u4.4 | AX_U8 | [32\] | [0, 255\] | [0.0, 15.9375\] | [16, 16, ... , 16\] | [1.0, 1.0, ... , 1.0\] | open | 'raw_ltm.detail_strength_lut' | the strength for high freq detail by detail | user | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_strength_lut_pos_low | detail_strength_lut_pos_low | u1.7 | AX_U8 | [33\] | [0, 255\] | [0.0, 1.9921875\] | [128, 128, ... , 128\] | [1.0, 1.0, ... , 1.0\] | open | 'raw_ltm.detail_strength_lut_pos_low' | the strength for low freq pos detail by luma | user | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_strength_lut_neg_low | detail_strength_lut_neg_low | u1.7 | AX_U8 | [33\] | [0, 255\] | [0.0, 1.9921875\] | [128, 128, ... , 128\] | [1.0, 1.0, ... , 1.0\] | open | 'raw_ltm.detail_strength_lut_neg_low' | the strength for low freq neg detail by luma | user | ['gain/lux'\] | [12\] | ['linear'\] |
| downsample_ratio | downsample_ratio | u8 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open |  | the downsample ratio of low freq luma | user | None | None | None |
| detail_low_enable | detail_low_enable | u8 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'raw_ltm.detail_low_enable' | low freq enhance enable | user | None | None | None |
| detail_gain_pos_low | detail_gain_pos_low | u3.5 | AX_U8 | [\] | [0, 255\] | [0.0, 7.96875\] | 32 | 1.0 | open | 'raw_ltm.detail_gain_pos_low' | the gain for low freq pos detail | user | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_gain_neg_low | detail_gain_neg_low | u3.5 | AX_U8 | [\] | [0, 255\] | [0.0, 7.96875\] | 32 | 1.0 | open | 'raw_ltm.detail_gain_neg_low' | the gain for low freq neg detail | user | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_limit_pos_low | detail_limit_pos_low | u3.13 | AX_U16 | [\] | [0, 65535\] | [0.0, 7.9998779296875\] | 65535 | 7.9998779296875 | open | 'raw_ltm.detail_limit_pos_low' | the limit for low freq pos detail | user | ['gain/lux'\] | [12\] | ['linear'\] |
| detail_limit_neg_low | detail_limit_neg_low | u3.13 | AX_U16 | [\] | [0, 65535\] | [0.0, 7.9998779296875\] | 65535 | 7.9998779296875 | open | 'raw_ltm.detail_limit_neg_low' | the limit for low freq neg detail | user | ['gain/lux'\] | [12\] | ['linear'\] |
| alpha | alpha | u1.15 | AX_U16 | [\] | [0, 32768\] | [0.0, 1.0\] | 8192 | 0.25 | open | 'raw_ltm.curve' | temporal filter strength | user | None | None | None |
| reset | reset | u2 | AX_U8 | [\] | [0, 2\] | [None, None\] | 2 | None | open |  | the num frame that use current rltm curve only | user | None | None | None |
| stop_updating | stop_updating | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open |  | stop updating curve | user | None | None | None |
| sigma_dis_pst | sigma_dis_pst | u3.13 | AX_U16 | [\] | [1, 65535\] | [0.0001220703125, 7.9998779296875\] | 9000 | 1.0986328125 | open | 'raw_ltm_pst.kernel_dis_pst' | spatial domain weight low freq | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sigma_val_pst | sigma_val_pst | u3.13 | AX_U16 | [\] | [1, 2048\] | [0.0001220703125, 0.25\] | 1000 | 0.1220703125 | open | 'raw_ltm_pst.sigma_val_lut_pst' | intensity domain weight low freq | user | ['gain/lux'\] | [12\] | ['linear'\] |
| coeff_win_r | win_r | u3 | AX_U8 | [\] | [0, 5\] | [None, None\] | 2 | None | open | 'ifa.guided_win_r' | guided filter win_radius | user | None | None | None |
| coeff_eps | eps | u16 | AX_U16 | [\] | [0, 65535\] | [None, None\] | 200 | None | open | 'ifa.guided_epsilon' | guided filter eps | user | None | None | None |
| sigma_dis_blur | sigma_dis_blur | u3.13 | AX_U16 | [\] | [1, 65535\] | [0.0001220703125, 7.9998779296875\] | 9000 | 1.0986328125 | open | 'ifa.blur_weight' | blur coeff a and b | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |