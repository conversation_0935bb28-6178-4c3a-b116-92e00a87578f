h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| mat_adj_offset |  | s8 | AX_S16 | [2, 3\] |  [np.int64(-256), np.int64(255)\] | [None, None\] | [[0, 0, 0\], [0, 0, 0\]\] | None | hidden | 'tde.mat_adj_offset' | in && out offset for DDR write adjustment | common |
| clip_offset |  | s8 | AX_S16 | [2, 3\] |  [np.int64(-256), np.int64(255)\] | [None, None\] | [[0, 0, 0\], [0, 0, 0\]\] | None | hidden | 'tde.clip_offset' | Global alpha enable control bit. It's priority is lower than special alpha | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| v0_format | v0 format | u6 | AX_U8 | [\] | [np.int64(0), np.int64(63)\] | [None, None\] | 0 | None | open | 'tde.v0_format', 'tde.mat_adj_enable', 'tde.mat_adj_matrix', 'tde.to422_filter_enable' | v0 input`s format | user | None | None | None |
| v0_global_chroma | v0 sp global chroma | u8 | AX_U8 | [2\] | [np.int64(0), np.int64(255)\] | [None, None\] | [128, 128\] | None | open | 'tde.v0_global_chroma' | v0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10 | user | None | None | None |
| output_format | output format | u3 | AX_U8 | [\] | [np.int64(0), np.int64(7)\] | [None, None\] | 0 | None | open | 'tde.output_format', 'tde.mat_adj_enable', 'tde.mat_adj_matrix', 'tde.to422_filter_enable' | dpu output format | user | None | None | None |
| dither_enable | dither enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 1 | None | open | 'tde.dither_enable', 'tde.dither_seed_ch0', 'tde.dither_seed_ch1', 'tde.dither_seed_ch2' | dither enable control bit | user | None | None | None |
| clip_enable | clip enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.clip_enable' | clip enable control bit | user | None | None | None |
| clip_gain | clip gain | u2.8 | AX_U16 | [3\] | [np.int64(0), np.int64(1023)\] | [np.float64(0.0), np.float64(3.99609375)\] | [256, 256, 256\] | [np.float64(1.0), np.float64(1.0), np.float64(1.0)\] | open | 'tde.clip_gain' | gains for 3 channels | user | None | None | None |
| clip_range | clip range | s8 | AX_S16 | [3, 2\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [[0, 255\], [0, 255\], [0, 255\]\] | None | open | 'tde.clip_range' | Global alpha enable control bit. It's priority is lower than special alpha | user | None | None | None |
| lba_enable | lba enable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.lba_enable' | lba enable control bit | user | None | None | None |
| lba_ext_size | lba ext size | u13 | AX_U16 | [4\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [80, 80, 80, 80\] | None | open | 'tde.lba_ext_size' | lba extension size(paddings on edges) | common | None | None | None |
| lba_background | lba background | u8 | AX_U8 | [3\] | [np.int64(0), np.int64(255)\] | [None, None\] | [100, 255, 255\] | None | open | 'tde.lba_background' | lba background color | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |