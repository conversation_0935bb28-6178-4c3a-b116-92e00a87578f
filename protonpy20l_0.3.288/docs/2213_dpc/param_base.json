{"partition_configs": ["dpc.static_info"], "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "dpc is 0x2213", "type": "AX_U16", "default": "0x2213"}}, "params": {"enable": {"display": "Dpc Enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "0:disable, 1:enable", "hidden": 0, "auto": 0, "target_conf": ["dpc.enable"], "type": "AX_U8", "dependency": ""}, "offset_in": {"display": "", "acc": [0, 8, 6], "size": [], "range": [0.0, 255.984375], "default": 1, "comment": "offset in", "hidden": 1, "auto": 0, "target_conf": ["dpc.offset_in"], "type": "AX_U16", "dependency": "common"}, "offset_out": {"display": "", "acc": [0, 8, 6], "size": [], "range": [0.0, 255.984375], "default": 1, "comment": "offset out", "hidden": 1, "auto": 0, "target_conf": ["dpc.offset_out"], "type": "AX_U16", "dependency": "common"}, "shot_noise_coefs": {"display": "Shot Noise Coefs", "acc": [1, 0, 31], "size": [4, 2], "range": [-1.0, 0.9999999995343387], "default": [[0.5, 0.05], [0.5, 0.05], [0.5, 0.05], [0.5, 0.05]], "comment": "shot noise coefs read from ACP module", "hidden": 1, "auto": 0, "target_conf": ["dpc.noise_lut"], "type": "AX_S32", "dependency": "common"}, "read_noise_coefs": {"display": "Read Noise Coefs", "acc": [1, 0, 31], "size": [4, 3], "range": [-1.0, 0.9999999995343387], "default": [[0.02, 0.02, 0.01], [0.02, 0.02, 0.01], [0.02, 0.02, 0.01], [0.02, 0.02, 0.01]], "comment": "read noise coefs read from ACP module", "hidden": 1, "auto": 0, "target_conf": ["dpc.noise_lut"], "type": "AX_S32", "dependency": "common"}, "noise_ratio": {"display": "Noise Lut Ratio", "acc": [0, 4, 10], "size": [], "range": [0.0, 15.9990234375], "default": 1.0, "comment": "noise level gain, the larger the value, the larger the noise level, less defect pixels", "hidden": 0, "auto": 1, "target_conf": ["dpc.noise_lut"], "type": "AX_U16", "dependency": ""}, "static_dpc_enable": {"display": "Static Dpc Enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "0:disable static dpc, 1:enable static dpc", "hidden": 0, "auto": 0, "target_conf": ["dpc.static_enable"], "type": "AX_U8", "dependency": ""}, "static_dpc_length": {"display": "Static dpc Length", "acc": [0, 16], "size": [], "range": [0, 4096], "default": 0, "comment": "static defect pixel storage length", "hidden": 0, "auto": 0, "target_conf": ["dpc.static_info"], "type": "AX_U16", "dependency": ""}, "static_dpc_buffer": {"display": "Static dpc Buffer", "acc": [0, 32], "size": [1024], "range": [0, 4294967295], "default": "np.zeros(8192).tolist()", "comment": "static defect pixel coordinates info", "hidden": 0, "auto": 0, "target_conf": ["dpc.static_info"], "type": "AX_U32", "dependency": ""}, "partition_info": {"display": "partition info", "size": [], "type": "ax_isp_ptn_info_t", "target_conf": ["dpc.static_info"], "range": [], "default": [], "comment": "partition related info", "hidden": 1, "dependency": "common", "auto": 0}, "dynamic_dpc_enable": {"display": "Dynamic Dpc Enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "0:disable dynamic dpc, 1:enable dynamic dpc", "hidden": 0, "auto": 0, "target_conf": ["dpc.dp_det_m2", "dpc.calc_limit_enable"], "type": "AX_U8", "dependency": ""}, "predet_enable": {"display": "predet Enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "0:disable predet, 1:enable predet", "hidden": 0, "auto": 0, "target_conf": ["dpc.quick_det_enable"], "type": "AX_U8", "dependency": ""}, "predet_level_slope": {"display": "Predet Level Slope", "acc": [0, 0, 4], "size": [], "range": [0.0, 0.9375], "default": 0.25, "comment": "defect pixel margin gain for quick detection, the larger the value, less defect pixel detected", "hidden": 0, "auto": 1, "target_conf": ["dpc.quick_det_th_slope"], "type": "AX_U8", "dependency": ""}, "predet_level_offset": {"display": "Predet Level Offset", "acc": [0, 8, 6], "size": [], "range": [0.0, 255.984375], "default": 0.0, "comment": "defect pixel base margin for quick detection, the larger the value, less defect pixel detected", "hidden": 0, "auto": 1, "target_conf": ["dpc.quick_det_th_offset"], "type": "AX_U16", "dependency": ""}, "predet_level_max": {"display": "Predet Level Max", "acc": [0, 8, 6], "size": [], "range": [0.0, 255.984375], "default": 256.0, "comment": "defect pixel max margin for quick detection, the larger the value, less defect pixel detected", "hidden": 0, "auto": 1, "target_conf": ["dpc.quick_det_th_max"], "type": "AX_U16", "dependency": ""}, "defective_pixel_type": {"display": "Defective Pixel Type", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Dynamic dp Detect Module, 0:single defective pixel mode, 1:dual defective pixel mode", "hidden": 0, "auto": 1, "target_conf": ["dpc.dp_det_mode"], "type": "AX_U8", "dependency": ""}, "det_fine_strength": {"display": "Detect Fine Strength", "acc": [0, 0, 6], "size": [], "range": [0.0, 0.984375], "default": 0.75, "comment": "local detction strength for defect pixel detection, the larger the value, more defect pixels will be detected", "hidden": 0, "auto": 1, "target_conf": ["dpc.dp_det_th_ratio"], "type": "AX_U8", "dependency": ""}, "det_coarse_strength": {"display": "Detect Coarse Strength", "acc": [0, 4, 4], "size": [], "range": [0.0, 15.9375], "default": 14.75, "comment": "defect pixel detection threshold, the larger the value, more defect pixels are detected", "hidden": 0, "auto": 1, "target_conf": ["dpc.dp_det_m1"], "type": "AX_U8", "dependency": ""}, "dynamic_dpc_strength": {"display": "Dynamic Dpc Strength", "acc": [0, 4, 8], "size": [], "range": [0.0, 15.99609375], "default": 0.5, "comment": "the larger the value, the larger the defect pixel probability", "hidden": 0, "auto": 1, "target_conf": ["dpc.dp_det_m2"], "type": "AX_U16", "dependency": ""}, "edge_strength": {"display": "Edge Strength", "acc": [0, 1, 7], "size": [], "range": [0.0, 1.9921875], "default": 0.796875, "comment": "direction interpolation ratio used in interpolation, the larger the value, more direction interpolated result used in interpolation, better performance for defective pixels at edge locations", "hidden": 0, "auto": 1, "target_conf": ["dpc.dp_cor_m3"], "type": "AX_U8", "dependency": ""}, "hot_cold_type_strength": {"display": "Hot Cold Type Strength", "acc": [0, 1, 7], "size": [], "range": [0.0, 1.0], "default": 0.25, "comment": "hot_cold result ratio, the larger the value, more hot_cold result used", "hidden": 0, "auto": 1, "target_conf": ["dpc.dp_cor_m4"], "type": "AX_U8", "dependency": ""}, "sup_wink_threshold": {"display": "Suppress wink <PERSON><PERSON><PERSON><PERSON>", "acc": [0, 4, 4], "size": [], "range": [0.0, 15.9375], "default": 1.0, "comment": "the threshold to use direction interpoation or local avg interpolation result", "hidden": 0, "auto": 1, "target_conf": ["dpc.dp_cor_m5"], "type": "AX_U8", "dependency": ""}, "color_limit_enable": {"display": "Color Limit Enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "0:disable color limit, 1:enable color limit", "hidden": 0, "auto": 0, "target_conf": ["dpc.calc_limit_enable"], "type": "AX_U8", "dependency": ""}, "dynamic_dp_upperlimit": {"display": "Dynamic Dp Color Upperlimit Offset", "acc": [0, 8, 6], "size": [], "range": [0.0, 255.984375], "default": 4.0, "comment": "up margin for color limit, the larger the value, less color limitation", "hidden": 0, "auto": 1, "target_conf": ["dpc.margin_u"], "type": "AX_U16", "dependency": ""}, "dynamic_dp_lowerlimit": {"display": "Dynamic Dp Color Lowerlimit Offset", "acc": [0, 8, 6], "size": [], "range": [0.0, 255.984375], "default": 4.0, "comment": "low margin for color limit, the larger the value, less color limitation", "hidden": 0, "auto": 1, "target_conf": ["dpc.margin_l"], "type": "AX_U16", "dependency": ""}, "static_dp_upperlimit": {"display": "Static Dp Color Upperlimit Offset", "acc": [0, 8, 6], "size": [], "range": [0.0, 255.984375], "default": 4.0, "comment": "up margin for color limit, the larger the value, less color limitation", "hidden": 0, "auto": 1, "target_conf": ["dpc.margin_u"], "type": "AX_U16", "dependency": ""}, "static_dp_lowerlimit": {"display": "Static  Dp Color Lowerlimit Offset", "acc": [0, 8, 6], "size": [], "range": [0.0, 255.984375], "default": 4.0, "comment": "low margin for color limit, the larger the value, less color limitation", "hidden": 0, "auto": 1, "target_conf": ["dpc.margin_l"], "type": "AX_U16", "dependency": ""}, "normal_pixel_upperlimit": {"display": "Normal Pixel Color Upperlimit Offset", "acc": [0, 8, 6], "size": [], "range": [0.0, 255.984375], "default": 4.0, "comment": "up margin for color limit, the larger the value, less color limitation", "hidden": 0, "auto": 1, "target_conf": ["dpc.margin_u"], "type": "AX_U16", "dependency": ""}, "normal_pixel_lowerlimit": {"display": "Normal Pixel Color Lowerlimit Offset", "acc": [0, 8, 6], "size": [], "range": [0.0, 255.984375], "default": 4.0, "comment": "low margin for color limit, the larger the value, less color limitation", "hidden": 0, "auto": 1, "target_conf": ["dpc.margin_l"], "type": "AX_U16", "dependency": ""}, "wb_gain": {"display": "<PERSON>b <PERSON><PERSON>", "acc": [0, 4, 8], "size": [4], "range": [0.0, 15.99609375], "default": [1.0, 1.0, 1.0, 1.0], "comment": "wbc gain. calculated wbc(fw) based on sensor setting and stat from blc(fw), This param is obtained from isp_result_context_t, The value of wbgain  is taken from module AWB and  made up of wb_rGain,wb_grGain ,wb_gbGain and wb_bGain.", "hidden": 1, "auto": 0, "target_conf": ["dpc.wb_gain", "dpc.inv_wb_gain"], "type": "AX_U8", "dependency": "common"}, "again": {"display": "Again", "acc": [0, 22, 10], "size": [], "range": [0.0, 4194303.9990234375], "default": 1.0, "comment": "again used in calc noise lut", "hidden": 1, "auto": 0, "target_conf": [], "type": "AX_U32", "dependency": "common"}}, "submodules": {"setup": {"params": ["offset_in", "offset_out"], "configs": ["dpc.dp_cor_dir_wt_r2", "dpc.dp_cor_ch_mode", "dpc.dp_cor_clip_ratio", "dpc.color_limit_ratio", "dpc.offset_in", "dpc.offset_out"]}, "partition": {"params": ["partition_info", "static_dpc_length", "static_dpc_buffer"], "configs": []}, "calc_noise": {"params": ["shot_noise_coefs", "read_noise_coefs", "noise_ratio", "again"], "configs": []}, "calc_wb_gain": {"params": ["wb_gain"], "configs": []}, "calc_top_control": {"params": ["enable", "predet_enable"], "configs": []}, "calc_quick_det": {"params": ["predet_level_slope", "predet_level_offset", "predet_level_max"], "configs": []}, "calc_dp_strength": {"params": ["static_dpc_enable", "dynamic_dpc_enable", "color_limit_enable", "defective_pixel_type", "det_fine_strength", "det_coarse_strength", "dynamic_dpc_strength"], "configs": []}, "calc_dp_correct": {"params": ["edge_strength", "hot_cold_type_strength", "sup_wink_threshold"], "configs": []}, "calc_color_limit": {"params": ["dynamic_dp_upperlimit", "dynamic_dp_lowerlimit", "static_dp_upperlimit", "static_dp_lowerlimit", "normal_pixel_upperlimit", "normal_pixel_lowerlimit"], "configs": []}}, "target_module": {"mc20l": {"dpc": {"id": 1400, "method": 0}}}, "structs": {}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "configs": {"dpc": {"enable": {"acc": [0, 1], "size": [], "description": "DPC bypass/enable, 0: bypass, 1: enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "offset_in": {"acc": [0, 8, 6], "size": [], "description": "minus offset from input", "usage": "normally set [16]", "constraints": "all", "type": "AX_U16", "partition": "-"}, "offset_out": {"acc": [0, 8, 6], "size": [], "description": "add offset from output", "usage": "normally set [16]", "constraints": "all", "type": "AX_U16", "partition": "-"}, "noise_lut": {"acc": [0, 8, 6], "size": [9], "description": "noise lut for defect pixel detection", "usage": "adjust according to sensor gain", "constraints": "all", "type": "AX_U16", "partition": "-"}, "static_enable": {"acc": [0, 1], "size": [], "description": "enable static dp correction or not, 0:disable 1:enable", "usage": "", "constraints": "all", "type": "AX_U8", "partition": "-"}, "static_info": {"acc": [0, 14], "size": [16384, 2], "description": "the static defect points coordinates info, [[2^14-1, 2^14-1]] mean no static_info, max 16384 coordinates, each coordinate represented by(x, y)", "usage": "", "constraints": "coordinates <= image_size.must be in ascending order, first col, then row.", "type": "AX_U16", "partition": "support"}, "wb_gain": {"acc": [0, 4, 4], "size": [2], "description": "wb gain for r and b channel, first value represent r gain, second value represent b gain", "usage": "for linear input, use AWB gain, for non-linear input, algo logic use DPC stat to calc gain", "constraints": "[1.0, 16.0)", "type": "AX_U8", "partition": "-"}, "inv_wb_gain": {"acc": [0, 1, 7], "size": [2], "description": "inverse wb gain for r and b channel, first value represent r gain, second value represent b gain", "usage": "set 1 / wb_gain", "constraints": "(0, 1.0] and set inv_wb_gain = 1 / wb_gain", "type": "AX_U8", "partition": "-"}, "dp_det_mode": {"acc": [0, 1], "size": [], "description": "two loacl dif method for defect pixel detection, 0:max-min mode,1:max2-min2 mode", "usage": "the larger the value, more defect pixels will be detected", "constraints": "", "type": "AX_U8", "partition": "-"}, "dp_det_th_ratio": {"acc": [0, 0, 6], "size": [], "description": "local diff str for defect pixel detection, 0:max(max-ave, ave-min)~1.0:min(max-ave, ave-min)", "usage": "the larger the value, more defect pixels will be detected, ", "constraints": "[0,1.0)", "type": "AX_U8", "partition": "-"}, "dp_det_m1": {"acc": [0, 4, 4], "size": [], "description": "local diff gain for defect pixel detection", "usage": "the larger the value, less defect pixels will be detected", "constraints": "[0,16.0)", "type": "AX_U8", "partition": "-"}, "dp_det_m2": {"acc": [0, 4, 8], "size": [], "description": "defect pixel probability gain, the larger the defect pixel probability", "usage": "the larger the value, the larger the defect pixel probability and more pixel will be corrected", "constraints": "[0,16.0)", "type": "AX_U16", "partition": "-"}, "dp_cor_ch_mode": {"acc": [0, 1], "size": [], "description": "mode for direction detection, 0:NON_CH_WISE,  for R channel, use both R channel and surround B,G to dirDirection, 1:CH_WISE, for R channel, just use surround R to dirDirection", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "dp_cor_clip_ratio": {"acc": [0, 0, 6], "size": [], "description": "local max and min clip margin, 0:no clip ~ 1.0:clip margin equal to local dif", "usage": "", "constraints": "[0,1.0)", "type": "AX_U8", "partition": "-"}, "dp_cor_dir_wt_r2": {"acc": [0, 1, 4], "size": [], "description": "channel-wise direction detection ratio for deirection detection, direction = non channel-wise direction + channel-wise direction * dp_cor_dir_wt_r2", "usage": "the larger the value, more channel-wise direction used", "constraints": "[0,2.0)", "type": "AX_U8", "partition": "-"}, "dp_cor_m3": {"acc": [0, 1, 7], "size": [], "description": "direction interpolation ratio used in interpolation", "usage": "the larger the value, more direction interpolated result used in interpolation", "constraints": "[0,2.0)", "type": "AX_U8", "partition": "-"}, "dp_cor_m4": {"acc": [0, 1, 7], "size": [], "description": "interpolation reusult modify ratio according to hot or cold type", "usage": "the larger the value, more interpolation result used, less modify", "constraints": "[0,1.0]", "type": "AX_U8", "partition": "-"}, "dp_cor_m5": {"acc": [0, 4, 4], "size": [], "description": "the threshold to use direction interpoation or local avg interpolation result", "usage": "the larger the value, more avg interpolation result used, less direction interpolation", "constraints": "[0,16.0)", "type": "AX_U8", "partition": "-"}, "quick_det_enable": {"acc": [0, 1], "size": [], "description": "enable quick detection enable or not, 0:disable,all pixel skip to normal deteciton and correction;1:enable,use quick detection to select defect pixel, then for quick detected bad pixel, skip to normal detection and correction.", "usage": "set enable in usual to save power", "constraints": "", "type": "AX_U8", "partition": "-"}, "quick_det_th_slope": {"acc": [0, 0, 4], "size": [], "description": "defect pixel margin gain for quick detection", "usage": "the larger the value, the larger the margin, less defect pixel detected", "constraints": "[0,1.0)", "type": "AX_U8", "partition": "-"}, "quick_det_th_offset": {"acc": [0, 8, 6], "size": [], "description": "defect pixel base margin for quick detection", "usage": "the larger the value, the larger the margin, less defect pixel detected", "constraints": "[0,256.0)", "type": "AX_U16", "partition": "-"}, "quick_det_th_max": {"acc": [0, 8, 6], "size": [], "description": "defect pixel max margin for quick detection", "usage": "the larger the value, the larger the margin, less defect pixel detected", "constraints": "[0,256.0)", "type": "AX_U16", "partition": "-"}, "calc_limit_enable": {"acc": [0, 1], "size": [], "description": "enable color limit or not, 0:disable,1:enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "margin_u": {"acc": [0, 8, 6], "size": [3], "description": "up margin for color limit, index 0-3 represented bad pixel type[DYNAMIC_DP, STATIC_DP, NOT_DP]", "usage": "the larger the value, less color limitation", "constraints": "[0,256.0)", "type": "AX_U16", "partition": "-"}, "margin_l": {"acc": [0, 8, 6], "size": [3], "description": "low margin for color limit, index 0-3 represented bad pixel type[DYNAMIC_DP, STATIC_DP, NOT_DP]", "usage": "the larger the value, less color limitation", "constraints": "[0,256.0)", "type": "AX_U16", "partition": "-"}, "color_limit_ratio": {"acc": [0, 1, 7], "size": [3], "description": "color limit ratio, index 0-3 represented defect pixel type[DYNAMIC_DP, STATIC_DP, NOT_DP]", "usage": "the larger the value, less color limitation, means stronger saturation", "constraints": "[0, 1.0]", "type": "AX_U8", "partition": "-"}}}}