{"v0_format": {"api": "nV0Format", "display": "v0Format", "comments": "v0 format", "hint": "Accuracy: U6.0 Range: [0, 63]"}, "v0_global_chroma": {"api": "nGlobalChroma", "display": "globalChroma", "comments": "v0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "output_format": {"api": "nOutputFormat", "display": "outputFormat", "comments": "dpu output format", "hint": "Accuracy: U3.0 Range: [0, 7]"}, "dither_enable": {"api": "bD<PERSON>er<PERSON>nable", "display": "ditherEnable", "comments": "dither enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "clip_enable": {"api": "bClipEnable", "display": "clipEnable", "comments": "clip enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "clip_gain": {"api": "nClipGain", "display": "gain", "comments": "gains for 3 channels", "hint": "Accuracy: U2.8 Range: [0, 1023]"}, "clip_range": {"api": "nClipRange", "display": "range", "comments": "clamp range for 3 channels", "hint": "Accuracy: S8.0 Range: [-256, 255]"}, "lba_enable": {"api": "bLbaEnable", "display": "lbaEnable", "comments": "lba enable control bit", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "lba_ext_size": {"api": "nExtSize", "display": "extSize", "comments": "lba extension size(paddings on edges)", "hint": "Accuracy: U13.0 Range: [0, 8191]"}, "lba_background": {"api": "nBackground", "display": "background", "comments": "lba background color", "hint": "Accuracy: U8.0 Range: [0, 255]"}}