h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| pre_debug_enable |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 1 | None | hidden | 'y_2dnr.enable' | interact with pre module debug mode, if pre module debug enable, then disable y2dnr module. 0:disable, 1:enable | common |
| debug_mode |  | u4 | AX_U8 | [\] |  [0, 8\] | [None, None\] | 0 | None | hidden | 'y_2dnr.debug_mode' | debug mode, 0: normal, 1: motion mask, 2: bp mask, 3:flat weight, 4:detail weight, 5:edge weight, 6:var, 7:sf1 out, 8:sf2 out | common |
| debug_gain |  | u3 | AX_U8 | [\] |  [0, 7\] | [None, None\] | 0 | None | hidden | 'y_2dnr.debug_gain' | debug mode gain for visualization | common |
| dither_seed_enable |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 1 | None | hidden | 'y_2dnr.dither_seed_enable' | 0: use stat dither_seed, 1: use conf dither_seed. set 1 for the 1st frame, then set 0 from the 2nd frame onwards. | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'y_2dnr.enable' | 0:disable, 1:enable |  | None | None | None |
| motion_enable | motion enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'y_2dnr.motion_mask_enable' | motion enable, 0:disable, 1:enable |  | None | None | None |
| dir_strength | dir strength | u0.4 | AX_U8 | [\] | [0, 15\] | [0.0, 0.9375\] | 12 | 0.75 | open | 'y_2dnr.dir_det_ud_scale', 'y_2dnr.dir_det_dir_scale' | direction detection strength |  | ['gain/lux'\] | [16\] | ['linear'\] |
| dir_base | dir base | u8.4 | AX_U16 | [\] | [0, 4095\] | [0.0, 255.9375\] | 512 | 32.0 | open | 'y_2dnr.dir_det_edge_noise' | base noise for direciton detection |  | ['gain/lux'\] | [16\] | ['linear'\] |
| dpc_enable | dpc enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'y_2dnr.bpc_en' | dpc enable, 0:disable, 1:enable |  | None | None | None |
| dp_det_mode | dp detection mode | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'y_2dnr.bp_det_mode' | dp detection mode, 0:signgle, 1:dual |  | None | None | None |
| dp_det_strength | dp detection strength | u4.4 | AX_U8 | [\] | [0, 255\] | [0.0, 15.9375\] | 16 | 1.0 | open | 'y_2dnr.bp_det_m1' | dp detection strength |  | ['gain/lux'\] | [16\] | ['linear'\] |
| dp_strength | dp strength | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 256 | 1.0 | open | 'y_2dnr.bp_det_m2' | dp strength |  | ['gain/lux'\] | [16\] | ['linear'\] |
| dp_noise_level | dp noise level | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 20 | None | open | 'y_2dnr.bp_noise_strength' | dp noise level |  | ['gain/lux'\] | [16\] | ['linear'\] |
| dp_noise_strength | dp noise gain | u0.8 | AX_U8 | [9\] | [0, 255\] | [0.0, 0.99609375\] | [255, 255, ... , 255\] | [0.99609375, 0.99609375, ... , 0.99609375\] | open | 'y_2dnr.bp_noise_strength' | dp noise gain |  | ['gain/lux'\] | [16\] | ['linear'\] |
| dp_detail_gain | dp detail gain | u0.8 | AX_U8 | [9\] | [0, 255\] | [0.0, 0.99609375\] | [255, 255, ... , 255\] | [0.99609375, 0.99609375, ... , 0.99609375\] | open | 'y_2dnr.bp_detail_lut' | dp detail gain |  | ['gain/lux'\] | [16\] | ['linear'\] |
| dir_det_cor_str_fg | dir_det_cor_str_fg | u8.2 | AX_U16 | [2\] | [0, 1023\] | [0.0, 255.75\] | [0, 1\] | [0.0, 0.25\] | open | 'y_2dnr.dir_det_var_w_thr', 'y_2dnr.dir_det_var_w_slope' | direction detection correction strength for foreground |  | ['gain/lux'\] | [16\] | ['linear'\] |
| dir_det_cor_str_bg | dir_det_cor_str_bg | u8.2 | AX_U16 | [2\] | [0, 1023\] | [0.0, 255.75\] | [0, 1\] | [0.0, 0.25\] | open | 'y_2dnr.dir_det_var_w_thr', 'y_2dnr.dir_det_var_w_slope' | direction detection correction strength for background |  | ['gain/lux'\] | [16\] | ['linear'\] |
| detail_det_str_fg | detail_det_str_fg | u8.2 | AX_U16 | [2\] | [0, 1023\] | [0.0, 255.75\] | [0, 1\] | [0.0, 0.25\] | open | 'y_2dnr.detail_w_thr', 'y_2dnr.detail_w_slope' | detail detection strength for foreground |  | ['gain/lux'\] | [16\] | ['linear'\] |
| detail_det_str_bg | detail_det_str_bg | u8.2 | AX_U16 | [2\] | [0, 1023\] | [0.0, 255.75\] | [0, 1\] | [0.0, 0.25\] | open | 'y_2dnr.detail_w_thr', 'y_2dnr.detail_w_slope' | detail detection strength for background |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf1_str | sf1_str | u1.8 | AX_U16 | [\] | [0, 256\] | [0.0, 1.0\] | 256 | 1.0 | open | 'y_2dnr.nlm_str' | sf1 strength, the larger the value, the more nosie reduction |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf1_local_str | sf1_local_str | u0.8 | AX_U8 | [17\] | [0, 255\] | [0.0, 0.99609375\] | [255, 255, ... , 255\] | [0.99609375, 0.99609375, ... , 0.99609375\] | open | 'y_2dnr.nlm_str' | sf1 local strength, the larger the value, the more nosie reduction |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf1_style_adjust | sf1_style_adjust | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 100 | None | open | 'y_2dnr.nlm_dis_coef' | sf1 style adjust, the larger the value, the more nosie reduction, also may be more annoying patterns |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf1_dir_str_scale | sf1_dir_str_scale | u1.4 | AX_U8 | [\] | [0, 16\] | [0.0, 1.0\] | 16 | 1.0 | open | 'y_2dnr.nlm_dir_str_scale' | sf1 direction strength scale, the larger the value, the more direction noise reduction result used |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf1_dir_str | sf1_dir_str | u8.2 | AX_U16 | [2\] | [0, 1023\] | [0.0, 255.75\] | [8, 40\] | [2.0, 10.0\] | open | 'y_2dnr.nlm_w_thr', 'y_2dnr.nlm_w_slope' | sf1 direction strength, the larger the value, the more noise reduction |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf1_detail_str | sf1_detail_str | u8.2 | AX_U16 | [2\] | [0, 1023\] | [0.0, 255.75\] | [8, 40\] | [2.0, 10.0\] | open | 'y_2dnr.nlm_w_thr', 'y_2dnr.nlm_w_slope' | sf1 detail strength, the larger the value, the more noise reduction |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf1_flat_str | sf1_flat_str | u8.2 | AX_U16 | [2\] | [0, 1023\] | [0.0, 255.75\] | [8, 40\] | [2.0, 10.0\] | open | 'y_2dnr.nlm_w_thr', 'y_2dnr.nlm_w_slope' | sf1 flat strength, the larger the value, the more noise reduction |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf2_str | sf2_str | u1.8 | AX_U16 | [\] | [0, 256\] | [0.0, 1.0\] | 256 | 1.0 | open | 'y_2dnr.bf_str' | sf2 strength, the larger the value, the more nosie reduction |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf2_local_str | sf2_local_str | u0.8 | AX_U8 | [17\] | [0, 255\] | [0.0, 0.99609375\] | [255, 255, ... , 255\] | [0.99609375, 0.99609375, ... , 0.99609375\] | open | 'y_2dnr.bf_str' | sf2 local strength, the larger the value, the more nosie reduction |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf2_style_adjust | sf2_style_adjust | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 100 | None | open | 'y_2dnr.bf_dis_coef' | sf2 style adjust, the larger the value, the more nosie reduction, also may be more annoying patterns |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf2_dir_str | sf2_dir_str | u8.2 | AX_U16 | [2\] | [0, 1023\] | [0.0, 255.75\] | [8, 40\] | [2.0, 10.0\] | open | 'y_2dnr.bf_w_thr', 'y_2dnr.bf_w_slope' | sf2 direction strength, the larger the value, the more noise reduction |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf2_detail_str | sf2_detail_str | u8.2 | AX_U16 | [2\] | [0, 1023\] | [0.0, 255.75\] | [8, 40\] | [2.0, 10.0\] | open | 'y_2dnr.bf_w_thr', 'y_2dnr.bf_w_slope' | sf2 detail strength, the larger the value, the more noise reduction |  | ['gain/lux'\] | [16\] | ['linear'\] |
| sf2_flat_str | sf2_flat_str | u8.2 | AX_U16 | [2\] | [0, 1023\] | [0.0, 255.75\] | [8, 40\] | [2.0, 10.0\] | open | 'y_2dnr.bf_w_thr', 'y_2dnr.bf_w_slope' | sf2 flat strength, the larger the value, the more noise reduction |  | ['gain/lux'\] | [16\] | ['linear'\] |
| edge_style_adjust_fg | edge_style_adjust_fg | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 255 | 0.99609375 | open | 'y_2dnr.nlm_bf_w' | edge style adjust for foreground, the larger the value, the smoother the edge, the smaller the vaule, the more annoying pixel along edge |  | ['gain/lux'\] | [16\] | ['linear'\] |
| detail_style_adjust_fg | detail_style_adjust_fg | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 192 | 0.75 | open | 'y_2dnr.nlm_bf_w' | detail style adjust for foreground, the larger the value, maybe the more annoying patterns in detail region |  | ['gain/lux'\] | [16\] | ['linear'\] |
| flat_style_adjust_fg | flat_style_adjust_fg | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 128 | 0.5 | open | 'y_2dnr.nlm_bf_w' | flat style adjust for foreground, the larger the value, maybe the more annoying patterns in flat region |  | ['gain/lux'\] | [16\] | ['linear'\] |
| edge_style_adjust_bg | edge_style_adjust_bg | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 255 | 0.99609375 | open | 'y_2dnr.nlm_bf_w' | edge style adjust for background, the larger the value, the smoother the edge, the smaller the vaule, the more annoying pixel along edge |  | ['gain/lux'\] | [16\] | ['linear'\] |
| detail_style_adjust_bg | detail_style_adjust_bg | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 192 | 0.75 | open | 'y_2dnr.nlm_bf_w' | detail style adjust for background, the larger the value, maybe the more annoying patterns in detail region |  | ['gain/lux'\] | [16\] | ['linear'\] |
| flat_style_adjust_bg | flat_style_adjust_bg | u0.8 | AX_U8 | [\] | [0, 255\] | [0.0, 0.99609375\] | 128 | 0.5 | open | 'y_2dnr.nlm_bf_w' | flat style adjust for background, the larger the value, maybe the more annoying patterns in flat region |  | ['gain/lux'\] | [16\] | ['linear'\] |
| luma_gain_fg | luma_gain_fg | u9.2 | AX_U16 | [17\] | [0, 2047\] | [0.0, 511.75\] | [1024, 1024, ... , 1024\] | [256.0, 256.0, ... , 256.0\] | open | 'y_2dnr.luma_thr_bias_lut' | luma adjust strength for foreground, the larger the value, more noise reduciton in corresponding luma region |  | ['gain/lux'\] | [16\] | ['linear'\] |
| luma_gain_bg | luma_gain_bg | u9.2 | AX_U16 | [17\] | [0, 2047\] | [0.0, 511.75\] | [1024, 1024, ... , 1024\] | [256.0, 256.0, ... , 256.0\] | open | 'y_2dnr.luma_thr_bias_lut' | luma adjust strength for background, the larger the value, more noise reduciton in corresponding luma region |  | ['gain/lux'\] | [16\] | ['linear'\] |
| luma_gain_fg_ex | luma_gain_fg_ex | u3.8 | AX_U16 | [17\] | [0, 2047\] | [0.0, 7.99609375\] | [1024, 1024, ... , 1024\] | [4.0, 4.0, ... , 4.0\] | open | 'y_2dnr.luma_slope_bias_lut' | extension luma adjust strength for foreground, the larger the value, more noise reduciton in corresponding luma region, used when luma_gain_fg is not enough |  | ['gain/lux'\] | [16\] | ['linear'\] |
| luma_gain_bg_ex | luma_gain_bg_ex | u3.8 | AX_U16 | [17\] | [0, 2047\] | [0.0, 7.99609375\] | [1024, 1024, ... , 1024\] | [4.0, 4.0, ... , 4.0\] | open | 'y_2dnr.luma_slope_bias_lut' | extension luma adjust strength for background, the larger the value, more noise reduciton in corresponding luma region, used when luma_gain_bg is not enough |  | ['gain/lux'\] | [16\] | ['linear'\] |
| motion_lut | motion_lut | u0.8 | AX_U8 | [9\] | [0, 255\] | [0.0, 0.99609375\] | [0, 32, 64, 96, 128, 160, 192, 224, 255\] | [0.0, 0.125, 0.25, 0.375, 0.5, 0.625, 0.75, 0.875, 0.99609375\] | open | 'y_2dnr.motion_lut' | motion mapping lut |  | ['gain/lux'\] | [16\] | ['linear'\] |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |