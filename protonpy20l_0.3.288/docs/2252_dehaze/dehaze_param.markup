h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| init_enable |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'dehaze.coef_set_en', 'dehaze.coef_set_a', 'dehaze.coef_set_b', 'dehaze.a', 'dehaze.tx' | is_first_frame | common |
| offset_in |  | u8.2 | AX_U16 | [\] |  [0, 1023\] | [0.0, 255.75\] | 64 | 16.0 | hidden | 'dehaze.offset_in' | offset_in | common |
| offset_out |  | u8.2 | AX_U16 | [\] |  [0, 1023\] | [0.0, 255.75\] | 64 | 16.0 | hidden | 'dehaze.offset_out' | offset_out | common |
| dehaze_partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'dehaze.locat_offset', 'dehaze.inv_start_offset', 'dehaze.ptn_offset_h_w', 'dehaze.ptn_roi_t_b_l_r', 'dehaze.ptn_h_w', 'dehaze.pic_h_w', 'dehaze.norm_ratio' | dehaze partition | common |
| ifa_partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'ifa.output_hor_crop_enable', 'ifa.output_hor_crop_st', 'ifa.output_hor_crop_width' | ifa partition | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| air_reflect | air reflect | u8.8 | AX_U16 | [\] | [1, 65535\] | [0.00390625, 255.99609375\] | 61440 | 240.0 | open | 'dehaze.a', 'ifa.guided_dehaze_dc_k', 'dehaze.coef_set_a' | air reflect. Too small will lead to over exposure of the image, and too large will lead to dead black of the image | user | None | None | None |
| blur_enable | blur enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'ifa.blur_enable' | 0 is turned off and 1 is turned on to alleviate the halo problem | user | None | None | None |
| calc_mode | calc mode | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'dehaze.coef_set_en', 'dehaze.coef_set_a', 'dehaze.coef_set_b' | 0 using traditional algorithm, 1 using accelerated algorithm. | user | None | None | None |
| effective_strength | effective strength | u1.15 | AX_U16 | [\] | [0, 32768\] | [0.0, 1.0\] | 14746 | 0.45001220703125 | open | 'dehaze.coef_set_a', 'ifa.guided_dehaze_dc_k' | adjust defogging intensity | user | ['gain/lux'\] | [16\] | ['linear'\] |
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'dehaze.a', 'dehaze.tx', 'dehaze.coef_set_en', 'dehaze.coef_set_a', 'dehaze.coef_set_b' | SW enable | user | None | None | None |
| eps | eps | u8.4 | AX_U16 | [\] | [1, 4095\] | [0.0625, 255.9375\] | 4095 | 255.9375 | open | 'ifa.guided_epsilon' | Generally, no adjustment is required, which affects the intensity of extreme contrast areas | user | None | None | None |
| mesh_size | mesh size | u7 | AX_U8 | [\] | {32, 64\} | { None \} | 32 | None | open | 'dehaze.zoom_ratio', 'dehaze.inv_zoom_ratio', 'dehaze.graystat_binratio', 'dehaze.locat_offset', 'dehaze.inv_start_offset', 'dehaze.norm_ratio' | Affects the uniformity of defogging intensity. The higher the value, the more uniform the defogging intensity will be, and is equal to 64 when calc_mode == 1 | user | None | None | None |
| sigma_blur | sigma blur | u3.13 | AX_U16 | [\] | [1, 65535\] | [0.0001220703125, 7.9998779296875\] | 57344 | 7.0 | open | 'ifa.blur_weight' | blur intensity of the halo problem | user | None | None | None |
| spatial_smoothness | spatial smoothness | u2 | AX_U8 | [\] | [0, 3\] | [None, None\] | 2 | None | open | 'ifa.guided_win_r' | The higher the value, the more global the defogging effect | user | None | None | None |
| strength_limit | strength limit | u1.8 | AX_U16 | [\] | [1, 256\] | [0.00390625, 1.0\] | 26 | 0.1015625 | open | 'dehaze.tx' | effective strength limit | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |