h2. Conf list
h3. tde
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - | N/A | DPU enable control bit | 0 - disable; 1 - enable |
| v0_format | u6 | [\] | - | N/A | v0 format | Set to proper value |
| v0_global_chroma | u8 | [2\] | - | N/A | v0's global UV channel value(with 128 bias) which is used when input format is BYTE / HALFWORD / BIT10. | Set to proper value |
| output_format | u3 | [\] | - | N/A | dpu output format | Set to proper value |
| mat_adj_enable | u1 | [\] | - | N/A | After blending, result will be dumped into DDR. When this happens, this bit controls whether the result will be adjusted by using 3x3 matrix. | Set to proper value |
| mat_adj_matrix | s2.10 | [3, 3\] | - | N/A | 3x3 aadjustment matrix | Set to proper value |
| mat_adj_offset | s8 | [2, 3\] | - | N/A | in && out offset for DDR write adjustment. | Set to proper value |
| dither_enable | u1 | [\] | - | N/A | dither enable control bit | Set to proper value |
| dither_seed_enable | u1 | [\] | - | N/A | dither seed enable bit. | 0 - use stat; 1 - use config |
| dither_seed_ch0 | u16 | [2\] | - | N/A | dither seed for ch0 channel | Set to proper value |
| dither_seed_ch1 | u16 | [2\] | - | N/A | dither seed for ch1 channel | Set to proper value |
| dither_seed_ch2 | u16 | [2\] | - | N/A | dither seed for ch2 channel | Set to proper value |
| dither_pmask_ch0 | u16 | [2\] | - | N/A | pmask for ch0 channel | Set to proper value |
| dither_pmask_ch1 | u16 | [2\] | - | N/A | pmask for ch1 channel | Set to proper value |
| dither_pmask_ch2 | u16 | [2\] | - | N/A | pmask for ch2 channel | Set to proper value |
| to422_filter_enable | u1 | [\] | - | N/A | If enabled and output format is YUV422, UV will be filtered at horizontal direction using provided filter. After that, UV444 will be decimated to generate UV422. | Set to proper value |
| to422_filter_weight_hor | s1.5 | [7\] | - | N/A | The filter weight used to filter UV444 if 'to422_filter_enable' is ON. | Set to proper value |
| clip_enable | u1 | [\] | - | N/A | clip enable control bit | Set to proper value |
| clip_gain | u2.8 | [3\] | - | N/A | gains for 3 channels | Set to proper value |
| clip_offset | s8.0 | [2, 3\] | - | N/A | input -offset[0\][x\], output + offset[1\][x\] | Set to proper value |
| clip_range | s8.0 | [3, 2\] | - | N/A | clamp range for 3 channels | Set to proper value |
| lba_enable | u1 | [\] | - | N/A | lba enable control bit | Set to proper value |
| lba_ext_size | u13 | [4\] | - | N/A | lba extension size(paddings on edges) | Set to proper value |
| lba_background | u8.0 | [3\] | - | N/A | lba background color | Set to proper value |

