h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| offset_in |  | u8.4 | AX_U16 | [\] |  [0, 4095\] | [0.0, 255.9375\] | 256 | 16.0 | hidden | 'gam.offset_in' | only support set to 16.0 | common |
| offset_out |  | u8.2 | AX_U16 | [\] |  [0, 1023\] | [0.0, 255.75\] | 64 | 16.0 | hidden | 'gam.offset_out' | only support set to 16.0 | common |
| dither_seed_enable |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 1 | None | hidden | 'gam.dither_seed_enable' | 0: use stat dither_seed, 1: use conf dither_seed. set 1 for the 1st frame, then set 0 from the 2nd frame onwards. | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | Enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'gam.lut' | 0: linear lut, 1: set lut in params |   | None | None | None |
| mode | Mode | u8 | AX_U8 | [\] | [0, 6\] | [None, None\] | 3 | None | open | 'gam.lut' | 0: linear, 1: BT709, 2: srgb, 3: AX gamma0(default gamma) 4: AX gamma1, 5: AX gamma2, 6: customer. |   | None | None | None |
| custom_lut | Custom Lut | u8.4 | AX_U16 | [129\] | [0, 4095\] | [0.0, 255.9375\] | [0, 105, 211, 317, 423, 529, 635, 741, 847, 948, 1041, 1127, 1207, 1281, 1352, 1419, 1482, 1542, 1600, 1655, 1708, 1759, 1808, 1856, 1902, 1946, 1989, 2031, 2071, 2111, 2149, 2186, 2223, 2259, 2293, 2327, 2361, 2393, 2425, 2456, 2487, 2517, 2546, 2575, 2603, 2631, 2659, 2686, 2712, 2738, 2764, 2789, 2814, 2839, 2863, 2887, 2910, 2933, 2956, 2979, 3001, 3023, 3045, 3066, 3087, 3108, 3129, 3149, 3169, 3189, 3209, 3228, 3248, 3267, 3286, 3304, 3323, 3341, 3359, 3377, 3395, 3412, 3430, 3447, 3464, 3481, 3498, 3515, 3531, 3547, 3564, 3580, 3596, 3611, 3627, 3643, 3658, 3673, 3688, 3703, 3718, 3733, 3748, 3762, 3777, 3791, 3805, 3820, 3834, 3848, 3861, 3875, 3889, 3902, 3916, 3929, 3942, 3956, 3969, 3982, 3995, 4008, 4020, 4033, 4046, 4058, 4071, 4083, 4095\] | [0.0, 6.5625, 13.1875, 19.8125, 26.4375, 33.0625, 39.6875, 46.3125, 52.9375, 59.25, 65.0625, 70.4375, 75.4375, 80.0625, 84.5, 88.6875, 92.625, 96.375, 100.0, 103.4375, 106.75, 109.9375, 113.0, 116.0, 118.875, 121.625, 124.3125, 126.9375, 129.4375, 131.9375, 134.3125, 136.625, 138.9375, 141.1875, 143.3125, 145.4375, 147.5625, 149.5625, 151.5625, 153.5, 155.4375, 157.3125, 159.125, 160.9375, 162.6875, 164.4375, 166.1875, 167.875, 169.5, 171.125, 172.75, 174.3125, 175.875, 177.4375, 178.9375, 180.4375, 181.875, 183.3125, 184.75, 186.1875, 187.5625, 188.9375, 190.3125, 191.625, 192.9375, 194.25, 195.5625, 196.8125, 198.0625, 199.3125, 200.5625, 201.75, 203.0, 204.1875, 205.375, 206.5, 207.6875, 208.8125, 209.9375, 211.0625, 212.1875, 213.25, 214.375, 215.4375, 216.5, 217.5625, 218.625, 219.6875, 220.6875, 221.6875, 222.75, 223.75, 224.75, 225.6875, 226.6875, 227.6875, 228.625, 229.5625, 230.5, 231.4375, 232.375, 233.3125, 234.25, 235.125, 236.0625, 236.9375, 237.8125, 238.75, 239.625, 240.5, 241.3125, 242.1875, 243.0625, 243.875, 244.75, 245.5625, 246.375, 247.25, 248.0625, 248.875, 249.6875, 250.5, 251.25, 252.0625, 252.875, 253.625, 254.4375, 255.1875, 255.9375\] | open | 'gam.lut' | gamma curve. 0 <= lut[n+1\]-lut[n\] < 32.0. default is GAM_AX0 |   | ['gain/lux'\] | [16\] | ['linear'\] |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |