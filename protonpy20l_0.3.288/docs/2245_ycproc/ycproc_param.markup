h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| ycrt_reset_uv_for_debug |  | u5 | AX_U8 | [\] |  [0, 18\] | [None, None\] | 0 | None | hidden | 'lce.cclip_clip' | reset uv for debug | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| ycproc_enable | ycproc enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'lce.yadj_enable', 'lce.yclip_enable', 'lce.cclip_enable' | ycproc enable |   | None | None | None |
| ycproc_brightness | brightness | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 256 | 1.0 | open | 'lce.yadj_lut' | brightness control |   | None | None | None |
| ycproc_contrast | contrast | s4.8 | AX_S16 | [\] | [-4096, 4095\] | [-16.0, 15.99609375\] | 256 | 1.0 | open | 'lce.yadj_lut' | contrast control |   | None | None | None |
| ycproc_saturation | saturation | u4.12 | AX_U16 | [\] | [0, 65535\] | [0.0, 15.999755859375\] | 4096 | 1.0 | open | 'lce.cclip_cmtx' | saturation control |   | None | None | None |
| ycproc_hue | hue | s0.15 | AX_S16 | [\] | [-32768, 32767\] | [-1.0, 0.999969482421875\] | 0 | 0.0 | open | 'lce.cclip_cmtx' | hue control |   | None | None | None |
| ycrt_enable | ycrt enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'lce.yadj_enable', 'lce.yclip_enable', 'lce.cclip_enable' | 0: bypass (limited range [HDMI\]), 1: enable |   | None | None | None |
| ycrt_signal_range_mode | signal range mode | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'lce.yadj_lut', 'lce.yclip_clip', 'lce.cclip_cmtx', 'lce.cclip_clip' | 0: full range, 1: limited range (survilliance) |   | None | None | None |
| ycrt_clip_level_y | y clip level | u8.0 | AX_U8 | [2\] | [0, 255\] | [0.0, 255.0\] | [0, 255\] | [0.0, 255.0\] | open | 'lce.yclip_clip' | y clip level |   | None | None | None |
| ycrt_clip_level_uv | uv clip level | s7.0 | AX_S8 | [2\] | [-128, 127\] | [-128.0, 127.0\] | [-128, 127\] | [-128.0, 127.0\] | open | 'lce.cclip_clip' | uv clip level |   | None | None | None |
| hs2dlut_enable | hs2dlut enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'lce.hsvc_enable' |  |   | None | None | None |
| hs2dlut_hue_table | hs2dlut hue table | u9.7 | AX_U16 | [24, 16\] | [0, 46080\] | [0.0, 360.0\] | np.array([[15.0 \* x for x in range(24)\]\] \* 16).transpose() | np.array([[15.0 \* x for x in range(24)\]\] \* 16).transpose() | open | 'lce.hsvc_h_lut' |  |   | ['gain/lux', 'color_temp'\] | [12, 12\] | ['linear', 'linear'\] |
| hs2dlut_sat_table | hs2dlut saturation table | u1.15 | AX_U16 | [24, 16\] | [0, 32768\] | [0.0, 1.0\] | np.array([[0.0625 \* x for x in range(1, 17)\]\] \* 24) | np.array([[0.0625 \* x for x in range(1, 17)\]\] \* 24) | open | 'lce.hsvc_s_lut' |  |   | ['gain/lux', 'color_temp'\] | [12, 12\] | ['linear', 'linear'\] |
| ccmp_enable | ccmp enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'lce.ccmp_enable' |  |   | None | None | None |
| ccmp_y_lut | chroma compression y lut | u1.9 | AX_U16 | [29\] | [0, 512\] | [0.0, 1.0\] | [512, 512, ... , 512\] | [1.0, 1.0, ... , 1.0\] | open | 'lce.ccmp_y_lut' | smaller value means stronger saturation compression |   | ['gain/lux'\] | [12\] | ['linear'\] |
| ccmp_sat_lut | chroma compression saturation lut | u1.9 | AX_U16 | [23\] | [0, 512\] | [0.0, 1.0\] | [512, 512, ... , 512\] | [1.0, 1.0, ... , 1.0\] | open | 'lce.ccmp_sat_lut' | smaller value means stronger saturation compression |   | ['gain/lux'\] | [12\] | ['linear'\] |
| scm_enable | scm enalbe | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'lce.cset_enable' |  |   | None | None | None |
| scm_inverse_selection | inverse selection | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'lce.cset_io_flag' | 0: normal selection, 1: inverse selection |   | None | None | None |
| scm_src_y | center y | u8.2 | AX_U16 | [\] | [0, 1023\] | [0.0, 255.75\] | 0 | 0.0 | open | 'lce.cset_center_y' | color mask center y |   | ['gain/lux'\] | [12\] | ['linear'\] |
| scm_src_uv | center uv | s7.2 | AX_S16 | [2\] | [-512, 511\] | [-128.0, 127.75\] | [0, 0\] | [0.0, 0.0\] | open | 'lce.cset_center_uv' | color mask center uv |   | ['gain/lux'\] | [12\] | ['linear'\] |
| scm_radius | radius | u7.2 | AX_U16 | [3\] | [0, 511\] | [0.0, 127.75\] | [0, 0, 0\] | [0.0, 0.0, 0.0\] | open | 'lce.cset_radius' | color mask radius, [0\]: y, [1\]: u, [2\]: v |   | ['gain/lux'\] | [12\] | ['linear'\] |
| scm_smooth | smooth | u3 | AX_U8 | [3\] | [0, 7\] | [None, None\] | [0, 0, 0\] | None | open | 'lce.cset_t_grad' | color mask transition band |   | ['gain/lux'\] | [12\] | ['linear'\] |
| scm_dst_uv | target color | s7.2 | AX_S16 | [2\] | [-512, 511\] | [-128.0, 127.75\] | [0, 0\] | [0.0, 0.0\] | open | 'lce.cset_color' | target color |   | ['gain/lux'\] | [12\] | ['linear'\] |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |