# 文档写作指南

## VS Code 插件

建议安装以下插件:
- [Draw.io Integration](https://marketplace.visualstudio.com/items?itemName=hediet.vscode-drawio)
- [Markdown Preview Enhanced](https://marketplace.visualstudio.com/items?itemName=shd101wyy.markdown-preview-enhanced)

这样就可以在 VSCode 中直接用 draw.io 画图，并获得 markdown 实时预览。

![](../assets/imgs/vscode.jpg)


## 本地查看文档

搞一个静态 HTTP Server，例如 `python3 -m http.server` 或 [miniserve](https://github.com/svenstaro/miniserve) (推荐)

```bash
cd docs
python3 -m http.server  #  使用 python 自带 http server
miniserve --index index.html .  # 使用 miniserve
```

在 Brain++ 上，使用 VS Code Remote 时，开启 HTTP Server 后，VS Code 会自动做端口转发。


## 新增文档
1. 在 `/docs/` 目录里合适的位置新增一个 markdown 文件，例如 `new_doc.md`
2. 在 `_sidebar.md` 中增加对新文档的链接，例如

```
- [新文档](/new_doc.md)
```

虽然 vscode 能预览 markdown，但效果和最终文档仍有差异，所以发布前一定要开个本地服务看下最终效果。


## Markdown 写作

### 基础
这个就自己 google 吧～


### 写 mermaid

用 markdown 语法
<pre>
```mermaid
graph LR;
    A-->B;
    A-->C;
    B-->D;
    C-->D;
```
</pre>
即可。

```mermaid
graph LR;
    A-->B;
    A-->C;
    B-->D;
    C-->D;
```

### 用 draw.io 画更复杂的流程图

1. 安装 VS Code [Draw.io Integration](https://marketplace.visualstudio.com/items?itemName=hediet.vscode-drawio) 插件
2. 在 `/docs/assets/imgs` 里新建一个 drawio 文件，例如 `diagram.drawio`
3. 这时双击 `diagram.drawio`，vscode 就会自动弹出画图页面，画完保存
4. 在 markdown 中用以下语法插入图片

```
[Diagram](assets/imgs/diagram.drawio ':include :type=code')
```

注意图片的路径，建议本地查看最终文档确认效果。


### 静态图片

把图片放在 `/docs/assets/imgs` 里，然后用 markdown 语法插入

```
![标题](assets/imgs/vscode.jpg)
```

注意图片的路径，建议本地查看最终文档确认效果。

尽量不插 png 等二进制大图，避免 repo 太大。
