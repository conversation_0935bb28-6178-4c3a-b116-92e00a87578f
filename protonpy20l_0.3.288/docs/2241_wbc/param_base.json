{"partition_configs": [], "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "WBC is 0x2241", "type": "AX_U16"}}, "params": {"enable": {"acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "SW enable", "hidden": 0, "auto": 0, "target_conf": ["wbc.wb_gain"], "dependency": " "}, "d_gain": {"acc": [0, 10, 8], "size": [], "range": [0.0, 1023.99609375], "default": 256.0, "comment": "isp d_gain", "hidden": 1, "auto": 0, "target_conf": ["wbc.d_gain"], "dependency": "common"}, "wbc_gain": {"acc": [0, 4, 8], "size": [4], "range": [0.0, 15.99609375], "default": [1.0, 1.0, 1.0, 1.0], "hidden": 1, "auto": 0, "target_conf": ["wbc.wb_gain"], "comment": "wbc r/gr/gb/b gain", "dependency": "common"}, "clip_level": {"acc": [0, 14, 6], "size": [], "range": [0.0, 16383.984375], "default": 16383.0, "comment": "Obtained from ACP, represents the maximum range of significant values. It is equivalent to a white level without offset.", "hidden": 1, "auto": 0, "target_conf": ["wbc.clip_level"], "dependency": "common"}, "offset_in": {"acc": [0, 14, 6], "size": [], "range": [0.0, 16383.984375], "default": 16.0, "hidden": 1, "auto": 0, "target_conf": ["wbc.offset_in"], "comment": "wbc offset in", "dependency": "common"}, "offset_out": {"acc": [0, 14, 6], "size": [], "range": [0.0, 16383.984375], "default": 16.0, "hidden": 1, "auto": 0, "target_conf": ["wbc.offset_out"], "comment": "wbc offset out", "dependency": "common"}}, "submodules": {"setup": {"params": ["offset_in", "offset_out"], "configs": ["wbc.enable"]}, "ctrl": {"params": ["enable", "d_gain", "wbc_gain", "clip_level"], "configs": []}}, "target_module": {"mc20l": {"wbc": {"id": 2600, "method": 0}}}, "structs": {}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "configs": {"wbc": {"enable": {"acc": [0, 1], "size": [], "description": "bypass/enable", "usage": "0: bypass, 1: enable", "constraints": "", "type": "AX_U8", "partition": "-"}, "d_gain": {"acc": [0, 10, 8], "size": [], "description": "digital gain", "usage": "", "constraints": "all", "type": "AX_U32", "partition": "-"}, "wb_gain": {"acc": [0, 4, 8], "size": [4], "description": "white balance gain [0]:R,[1]:Gr,[2]:Gb,[3]:B", "usage": "", "constraints": "all", "type": "AX_U16", "partition": "-"}, "clip_level": {"acc": [0, 14, 6], "size": [], "description": "white clip level", "usage": "set to (white_level - offset_in) in usual", "constraints": "all", "type": "AX_U32", "partition": "-"}, "offset_in": {"acc": [0, 14, 6], "size": [], "description": "offset", "usage": "set to 256.0 in usual", "constraints": "all", "type": "AX_U32", "partition": "-"}, "offset_out": {"acc": [0, 14, 6], "size": [], "description": "offset", "usage": "set to 256.0 in usual", "constraints": "all", "type": "AX_U32", "partition": "-"}}}}