{"partition_configs": {}, "context": {"AN_ID": {"size": [], "acc": [0, 16, 0], "comment": "COLOR is 0x2242", "type": "AX_U16"}}, "params": {"enable": {"display": "Enable CLC", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "clc enable", "hidden": 0, "auto": 0, "target_conf": ["clc.matrix_list"], "type": "AX_U8", "dependency": "user"}, "mode": {"display": "Mode", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "clc ctrl mode, 0 for basic (global); 1 for advanced (16 phase)", "hidden": 0, "target_conf": ["clc.matrix_list"], "type": "AX_U8", "enum_field": {"0": "BASIC", "1": "ADVANCED"}, "auto": 0, "dependency": "user"}, "ctrl_luma_ratio": {"display": "<PERSON><PERSON>", "acc": [0, 1, 7], "size": [2], "range": [0.0, 1.0], "default": [0.2109375, 0.0703125], "comment": "clc control luma ratio, sum will be 1.0", "hidden": 0, "target_conf": ["clc.matrix_list"], "type": "AX_U8", "auto": 0, "dependency": "user"}, "offset_in": {"display": "clc offset in", "acc": [0, 8, 4], "size": [], "range": [0.0, 255.9375], "default": 16.0, "comment": "clc offset_in", "hidden": 1, "target_conf": ["clc.offset_in"], "type": "AX_U16", "auto": 0, "dependency": "common"}, "offset_out": {"display": "clc offset out", "acc": [0, 8, 4], "size": [], "range": [0.0, 255.9375], "default": 16.0, "comment": "clc offset_out", "hidden": 1, "target_conf": ["clc.offset_out"], "type": "AX_U16", "auto": 0, "dependency": "common"}, "ccm": {"display": "CCM", "acc": [1, 3, 8], "size": [3, 2], "range": [-7.99609375, 7.99609375], "default": [[0, 0], [0, 0], [0, 0]], "comment": "clc ccm matrix", "hidden": 0, "auto": 1, "target_conf": ["clc.matrix_list"], "type": "AX_S16", "dependency": "user"}, "ccm_sat": {"display": "Sat", "acc": [1, 1, 6], "size": [], "range": [-1.0, 1.0], "default": 0, "comment": "clc global saturation adjust", "hidden": 0, "auto": 1, "target_conf": ["clc.matrix_list"], "type": "AX_S8", "dependency": "user"}, "ccm_hue": {"display": "<PERSON><PERSON>", "acc": [1, 5, 6], "size": [], "range": [-30.0, 30.0], "default": 0, "comment": "clc global hue adjust", "hidden": 0, "auto": 1, "target_conf": ["clc.matrix_list"], "type": "AX_S16", "dependency": "user"}, "ccm_ctrl_level": {"display": "clc ccm ctrl level", "acc": [0, 1, 8], "size": [], "range": [0.0, 1.0], "default": 1.0, "comment": "clc global adjust ctrl level", "hidden": 0, "auto": 1, "target_conf": ["clc.matrix_list"], "type": "AX_U16", "dependency": "user"}, "xcm_sats": {"display": "clc xcm sats", "acc": [1, 1, 6], "size": [16], "range": [-0.5, 0.5], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "clc local saturation adjust", "hidden": 0, "auto": 1, "target_conf": ["clc.matrix_list"], "type": "AX_S8", "dependency": "user"}, "xcm_hues": {"display": "clc xcm hues", "acc": [1, 5, 6], "size": [16], "range": [-10.0, 10.0], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "clc local hue adjust", "hidden": 0, "auto": 1, "target_conf": ["clc.matrix_list"], "type": "AX_S16", "dependency": "user"}, "xcm_ctrl_levels": {"display": "clc xcm ctrl levels", "acc": [0, 1, 8], "size": [16], "range": [0.0, 1.0], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "comment": "clc local adjust ctrl level", "hidden": 0, "auto": 1, "target_conf": ["clc.matrix_list"], "type": "AX_U16", "dependency": "user"}, "sat_strength": {"display": "clc sat strength", "size": [], "range": [], "default": [], "comment": "from AWB, try to decrease saturation when AWB has low confidence", "hidden": 1, "auto": 0, "target_conf": ["clc.matrix_list"], "type": "AX_F32", "dependency": "common"}, "sat_mit_enable": {"display": "sat_mit_enable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "clc sat_mit_enable", "hidden": 0, "auto": 0, "target_conf": ["clc.sat_mit_enable"], "type": "AX_U8", "dependency": "user"}, "sat_mit_ratio_lower_lut": {"display": "sat_mit_ratio_lower_lut", "acc": [0, 1, 7], "size": [8], "range": [0.0, 1.0], "default": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "comment": "clc sat_mit_ratio_lower_lut", "hidden": 0, "auto": 0, "target_conf": ["clc.sat_mit_ratio_lower_lut"], "type": "AX_U8", "dependency": "user"}, "sat_mit_ratio_upper_lut": {"display": "sat_mit_ratio_upper_lut", "acc": [0, 1, 7], "size": [8], "range": [0.0, 1.0], "default": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "comment": "clc sat_mit_ratio_upper_lut", "hidden": 0, "auto": 0, "target_conf": ["clc.sat_mit_ratio_upper_lut"], "type": "AX_U8", "dependency": "user"}, "sat_mit_lower_cp": {"display": "sat_mit_lower_ctrl_point", "acc": [1, 8], "size": [6], "range": [-255, 255], "default": [0, 0, 0, 0, 0, 0], "comment": "clc sat_mit_lower's control point with [x0, y0, x1, y1, x2, y2], [x2, y2] is the dividing point from y=x", "hidden": 0, "auto": 0, "target_conf": ["clc.sat_mit_lower_lut_x", "clc.sat_mit_lower_lut_y", "clc.sat_mit_lower_lut_slope"], "type": "AX_S16", "dependency": "user"}, "sat_mit_upper_cp": {"display": "sat_mit_upper_ctrl_point", "acc": [0, 9], "size": [6], "range": [0, 511], "default": [0, 0, 0, 0, 0, 0], "comment": "clc sat_mit_upper's control point with [x0, y0, x1, y1, x2, y2], [x0, y0] is the dividing point from y=x, with y0, y1, y2 <= 255", "hidden": 0, "auto": 0, "target_conf": ["clc.sat_mit_upper_lut_x", "clc.sat_mit_upper_lut_y", "clc.sat_mit_upper_lut_slope"], "type": "AX_U16", "dependency": "user"}}, "submodules": {"clc_setup": {"params": ["offset_in", "offset_out"], "configs": ["clc.enable", "clc.hr_lpf", "clc.mode"]}, "clc": {"params": ["enable", "mode", "ctrl_luma_ratio", "ccm", "ccm_sat", "ccm_hue", "ccm_ctrl_level", "xcm_sats", "xcm_hues", "xcm_ctrl_levels", "sat_mit_enable", "sat_mit_ratio_lower_lut", "sat_mit_ratio_upper_lut", "sat_mit_lower_cp", "sat_mit_upper_cp", "sat_strength"], "configs": []}}, "target_module": {"mc20l": {"clc": {"id": 3400, "method": 0}}}, "structs": {}, "configs": {"clc": {"enable": {"acc": [0, 1], "size": [], "description": "clc enable, 0: bypass, 1: enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "mode": {"acc": [0, 1], "size": [], "description": "mode=0 : Low power mode. Do not do matrix interpolation． mode=1 : Default configuration for mc20", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "matrix_list": {"acc": [1, 3, 8], "size": [16, 6], "description": "ccm matrices corresponding to 16 different color phase", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "hr_lpf": {"acc": [1, 1, 7], "size": [5], "description": "low pass filter", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "sat_mit_enable": {"acc": [0, 1], "size": [], "description": "sat mitigation enable", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sat_mit_ratio_lower_lut": {"acc": [0, 1, 7], "size": [8], "description": "sat mit ratio lower lut", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sat_mit_ratio_upper_lut": {"acc": [0, 1, 7], "size": [8], "description": "sat mit ratio upper lut", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sat_mit_lower_lut_x": {"acc": [1, 8, 4], "size": [8], "description": "sat mit lower lut x index", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "sat_mit_lower_lut_y": {"acc": [1, 8, 4], "size": [8], "description": "sat mit lower lut y value", "usage": "", "constraints": "", "type": "AX_S16", "partition": "-"}, "sat_mit_lower_lut_slope": {"acc": [0, 1, 7], "size": [9], "description": "sat mit lower lut slope", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "sat_mit_upper_lut_x": {"acc": [0, 9, 4], "size": [8], "description": "sat mit upper lut x index", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sat_mit_upper_lut_y": {"acc": [0, 8, 4], "size": [8], "description": "sat mit upper lut y value", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "sat_mit_upper_lut_slope": {"acc": [0, 1, 7], "size": [9], "description": "sat mit upper lut slope", "usage": "", "constraints": "", "type": "AX_U8", "partition": "-"}, "offset_in": {"acc": [0, 8, 4], "size": [], "description": "", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}, "offset_out": {"acc": [0, 8, 4], "size": [], "description": "", "usage": "", "constraints": "", "type": "AX_U16", "partition": "-"}}}, "constraints": ["sat_mit_lower_cp[4] == sat_mit_lower_cp[5]", "sat_mit_upper_cp[0] == sat_mit_upper_cp[1]"], "autos": {"1": {"ref_mode": ["gain/lux", "color_temp"], "ref_group_num": [5, 12], "ref_interp_method": ["linear", "linear"]}}}