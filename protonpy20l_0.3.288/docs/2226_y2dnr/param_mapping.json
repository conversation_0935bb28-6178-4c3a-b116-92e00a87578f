{"enable": {"api": "nY2dnrEn", "display": "enable", "comments": "0:disable, 1:enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "motion_enable": {"api": "nMotionEnable", "display": "motionEnable", "comments": "motion enable, 0:disable, 1:enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "dpc_enable": {"api": "nDpcEnable", "display": "dpcEnable", "comments": "dpc enable, 0:disable, 1:enable", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "dir_strength": {"api": "nDirStr", "display": "dirStr", "comments": "direction detection strength", "hint": "Accuracy: U0.4 Range: [0, 15]"}, "dir_base": {"api": "nDirBase", "display": "dirBase", "comments": "base noise for direciton detection", "hint": "Accuracy: U8.4 Range: [0, 4095]"}, "dir_det_cor_str_fg": {"api": "nDirDetCorStrFg", "display": "dirDetCorStrFg", "comments": "direction detection correction strength for foreground", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "dir_det_cor_str_bg": {"api": "nDirDetCorStrBg", "display": "dirDetCorStrBg", "comments": "direction detection correction strength for background", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "detail_det_str_fg": {"api": "nDetailDetStrFg", "display": "detailDetStrFg", "comments": "detail detection strength for foreground", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "detail_det_str_bg": {"api": "nDetailDetStrBg", "display": "detailDetStrBg", "comments": "detail detection strength for background", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "dp_det_mode": {"api": "nDpDetMode", "display": "dpDetMode", "comments": "dp detection mode, 0:signgle, 1:dual", "hint": "Accuracy: U1.0 Range: [0, 1]"}, "dp_det_strength": {"api": "nDpDetStr", "display": "dpDetStr", "comments": "dp detection strength", "hint": "Accuracy: U4.4 Range: [0, 255]"}, "dp_strength": {"api": "nDpStr", "display": "dpStr", "comments": "dp strength", "hint": "Accuracy: U4.8 Range: [0, 4095]"}, "dp_noise_level": {"api": "nDpNoiseLevel", "display": "dpNoiseLevel", "comments": "dp noise level", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "dp_noise_strength": {"api": "nDpNoiseStr", "display": "dpNoiseStr", "comments": "dp noise gain", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "dp_detail_gain": {"api": "nDpDetailGain", "display": "dpDetailGain", "comments": "dp detail gain", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "sf1_str": {"api": "nSf1Str", "display": "sf1Str", "comments": "sf1 strength, the larger the value, the more nosie reduction", "hint": "Accuracy: U1.8 Range: [0, 256]"}, "sf1_local_str": {"api": "nSf1LocalStr", "display": "sf1LocalStr", "comments": "sf1 local strength, the larger the value, the more nosie reduction", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "sf1_style_adjust": {"api": "nSf1StyleAdj", "display": "sf1StyleAdj", "comments": "sf1 style adjust, the larger the value, the more nosie reduction, also may be more annoying patterns", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "sf1_dir_str_scale": {"api": "nSf1DirStrScale", "display": "sf1DirStrScale", "comments": "sf1 direction strength scale, the larger the value, the more direction noise reduction result used", "hint": "Accuracy: U1.4 Range: [0, 16]"}, "sf1_dir_str": {"api": "nSf1DirStr", "display": "sf1DirStr", "comments": "sf1 direction strength, the larger the value, the more noise reduction", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf1_detail_str": {"api": "nSf1DetailStr", "display": "sf1DetailStr", "comments": "sf1 detail strength, the larger the value, the more noise reduction", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf1_flat_str": {"api": "nSf1FlatStr", "display": "sf1FlatStr", "comments": "sf1 flat strength, the larger the value, the more noise reduction", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf2_str": {"api": "nSf2Str", "display": "sf2Str", "comments": "sf2 strength, the larger the value, the more nosie reduction", "hint": "Accuracy: U1.8 Range: [0, 256]"}, "sf2_local_str": {"api": "nSf2LocalStr", "display": "sf2LocalStr", "comments": "sf2 local strength, the larger the value, the more nosie reduction", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "sf2_style_adjust": {"api": "nSf2StyleAdj", "display": "sf2StyleAdj", "comments": "sf2 style adjust, the larger the value, the more nosie reduction, also may be more annoying patterns", "hint": "Accuracy: U8.0 Range: [0, 255]"}, "sf2_dir_str": {"api": "nSf2DirStr", "display": "sf2DirStr", "comments": "sf2 direction strength, the larger the value, the more noise reduction", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf2_detail_str": {"api": "nSf2DetailStr", "display": "sf2DetailStr", "comments": "sf2 detail strength, the larger the value, the more noise reduction", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "sf2_flat_str": {"api": "nSf2FlatStr", "display": "sf2FlatStr", "comments": "sf2 flat strength, the larger the value, the more noise reduction", "hint": "Accuracy: U8.2 Range: [0, 1023]"}, "edge_style_adjust_fg": {"api": "nEdgeStyleAdjFg", "display": "edgeStyleAdjFg", "comments": "edge style adjust for foreground, the larger the value, the smoother the edge, the smaller the vaule, the more annoying pixel along edge", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "detail_style_adjust_fg": {"api": "nDetailStyleAdjFg", "display": "detailStyleAdjFg", "comments": "detail style adjust for foreground, the larger the value, maybe the more annoying patterns in detail region", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "flat_style_adjust_fg": {"api": "nFlatStyleAdjFg", "display": "flatStyleAdjFg", "comments": "flat style adjust for foreground, the larger the value, maybe the more annoying patterns in flat region", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "edge_style_adjust_bg": {"api": "nEdgeStyleAdjBg", "display": "edgeStyleAdjBg", "comments": "edge style adjust for background, the larger the value, the smoother the edge, the smaller the vaule, the more annoying pixel along edge", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "detail_style_adjust_bg": {"api": "nDetailStyleAdjBg", "display": "detailStyleAdjBg", "comments": "detail style adjust for background, the larger the value, maybe the more annoying patterns in detail region", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "flat_style_adjust_bg": {"api": "nFlatStyleAdjBg", "display": "flatStyleAdjBg", "comments": "flat style adjust for background, the larger the value, maybe the more annoying patterns in flat region", "hint": "Accuracy: U0.8 Range: [0, 255]"}, "luma_gain_fg": {"api": "nLumaGainFg", "display": "lumaGainFg", "comments": "luma adjust strength for foreground, the larger the value, more noise reduciton in corresponding luma region", "hint": "Accuracy: U9.2 Range: [0, 2047]"}, "luma_gain_bg": {"api": "nLumaGainBg", "display": "lumaGainBg", "comments": "luma adjust strength for background, the larger the value, more noise reduciton in corresponding luma region", "hint": "Accuracy: U9.2 Range: [0, 2047]"}, "luma_gain_fg_ex": {"api": "nLumaGainFgEx", "display": "lumaGainFgEx", "comments": "extension luma adjust strength for foreground, the larger the value, more noise reduciton in corresponding luma region, used when luma_gain_fg is not enough", "hint": "Accuracy: U3.8 Range: [0, 2047]"}, "luma_gain_bg_ex": {"api": "nLumaGainBgEx", "display": "lumaGainBgEx", "comments": "extension luma adjust strength for background, the larger the value, more noise reduciton in corresponding luma region, used when luma_gain_bg is not enough", "hint": "Accuracy: U3.8 Range: [0, 2047]"}, "motion_lut": {"api": "nMotionLut", "display": "motionLut", "comments": "motion mapping lut", "hint": "Accuracy: U0.8 Range: [0, 255]"}}