h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| shp_debug_mode |  | u5 | AX_U8 | [\] |  [np.int64(0), np.int64(31)\] | [None, None\] | 0 | None | hidden | 'tde.shp_debug_mode_0' | sharpen debug mode | common |
| shp_disable_for_prev_debug |  | u1 | AX_U8 | [\] |  [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | hidden |  | disable sharpen when using yuv3dnr debug mode | common |
| yclip_offset_0 |  | u8 | AX_U8 | [2\] |  [np.int64(0), np.int64(255)\] | [None, None\] | [0, 0\] | None | hidden | 'tde.yclip_offset_0' | -offset[0\] --> x gain --> +offset[1\] --> clip | common |
| yclip_offset_1 |  | u8 | AX_U8 | [2\] |  [np.int64(0), np.int64(255)\] | [None, None\] | [0, 0\] | None | hidden | 'tde.yclip_offset_1' | -offset[0\] --> x gain --> +offset[1\] --> clip | common |
| yclip_offset_2 |  | u8 | AX_U8 | [2\] |  [np.int64(0), np.int64(255)\] | [None, None\] | [0, 0\] | None | hidden | 'tde.yclip_offset_2' | -offset[0\] --> x gain --> +offset[1\] --> clip | common |
| yclip_offset_3 |  | u8 | AX_U8 | [2\] |  [np.int64(0), np.int64(255)\] | [None, None\] | [0, 0\] | None | hidden | 'tde.yclip_offset_3' | -offset[0\] --> x gain --> +offset[1\] --> clip | common |
| yclip_offset_4 |  | u8 | AX_U8 | [2\] |  [np.int64(0), np.int64(255)\] | [None, None\] | [0, 0\] | None | hidden | 'tde.yclip_offset_4' | -offset[0\] --> x gain --> +offset[1\] --> clip | common |
| yclip_offset_5 |  | u8 | AX_U8 | [2\] |  [np.int64(0), np.int64(255)\] | [None, None\] | [0, 0\] | None | hidden | 'tde.yclip_offset_5' | -offset[0\] --> x gain --> +offset[1\] --> clip | common |
| csc_yuvTrgb_offset_in_5 |  | s8 | AX_S16 | [3\] |  [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | hidden | 'tde.csc_yuvTrgb_offset_in_5' | YUV to RGB conversion offset in. | common |
| csc_yuvTrgb_offset_out_5 |  | s8 | AX_S16 | [3\] |  [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | hidden | 'tde.csc_yuvTrgb_offset_out_5' | YUV to RGB conversion offset out. | common |
| csc_rgbTxyz_offset_out_5 |  | s8 | AX_S16 | [3\] |  [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | hidden | 'tde.csc_rgbTxyz_offset_out_5' | rgb2xyz offset out. | common |
| csc_xyzTlab_offset_out_5 |  | s8 | AX_S16 | [3\] |  [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | hidden | 'tde.csc_xyzTlab_offset_out_5' | xyz2lab offset out. | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| input_0_size | input 0 size | u13 | AX_U16 | [2\] | [np.int64(2), np.int64(4096)\] | [None, None\] | [1080, 1920\] | None | open |  | input 0 size | user | None | None | None |
| input_1_size | input 0 size | u13 | AX_U16 | [2\] | [np.int64(2), np.int64(4096)\] | [None, None\] | [1080, 1920\] | None | open |  | input 0 size | user | None | None | None |
| input_2_size | input 0 size | u13 | AX_U16 | [2\] | [np.int64(2), np.int64(4096)\] | [None, None\] | [1080, 1920\] | None | open |  | input 0 size | user | None | None | None |
| input_offset_x | input offset x | s13.10 | AX_S32 | [3\] | [np.int64(-8388608), np.int64(8388607)\] | [np.float64(-8192.0), np.float64(8191.9990234375)\] | [524288, 524288, 524288\] | [np.float64(512.0), np.float64(512.0), np.float64(512.0)\] | open |  | input offset x for fbcbc | user | None | None | None |
| output_stride | output stride | u13 | AX_U16 | [6\] | [np.int64(128), np.int64(4096)\] | [None, None\] | [1920, 1920, 1920, 1920, 1920, 1920\] | None | open |  | output stride for fbcbc | user | None | None | None |
| input_format | input format | u6 | AX_U8 | [4\] | [np.int64(0), np.int64(63)\] | [None, None\] | [0, 0, 0, 15\] | None | open | 'tde.input_format' | input formats | user | None | None | None |
| stream_mux | stream mux | u2 | AX_U8 | [6\] | [np.int64(0), np.int64(2)\] | [None, None\] | [0, 0, 0, 0, 0, 0\] | None | open | 'tde.stream_mux' | Choose different src for 6 streams | user | None | None | None |
| output_format | input format | u6 | AX_U8 | [6\] | [np.int64(0), np.int64(63)\] | [None, None\] | [0, 0, 0, 0, 0, 9\] | None | open | 'tde.output_format' | input formats | user | None | None | None |
| quad_enable | quad enable | u1 | AX_U8 | [8\] | [np.int64(0), np.int64(1)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.quad_enable' | Quad enable control bit | user | None | None | None |
| quad_mux | quad mux | u2 | AX_U8 | [\] | [np.int64(0), np.int64(2)\] | [None, None\] | 0 | None | open | 'tde.quad_mux' | Choose which input pic to draw quad on | user | None | None | None |
| quad_x_coords | quad in x coords | s13 | AX_S16 | [8, 4\] | [np.int64(-8192), np.int64(8191)\] | [None, None\] | [[0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\]\] | None | open | 'tde.quad_x_coords', 'tde.quad_steps', 'tde.quad_directions' | x coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed | user | None | None | None |
| quad_y_coords | quad in y coords | s13 | AX_S16 | [8, 4\] | [np.int64(-8192), np.int64(8191)\] | [None, None\] | [[0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\], [0, 0, 0, 0\]\] | None | open | 'tde.quad_y_coords', 'tde.quad_steps', 'tde.quad_directions' | y coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed | user | None | None | None |
| quad_mode | quad out mode | u1 | AX_U8 | [8\] | [np.int64(0), np.int64(1)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.quad_mode' | Quad feature mode control bit. 0 - mosaic; 1 - fill | user | None | None | None |
| quad_mosaic_step_size | mosaic step size | u7 | AX_U8 | [2\] | [np.int64(0), np.int64(127)\] | [None, None\] | [16, 16\] | None | open | 'tde.quad_mosaic_step_size' | Mosaic step size | user | None | None | None |
| quad_fill_luma | outer quad luma | u8 | AX_U8 | [8\] | [np.int64(0), np.int64(255)\] | [None, None\] | [128, 128, ... , 128\] | None | open | 'tde.quad_fill_luma' | Outer quad luma | user | None | None | None |
| quad_fill_chroma | Outer quad chroma | u8 | AX_U8 | [8, 2\] | [np.int64(0), np.int64(255)\] | [None, None\] | [[128, 128\], [128, 128\], [128, 128\], [128, 128\], [128, 128\], [128, 128\], [128, 128\], [128, 128\]\] | None | open | 'tde.quad_fill_chroma' | Outer quad chroma(with 128 bias) | user | None | None | None |
| quad_fill_mode | quad fill mode | u2 | AX_U8 | [8\] | [np.int64(0), np.int64(3)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.quad_fill_mode' | Quad fill mode, 0 - inner Quad; 1 - outter Quad; 2 - inner clear Quad; 3 - outter clear Quad. | user | None | None | None |
| quad_alpha | outer quad blending alpha | u8 | AX_U8 | [8\] | [np.int64(0), np.int64(255)\] | [None, None\] | [255, 255, ... , 255\] | None | open | 'tde.quad_alpha' | outer quad blending alpha | user | None | None | None |
| scale_enable_0 | scale enable 0 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.scale_enable_0' | Scalar enable control bit | user | None | None | None |
| scale_output_size_0 | scale output size 0 | u13 | AX_U16 | [2\] | [np.int64(2), np.int64(4096)\] | [None, None\] | [1080, 1920\] | None | open | 'tde.scale_output_size_0', 'tde.scale_step_0', 'tde.scale_offset_luma_0', 'tde.scale_offset_chroma_0' | Scalar output size | user | None | None | None |
| scale_filter_kernel_mode_0 | scale filter kernel mode 0 | u1 | AX_U8 | [4\] | [np.int64(0), np.int64(1)\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'tde.scale_filter_x_luma_0', 'tde.scale_filter_y_luma_0', 'tde.scale_filter_x_chroma_0', 'tde.scale_filter_y_chroma_0' | kernel mode to be selected, 0:triangle, 1:cubic | user | None | None | None |
| scale_filter_bicubic_a_0 | scale filter bicubic a 0 | s1.10 | AX_S16 | [4\] | [np.int64(-1024), np.int64(1024)\] | [np.float64(-1.0), np.float64(1.0)\] | [0, 0, 0, 0\] | [np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0)\] | open | 'tde.scale_filter_x_luma_0', 'tde.scale_filter_y_luma_0', 'tde.scale_filter_x_chroma_0', 'tde.scale_filter_y_chroma_0' | Param a of bicubic. The less the sharper. the large the more smooth | user | None | None | None |
| shp_enable | shpEnable | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 1 | None | open | 'tde.shp_limit_sup_max_over_change_0', 'tde.shp_limit_sup_max_under_change_0' | sharpen enable |   | None | None | None |
| shp_ud_gain_lut | UD Gain | u8.4 | AX_U16 | [33\] | [np.int64(0), np.int64(4095)\] | [np.float64(0.0), np.float64(255.9375)\] | [128, 128, ... , 128\] | [8.0, 8.0, ... , 8.0\] | open | 'tde.shp_ud_gain_lut_0' | set undirectional gain w.r.t. texture strength |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_ud_gain_lut_anchor | UD Lut Anchor | u3 | AX_U8 | [\] | [np.int64(0), np.int64(7)\] | [None, None\] | 1 | None | open | 'tde.shp_ud_texture_shift_0' | smaller value means more anchor points in flat/weak texture regions |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_dir_gain_lut | Dir Gain | u8.4 | AX_U16 | [33\] | [np.int64(0), np.int64(4095)\] | [np.float64(0.0), np.float64(255.9375)\] | [128, 128, ... , 128\] | [8.0, 8.0, ... , 8.0\] | open | 'tde.shp_dir_gain_lut_0' | set directional gain w.r.t. edge strength |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_dir_gain_lut_anchor | Dir Lut Anchor | u3 | AX_U8 | [\] | [np.int64(0), np.int64(7)\] | [None, None\] | 1 | None | open | 'tde.shp_dir_texture_shift_0' | smaller value means more anchor points in flat/weak texture regions |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_ud_freq | UD Frequency | u5.5 | AX_U16 | [\] | [np.int64(0), np.int64(1023)\] | [np.float64(0.0), np.float64(31.96875)\] | 32 | 1.0 | open | 'tde.shp_filter_ud_0', 'tde.shp_ud_gain_lut_0' | larger value means higher undirectional frequecny will be enhanced |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_dir_freq | Dir Frequency | u5.5 | AX_U16 | [\] | [np.int64(0), np.int64(1023)\] | [np.float64(0.0), np.float64(31.96875)\] | 32 | 1.0 | open | 'tde.shp_filter_dir_0_0', 'tde.shp_filter_dir_22_0', 'tde.shp_filter_dir_45_0', 'tde.shp_dir_gain_lut_0' | larger value means higher directional frequecny will be enhanced |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_ud_coring | UD Coring | u3.3 | AX_U8 | [\] | [np.int64(0), np.int64(63)\] | [np.float64(0.0), np.float64(7.875)\] | 0 | 0.0 | open | 'tde.shp_coring_ud_level_0' | larger value means stronger coring for undirectional details |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_dir_coring | Dir Coring | u3.3 | AX_U8 | [\] | [np.int64(0), np.int64(63)\] | [np.float64(0.0), np.float64(7.875)\] | 0 | 0.0 | open | 'tde.shp_coring_dir_level_0' | larger value means stronger coring for directional details |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_edge_strength | Edge Strength | u4 | AX_U8 | [\] | [np.int64(0), np.int64(15)\] | [None, None\] | 8 | None | open | 'tde.shp_ud_scale_0', 'tde.shp_dir_scale_0' | larger value means to use more directional filter's result |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_edge_threshold | Edge Threshold | u8.4 | AX_U16 | [\] | [np.int64(0), np.int64(4095)\] | [np.float64(0.0), np.float64(255.9375)\] | 256 | 16.0 | open | 'tde.shp_edge_sad_noise_0' | larger value means less pixels will be considered as edge pixels |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_edge_smooth_thin_ratio | Edge Smooth Thin Ratio | u4 | AX_U8 | [\] | [np.int64(0), np.int64(15)\] | [None, None\] | 0 | None | open | 'tde.shp_filter_dir_0_0', 'tde.shp_filter_dir_22_0', 'tde.shp_filter_dir_45_0' | larger value means to make the edge much thinner |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_overshoot | Overshoot | u0.7 | AX_U8 | [\] | [np.int64(0), np.int64(127)\] | [np.float64(0.0), np.float64(0.9921875)\] | 80 | 0.625 | open | 'tde.shp_overshoot_0' | larger value means stronger overshoot |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_undershoot | Undershoot | u0.7 | AX_U8 | [\] | [np.int64(0), np.int64(127)\] | [np.float64(0.0), np.float64(0.9921875)\] | 90 | 0.703125 | open | 'tde.shp_undershoot_0' | larger value means stronger undershoot |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_detail_region_threshold | Detail Threshold | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 20 | None | open | 'tde.shp_detail_overshoot_slope_0', 'tde.shp_detail_overshoot_offset_0', 'tde.shp_detail_overshoot_limit_0', 'tde.shp_detail_undershoot_slope_0', 'tde.shp_detail_undershoot_offset_0', 'tde.shp_detail_undershoot_limit_0' | larger value means more pixels will be considered as detail region |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_detail_overshoot_adjust | Detail Overshoot Adjust | s0.7 | AX_S8 | [\] | [np.int64(-127), np.int64(127)\] | [np.float64(-0.9921875), np.float64(0.9921875)\] | 0 | 0.0 | open | 'tde.shp_detail_adj_enable_0', 'tde.shp_detail_overshoot_slope_0', 'tde.shp_detail_overshoot_offset_0', 'tde.shp_detail_overshoot_limit_0' | larger value means stronger overshoot for detail region |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_detail_undershoot_adjust | Detail Undershoot Adjust | s0.7 | AX_S8 | [\] | [np.int64(-127), np.int64(127)\] | [np.float64(-0.9921875), np.float64(0.9921875)\] | 0 | 0.0 | open | 'tde.shp_detail_adj_enable_0', 'tde.shp_detail_undershoot_slope_0', 'tde.shp_detail_undershoot_offset_0', 'tde.shp_detail_undershoot_limit_0' | larger value means stronger undershoot for detail region |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_shoot_release_ratio | Shoot Release Ratio | u1.4 | AX_U8 | [\] | [np.int64(0), np.int64(16)\] | [np.float64(0.0), np.float64(1.0)\] | 0 | 0.0 | open | 'tde.shp_shoot_sup_enable_0', 'tde.shp_shoot_sup_blend_ratio_0' | larger value means shoot suppression tends to ignore more weak texture/detail region |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_shoot_sup_range | Shoot Suppression Range | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 0 | None | open | 'tde.shp_shoot_sup_enable_0', 'tde.shp_shoot_sup_var_min_slope_0', 'tde.shp_shoot_sup_var_min_offset_0', 'tde.shp_shoot_sup_var_min_limit_0' | larger value means shoot suppression tends to control more edges |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_shoot_sup_strength | Shoot Suppression Strength | u0.7 | AX_U8 | [\] | [np.int64(0), np.int64(127)\] | [np.float64(0.0), np.float64(0.9921875)\] | 96 | 0.75 | open | 'tde.shp_shoot_sup_enable_0', 'tde.shp_shoot_sup_var_min_slope_0', 'tde.shp_shoot_sup_var_min_offset_0', 'tde.shp_shoot_sup_var_min_limit_0' | larger value means stronger shoot suppression on edges |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_limit | Sharpen Limit | u5.5 | AX_U16 | [\] | [np.int64(0), np.int64(1023)\] | [np.float64(0.0), np.float64(31.96875)\] | 1023 | 31.96875 | open | 'tde.shp_limit_sup_max_over_gain_0', 'tde.shp_limit_sup_max_under_gain_0' | smaller value means more restriction on the pixel value change, and may reduce the number of white/black points |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_luma_gain_lut_negative | Luma Negative Gain | u1.5 | AX_U8 | [33\] | [np.int64(0), np.int64(63)\] | [np.float64(0.0), np.float64(1.96875)\] | [32, 32, ... , 32\] | [1.0, 1.0, ... , 1.0\] | open | 'tde.shp_luma_mask_enable_0', 'tde.shp_luma_gain_lut_0' | adjust sharpen gain for negative hf based on image brightness |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_luma_gain_lut_positive | Luma Positive Gain | u1.5 | AX_U8 | [33\] | [np.int64(0), np.int64(63)\] | [np.float64(0.0), np.float64(1.96875)\] | [32, 32, ... , 32\] | [1.0, 1.0, ... , 1.0\] | open | 'tde.shp_luma_mask_enable_0', 'tde.shp_luma_gain_lut_0' | adjust sharpen gain for positive hf based on image brightness |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_nr_strength | NR Strength | u1.5 | AX_U8 | [\] | [np.int64(0), np.int64(32)\] | [np.float64(0.0), np.float64(1.0)\] | 0 | 0.0 | open | 'tde.shp_nr_level_0' | larger value means stronger noise reduction |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_nr_ud_texture_sensitivity | UD Texture Sensitivity | u0.8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [np.float64(0.0), np.float64(0.99609375)\] | 64 | 0.25 | open | 'tde.shp_nr_ud_texture_scale_0' | larger value means image has more undirectional textures and apply less noise reduction |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_nr_ud_texture_threshold | UD Texture Threshold | u0.8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [np.float64(0.0), np.float64(0.99609375)\] | 64 | 0.25 | open | 'tde.shp_nr_ud_texture_offset_0' | larger value means less pixels will be considered as real texture and apply more noise reduction |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_nr_ud_limit | UD Noise Limit | u8.2 | AX_U16 | [\] | [np.int64(0), np.int64(1023)\] | [np.float64(0.0), np.float64(255.75)\] | 256 | 64.0 | open | 'tde.shp_nr_ud_limit_0' | larger value means less restriction on undirectional noise reduction |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_nr_dir_edge_sensitivity | Dir Edge Sensitivity | u0.8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [np.float64(0.0), np.float64(0.99609375)\] | 64 | 0.25 | open | 'tde.shp_nr_dir_texture_scale_0' | larger value means image has more directional edges and apply less noise reduction |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_nr_dir_edge_threshold | Dir Edge Threshold | u4.4 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [np.float64(0.0), np.float64(15.9375)\] | 64 | 4.0 | open | 'tde.shp_nr_dir_texture_offset_0' | larger value means less pixels will be considered as real edge and apply more noise reduction |   | ['gain/lux'\] | [16\] | ['linear'\] |
| shp_nr_dir_limit | Dir Noise Limit | u8.2 | AX_U16 | [\] | [np.int64(0), np.int64(1023)\] | [np.float64(0.0), np.float64(255.75)\] | 256 | 64.0 | open | 'tde.shp_nr_dir_limit_0' | larger value means less restriction on directional noise reduction |   | ['gain/lux'\] | [16\] | ['linear'\] |
| rect_enable_0 | rect enable 0 | u1 | AX_U8 | [16\] | [np.int64(0), np.int64(1)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_enable_0' | Rectangles' enable control bits | user | None | None | None |
| rect_tl_coord_x_0 | rect tl coord x 0 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_tl_coord_x_0' | top-left x coordinates of rectangles | user | None | None | None |
| rect_tl_coord_y_0 | rect tl coord y 0 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_tl_coord_y_0' | top-left y coordinates of rectangles | user | None | None | None |
| rect_width_0 | rect width 0 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_br_coord_x_0' | width of rectangles | user | None | None | None |
| rect_height_0 | rect height 0 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_br_coord_y_0' | height coordinates of rectangles | user | None | None | None |
| rect_edge_thickness_0 | rect edge thickness 0 | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 4 | None | open | 'tde.rect_edge_thickness_0' | Rectangles' edge thickness | user | None | None | None |
| rect_edge_color_select_0 | rect edge color select 0 | u2 | AX_U8 | [16\] | [np.int64(0), np.int64(3)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_edge_color_select_0' | Rectangles' color select | user | None | None | None |
| rect_edge_color_val_0 | rect edge color val 0 | u8 | AX_U8 | [4, 4\] | [np.int64(0), np.int64(255)\] | [None, None\] | [[255, 255, 255, 255\], [255, 255, 255, 255\], [255, 255, 255, 255\], [255, 255, 255, 255\]\] | None | open | 'tde.rect_edge_color_val_0' | larger value means less restriction on directional noise reduction | user | None | None | None |
| roi_luma_sum_tl_coords_0 | top-left corner coordinates of 8 roi areas | u12 | AX_U16 | [8, 2\] | [np.int64(0), np.int64(4095)\] | [None, None\] | [[0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\]\] | None | open | 'tde.roi_luma_sum_tl_coords_0' | roi luma sum tl coords 0 | user | None | None | None |
| roi_luma_sum_br_coords_0 | bottom-right corner coordinates of 8 roi areas | u12 | AX_U16 | [8, 2\] | [np.int64(0), np.int64(4095)\] | [None, None\] | [[0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\], [0, 0\]\] | None | open | 'tde.roi_luma_sum_br_coords_0' | roi luma sum br coords 0 | user | None | None | None |
| osd_enable_0 | rect enable 0 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.osd_enable_0' | osd enable control bit | user | None | None | None |
| osd_tl_coord_x_0 | osd tl coord x 0 | u13 | AX_U16 | [32\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.osd_tl_coord_x_0', 'tde.osd_br_coord_x_0' | osd blending top-left coord(x) | user | None | None | None |
| osd_tl_coord_y_0 | osd tl coord x 0 | u13 | AX_U16 | [32\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.osd_tl_coord_y_0', 'tde.osd_br_coord_y_0' | 4 groups of osd blending top-left coord(y) | user | None | None | None |
| osd_width_0 | osd width 0 | u13 | AX_U16 | [32\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.osd_tl_coord_x_0', 'tde.osd_br_coord_x_0' | osd blending width | user | None | None | None |
| osd_height_0 | osd height 0 | u13 | AX_U16 | [32\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.osd_tl_coord_y_0', 'tde.osd_br_coord_y_0' | osd blending height(y) | user | None | None | None |
| yclip_enable_0 | yclip enable 0 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.yclip_enable_0' | Luma clip enable control bit | user | None | None | None |
| yclip_gain_0 | yclip gain 0 | s4.8 | AX_S16 | [\] | [np.int64(-4096), np.int64(4095)\] | [np.float64(-16.0), np.float64(15.99609375)\] | 256 | 1.0 | open | 'tde.yclip_gain_0' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| yclip_boundary_0 | yclip boundary 0 | u8 | AX_U8 | [2\] | [np.int64(0), np.int64(255)\] | [None, None\] | [0, 255\] | None | open | 'tde.yclip_boundary_0' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| cclip_enable_0 | cclip enable 0 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.cclip_enable_0' | Chroma clip enable control bit | user | None | None | None |
| cclip_cmtx_0 | cclip cmtx 0 | s2.5 | AX_S8 | [2, 2\] | [np.int64(-128), np.int64(127)\] | [np.float64(-4.0), np.float64(3.96875)\] | [[32, 0\], [0, 32\]\] | [[np.float64(1.0), np.float64(0.0)\], [np.float64(0.0), np.float64(1.0)\]\] | open | 'tde.cclip_cmtx_0' | 2x2 chroma adjustment matrix | user | None | None | None |
| cclip_boundary_0 | cclip boundary 0 | s8 | AX_S16 | [2\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [-128, 128\] | None | open | 'tde.cclip_boundary_0' | 2x2 chroma adjustment --> clip | user | None | None | None |
| crop_enable_0 | crop enable 0 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.crop_enable_0' | Crop enable control bit | user | None | None | None |
| crop_start_0 | crop start 0 | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open | 'tde.crop_start_0' | Crop start coordinates | user | None | None | None |
| crop_size_0 | crop size 0 | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open | 'tde.crop_size_0' | Crop size | user | None | None | None |
| lba_enable_0 | lba enable 0 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.lba_enable_0' | lba enable control bit | user | None | None | None |
| lba_exd_size_0 | lba exd size 0 | u13 | AX_U16 | [4\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'tde.lba_exd_size_0' | lba extension size(paddings on edges) | user | None | None | None |
| lba_background_0 | lba enable 0 | s8 | AX_S16 | [3\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | open | 'tde.lba_background_0' | lba background color | user | None | None | None |
| scale_input_mux_1 | scale input mux 1 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.scale_input_mux_1' | Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output | user | None | None | None |
| scale_enable_1 | scale enable 1 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.scale_enable_1' | Scalar enable control bit | user | None | None | None |
| scale_output_size_1 | scale output size 1 | u13 | AX_U16 | [2\] | [np.int64(2), np.int64(4096)\] | [None, None\] | [1080, 1920\] | None | open | 'tde.scale_output_size_1', 'tde.scale_step_1', 'tde.scale_offset_luma_1', 'tde.scale_offset_chroma_1' | Scalar output size | user | None | None | None |
| rect_enable_1 | rect enable 1 | u1 | AX_U8 | [16\] | [np.int64(0), np.int64(1)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_enable_1' | Rectangles' enable control bits | user | None | None | None |
| rect_tl_coord_x_1 | rect tl coord x 1 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_tl_coord_x_1' | top-left x coordinates of rectangles | user | None | None | None |
| rect_tl_coord_y_1 | rect tl coord y 1 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_tl_coord_y_1' | top-left y coordinates of rectangles | user | None | None | None |
| rect_width_1 | rect width 1 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_br_coord_x_1' | width of rectangles | user | None | None | None |
| rect_height_1 | rect height 1 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_br_coord_y_1' | height coordinates of rectangles | user | None | None | None |
| rect_edge_thickness_1 | rect edge thickness 1 | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 4 | None | open | 'tde.rect_edge_thickness_1' | Rectangles' edge thickness | user | None | None | None |
| rect_edge_color_select_1 | rect edge color select 1 | u2 | AX_U8 | [16\] | [np.int64(0), np.int64(3)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_edge_color_select_1' | Rectangles' color select | user | None | None | None |
| rect_edge_color_val_1 | rect edge color val 1 | u8 | AX_U8 | [4, 4\] | [np.int64(0), np.int64(255)\] | [None, None\] | [[255, 255, 255, 255\], [255, 255, 255, 255\], [255, 255, 255, 255\], [255, 255, 255, 255\]\] | None | open | 'tde.rect_edge_color_val_1' | larger value means less restriction on directional noise reduction | user | None | None | None |
| yclip_enable_1 | yclip enable 1 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.yclip_enable_1' | Luma clip enable control bit | user | None | None | None |
| yclip_gain_1 | yclip gain 1 | s4.8 | AX_S16 | [\] | [np.int64(-4096), np.int64(4095)\] | [np.float64(-16.0), np.float64(15.99609375)\] | 256 | 1.0 | open | 'tde.yclip_gain_1' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| yclip_boundary_1 | yclip boundary 1 | u8 | AX_U8 | [2\] | [np.int64(0), np.int64(255)\] | [None, None\] | [0, 255\] | None | open | 'tde.yclip_boundary_1' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| cclip_enable_1 | cclip enable 1 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.cclip_enable_1' | Chroma clip enable control bit | user | None | None | None |
| cclip_cmtx_1 | cclip cmtx 1 | s2.5 | AX_S8 | [2, 2\] | [np.int64(-128), np.int64(127)\] | [np.float64(-4.0), np.float64(3.96875)\] | [[32, 0\], [0, 32\]\] | [[np.float64(1.0), np.float64(0.0)\], [np.float64(0.0), np.float64(1.0)\]\] | open | 'tde.cclip_cmtx_1' | 2x2 chroma adjustment matrix | user | None | None | None |
| cclip_boundary_1 | cclip boundary 1 | s8 | AX_S16 | [2\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [-128, 128\] | None | open | 'tde.cclip_boundary_1' | 2x2 chroma adjustment --> clip | user | None | None | None |
| crop_enable_1 | crop enable 1 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.crop_enable_1' | Crop enable control bit | user | None | None | None |
| crop_start_1 | crop start 1 | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open | 'tde.crop_start_1' | Crop start coordinates | user | None | None | None |
| crop_size_1 | crop size 1 | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open | 'tde.crop_size_1' | Crop size | user | None | None | None |
| lba_enable_1 | lba enable 1 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.lba_enable_1' | lba enable control bit | user | None | None | None |
| lba_exd_size_1 | lba exd size 1 | u13 | AX_U16 | [4\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'tde.lba_exd_size_1' | lba extension size(paddings on edges) | user | None | None | None |
| lba_background_1 | lba enable 1 | s8 | AX_S16 | [3\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | open | 'tde.lba_background_1' | lba background color | user | None | None | None |
| scale_input_mux_2 | scale input mux 2 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.scale_input_mux_2' | Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output | user | None | None | None |
| scale_enable_2 | scale enable 2 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.scale_enable_2' | Scalar enable control bit | user | None | None | None |
| scale_output_size_2 | scale output size 2 | u13 | AX_U16 | [2\] | [np.int64(2), np.int64(4096)\] | [None, None\] | [1080, 1920\] | None | open | 'tde.scale_output_size_2', 'tde.scale_step_2', 'tde.scale_offset_luma_2', 'tde.scale_offset_chroma_2' | Scalar output size | user | None | None | None |
| scale_filter_kernel_mode_2 | scale filter kernel mode 2 | u1 | AX_U8 | [4\] | [np.int64(0), np.int64(1)\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'tde.scale_filter_x_luma_2', 'tde.scale_filter_y_luma_2', 'tde.scale_filter_x_chroma_2', 'tde.scale_filter_y_chroma_2' | kernel mode to be selected, 0:triangle, 1:cubic | user | None | None | None |
| scale_filter_bicubic_a_2 | scale filter bicubic a 2 | s1.10 | AX_S16 | [4\] | [np.int64(-1024), np.int64(1024)\] | [np.float64(-1.0), np.float64(1.0)\] | [0, 0, 0, 0\] | [np.float64(0.0), np.float64(0.0), np.float64(0.0), np.float64(0.0)\] | open | 'tde.scale_filter_x_luma_2', 'tde.scale_filter_y_luma_2', 'tde.scale_filter_x_chroma_2', 'tde.scale_filter_y_chroma_2' | Param a of bicubic. The less the sharper. the large the more smooth | user | None | None | None |
| rect_enable_2 | rect enable 2 | u1 | AX_U8 | [16\] | [np.int64(0), np.int64(1)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_enable_2' | Rectangles' enable control bits | user | None | None | None |
| rect_tl_coord_x_2 | rect tl coord x 2 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_tl_coord_x_2' | top-left x coordinates of rectangles | user | None | None | None |
| rect_tl_coord_y_2 | rect tl coord y 2 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_tl_coord_y_2' | top-left y coordinates of rectangles | user | None | None | None |
| rect_width_2 | rect width 2 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_br_coord_x_2' | width of rectangles | user | None | None | None |
| rect_height_2 | rect height 2 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_br_coord_y_2' | height coordinates of rectangles | user | None | None | None |
| rect_edge_thickness_2 | rect edge thickness 2 | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 4 | None | open | 'tde.rect_edge_thickness_2' | Rectangles' edge thickness | user | None | None | None |
| rect_edge_color_select_2 | rect edge color select 2 | u2 | AX_U8 | [16\] | [np.int64(0), np.int64(3)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_edge_color_select_2' | Rectangles' color select | user | None | None | None |
| rect_edge_color_val_2 | rect edge color val 2 | u8 | AX_U8 | [4, 4\] | [np.int64(0), np.int64(255)\] | [None, None\] | [[255, 255, 255, 255\], [255, 255, 255, 255\], [255, 255, 255, 255\], [255, 255, 255, 255\]\] | None | open | 'tde.rect_edge_color_val_2' | larger value means less restriction on directional noise reduction | user | None | None | None |
| yclip_enable_2 | yclip enable 2 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.yclip_enable_2' | Luma clip enable control bit | user | None | None | None |
| yclip_gain_2 | yclip gain 2 | s4.8 | AX_S16 | [\] | [np.int64(-4096), np.int64(4095)\] | [np.float64(-16.0), np.float64(15.99609375)\] | 256 | 1.0 | open | 'tde.yclip_gain_2' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| yclip_boundary_2 | yclip boundary 2 | u8 | AX_U8 | [2\] | [np.int64(0), np.int64(255)\] | [None, None\] | [0, 255\] | None | open | 'tde.yclip_boundary_2' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| cclip_enable_2 | cclip enable 2 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.cclip_enable_2' | Chroma clip enable control bit | user | None | None | None |
| cclip_cmtx_2 | cclip cmtx 2 | s2.5 | AX_S8 | [2, 2\] | [np.int64(-128), np.int64(127)\] | [np.float64(-4.0), np.float64(3.96875)\] | [[32, 0\], [0, 32\]\] | [[np.float64(1.0), np.float64(0.0)\], [np.float64(0.0), np.float64(1.0)\]\] | open | 'tde.cclip_cmtx_2' | 2x2 chroma adjustment matrix | user | None | None | None |
| cclip_boundary_2 | cclip boundary 2 | s8 | AX_S16 | [2\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [-128, 128\] | None | open | 'tde.cclip_boundary_2' | 2x2 chroma adjustment --> clip | user | None | None | None |
| crop_enable_2 | crop enable 2 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.crop_enable_2' | Crop enable control bit | user | None | None | None |
| crop_start_2 | crop start 2 | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open | 'tde.crop_start_2' | Crop start coordinates | user | None | None | None |
| crop_size_2 | crop size 2 | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open | 'tde.crop_size_2' | Crop size | user | None | None | None |
| lba_enable_2 | lba enable 1 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.lba_enable_2' | lba enable control bit | user | None | None | None |
| lba_exd_size_2 | lba exd size 2 | u13 | AX_U16 | [4\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'tde.lba_exd_size_2' | lba extension size(paddings on edges) | user | None | None | None |
| lba_background_2 | lba enable 2 | s8 | AX_S16 | [3\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | open | 'tde.lba_background_2' | lba background color | user | None | None | None |
| scale_input_mux_3 | scale input mux 3 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.scale_input_mux_3' | Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output | user | None | None | None |
| scale_enable_3 | scale enable 3 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.scale_enable_3' | Scalar enable control bit | user | None | None | None |
| scale_output_size_3 | scale output size 3 | u13 | AX_U16 | [2\] | [np.int64(2), np.int64(4096)\] | [None, None\] | [1080, 1920\] | None | open | 'tde.scale_output_size_3', 'tde.scale_step_3', 'tde.scale_offset_luma_3', 'tde.scale_offset_chroma_3' | Scalar output size | user | None | None | None |
| rect_enable_3 | rect enable 3 | u1 | AX_U8 | [16\] | [np.int64(0), np.int64(1)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_enable_3' | Rectangles' enable control bits | user | None | None | None |
| rect_tl_coord_x_3 | rect tl coord x 3 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_tl_coord_x_3' | top-left x coordinates of rectangles | user | None | None | None |
| rect_tl_coord_y_3 | rect tl coord y 3 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_tl_coord_y_3' | top-left y coordinates of rectangles | user | None | None | None |
| rect_width_3 | rect width 3 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_br_coord_x_3' | width of rectangles | user | None | None | None |
| rect_height_3 | rect height 3 | u13 | AX_U16 | [16\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_br_coord_y_3' | height coordinates of rectangles | user | None | None | None |
| rect_edge_thickness_3 | rect edge thickness 3 | u8 | AX_U8 | [\] | [np.int64(0), np.int64(255)\] | [None, None\] | 4 | None | open | 'tde.rect_edge_thickness_3' | Rectangles' edge thickness | user | None | None | None |
| rect_edge_color_select_3 | rect edge color select 3 | u2 | AX_U8 | [16\] | [np.int64(0), np.int64(3)\] | [None, None\] | [0, 0, ... , 0\] | None | open | 'tde.rect_edge_color_select_3' | Rectangles' color select | user | None | None | None |
| rect_edge_color_val_3 | rect edge color val 3 | u8 | AX_U8 | [4, 4\] | [np.int64(0), np.int64(255)\] | [None, None\] | [[255, 255, 255, 255\], [255, 255, 255, 255\], [255, 255, 255, 255\], [255, 255, 255, 255\]\] | None | open | 'tde.rect_edge_color_val_3' | larger value means less restriction on directional noise reduction | user | None | None | None |
| yclip_enable_3 | yclip enable 3 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.yclip_enable_3' | Luma clip enable control bit | user | None | None | None |
| yclip_gain_3 | yclip gain 3 | s4.8 | AX_S16 | [\] | [np.int64(-4096), np.int64(4095)\] | [np.float64(-16.0), np.float64(15.99609375)\] | 256 | 1.0 | open | 'tde.yclip_gain_3' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| yclip_boundary_3 | yclip boundary 3 | u8 | AX_U8 | [2\] | [np.int64(0), np.int64(255)\] | [None, None\] | [0, 255\] | None | open | 'tde.yclip_boundary_3' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| cclip_enable_3 | cclip enable 3 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.cclip_enable_3' | Chroma clip enable control bit | user | None | None | None |
| cclip_cmtx_3 | cclip cmtx 3 | s2.5 | AX_S8 | [2, 2\] | [np.int64(-128), np.int64(127)\] | [np.float64(-4.0), np.float64(3.96875)\] | [[32, 0\], [0, 32\]\] | [[np.float64(1.0), np.float64(0.0)\], [np.float64(0.0), np.float64(1.0)\]\] | open | 'tde.cclip_cmtx_3' | 2x2 chroma adjustment matrix | user | None | None | None |
| cclip_boundary_3 | cclip boundary 3 | s8 | AX_S16 | [2\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [-128, 128\] | None | open | 'tde.cclip_boundary_3' | 2x2 chroma adjustment --> clip | user | None | None | None |
| crop_enable_3 | crop enable 3 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.crop_enable_3' | Crop enable control bit | user | None | None | None |
| crop_start_3 | crop start 3 | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open | 'tde.crop_start_3' | Crop start coordinates | user | None | None | None |
| crop_size_3 | crop size 3 | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open | 'tde.crop_size_3' | Crop size | user | None | None | None |
| lba_enable_3 | lba enable 3 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.lba_enable_3' | lba enable control bit | user | None | None | None |
| lba_exd_size_3 | lba exd size 3 | u13 | AX_U16 | [4\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'tde.lba_exd_size_3' | lba extension size(paddings on edges) | user | None | None | None |
| lba_background_3 | lba enable 3 | s8 | AX_S16 | [3\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | open | 'tde.lba_background_3' | lba background color | user | None | None | None |
| scale_input_mux_4 | scale input mux 4 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.scale_input_mux_4' | Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output | user | None | None | None |
| scale_enable_4 | scale enable 4 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.scale_enable_4' | Scalar enable control bit | user | None | None | None |
| scale_output_size_4 | scale output size 4 | u13 | AX_U16 | [2\] | [np.int64(2), np.int64(4096)\] | [None, None\] | [1080, 1920\] | None | open | 'tde.scale_output_size_4', 'tde.scale_step_4', 'tde.scale_offset_luma_4', 'tde.scale_offset_chroma_4' | Scalar output size | user | None | None | None |
| yclip_enable_4 | yclip enable 4 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.yclip_enable_4' | Luma clip enable control bit | user | None | None | None |
| yclip_gain_4 | yclip gain 4 | s4.8 | AX_S16 | [\] | [np.int64(-4096), np.int64(4095)\] | [np.float64(-16.0), np.float64(15.99609375)\] | 256 | 1.0 | open | 'tde.yclip_gain_4' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| yclip_boundary_4 | yclip boundary 4 | u8 | AX_U8 | [2\] | [np.int64(0), np.int64(255)\] | [None, None\] | [0, 255\] | None | open | 'tde.yclip_boundary_4' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| cclip_enable_4 | cclip enable 4 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.cclip_enable_4' | Chroma clip enable control bit | user | None | None | None |
| cclip_cmtx_4 | cclip cmtx 4 | s2.5 | AX_S8 | [2, 2\] | [np.int64(-128), np.int64(127)\] | [np.float64(-4.0), np.float64(3.96875)\] | [[32, 0\], [0, 32\]\] | [[np.float64(1.0), np.float64(0.0)\], [np.float64(0.0), np.float64(1.0)\]\] | open | 'tde.cclip_cmtx_4' | 2x2 chroma adjustment matrix | user | None | None | None |
| cclip_boundary_4 | cclip boundary 4 | s8 | AX_S16 | [2\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [-128, 128\] | None | open | 'tde.cclip_boundary_4' | 2x2 chroma adjustment --> clip | user | None | None | None |
| scale_input_mux_5 | scale input mux 5 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.scale_input_mux_5' | Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output | user | None | None | None |
| scale_enable_5 | scale enable 5 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.scale_enable_5' | Scalar enable control bit | user | None | None | None |
| scale_output_size_5 | scale output size 5 | u13 | AX_U16 | [2\] | [np.int64(2), np.int64(4096)\] | [None, None\] | [1080, 1920\] | None | open | 'tde.scale_output_size_5', 'tde.scale_step_5', 'tde.scale_offset_luma_5', 'tde.scale_offset_chroma_5' | Scalar output size | user | None | None | None |
| yclip_enable_5 | yclip enable 5 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.yclip_enable_5' | Luma clip enable control bit | user | None | None | None |
| yclip_gain_5 | yclip gain 5 | s4.8 | AX_S16 | [\] | [np.int64(-4096), np.int64(4095)\] | [np.float64(-16.0), np.float64(15.99609375)\] | 256 | 1.0 | open | 'tde.yclip_gain_5' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| yclip_boundary_5 | yclip boundary 5 | u8 | AX_U8 | [2\] | [np.int64(0), np.int64(255)\] | [None, None\] | [0, 255\] | None | open | 'tde.yclip_boundary_5' | -offset[0\] --> x gain --> +offset[1\] --> clip | user | None | None | None |
| cclip_enable_5 | cclip enable 5 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.cclip_enable_5' | Chroma clip enable control bit | user | None | None | None |
| cclip_cmtx_5 | cclip cmtx 5 | s2.5 | AX_S8 | [2, 2\] | [np.int64(-128), np.int64(127)\] | [np.float64(-4.0), np.float64(3.96875)\] | [[32, 0\], [0, 32\]\] | [[np.float64(1.0), np.float64(0.0)\], [np.float64(0.0), np.float64(1.0)\]\] | open | 'tde.cclip_cmtx_5' | 2x2 chroma adjustment matrix | user | None | None | None |
| cclip_boundary_5 | cclip boundary 5 | s8 | AX_S16 | [2\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [-128, 128\] | None | open | 'tde.cclip_boundary_5' | 2x2 chroma adjustment --> clip | user | None | None | None |
| crop_enable_5 | crop enable 5 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.crop_enable_5' | Crop enable control bit | user | None | None | None |
| crop_start_5 | crop start 5 | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open | 'tde.crop_start_5' | Crop start coordinates | user | None | None | None |
| crop_size_5 | crop size 5 | u13 | AX_U16 | [2\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0\] | None | open | 'tde.crop_size_5' | Crop size | user | None | None | None |
| lba_enable_5 | lba enable 5 | u1 | AX_U8 | [\] | [np.int64(0), np.int64(1)\] | [None, None\] | 0 | None | open | 'tde.lba_enable_5' | lba enable control bit | user | None | None | None |
| lba_exd_size_5 | lba exd size 5 | u13 | AX_U16 | [4\] | [np.int64(0), np.int64(8191)\] | [None, None\] | [0, 0, 0, 0\] | None | open | 'tde.lba_exd_size_5' | lba extension size(paddings on edges) | user | None | None | None |
| lba_background_5 | lba enable 5 | s8 | AX_S16 | [3\] | [np.int64(-256), np.int64(255)\] | [None, None\] | [0, 0, 0\] | None | open | 'tde.lba_background_5' | lba background color | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |