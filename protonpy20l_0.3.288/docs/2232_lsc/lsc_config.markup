h2. Conf list
h3. lsc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| enable | u1 | [\] | - |  | 0: bypass, 1: enable |  |
| mesh_length | u10 | [2\] | - |  | The number of pixels in a coarse mesh grid |  |
| inv_mesh_length | u1.22 | [2\] | - | 0 <= 1 - mesh_length\*inv_mesh_length <= 1/4096 | inv of mesh length |  |
| inv_mesh_start | u1.22 | [2\] | support | [0.0, 1.0) | The coordinates of the tile starting point in the coarse grid which is scaled according to inv_mesh_length | inv_mesh_start = (locat/2)\*inv_mesh_length |
| locat | u10 | [2\] | support | [0, 2\*mesh_length) | The coordinates of the upper left corner pixel of the current tile in the mesh_start grid |  |
| mesh_start | u7 | [2\] | support | [1, mesh_shape - 3\] | The coordinates of the coarse mesh grid where the upper left corner of the current tile is located |  |
| mesh_end | u7 | [\] | support | [mesh_start + 1, mesh_shape - 2\] | The coordinates of the coarse mesh grid where the lower right corner of the current tile is located |  |
| rr_mesh | u4.14 | [19, 15\] | - | [1.0, 17.0) | rr channel correction table |  |
| gr_mesh | u4.14 | [19, 15\] | - | [1.0, 17.0) | gr channel correction table |  |
| gb_mesh | u4.14 | [19, 15\] | - | [1.0, 17.0) | gb channel correction table |  |
| bb_mesh | u4.14 | [19, 15\] | - | [1.0, 17.0) | bb channel correction table |  |
| offset_in | u14.6 | [\] | - |  | offset for input | Set according to pipe |
| offset_out | u14.6 | [\] | - |  | offset for output | Set according to pipe |

