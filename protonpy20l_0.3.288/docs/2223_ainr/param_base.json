{"configs": {"ainr": {"enable": {"acc": [0, 1], "size": [], "description": "bypass/enable", "usage": "0: bypass, 1: enable", "constraints": "0, 1", "type": "AX_U8", "partition": "-"}, "hcg_mode": {"acc": [0, 1], "constraints": "0, 1", "type": "AX_U8", "size": [], "description": "hcg_mode", "partition": "-"}, "model_list_num": {"type": "AX_U8", "acc": [0, 8], "constraints": "[0, 6]", "size": [], "description": "model_list_num", "partition": "-"}, "model_list_path": {"type": "AX_CHAR", "size": [6, 128], "description": "model_list_path", "partition": "-"}, "curr_model_path": {"type": "AX_CHAR", "size": [128], "description": "curr_model_path", "partition": "-"}, "curr_temporal_nr_name": {"type": "AX_CHAR", "size": [128], "description": "curr_temporal_nr_name", "partition": "-"}, "curr_spatial_nr_name": {"type": "AX_CHAR", "size": [128], "description": "curr_spatial_nr_name", "partition": "-"}, "offset_2d": {"acc": [0, 16], "size": [4], "description": "offset_2d", "usage": "normal set [4096, 4096, 4096, 4096]", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "offset_3d": {"acc": [0, 16], "size": [4], "description": "offset_3d", "usage": "normal set [4096, 4096, 4096, 4096]", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "lut_temporal_blend": {"acc": [0, 16], "size": [257], "description": "lut_temporal_blend", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "lut_spatial_guidance": {"acc": [0, 16], "size": [257], "description": "lut_spatial_guidance", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "lut_spatial_blend": {"acc": [0, 16], "size": [257], "description": "lut_spatial_blend", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "lut_hdr_mask": {"acc": [0, 16], "size": [257], "description": "lut_hdr_mask", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "lut_vst_inv": {"acc": [0, 16], "size": [257], "description": "lut_vst_inv", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "lut_ext_temporal": {"acc": [0, 16], "size": [257], "description": "lut_ext_temporal", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "lut_high_frequency": {"acc": [0, 16], "size": [257], "description": "lut_high_frequency", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "position_temporal_blend": {"acc": [0, 16], "size": [16], "description": "position_temporal_blend", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "step_temporal_blend": {"acc": [0, 32], "size": [16], "description": "step_temporal_blend", "usage": "normal set x==y", "constraints": "[0, 4294967295]", "type": "AX_U32", "partition": "-"}, "position_spatial_guidance": {"acc": [0, 16], "size": [16], "description": "position_spatial_guidance", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "step_spatial_guidance": {"acc": [0, 32], "size": [16], "description": "step_spatial_guidance", "usage": "normal set x==y", "constraints": "[0, 4294967295]", "type": "AX_U32", "partition": "-"}, "position_spatial_blend": {"acc": [0, 16], "size": [16], "description": "position_spatial_blend", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "step_spatial_blend": {"acc": [0, 32], "size": [16], "description": "step_spatial_blend", "usage": "normal set x==y", "constraints": "[0, 4294967295]", "type": "AX_U32", "partition": "-"}, "position_hdr_mask": {"acc": [0, 16], "size": [16], "description": "position_hdr_mask", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "step_hdr_mask": {"acc": [0, 32], "size": [16], "description": "step_hdr_mask", "usage": "normal set x==y", "constraints": "[0, 4294967295]", "type": "AX_U32", "partition": "-"}, "position_vst_inv": {"acc": [0, 16], "size": [16], "description": "position_vst_inv", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "step_vst_inv": {"acc": [0, 32], "size": [16], "description": "step_vst_inv", "usage": "normal set x==y", "constraints": "[0, 4294967295]", "type": "AX_U32", "partition": "-"}, "position_ext_temporal": {"acc": [0, 16], "size": [16], "description": "position_ext_temporal", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "step_ext_temporal": {"acc": [0, 32], "size": [16], "description": "step_ext_temporal", "usage": "normal set x==y", "constraints": "[0, 4294967295]", "type": "AX_U32", "partition": "-"}, "position_high_frequency": {"acc": [0, 16], "size": [16], "description": "position_high_frequency", "usage": "normal set x==y", "constraints": "[0, 65535]", "type": "AX_U16", "partition": "-"}, "step_high_frequency": {"acc": [0, 32], "size": [16], "description": "step_high_frequency", "usage": "normal set x==y", "constraints": "[0, 4294967295]", "type": "AX_U32", "partition": "-"}}, "hdrc": {"enable": {"acc": [0, 1], "size": [], "type": "AX_U8", "partition": "-"}, "affine_k": {"acc": [0, 7, 9], "size": [], "type": "AX_U16", "partition": "-"}, "lut": {"acc": [0, 10, 6], "size": [257], "type": "AX_U16", "partition": "-"}, "clip_level": {"acc": [0, 10, 6], "size": [], "type": "AX_U16", "partition": "-"}, "offset_in": {"acc": [0, 14, 6], "size": [4], "type": "AX_U32", "partition": "-"}, "offset_out": {"acc": [0, 10, 6], "size": [], "type": "AX_U16", "partition": "-"}, "dither_enable": {"acc": [0, 1], "size": [], "type": "AX_U8", "partition": "-"}, "dither_seed_enable": {"acc": [0, 1, 0], "size": [], "type": "AX_U8", "partition": "-"}, "dither_seed": {"acc": [0, 16, 0], "size": [2], "type": "AX_U16", "partition": "-"}, "dither_pmask_16bit": {"acc": [0, 16, 0], "size": [], "type": "AX_U16", "partition": "-"}, "dither_pmask_15bit": {"acc": [0, 16, 0], "size": [], "type": "AX_U16", "partition": "-"}}, "hidrc": {"enable": {"acc": [0, 1], "size": [], "type": "AX_U8", "partition": "-"}, "affine_k": {"acc": [0, 1, 11], "size": [], "type": "AX_U16", "partition": "-"}, "lut": {"acc": [0, 14, 6], "size": [513], "type": "AX_U32", "partition": "-"}, "lut_inv_mode": {"acc": [0, 1], "size": [], "type": "AX_U8", "partition": "-"}, "offset_in": {"acc": [0, 10, 6], "size": [], "type": "AX_U16", "partition": "-"}, "offset_out": {"acc": [0, 14, 6], "size": [4], "type": "AX_U32", "partition": "-"}}}, "context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "Ainr is 0x2223", "type": "AX_U16"}}, "params": {"enable": {"display": "enable", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": "1", "comment": "SW enable", "hidden": 0, "auto": 0, "target_conf": ["ainr.enable"], "dependency": "user"}, "hcg_mode": {"display": "hcg_mode", "type": "AX_U8", "acc": [0, 1], "size": [], "range": [0, 1], "default": "1", "comment": "hcg_mode", "hidden": 1, "auto": 0, "target_conf": ["ainr.hcg_mode"], "dependency": "common"}, "model_list_num": {"display": "model_list_num", "type": "AX_U8", "acc": [0, 8], "range": [0, 6], "size": [], "default": 6, "comment": "model_list_num", "hidden": 0, "auto": 0, "target_conf": ["ainr.model_list_num"], "dependency": "user"}, "model_list_path": {"display": "model_list_path", "type": "AX_CHAR", "range": [-128, 127], "size": [6, 128], "comment": "model_list_path", "hidden": 0, "auto": 0, "target_conf": ["ainr.model_list_path"], "dependency": "user"}, "curr_model_path": {"display": "curr_model_path", "type": "AX_CHAR", "range": [-128, 127], "size": [128], "default": "", "comment": "curr_model_path", "hidden": 0, "auto": 2, "target_conf": ["ainr.curr_model_path"], "dependency": "user"}, "curr_temporal_nr_name": {"display": "curr_temporal_nr_name", "type": "AX_CHAR", "range": [-128, 127], "size": [128], "default": "", "comment": "curr_temporal_nr_name", "hidden": 0, "auto": 2, "target_conf": ["ainr.curr_temporal_nr_name"], "dependency": "user"}, "curr_spatial_nr_name": {"display": "curr_spatial_nr_name", "type": "AX_CHAR", "range": [-128, 127], "size": [128], "default": "", "comment": "curr_spatial_nr_name", "hidden": 0, "auto": 2, "target_conf": ["ainr.curr_spatial_nr_name"], "dependency": "user"}, "bias_out": {"display": "bias_out", "acc": [1, 9, 6], "type": "AX_S16", "size": [4], "range": [-512.0, 511.984375], "default": [0, 0, 0, 0], "comment": "bias_out", "hidden": 0, "auto": 2, "target_conf": [], "dependency": "user"}, "offset_2d": {"display": "offset_2d", "acc": [0, 16], "type": "AX_U16", "size": [4], "range": [0, 65535], "default": [4096, 4096, 4096, 4096], "comment": "offset_2d", "hidden": 0, "auto": 2, "target_conf": ["ainr.offset_2d"], "dependency": "user"}, "offset_3d": {"display": "offset_3d", "acc": [0, 16], "type": "AX_U16", "size": [4], "range": [0, 65535], "default": [4096, 4096, 4096, 4096], "comment": "offset_3d", "hidden": 0, "auto": 2, "target_conf": ["ainr.offset_3d"], "dependency": "user"}, "lut_temporal_blend": {"display": "lut_temporal_blend", "acc": [0, 8], "type": "AX_U8", "size": [17, 2], "range": [0, 255], "default": [[0, 0], [16, 16], [32, 32], [48, 48], [64, 64], [80, 80], [96, 96], [112, 112], [128, 128], [143, 143], [159, 159], [175, 175], [191, 191], [207, 207], [223, 223], [239, 239], [255, 255]], "comment": "lut_temporal_blend", "hidden": 0, "auto": 1, "target_conf": ["ainr.lut_temporal_blend", "ainr.position_temporal_blend", "ainr.step_temporal_blend"], "dependency": "user"}, "lut_spatial_guidance": {"display": "lut_spatial_guidance", "acc": [0, 8], "type": "AX_U8", "size": [17, 2], "range": [0, 255], "default": [[0, 0], [16, 16], [32, 32], [48, 48], [64, 64], [80, 80], [96, 96], [112, 112], [128, 128], [143, 143], [159, 159], [175, 175], [191, 191], [207, 207], [223, 223], [239, 239], [255, 255]], "comment": "lut_spatial_guidance", "hidden": 0, "auto": 1, "target_conf": ["ainr.lut_spatial_guidance", "ainr.position_spatial_guidance", "ainr.step_spatial_guidance"], "dependency": "user"}, "lut_spatial_blend": {"display": "lut_spatial_blend", "acc": [0, 8], "type": "AX_U8", "size": [17, 2], "range": [0, 255], "default": [[0, 0], [16, 16], [32, 32], [48, 48], [64, 64], [80, 80], [96, 96], [112, 112], [128, 128], [143, 143], [159, 159], [175, 175], [191, 191], [207, 207], [223, 223], [239, 239], [255, 255]], "comment": "lut_spatial_blend", "hidden": 0, "auto": 1, "target_conf": ["ainr.lut_spatial_blend", "ainr.position_spatial_blend", "ainr.step_spatial_blend"], "dependency": "user"}, "lut_hdr_mask": {"display": "lut_hdr_mask", "acc": [0, 8], "type": "AX_U8", "size": [17, 2], "range": [0, 255], "default": [[0, 0], [16, 16], [32, 32], [48, 48], [64, 64], [80, 80], [96, 96], [112, 112], [128, 128], [143, 143], [159, 159], [175, 175], [191, 191], [207, 207], [223, 223], [239, 239], [255, 255]], "comment": "lut_hdr_mask", "hidden": 0, "auto": 1, "target_conf": ["ainr.lut_hdr_mask", "ainr.position_hdr_mask", "ainr.step_hdr_mask"], "dependency": "user"}, "lut_ext_temporal": {"display": "lut_ext_temporal", "acc": [0, 8], "type": "AX_U8", "size": [17, 2], "range": [0, 255], "default": [[0, 0], [16, 16], [32, 32], [48, 48], [64, 64], [80, 80], [96, 96], [112, 112], [128, 128], [143, 143], [159, 159], [175, 175], [191, 191], [207, 207], [223, 223], [239, 239], [255, 255]], "comment": "lut_ext_temporal", "hidden": 0, "auto": 1, "target_conf": ["ainr.lut_ext_temporal", "ainr.position_ext_temporal", "ainr.step_ext_temporal"], "dependency": "user"}, "lut_high_frequency": {"display": "lut_high_frequency", "acc": [0, 8], "type": "AX_U8", "size": [17, 2], "range": [0, 255], "default": [[0, 0], [16, 16], [32, 32], [48, 48], [64, 64], [80, 80], [96, 96], [112, 112], [128, 128], [143, 143], [159, 159], [175, 175], [191, 191], [207, 207], [223, 223], [239, 239], [255, 255]], "comment": "lut_high_frequency", "hidden": 0, "auto": 1, "target_conf": ["ainr.lut_high_frequency", "ainr.position_high_frequency", "ainr.step_high_frequency"], "dependency": "user"}, "nr_level_mode": {"display": "nr_level_mode", "acc": [0, 1], "type": "AX_U8", "size": [], "range": [0, 1], "default": 0, "comment": "0: lut fusion, 1: only use nr level", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "spatial_nr_level": {"display": "spatial_nr_level", "acc": [1, 7], "type": "AX_S8", "size": [], "range": [-127, 127], "default": 0, "comment": "spatial_nr_level", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "temporal_nr_level": {"display": "temporal_nr_level", "acc": [1, 7], "type": "AX_S8", "size": [], "range": [-127, 127], "default": 0, "comment": "temporal_nr_level", "hidden": 0, "auto": 0, "target_conf": [], "dependency": "user"}, "hdr_ratio": {"acc": [0, 9, 8], "size": [], "range": [0.0, 511.99609375], "default": 1.0, "hidden": 1, "dependency": "common"}, "hdrc_input_max_value": {"acc": [0, 14, 6], "size": [], "range": [0.0, 16383.984375], "default": 16383.984375, "hidden": 1, "target_conf": [], "dependency": "common"}, "hdrc_output_max_value": {"acc": [0, 10, 6], "size": [], "range": [0.0, 1023.984375], "default": 1023.984375, "hidden": 1, "target_conf": [], "dependency": "common"}, "hdrc_offset_in": {"acc": [0, 14, 6], "size": [], "default": 16.0, "hidden": 1, "target_conf": ["hdrc.offset_in"], "dependency": "common"}, "hdrc_offset_out": {"acc": [0, 10, 6], "size": [], "default": 64.0, "hidden": 1, "target_conf": ["hdrc.offset_out"], "dependency": "common"}, "hdrc_dither_enable": {"acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "hidden": 1, "target_conf": ["hdrc.dither_enable"], "dependency": "common"}, "hdrc_dither_seed_enable": {"acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "hidden": 1, "target_conf": ["hdrc.dither_seed_enable"], "dependency": "common"}, "hidrc_offset_in": {"acc": [0, 10, 6], "size": [], "default": 64.0, "hidden": 1, "target_conf": ["hidrc.offset_in"], "dependency": "common"}, "hidrc_offset_out": {"acc": [0, 14, 6], "size": [], "default": 16.0, "hidden": 1, "target_conf": ["hidrc.offset_out"], "dependency": "common"}, "hidrc_input_max_value": {"acc": [0, 10, 6], "size": [], "range": [0.0, 1023.984375], "default": 1023.984375, "hidden": 1, "target_conf": [], "dependency": "common"}, "hidrc_output_max_value": {"acc": [0, 14, 6], "size": [], "range": [0.0, 16383.984375], "default": 16383.984375, "hidden": 1, "target_conf": [], "dependency": "common"}}, "submodules": {"setup": {"configs": [], "params": ["enable"]}, "model_name": {"configs": [], "params": ["hcg_mode", "model_list_num", "model_list_path", "curr_model_path", "curr_temporal_nr_name", "curr_spatial_nr_name"]}, "info": {"configs": [], "params": ["bias_out", "offset_2d", "offset_3d"]}, "temporal_spatial_lut": {"configs": [], "params": ["lut_temporal_blend", "lut_spatial_blend", "nr_level_mode", "temporal_nr_level", "spatial_nr_level"]}, "spatial_guidance_lut": {"configs": [], "params": ["lut_spatial_guidance"]}, "hdr_mask_lut": {"configs": [], "params": ["lut_hdr_mask"]}, "vst_inv_lut": {"configs": ["ainr.lut_vst_inv", "ainr.position_vst_inv", "ainr.step_vst_inv"], "params": []}, "ext_temporal_lut": {"configs": [], "params": ["lut_ext_temporal"]}, "high_frequency_lut": {"configs": [], "params": ["lut_high_frequency"]}, "hdrc_lut": {"params": ["hdrc_offset_in", "hdrc_offset_out", "hdr_ratio", "hdrc_input_max_value", "hdrc_output_max_value", "hdrc_dither_enable", "hdrc_dither_seed_enable"], "configs": ["hdrc.clip_level", "hdrc.dither_enable", "hdrc.dither_seed_enable", "hdrc.dither_seed", "hdrc.dither_pmask_16bit", "hdrc.dither_pmask_15bit", "hdrc.enable", "hdrc.affine_k", "hdrc.lut"]}, "hidrc_lut": {"params": ["hidrc_offset_in", "hidrc_offset_out", "hidrc_input_max_value", "hidrc_output_max_value"], "configs": ["hidrc.lut_inv_mode", "hidrc.enable", "hidrc.affine_k", "hidrc.lut"]}}, "structs": {}, "autos": {"1": {"ref_mode": ["gain/lux", "hdr_ratio"], "ref_group_num": [12, 4], "ref_interp_method": ["linear", "linear"]}, "2": {"ref_mode": ["gain/lux", "hdr_ratio"], "ref_group_num": [12, 4], "ref_interp_method": ["nearest", "nearest"]}}, "target_module": {"mc20l": {"hdrc": {"id": 2150, "method": 0}, "hidrc": {"id": 2151, "method": 0}}}}