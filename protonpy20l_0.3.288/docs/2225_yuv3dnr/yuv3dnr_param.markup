h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| debug_mode |  | u4 | AX_U8 | [\] |  [0, 13\] | [None, None\] | 1 | None | hidden | 'yuv_3dnr.debug_mode' | Debug mode, 0:normal, 1:y motion, 2:uv motion, 3:cur tweight, 4: ref y lf mask, 5:ref y hf mask, 6:motion to static mask, 7:cur y lf edge, , 8: cur y lf detail, 9: cur y lf sf result, 10: cur y hf edge, 11: cur y hf detail, 12: cur y hf sf result, 13:ext mask | common |
| debug_gain |  | u3 | AX_U8 | [\] |  [0, 7\] | [None, None\] | 1 | None | hidden | 'yuv_3dnr.debug_gain' | Shift the debug output left by the number of bits | common |
| init_ref |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'yuv_3dnr.init_ref' | is_first_frame | common |
| init_tweight |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'yuv_3dnr.init_tweight' | is_first_frame | common |
| init_coef_ab |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 0 | None | hidden | 'yuv_3dnr.init_coef_ab' | is_first_frame | common |
| dither_seed_enable |  | u1 | AX_U8 | [\] |  [0, 1\] | [None, None\] | 1 | None | hidden | 'yuv_3dnr.dither_seed_enable' | 0: use stat dither_seed, 1: use conf dither_seed. set 1 for the 1st frame, then set 0 from the 2nd frame onwards. | common |
| yuv3dnr_partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'yuv_3dnr.ptn_roi_t_b_l_r', 'yuv_3dnr.pic_h_w', 'yuv_3dnr.ptn_h_w', 'yuv_3dnr.ptn_offset_h_w' | dehaze partition | common |
| ifa_partition_info |  | acc_unknown | ax_isp_ptn_info_t | [\] |  [None, None\] | [None, None\] | None | None | hidden | 'ifa.output_hor_crop_enable', 'ifa.output_hor_crop_st', 'ifa.output_hor_crop_width' | ifa partition | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'yuv_3dnr.tf_y_en', 'yuv_3dnr.tf_uv_en', 'yuv_3dnr.sf_gau_y_en', 'yuv_3dnr.sf_lap_y_en', 'yuv_3dnr.sf_gau_uv_en', 'yuv_3dnr.sf_lap_uv_en', 'yuv_3dnr.gf_uv_en', 'yuv_3dnr.debug_gain', 'yuv_3dnr.debug_mode' | sw enable | user | None | None | None |
| tf_y_en | tf y en | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'yuv_3dnr.tf_y_en' | y tf enable | user | None | None | None |
| tf_uv_en | tf uv en | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'yuv_3dnr.tf_uv_en' | uv tf enable | user | None | None | None |
| sf_gau_y_en | sf gau y en | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'yuv_3dnr.sf_gau_y_en' | y gau sf enable | user | None | None | None |
| sf_lap_y_en | sf lap y en | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'yuv_3dnr.sf_lap_y_en' | y lap sf enable | user | None | None | None |
| sf_gau_uv_en | sf gau uv en | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'yuv_3dnr.sf_gau_uv_en' | uv gau sf enable | user | None | None | None |
| sf_lap_uv_en | sf lap uv en | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'yuv_3dnr.sf_lap_uv_en' | uv lap sf enable | user | None | None | None |
| gf_uv_en | gf uv en | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'yuv_3dnr.gf_uv_en' | uv gf enable | user | None | None | None |
| tweight_sf_en | tweight sf en | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'yuv_3dnr.tweight_sf_en' | tweight sf enable | user | None | None | None |
| ext_mask_en | ext mask en | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'yuv_3dnr.ext_mask_en' | ext mask enable | user | None | None | None |
| ext_mask_mode | ext mask mode | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'yuv_3dnr.ext_mask_mode' | ext mask mode, 0:ai, 1:hdr | user | None | None | None |
| ext_mask_thre | ext mask thre | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 0 | 0.0 | open | 'yuv_3dnr.ext_mask_thre' | The starting point for remapping ext mask, below which the probability will be mapped to 0, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| ext_mask_slope | ext mask slope | u8.8 | AX_U16 | [\] | [0, 65535\] | [0.0, 255.99609375\] | 256 | 1.0 | open | 'yuv_3dnr.ext_mask_slope' | The slope of ext mask remapping, the larger the value, the easier it is to map the mask to 1.0, corresponding slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| ext_mask_ratio | ext mask ratio | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 128 | 1.0 | open | 'yuv_3dnr.ext_mask_ratio' | Only effective in AI mode. The reference level of AI mask. The larger the size, the more likely it is to use an AI mask as the motion mask | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_small_sel_ratio | motion small sel ratio | u9 | AX_U16 | [\] | [0, 256\] | [None, None\] | 128 | None | open | 'yuv_3dnr.motion_small_win_sel', 'yuv_3dnr.motion_small_sel_ratio' | Applied to the feature of motion judgement, the larger the value, the larger the detection window used for motion judgement, which improves the noise resistance and smoothness of motion detection while losing the ability to detect texture details. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_large_sel_ratio | motion large sel ratio | u9 | AX_U16 | [\] | [0, 256\] | [None, None\] | 128 | None | open | 'yuv_3dnr.motion_large_win_sel', 'yuv_3dnr.motion_large_sel_ratio' | The greater the values of tweight_sf_degional_y and tweight_sf_densitive_y features applied to time-domain fusion coefficient filtering, the larger the detection window used for motion judgement. This improves the noise resistance and smoothness of motion detection while losing texture detail detection ability, and is generally slightly larger than the motion_small_sel_ratio. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_feature_gauss_str | motion feature gauss str | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 128 | 1.0 | open | 'yuv_3dnr.motion_feature_gauss_str' | The pre smoothing degree of the motion judgement features, the larger the value, the smoother the motion judgement features. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_sad_sd_th | motion sad sd th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 1 | None | open | 'yuv_3dnr.motion_sad_sd_th' | Representing the difference between two types of motion judgement features, used to map the fusion ratio of texture sensitive motion judgement features, corresponding to x_th. The larger the value, the less texture sensitive motion judgement features are used, and the probability of misjudgment caused by noise decreases. However, it may lead to some areas being misjudged as stationary. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_sad_weight_slope | motion sad weight slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 26 | 0.1015625 | open | 'yuv_3dnr.motion_sad_weight_slope' | Based on the fusion mapping of texture sensitive motion judgement features using motion_densitive-diff_th, the change speed corresponds to the slope. The larger the value, the faster the texture sensitive motion judgement features are used, and the probability of misjudgment caused by noise increases, resulting in more accurate motion judgement. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_sad_weight_th | motion sad weight th | u1.7 | AX_U8 | [2\] | [0, 128\] | [0.0, 1.0\] | [128, 128\] | [1.0, 1.0\] | open | 'yuv_3dnr.motion_sad_weight_th' | The upper and lower limits of the fusion ratio of texture sensitive motion judgement features correspond to y_th0 and y_th1. The larger the value, the more texture sensitive motion judgement features are used, and the probability of misjudgment caused by noise increases, resulting in more accurate motion judgement | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_highlight_diff_th | motion highlight diff th | u8.2 | AX_U16 | [\] | [0, 1023\] | [0.0, 255.75\] | 4 | 1.0 | open | 'yuv_3dnr.motion_highlight_diff_th' | For highlight areas, the frame difference judgment threshold for motion sharpening is used. If the frame difference is higher than this threshold, sharpening will occur, and the larger the value, the less effective motion sharpening will be. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_highlight_luma_th | motion highlight luma th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 1 | None | open | 'yuv_3dnr.motion_highlight_luma_th' | For highlight areas, the brightness judgment threshold for motion sharpening is used. Only when the brightness is higher than this threshold will it sharpen. The larger the value, the less effective the motion sharpening will be, corresponding to x_th. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_highlight_luma_slope | motion highlight luma slope | u2.8 | AX_U16 | [\] | [0, 1023\] | [0.0, 3.99609375\] | 256 | 1.0 | open | 'yuv_3dnr.motion_highlight_luma_slope' | For the highlight area, the brightness judgment gain using motion sharpening is used. The larger the value, the faster the transition from motion sharpening in the dark area to brightness, corresponding to the slope. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_highlight_var_th | motion highlight var th | u7 | AX_U8 | [\] | [0, 127\] | [None, None\] | 1 | None | open | 'yuv_3dnr.motion_highlight_var_th' | For highlight areas, the variance judgment threshold for motion sharpening is used. Only when the variance is higher than this threshold will the sharpening be effective. The larger the value, the less effective the motion sharpening will be, corresponding to x_th. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_highlight_var_slope | motion highlight var slope | u2.7 | AX_U16 | [\] | [0, 511\] | [0.0, 3.9921875\] | 128 | 1.0 | open | 'yuv_3dnr.motion_highlight_var_slope' | For the highlight area, the variance judgment gain of motion sharpening is used. The larger the value, the faster the transition from small variance to large variance motion sharpening, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_sharpen_str | motion sharpen str | u3.5 | AX_U8 | [\] | [0, 255\] | [0.0, 7.96875\] | 32 | 1.0 | open | 'yuv_3dnr.motion_sharpen_str' | For highlight areas, motion sharpening intensity is used. The higher the value, the greater the intensity, making it easier to identify as motion. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_y_red_correct_str | motion y red correct str | u3.5 | AX_U8 | [\] | [0, 255\] | [0.0, 7.96875\] | 0 | 0.0 | open | 'yuv_3dnr.motion_y_red_correct_str' | The intensity of Y channel motion correction in the red area, the larger the value, the easier it is to determine as stationary. It does not take effect when the motion_small_sel_ratio is at its maximum. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_uv_red_correct_str | motion uv red correct str | u3.5 | AX_U8 | [\] | [0, 255\] | [0.0, 7.96875\] | 0 | 0.0 | open | 'yuv_3dnr.motion_uv_red_correct_str' | The intensity of UV channel motion correction in the red area, the larger the value, the easier it is to determine as stationary. It does not take effect when the motion_stmall_del_ratio is at its maximum. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_y_blue_correct_str | motion y blue correct str | u3.5 | AX_U8 | [\] | [0, 255\] | [0.0, 7.96875\] | 0 | 0.0 | open | 'yuv_3dnr.motion_y_blue_correct_str' | The intensity of Y channel motion correction in the blue area, the larger the value, the easier it is to determine as stationary. It does not take effect when the motion_stmall_del_ratio is at its maximum | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_uv_blue_correct_str | motion uv blue correct str | u3.5 | AX_U8 | [\] | [0, 255\] | [0.0, 7.96875\] | 0 | 0.0 | open | 'yuv_3dnr.motion_uv_blue_correct_str' | The intensity of UV channel motion correction in the blue area, the larger the value, the easier it is to determine as stationary. It does not take effect when the motion_stmall_del_ratio is at its maximum. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_dec_y_th | motion dec y th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 0 | None | open | 'yuv_3dnr.motion_dec_y_th' | Y motion judgement threshold, corresponding to x_th. The higher the value, the more the whole frame tends to be static. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_dec_y_slope | motion dec y slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 32 | 0.125 | open | 'yuv_3dnr.motion_dec_y_slope' | Y determines the transition speed based on movement and corresponds to the slope. The higher the value, the more inclined the whole frame is towards motion. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_m2s_dec_y_th | motion m2s dec y th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 0 | None | open | 'yuv_3dnr.motion_m2s_dec_y_th' | From dynamic to static region Y, the threshold for motion judgement corresponds to x_th. The higher the value, the more the whole frame tends to be static. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_m2s_dec_y_slope | motion m2s dec y slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 32 | 0.125 | open | 'yuv_3dnr.motion_m2s_dec_y_slope' | From dynamic to static zone Y, determine the transition speed and corresponding slope. The higher the value, the more inclined the whole frame is towards motion. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_hdr_adjust_y_th | motion hdr adjust y th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 0 | None | open | 'yuv_3dnr.motion_hdr_adjust_y_th' | When applied to Y, the larger the value, the easier it is to correct the short frame area to be still. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_dec_uv_th | motion dec uv th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 0 | None | open | 'yuv_3dnr.motion_dec_uv_th' | UV motion judgement threshold, corresponding to x_th. The higher the value, the more the whole frame tends to be static. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_dec_uv_slope | motion dec uv slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 32 | 0.125 | open | 'yuv_3dnr.motion_dec_uv_slope' | UV motion judgement of transition speed, corresponding to slope. The higher the value, the more inclined the whole frame is towards motion. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_adj_uv_th | motion adj uv th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 0 | None | open | 'yuv_3dnr.motion_adj_uv_th' | The UV motion judgement correction threshold will make the UV more inclined to use the current frame, corresponding to x_th, based on normal motion judgement. The higher the value, the more the overall UV tends to be static. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_adj_uv_slope | motion adj uv slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 32 | 0.125 | open | 'yuv_3dnr.motion_adj_uv_slope' | UV motion judgement correction excessive speed will make UV more inclined to use the current frame and corresponding slope based on normal motion judgement. The higher the value, the more the overall UV tends to move. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_hdr_adjust_uv_th | motion hdr adjust uv th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 0 | None | open | 'yuv_3dnr.motion_hdr_adjust_uv_th' | When applied to UV, the larger the value, the easier it is to correct the short frame area to be still.  | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_noise_y_lut | motion noise y lut | u8.2 | AX_U16 | [9\] | [0, 1023\] | [0.0, 255.75\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open | 'yuv_3dnr.motion_noise_y_lut' | Noise correction motion judgement Y channel feature lookup table, with brightness on the x-axis and subtraction method. The larger the value, the stronger the correction, and the easier it is to mistake real motion for stillness. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_hvs_y_lut | motion hvs y lut | u3.5 | AX_U8 | [9\] | [0, 255\] | [0.0, 7.96875\] | [32, 32, ... , 32\] | [1.0, 1.0, ... , 1.0\] | open | 'yuv_3dnr.motion_hvs_y_lut' | Noise correction motion judgement Y channel feature lookup table, with brightness on the x-axis and multiplication method. The smaller the value, the stronger the correction, and the easier it is to mistake real motion for stillness. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_noise_uv_lut | motion noise uv lut | u8.2 | AX_U16 | [9\] | [0, 1023\] | [0.0, 255.75\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open | 'yuv_3dnr.motion_noise_uv_lut' | Noise correction motion judgement UV channel feature lookup table, with brightness on the x-axis and subtraction method. The larger the value, the stronger the correction, and the easier it is to mistake real motion for stillness. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| motion_hvs_uv_lut | motion hvs uv lut | u3.5 | AX_U8 | [9\] | [0, 255\] | [0.0, 7.96875\] | [32, 32, ... , 32\] | [1.0, 1.0, ... , 1.0\] | open | 'yuv_3dnr.motion_hvs_uv_lut' | Noise correction motion judgement UV channel feature lookup table, with brightness on the x-axis and multiplication method. The smaller the value, the stronger the correction, and the easier it is to mistake real motion for stillness. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_cur_w | tweight cur w | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 128 | 1.0 | open | 'yuv_3dnr.tweight_cur_w' | The fusion ratio of current motion judgement and historical motion information, the larger the value, the smaller the historical motion information reference, the faster the convergence speed of dynamic and static, and the higher the probability of misjudging as static. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_pre_limit | tweight pre limit | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 128 | 1.0 | open | 'yuv_3dnr.tweight_pre_limit' | The reference threshold for historical motion information, significant motion information greater than this value will be referenced and integrated into the current motion judgment. The larger the value, the smaller the historical motion information reference, and the faster the convergence speed of motion and stillness, increasing the probability of misjudging as stationary. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_max_ratio | tweight max ratio | u10 | AX_U16 | [\] | [0, 512\] | [None, None\] | 128 | None | open | 'yuv_3dnr.tweight_max_win_sel', 'yuv_3dnr.tweight_max_ratio' | The maximum filtering strength of the time-domain fusion coefficient is greater, and the larger the value, the greater the filtering strength. The larger the proportion of using the current frame, the higher the probability of raindrop noise appearing. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_smooth_ratio | tweight smooth ratio | u10 | AX_U16 | [\] | [0, 512\] | [None, None\] | 128 | None | open | 'yuv_3dnr.tweight_smooth_win_sel', 'yuv_3dnr.tweight_smooth_ratio' | The average filtering strength of the time-domain fusion coefficient is higher, and the larger the value, the greater the average filtering strength. The proportion of using the current frame is smaller, and the probability of raindrop noise appearing is reduced.  | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_sf_sd_y_th | tweight sf sd y th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 1 | None | open | 'yuv_3dnr.tweight_sf_sd_y_th' | Guiding the selection of time-domain fusion coefficients based on regional motion judgement features, the larger the value, the more biased the global result is towards mean filtering, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_sf_sd_y_slope | tweight sf sd y slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 128 | 0.5 | open | 'yuv_3dnr.tweight_sf_sd_y_slope' | Guiding the selection of time-domain fusion coefficients based on regional motion judgement features, the larger the value, the steeper the transition, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_sf_sad_y_th | tweight sf sad y th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 1 | None | open | 'yuv_3dnr.tweight_sf_sad_y_th' | By using detail sensitive motion judgement features to guide the selection of time-domain fusion coefficients, the larger the value, the more the global result tends towards mean filtering, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_sf_sad_y_slope | tweight sf sad y slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 128 | 0.5 | open | 'yuv_3dnr.tweight_sf_sad_y_slope' | Guiding the selection of time-domain fusion coefficients based on detail sensitive motion judgement features, the larger the value, the steeper the transition, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_sf_sign_y_th | tweight sf sign y th | u7 | AX_U8 | [\] | [0, 81\] | [None, None\] | 72 | None | open | 'yuv_3dnr.tweight_sf_sign_y_th' | Guiding the selection of time-domain fusion coefficients based on pixel relative relationships, the larger the value, the more biased the global result is towards mean filtering, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_sf_sign_y_slope | tweight sf sign y slope | u1.7 | AX_U8 | [\] | [0, 255\] | [0.0, 1.9921875\] | 64 | 0.5 | open | 'yuv_3dnr.tweight_sf_sign_y_slope' | Guiding the selection of time-domain fusion coefficients through pixel relative relationships, the larger the value, the steeper the transition, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_str | tweight str | u0.7 | AX_U8 | [\] | [0, 127\] | [0.0, 0.9921875\] | 9 | 0.0703125 | open | 'yuv_3dnr.tweight_str' | The stronger the time-domain filtering strength, the more biased it is towards using the current frame result, and the greater the Y noise in the stationary region. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| ratio_uv_adjust_th | ratio uv adjust th | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 0 | 0.0 | open | 'yuv_3dnr.ratio_uv_adjust_th' | Based on the modified UV motion judgement, the threshold for mapping the UV correction temporal fusion coefficient is determined. The smaller the value, the more inclined it is to use the current frame for global UV, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| ratio_uv_adjust_slope | ratio uv adjust slope | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 0 | 0.0 | open | 'yuv_3dnr.ratio_uv_adjust_slope' | According to the modified UV motion judgement, the transition speed of the mapping UV correction temporal fusion coefficient is determined. The larger the value, the more inclined it is to use the current frame for the global UV, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| ratio_uv_adjust | ratio uv adjust | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 0 | 0.0 | open | 'yuv_3dnr.ratio_uv_adjust' | According to the modified UV motion judgement, the correction strength of the mapping UV correction temporal fusion coefficient is determined. The larger the value, the more inclined it is to use the current frame for the global UV, corresponding to y_th1 | user | ['gain/lux'\] | [12\] | ['linear'\] |
| ratio_uv_limit | ratio uv limit | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 13 | 0.1015625 | open | 'yuv_3dnr.ratio_uv_limit' | The stronger the UV time-domain denoising intensity, the more the current frame is used, and the greater the UV noise in the stationary area. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| m2s_motion_cur | m2s motion cur | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 48 | 0.375 | open | 'yuv_3dnr.m2s_motion_cur' | In the determination of moving to stationary areas, the current frame needs to be stationary and the historical frames need to be moving. This corresponds to the threshold for the current frame being stationary, and the larger the value, the more likely it is to be ineffective. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| m2s_tweight_ref | m2s tweight ref | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 48 | 0.375 | open | 'yuv_3dnr.m2s_tweight_ref' | In the determination of moving to stationary areas, the current frame needs to be stationary, and the historical frames need to be motion. Here, the corresponding historical frames are the threshold for motion, and the smaller the value, the more likely it is not to take effect. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| m2s_var_th | m2s var th | u7 | AX_U8 | [\] | [0, 127\] | [None, None\] | 1 | None | open | 'yuv_3dnr.m2s_var_th' | The variance threshold of the texture part in the transition area from motion to stillness is located, and the larger the value, the fewer textures are located, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| m2s_var_slope | m2s var slope | u1.11 | AX_U16 | [2\] | [0, 4095\] | [0.0, 1.99951171875\] | [0, 0\] | [0.0, 0.0\] | open | 'yuv_3dnr.m2s_var_slope' | Locate the texture overlap speed of the texture part in the transition zone from motion to stillness, with smaller values indicating less overlap, corresponding to the slope, [0\]: gau, [1\]: lap | user | ['gain/lux'\] | [12\] | ['linear'\] |
| m2s_blend_ratio | m2s blend ratio | u1.7 | AX_U8 | [2\] | [0, 128\] | [0.0, 1.0\] | [0, 0\] | [0.0, 0.0\] | open | 'yuv_3dnr.m2s_blend_ratio' | Locate the texture overlap ratio of the texture part in the transition area from motion to stillness, with smaller values indicating less overlap, corresponding to y_th1, [0\]: gau, [1\]: lap | user | ['gain/lux'\] | [12\] | ['linear'\] |
| tweight_static_lut | tweight static lut | u1.7 | AX_U8 | [17\] | [0, 128\] | [0.0, 1.0\] | [6, 8, 9, 12, 15, 20, 26, 29, 33, 37, 41, 44, 46, 49, 52, 58, 64\] | [0.046875, 0.0625, 0.0703125, 0.09375, 0.1171875, 0.15625, 0.203125, 0.2265625, 0.2578125, 0.2890625, 0.3203125, 0.34375, 0.359375, 0.3828125, 0.40625, 0.453125, 0.5\] | open | 'yuv_3dnr.tweight_static_lut' | Time domain fusion speed curve, the horizontal axis represents the degree to which the history has been stationary, the smaller the value, the longer the stationary time, the vertical axis represents the current mapped time domain fusion coefficient, and the smaller the value, the more reference frames are used. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_ref_weight_th | sf gau y ref weight th | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 13 | 0.1015625 | open | 'yuv_3dnr.sf_gau_y_ref_weight_th' | When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_ref_weight_slope | sf gau y ref weight slope | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 600 | 2.34375 | open | 'yuv_3dnr.sf_gau_y_ref_weight_slope' | When providing the next reference frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_ref_ratio_limit | sf gau y ref ratio limit | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 0 | 0.0 | open | 'yuv_3dnr.sf_gau_y_ref_ratio_limit' | When providing the next reference frame, the larger the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area and the smaller the noise. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_cur_weight_th | sf gau y cur weight th | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 13 | 0.1015625 | open | 'yuv_3dnr.sf_gau_y_cur_weight_th' | When provided to the output frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_cur_weight_slope | sf gau y cur weight slope | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 600 | 2.34375 | open | 'yuv_3dnr.sf_gau_y_cur_weight_slope' | When providing the output frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_cur_ratio_limit | sf gau y cur ratio limit | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 128 | 1.0 | open | 'yuv_3dnr.sf_gau_y_cur_ratio_limit' | When providing the output frame, the larger the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area and the smaller the noise. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_edge_th | sf gau y edge th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 1 | None | open | 'yuv_3dnr.sf_gau_y_edge_th' | The threshold for low-frequency edge judgment, the larger the value, the fewer areas are judged as edges. Corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_edge_slope | sf gau y edge slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 32 | 0.125 | open | 'yuv_3dnr.sf_gau_y_edge_slope' | Low frequency edge judgment transition speed, the smaller the value, the fewer areas are judged as edges. Corresponding Slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_detail_th | sf gau y detail th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 1 | None | open | 'yuv_3dnr.sf_gau_y_detail_th' | The threshold for low-frequency texture judgment is that the larger the value, the fewer areas are judged as texture. Corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_detail_slope | sf gau y detail slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 256 | 1.0 | open | 'yuv_3dnr.sf_gau_y_detail_slope' | Low frequency texture determines transition speed, and the smaller the value, the fewer areas are judged as texture. Corresponding Slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_sp_win_sel | sf gau y sp win sel | u2 | AX_U8 | [3\] | [0, 2\] | [None, None\] | [1, 1, 1\] | None | open | 'yuv_3dnr.sf_gau_y_sp_weight' | The size of the low-frequency denoising window is larger, and the larger the value, the larger the window filter is used, 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_sp_sigma | sf gau y sp sigma | u9 | AX_U16 | [3\] | [1, 320\] | [None, None\] | [64, 64, 64\] | None | open | 'yuv_3dnr.sf_gau_y_sp_weight' | The low-frequency denoising spatial filtering coefficient tends to use all pixels within the window as the value increases, and tends to use pixels in the center area of the window as the value decreases., 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_thres | sf gau y thres | u8.2 | AX_U16 | [3\] | [0, 1023\] | [0.0, 255.75\] | [0, 0, 0\] | [0.0, 0.0, 0.0\] | open | 'yuv_3dnr.sf_gau_y_thres' | Low frequency denoising threshold. The larger the value, the greater the denoising intensity, corresponding to x_th, 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_slope | sf gau y slope | u2.8 | AX_U16 | [3\] | [0, 1023\] | [0.0, 3.99609375\] | [32, 32, 32\] | [0.125, 0.125, 0.125\] | open | 'yuv_3dnr.sf_gau_y_slope' | The degree of excessive edge preservation in low-frequency denoising. The larger the value, the smaller the denoising intensity, corresponding to the slope, 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_sad_alpha | sf gau y sad alpha | u1.5 | AX_U8 | [3\] | [0, 32\] | [0.0, 1.0\] | [32, 32, 32\] | [1.0, 1.0, 1.0\] | open | 'yuv_3dnr.sf_gau_y_sad_alpha' | The fusion weight of bilateral and NLM filtering tends to use NLM as the weight increases, 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_hdr_adjust_th | sf gau y hdr adjust th | u8.2 | AX_U16 | [\] | [0, 1023\] | [0.0, 255.75\] | 0 | 0.0 | open | 'yuv_3dnr.sf_gau_y_hdr_adjust_th' | Short frame current frame brightness low-frequency noise reduction intensity correction, the larger the value, the greater the low-frequency noise reduction intensity of the short frame | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_ori_str | sf gau y ori str | u8.4 | AX_U16 | [\] | [0, 4095\] | [0.0, 255.9375\] | 16 | 1.0 | open | 'yuv_3dnr.sf_gau_y_ori_str' | The low-frequency denoising result overlaps the original noise ratio, and the larger the value, the closer it is to the original frame. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_thres_bias_lut | sf gau y thres bias lut | s6.2 | AX_S16 | [9\] | [-256, 255\] | [-64.0, 63.75\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open | 'yuv_3dnr.sf_gau_y_thres_bias_lut' | Adjust the low-frequency noise reduction threshold based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the stronger the noise reduction intensity. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_y_slope_gain_lut | sf gau y slope gain lut | u3.5 | AX_U8 | [9\] | [0, 255\] | [0.0, 7.96875\] | [32, 32, ... , 32\] | [1.0, 1.0, ... , 1.0\] | open | 'yuv_3dnr.sf_gau_y_slope_gain_lut' | Adjust the transition degree of denoising and edge preservation based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the smaller the denoising intensity. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_ref_weight_th | sf lap y ref weight th | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 13 | 0.1015625 | open | 'yuv_3dnr.sf_lap_y_ref_weight_th' | When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_ref_weight_slope | sf lap y ref weight slope | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 600 | 2.34375 | open | 'yuv_3dnr.sf_lap_y_ref_weight_slope' | When providing the next reference frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_ref_ratio_limit | sf lap y ref ratio limit | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 0 | 0.0 | open | 'yuv_3dnr.sf_lap_y_ref_ratio_limit' | When providing the next reference frame, the higher the ratio of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area and the smaller the noise. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_cur_weight_th | sf lap y cur weight th | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 13 | 0.1015625 | open | 'yuv_3dnr.sf_lap_y_cur_weight_th' | When provided to the output frame, the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image is smaller, and the smaller the threshold, the more inclined it is to use the denoising result globally, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_cur_weight_slope | sf lap y cur weight slope | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 600 | 2.34375 | open | 'yuv_3dnr.sf_lap_y_cur_weight_slope' | When providing the output frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_cur_ratio_limit | sf lap y cur ratio limit | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 128 | 1.0 | open | 'yuv_3dnr.sf_lap_y_cur_ratio_limit' | When providing the output frame, the higher the ratio of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area and the smaller the noise. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_edge_th | sf lap y edge th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 1 | None | open | 'yuv_3dnr.sf_lap_y_edge_th' | The threshold for high-frequency edge judgment is that the larger the value, the fewer areas are judged as edges. Corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_edge_slope | sf lap y edge slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 32 | 0.125 | open | 'yuv_3dnr.sf_lap_y_edge_slope' | High frequency edge judgment transition speed, the smaller the value, the fewer areas are judged as edges. Corresponding Slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_detail_th | sf lap y detail th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 1 | None | open | 'yuv_3dnr.sf_lap_y_detail_th' | High frequency texture judgment threshold, the larger the value, the fewer areas are judged as texture. Corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_detail_slope | sf lap y detail slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 256 | 1.0 | open | 'yuv_3dnr.sf_lap_y_detail_slope' | High frequency texture determines transition speed, and the smaller the value, the fewer areas are judged as texture. Corresponding Slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_sp_win_sel | sf lap y sp win sel | u1 | AX_U8 | [3\] | [0, 1\] | [None, None\] | [1, 1, 1\] | None | open | 'yuv_3dnr.sf_lap_y_sp_weight' | The size of the high-frequency edge preservation denoising window is larger, and the larger the value, the larger the window filtering is used. 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_sp_sigma | sf lap y sp sigma | u9 | AX_U16 | [3\] | [1, 320\] | [None, None\] | [64, 64, 64\] | None | open | 'yuv_3dnr.sf_lap_y_sp_weight' | The high-frequency edge preserving denoising spatial filtering coefficient tends to use all pixels within the window as the value increases, and tends to use pixels in the center area of the window as the value decreases., 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_thres | sf lap y thres | u8.2 | AX_U16 | [3\] | [0, 1023\] | [0.0, 255.75\] | [0, 0, 0\] | [0.0, 0.0, 0.0\] | open | 'yuv_3dnr.sf_lap_y_thres' | High frequency edge preservation denoising threshold. The larger the value, the greater the denoising intensity, corresponding to x_th, 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_slope | sf lap y slope | u2.8 | AX_U16 | [3\] | [0, 1023\] | [0.0, 3.99609375\] | [32, 32, 32\] | [0.125, 0.125, 0.125\] | open | 'yuv_3dnr.sf_lap_y_slope' | High frequency edge preservation denoising and excessive edge preservation. The larger the value, the smaller the denoising intensity, corresponding to the slope, 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_sad_alpha | sf lap y sad alpha | u1.5 | AX_U8 | [3\] | [0, 32\] | [0.0, 1.0\] | [32, 32, 32\] | [1.0, 1.0, 1.0\] | open | 'yuv_3dnr.sf_lap_y_sad_alpha' | The fusion weight of high-frequency edge preservation denoising bilateral and NLM filtering tends to use NLM as the weight increases, 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_gauss_win_sel | sf lap y gauss win sel | u2 | AX_U8 | [\] | [0, 2\] | [None, None\] | 2 | None | open | 'yuv_3dnr.sf_lap_y_gauss_weight' | The window size of high-frequency non edge preserving denoising filter is larger, and the larger the value, the more pixels are used for denoising, resulting in a blurred effect, 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_gauss_sigma | sf lap y gauss sigma | u9 | AX_U16 | [\] | [1, 320\] | [None, None\] | 64 | None | open | 'yuv_3dnr.sf_lap_y_gauss_weight' | High frequency non edge preserving denoising filter spatial filtering coefficient, the larger the value, the more inclined it is to use all pixels within the window, and the smaller the value, the more inclined it is to use pixels in the center area of the window, 0:flat, 1:detail, 2:edge | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_gauss_ratio | sf lap y gauss ratio | u1.5 | AX_U8 | [3\] | [0, 32\] | [0.0, 1.0\] | [32, 32, 32\] | [1.0, 1.0, 1.0\] | open | 'yuv_3dnr.sf_lap_y_gauss_ratio' | The fusion coefficient of high-frequency non edge preserving denoising filtering and edge preserving filtering, the larger the value, the more the non edge preserving filtering result is used. 0:flat, 1:detail, 2:edge. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_hdr_adjust_th | sf lap y hdr adjust th | u8.2 | AX_U16 | [\] | [0, 1023\] | [0.0, 255.75\] | 0 | 0.0 | open | 'yuv_3dnr.sf_lap_y_hdr_adjust_th' | Short frame current frame brightness high-frequency noise reduction intensity correction, the larger the value, the greater the high-frequency noise reduction intensity of the short frame | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_ori_str | sf lap y ori str | u8.4 | AX_U16 | [\] | [0, 4095\] | [0.0, 255.9375\] | 0 | 0.0 | open | 'yuv_3dnr.sf_lap_y_ori_str' | The high-frequency denoising result overlaps the original noise ratio, and the larger the value, the closer it is to the original frame. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_thres_bias_lut | sf lap y thres bias lut | s6.2 | AX_S16 | [9\] | [-256, 255\] | [-64.0, 63.75\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open | 'yuv_3dnr.sf_lap_y_thres_bias_lut' | Adjust the high-frequency edge preservation and noise reduction threshold based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the stronger the noise reduction intensity. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_y_slope_gain_lut | sf lap y slope gain lut | u3.5 | AX_U8 | [9\] | [0, 255\] | [0.0, 7.96875\] | [32, 32, ... , 32\] | [1.0, 1.0, ... , 1.0\] | open | 'yuv_3dnr.sf_lap_y_slope_gain_lut' | Adjust the transition degree of denoising and edge preservation based on brightness. The horizontal axis represents brightness, and the larger the vertical axis value, the smaller the denoising intensity. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_ref_weight_th | sf gau uv ref weight th | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 13 | 0.1015625 | open | 'yuv_3dnr.sf_gau_uv_ref_weight_th' | When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_ref_weight_slope | sf gau uv ref weight slope | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 600 | 2.34375 | open | 'yuv_3dnr.sf_gau_uv_ref_weight_slope' | When providing the next reference frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_ref_ratio_limit | sf gau uv ref ratio limit | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 0 | 0.0 | open | 'yuv_3dnr.sf_gau_uv_ref_ratio_limit' | When providing the next reference frame, the larger the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area UV and the smaller the color noise. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_cur_weight_th | sf gau uv cur weight th | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 13 | 0.1015625 | open | 'yuv_3dnr.sf_gau_uv_cur_weight_th' | When provided to the output frame, the lower the threshold of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_cur_weight_slope | sf gau uv cur weight slope | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 600 | 2.34375 | open | 'yuv_3dnr.sf_gau_uv_cur_weight_slope' | When providing the output frame, the transition speed of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_cur_ratio_limit | sf gau uv cur ratio limit | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 128 | 1.0 | open | 'yuv_3dnr.sf_gau_uv_cur_ratio_limit' | When provided to the output frame, the ratio of the time-domain fusion coefficient between the low-frequency denoising result and the denoised image fusion is higher, and the larger the ratio, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area UV and the smaller the color noise. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_th | sf gau uv th | u7.2 | AX_U16 | [\] | [0, 511\] | [0.0, 127.75\] | 0 | 0.0 | open | 'yuv_3dnr.sf_gau_uv_th' | The threshold for reducing saturation based on its original size, the smaller the value, the more likely it is to be globally ineffective, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_slope | sf gau uv slope | u2.7 | AX_U16 | [\] | [0, 511\] | [0.0, 3.9921875\] | 16 | 0.125 | open | 'yuv_3dnr.sf_gau_uv_slope' | Reduce the transition speed of saturation based on its original size. The larger the value, the more likely it is to be globally ineffective, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_limit | sf gau uv limit | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 128 | 1.0 | open | 'yuv_3dnr.sf_gau_uv_limit' | According to the proportion of saturation overlaid color noise, the smaller the value, the more inclined it is to reduce saturation, corresponding to y_th0 | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_hdr_adjust_th | sf gau uv hdr adjust th | u8.2 | AX_U16 | [\] | [0, 1023\] | [0.0, 255.75\] | 0 | 0.0 | open | 'yuv_3dnr.sf_gau_uv_hdr_adjust_th' | Short frame current frame color low-frequency noise reduction intensity correction, the larger the value, the greater the low-frequency noise reduction intensity of the short frame | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_ori_str | sf gau uv ori str | u1.5 | AX_U8 | [\] | [0, 32\] | [0.0, 1.0\] | 0 | 0.0 | open | 'yuv_3dnr.sf_gau_uv_ori_ratio' | The ratio of low-frequency fading noise to aliasing noise, the larger the value, the closer it is to the original input | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_thre_pre_lut | sf gau uv thre pre lut | u8.2 | AX_U16 | [9\] | [0, 1023\] | [0.0, 255.75\] | [4, 4, ... , 4\] | [1.0, 1.0, ... , 1.0\] | open | 'yuv_3dnr.sf_gau_uv_thre_pre_lut' | Low frequency color first stage denoising intensity, horizontal axis brightness, the larger the value, the greater the denoising intensity | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_thre_lut | sf gau uv thre lut | u8.2 | AX_U16 | [9\] | [0, 1023\] | [0.0, 255.75\] | [4, 4, ... , 4\] | [1.0, 1.0, ... , 1.0\] | open | 'yuv_3dnr.sf_gau_uv_thre_lut' | Low frequency color two-stage denoising intensity, horizontal axis brightness, the larger the value, the greater the denoising intensity | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_prot_thre_lut | sf gau uv prot thre lut | u8.2 | AX_U16 | [9\] | [0, 1023\] | [0.0, 255.75\] | [4, 4, ... , 4\] | [1.0, 1.0, ... , 1.0\] | open | 'yuv_3dnr.sf_gau_uv_prot_thre_lut' | Low frequency color denoising protection threshold, based on the diff aliasing color noise before and after denoising, the larger the value, the more likely it is not to aliasing, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_gau_uv_prot_slope_lut | sf gau uv prot slope lut | u2.8 | AX_U16 | [9\] | [0, 1023\] | [0.0, 3.99609375\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open | 'yuv_3dnr.sf_gau_uv_prot_slope_lut' | Low frequency color denoising protection transition speed, based on the diff of color noise before and after denoising, the larger the value, the more likely it is to be overlapped, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_uv_ref_weight_th | sf lap uv ref weight th | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 13 | 0.1015625 | open | 'yuv_3dnr.sf_lap_uv_ref_weight_th' | When providing the next reference frame, the lower the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_uv_ref_weight_slope | sf lap uv ref weight slope | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 600 | 2.34375 | open | 'yuv_3dnr.sf_lap_uv_ref_weight_slope' | When provided to the next reference frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_uv_ref_ratio_limit | sf lap uv ref ratio limit | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 0 | 0.0 | open | 'yuv_3dnr.sf_lap_uv_ref_ratio_limit' | When provided to the next reference frame, the higher the ratio of the temporal fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the still area UV and the smaller the color noise. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_uv_cur_weight_th | sf lap uv cur weight th | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 13 | 0.1015625 | open | 'yuv_3dnr.sf_lap_uv_cur_weight_th' | When provided to the output frame, the lower the threshold of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image, the more inclined it is to use the denoising result globally, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_uv_cur_weight_slope | sf lap uv cur weight slope | u4.8 | AX_U16 | [\] | [0, 4095\] | [0.0, 15.99609375\] | 600 | 2.34375 | open | 'yuv_3dnr.sf_lap_uv_cur_weight_slope' | When provided to the output frame, the transition speed of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is faster, and the larger the transition speed, the more inclined it is to use the denoising result globally, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_uv_cur_ratio_limit | sf lap uv cur ratio limit | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 128 | 1.0 | open | 'yuv_3dnr.sf_lap_uv_cur_ratio_limit' | When provided to the output frame, the ratio of the time-domain fusion coefficient between the high-frequency denoising result and the denoised image fusion is higher, and the larger the ratio, the more inclined it is to use the denoising result globally, corresponding to y_th0. The larger the value, the lower the clarity of the stationary area UV and the smaller the color noise. | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_uv_thre | sf lap uv thre | u8.2 | AX_U16 | [\] | [0, 1023\] | [0.0, 255.75\] | 4 | 1.0 | open | 'yuv_3dnr.sf_lap_uv_thre' | High frequency denoising threshold, the larger the value, the stronger the denoising intensity, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_uv_slope | sf lap uv slope | u2.8 | AX_U16 | [\] | [0, 1023\] | [0.0, 3.99609375\] | 0 | 0.0 | open | 'yuv_3dnr.sf_lap_uv_slope' | High frequency denoising transition speed, the larger the value, the weaker the denoising intensity, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_uv_hdr_adjust_gain | sf lap uv hdr adjust gain | u1.5 | AX_U8 | [\] | [0, 32\] | [0.0, 1.0\] | 32 | 1.0 | open | 'yuv_3dnr.sf_lap_uv_hdr_adjust_gain' | Short frame current frame color high-frequency noise reduction intensity correction, the larger the value, the greater the high-frequency noise reduction intensity of the short frame | user | ['gain/lux'\] | [12\] | ['linear'\] |
| sf_lap_uv_ori_str | sf lap uv ori str | u1.5 | AX_U8 | [\] | [0, 32\] | [0.0, 1.0\] | 0 | 0.0 | open | 'yuv_3dnr.sf_lap_uv_ori_ratio' | High frequency color fading noise and aliasing noise, the larger the value, the closer it is to the original image | user | ['gain/lux'\] | [12\] | ['linear'\] |
| gf_win_r | gf win r | u3 | AX_U8 | [\] | [0, 5\] | [None, None\] | 5 | None | open | 'ifa.guided_win_r' | The size of the window for guiding filtering to remove color noise, the larger the value, the greater the intensity of color noise removal | user | ['gain/lux'\] | [12\] | ['linear'\] |
| gf_eps | gf eps | u8 | AX_U8 | [\] | [1, 255\] | [None, None\] | 5 | None | open | 'ifa.guided_epsilon' | The bias size for guiding filtering to remove color noise, the larger the value, the greater the intensity of color noise removal | user | ['gain/lux'\] | [12\] | ['linear'\] |
| gf_uv_diff_th | gf uv diff th | u8 | AX_U8 | [\] | [0, 255\] | [None, None\] | 5 | None | open | 'yuv_3dnr.gf_uv_diff_th' | The diff protection strength of guided filtering for color noise reduction results is weaker as the value increases, and closer to non guided filtering as the value decreases | user | ['gain/lux'\] | [12\] | ['linear'\] |
| gf_uv_sat_th | gf uv sat th | u7 | AX_U8 | [\] | [0, 127\] | [None, None\] | 5 | None | open | 'yuv_3dnr.gf_uv_sat_th' | The results of guided filtering for removing color noise are based on the sat protection strength. The larger the value, the more inclined it is to use the guided filtering result, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| gf_uv_sat_slope | gf uv sat slope | u1.8 | AX_U16 | [\] | [0, 511\] | [0.0, 1.99609375\] | 32 | 0.125 | open | 'yuv_3dnr.gf_uv_sat_slope' | The guided filtering denoising result is based on the transition speed of SAT protection. The larger the value, the more inclined it is to use the original denoising result, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| gf_uv_sat_ratio | gf uv sat ratio | u1.7 | AX_U8 | [2\] | [0, 255\] | [0.0, 1.9921875\] | [0, 0\] | [0.0, 0.0\] | open | 'yuv_3dnr.gf_uv_sat_ratio' | The guided filtering denoising result is based on the fusion ratio between sat and the original denoising result. The larger the value, the more inclined it is to the original denoising result, corresponding to y_th0 and y_th1 | user | ['gain/lux'\] | [12\] | ['linear'\] |
| gf_uv_blend_ratio_th | gf uv blend ratio th | u1.7 | AX_U8 | [\] | [0, 128\] | [0.0, 1.0\] | 13 | 0.1015625 | open | 'yuv_3dnr.gf_uv_blend_ratio_th' | Determine the time-domain fusion coefficient threshold for the fusion guided filtering result of the dynamic and static regions based on the time-domain fusion coefficient. It is hoped that different fusion ratios will be used for the dynamic and static regions, corresponding to x_th | user | ['gain/lux'\] | [12\] | ['linear'\] |
| gf_uv_blend_ratio_slope | gf uv blend ratio slope | s7.7 | AX_S16 | [\] | [-16384, 16383\] | [-128.0, 127.9921875\] | 320 | 2.5 | open | 'yuv_3dnr.gf_uv_blend_ratio_slope' | Determine the transition speed of the fusion guided filtering result for the dynamic and static regions based on the time-domain fusion coefficient. It is hoped that different fusion ratios will be used for the dynamic and static regions, corresponding to the slope | user | ['gain/lux'\] | [12\] | ['linear'\] |
| gf_uv_blend_ratio | gf uv blend ratio | u1.7 | AX_U8 | [2\] | [0, 128\] | [0.0, 1.0\] | [128, 128\] | [1.0, 1.0\] | open | 'yuv_3dnr.gf_uv_blend_ratio' | According to the time-domain fusion coefficient, the proportion of the fusion guided filtering results in the dynamic and static regions is determined. The first value corresponds to the static region, and the second value corresponds to the motion region, corresponding to y_th0 and y_th1. The larger the value, the more inclined it is to use the guided filtering results | user | ['gain/lux'\] | [12\] | ['linear'\] |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |