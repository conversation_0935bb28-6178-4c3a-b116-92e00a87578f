h2. Non Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency ||
| offset_in |  | u8.4 | AX_U16 | [\] |  [0, 4095\] | [0.0, 255.9375\] | 256 | 16.0 | hidden | 'clc.offset_in' | clc offset_in | common |
| offset_out |  | u8.4 | AX_U16 | [\] |  [0, 4095\] | [0.0, 255.9375\] | 256 | 16.0 | hidden | 'clc.offset_out' | clc offset_out | common |
| sat_strength |  | acc_unknown | AX_F32 | [\] |  [\] | [\] | None | None | hidden | 'clc.matrix_list' | from AWB, try to decrease saturation when AWB has low confidence | common |



h2. Tunable Parameters
|| Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Publicity || Target Conf || Description || Dependency || Ref Mode || Ref Group Num || Ref Interp Method ||
| enable | Enable CLC | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'clc.matrix_list' | clc enable | user | None | None | None |
| mode | Mode | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 1 | None | open | 'clc.matrix_list' | clc ctrl mode, 0 for basic (global); 1 for advanced (16 phase) | user | None | None | None |
| ctrl_luma_ratio | Luma Ratio | u1.7 | AX_U8 | [2\] | [0, 128\] | [0.0, 1.0\] | [27, 9\] | [0.2109375, 0.0703125\] | open | 'clc.matrix_list' | clc control luma ratio, sum will be 1.0 | user | None | None | None |
| ccm | CCM | s3.8 | AX_S16 | [3, 2\] | [-2047, 2047\] | [-7.99609375, 7.99609375\] | [[0, 0\], [0, 0\], [0, 0\]\] | [[0.0, 0.0\], [0.0, 0.0\], [0.0, 0.0\]\] | open | 'clc.matrix_list' | clc ccm matrix | user | ['color_temp', 'gain/lux'\] | [12, 5\] | ['linear', 'linear'\] |
| ccm_sat | Sat | s1.6 | AX_S8 | [\] | [-64, 64\] | [-1.0, 1.0\] | 0 | 0.0 | open | 'clc.matrix_list' | clc global saturation adjust | user | ['color_temp', 'gain/lux'\] | [12, 5\] | ['linear', 'linear'\] |
| ccm_hue | Hue | s5.6 | AX_S16 | [\] | [-1920, 1920\] | [-30.0, 30.0\] | 0 | 0.0 | open | 'clc.matrix_list' | clc global hue adjust | user | ['color_temp', 'gain/lux'\] | [12, 5\] | ['linear', 'linear'\] |
| ccm_ctrl_level | clc ccm ctrl level | u1.8 | AX_U16 | [\] | [0, 256\] | [0.0, 1.0\] | 256 | 1.0 | open | 'clc.matrix_list' | clc global adjust ctrl level | user | ['color_temp', 'gain/lux'\] | [12, 5\] | ['linear', 'linear'\] |
| xcm_sats | clc xcm sats | s1.6 | AX_S8 | [16\] | [-32, 32\] | [-0.5, 0.5\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open | 'clc.matrix_list' | clc local saturation adjust | user | ['color_temp', 'gain/lux'\] | [12, 5\] | ['linear', 'linear'\] |
| xcm_hues | clc xcm hues | s5.6 | AX_S16 | [16\] | [-640, 640\] | [-10.0, 10.0\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open | 'clc.matrix_list' | clc local hue adjust | user | ['color_temp', 'gain/lux'\] | [12, 5\] | ['linear', 'linear'\] |
| xcm_ctrl_levels | clc xcm ctrl levels | u1.8 | AX_U16 | [16\] | [0, 256\] | [0.0, 1.0\] | [256, 256, ... , 256\] | [1.0, 1.0, ... , 1.0\] | open | 'clc.matrix_list' | clc local adjust ctrl level | user | ['color_temp', 'gain/lux'\] | [12, 5\] | ['linear', 'linear'\] |
| sat_mit_enable | sat_mit_enable | u1 | AX_U8 | [\] | [0, 1\] | [None, None\] | 0 | None | open | 'clc.sat_mit_enable' | clc sat_mit_enable | user | None | None | None |
| sat_mit_ratio_lower_lut | sat_mit_ratio_lower_lut | u1.7 | AX_U8 | [8\] | [0, 128\] | [0.0, 1.0\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open | 'clc.sat_mit_ratio_lower_lut' | clc sat_mit_ratio_lower_lut | user | None | None | None |
| sat_mit_ratio_upper_lut | sat_mit_ratio_upper_lut | u1.7 | AX_U8 | [8\] | [0, 128\] | [0.0, 1.0\] | [0, 0, ... , 0\] | [0.0, 0.0, ... , 0.0\] | open | 'clc.sat_mit_ratio_upper_lut' | clc sat_mit_ratio_upper_lut | user | None | None | None |
| sat_mit_lower_cp | sat_mit_lower_ctrl_point | s8 | AX_S16 | [6\] | [-255, 255\] | [None, None\] | [0, 0, 0, 0, 0, 0\] | None | open | 'clc.sat_mit_lower_lut_x', 'clc.sat_mit_lower_lut_y', 'clc.sat_mit_lower_lut_slope' | clc sat_mit_lower's control point with [x0, y0, x1, y1, x2, y2\], [x2, y2\] is the dividing point from y=x | user | None | None | None |
| sat_mit_upper_cp | sat_mit_upper_ctrl_point | u9 | AX_U16 | [6\] | [0, 511\] | [None, None\] | [0, 0, 0, 0, 0, 0\] | None | open | 'clc.sat_mit_upper_lut_x', 'clc.sat_mit_upper_lut_y', 'clc.sat_mit_upper_lut_slope' | clc sat_mit_upper's control point with [x0, y0, x1, y1, x2, y2\], [x0, y0\] is the dividing point from y=x, with y0, y1, y2 <= 255 | user | None | None | None |



h2. User Defined Structs
|| Struct Name || Param Name || Display || Acc || Type || Size || Range(Int) || Range(Float) || Default Value(Int) || Default Value(Float) || Description ||
| None | | | | | | | | | | |