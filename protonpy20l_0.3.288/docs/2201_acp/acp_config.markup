h2. Conf list
h3. blc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| det_window | u14 | [4\] | - |  | bl_det window, in [y, x, height, width\] order |  |
| active_region | u14 | [4\] | - |  | bl_det active region area, in [y, x, height, width\] order |  |
| channel_id | u1 | [\] | - |  | blc channel_id, 0: SDR/HDR_Main, 1: HDR_Sub |  |
| offset_out | u8.6 | [\] | - |  | blc offset_out |  |
| sensor_bl | u8.10 | [\] | - |  | blc sensor black level |  |
| dither_seed_enable | u1 | [\] | - |  | blc dither_seed_enable |  |

h3. dpc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| offset_in | u8.6 | [\] | - |  | dpc offset_in |  |
| offset_out | u8.6 | [\] | - |  | dpc offset_out |  |
| shot_noise_coefs | s0.31 | [4, 2\] | - |  | dpc shot_noise_coeffs |  |
| read_noise_coefs | s0.31 | [4, 3\] | - |  | dpc read_noise_coeffs |  |
| partition_info | acc_unknown | [\] | - |  | dpc partition_info |  |
| wb_gain | u4.8 | [4\] | - |  | dpc wb_gain |  |
| again | u22.10 | [\] | - |  | dpc again |  |

h3. hdr
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| use_isp_hdr | u1 | [\] | - |  | hdr use_isp_hdr |  |
| offset_in | u8.6 | [2\] | - |  | hdr offset_in |  |
| offset_out | u14.6 | [\] | - |  | hdr offset_out |  |
| wb_gain | u4.12 | [4\] | - |  | hdr wb_gain |  |
| exposure_ratio | u9.8 | [\] | - |  | hdr exposure_ratio |  |
| internal_lf_indicator | u1 | [\] | - |  | hdr internal_lf_indicator |  |
| a_gain | u22.10 | [\] | - |  | hdr again |  |
| shot_noise_coef | s0.31 | [4, 2\] | - |  | hdr shot_noise_coef |  |
| read_noise_coef | s0.31 | [4, 3\] | - |  | hdr read_noise_coef |  |

h3. ainr
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| hcg_mode | u1 | [\] | - |  | ainr hcg_mode, 0: lcg, 1: hcg |  |
| hdr_ratio | u9.8 | [\] | - |  | ainr hdr_ratio |  |
| hdrc_input_max_value | u14.6 | [\] | - |  | hdrc input_max_value |  |
| hdrc_output_max_value | u10.6 | [\] | - |  | hdrc output_max_value |  |
| hdrc_offset_in | u14.6 | [\] | - |  | hdrc offset_in |  |
| hdrc_offset_out | u10.6 | [\] | - |  | hdrc offset_out |  |
| hdrc_dither_enable | u1 | [\] | - |  | hdrc dither_enable |  |
| hdrc_dither_seed_enable | u1 | [\] | - |  | hdrc dither_seed_enabl |  |
| hidrc_offset_in | u10.6 | [\] | - |  | hidrc offset_in |  |
| hidrc_offset_out | u14.6 | [\] | - |  | hidrc offset_out |  |
| hidrc_input_max_value | u10.6 | [\] | - |  | hidrc input_max_value |  |
| hidrc_output_max_value | u14.6 | [\] | - |  | hidrc output_max_value |  |

h3. rawnr
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| offset_in | u14.6 | [\] | - |  | rawnr offset_in |  |
| offset_out | u14.6 | [\] | - |  | rawnr offset_out |  |
| a_gain | u8.8 | [\] | - |  | rawnr again |  |
| hdr_ratio | u9.8 | [\] | - |  | rawnr hdr_ratio |  |
| shot_noise_coeffs | s0.31 | [4, 2\] | - |  | rawnr shot_noise_coef |  |
| read_noise_coeffs | s0.31 | [4, 3\] | - |  | rawnr read_noise_coef |  |

h3. lsc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| offset_in | u14.6 | [\] | - |  | lsc offset_in |  |
| offset_out | u14.6 | [\] | - |  | lsc offset_out |  |
| partition_info | acc_unknown | [\] | - |  | lsc par_info |  |

h3. wbc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| d_gain | u10.8 | [\] | - |  | wbc d_gain |  |
| wbc_gain | u4.8 | [4\] | - |  | wbc wbc_gain |  |
| clip_level | u14.6 | [\] | - |  | wbc clip_level |  |
| offset_in | u14.6 | [\] | - |  | wbc offset_in |  |
| offset_out | u14.6 | [\] | - |  | wbc offset_out |  |

h3. rltm
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| offset_in | u14.6 | [\] | - |  | rltm offset_in |  |
| offset_out | u8.4 | [\] | - |  | rltm offset_out |  |
| hdr_ratio | u9.8 | [\] | - |  | rltm hdr_ratio |  |
| input_max_value | u14.6 | [\] | - |  | rltm input_max_value |  |
| detail_low_reset_coeffab | u1 | [\] | - |  | rltm detail_low_reset_coeffab |  |
| dither_seed_enable | u1 | [\] | - |  | rltm dither_seed_enable |  |
| pic_h_w | u15 | [2\] | - |  | rltm pic_h_w |  |
| partition_info | acc_unknown | [\] | - |  | rltm partition_info |  |
| ifa_partition_edge_width | u16 | [\] | - |  | rltm ifa partition edge width |  |
| ifa_naked_partition_width | u16 | [\] | - |  | rltm ifa naked partition width |  |

h3. depurple
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| offset_in | u8.4 | [\] | - |  | depurple offset_in |  |
| partition_info | acc_unknown | [\] | - |  | depurple partition_info |  |

h3. dem
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| partition_info | acc_unknown | [\] | - |  | dem partition_info |  |
| init_enable | u1 | [\] | - |  | dem init_enable |  |
| offset_in | u8.4 | [\] | - |  | dem offset_in |  |
| offset_out | u8.4 | [\] | - |  | dem offset_out |  |
| wb_gain | u4.8 | [4\] | - |  | dem wb_gain |  |

h3. clc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| offset_in | u8.4 | [\] | - |  | clc offset_out |  |
| offset_out | u8.4 | [\] | - |  | clc offset_out |  |
| sat_strength | acc_unknown | [\] | - |  | clc sat_strength, from awb algorithm |  |

h3. gam
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| dither_seed_enable | u1 | [\] | - |  | gamma dither_seed_enable |  |
| offset_in | u8.4 | [\] | - |  | gamma offset_in |  |
| offset_out | u8.2 | [\] | - |  | gamma offset_out |  |

h3. dehaze
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| init_enable | u1 | [\] | - |  | dehaze init_enable |  |
| offset_in | u8.2 | [\] | - |  | dehaze offset_in |  |
| offset_out | u8.2 | [\] | - |  | dehaze offset_out |  |
| dehaze_partition_info | acc_unknown | [\] | - |  | dehaze dehaze_partition_info |  |
| ifa_partition_edge_width | u16 | [\] | - |  | dehaze ifa partition edge width |  |
| ifa_naked_partition_width | u16 | [\] | - |  | dehaze ifa naked partition width |  |

h3. csc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| csc_mode | u2 | [\] | - |  | csc csc_mode |  |
| offset_in | s8.2 | [\] | - |  | csc offset_in |  |
| offset_out | s8.2 | [\] | - |  | csc offset_out |  |

h3. yuv3dnr
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| debug_mode | u4 | [\] | - |  | yuv3dnr debug_mode |  |
| debug_gain | u3 | [\] | - |  | yuv3dnr debug_gain |  |
| init_ref | u1 | [\] | - |  | yuv3dnr init_enable |  |
| init_tweight | u1 | [\] | - |  | yuv3dnr init_tweight |  |
| init_coef_ab | u1 | [\] | - |  | yuv3dnr init_coef_ab |  |
| dither_seed_enable | u1 | [\] | - |  | yuv3dnr dither_seed_enable |  |
| yuv3dnr_partition_info | acc_unknown | [\] | - |  | yuv3dnr yuv3dnr_partition_info |  |
| ifa_partition_edge_width | u16 | [\] | - |  | yuv3dnr ifa partition edge width |  |
| ifa_naked_partition_width | u16 | [\] | - |  | yuv3dnr ifa naked partition width |  |

h3. sharpen
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| shp_debug_mode | u5 | [\] | - |  | sharpen shp_debug_mode |  |
| disable_for_prev_debug | u1 | [\] | - |  | sharpen disable_for_prev_debug |  |

h3. y2dnr
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| debug_mode | u4 | [\] | - |  | y2dnr debug_mode |  |
| debug_gain | u3 | [\] | - |  | y2dnr debug_gain |  |
| pre_debug_enable | u1 | [\] | - |  | y2dnr pre_debug_enable |  |
| dither_seed_enable | u1 | [\] | - |  | y2dnr dither_seed_enable |  |

h3. ycproc
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| ycrt_reset_uv_for_debug | u1 | [\] | - |  | ycproc shp_debug_mode |  |

h3. aaas
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| aes0_is_transposed | u1 | [\] | - |  | is aes in ITP |  |
| aes0_hist_wl_gain | u1.8 | [\] | - |  | white level used for compute histogram maximum compensation gain |  |
| aes0_dgain | u10.8 | [4\] | - |  | aes dgain for statics |  |
| aes0_offset_in | u14.6 | [\] | - |  | aes offset in |  |
| aes0_partition_info | acc_unknown | [\] | - |  | aes partition information |  |
| aes1_is_transposed | u1 | [\] | - |  | is aes in ITP |  |
| aes1_hist_wl_gain | u1.8 | [\] | - |  | white level used for compute histogram maximum compensation gain |  |
| aes1_dgain | u10.8 | [4\] | - |  | aes dgain for statics |  |
| aes1_offset_in | u14.6 | [\] | - |  | aes offset in |  |
| aes1_partition_info | acc_unknown | [\] | - |  | aes partition information |  |
| awbs_is_transposed | u1 | [\] | - |  | is awbs in ITP |  |
| awbs_dgain | u10.8 | [4\] | - |  | awbs dgain for statics |  |
| awbs_offset_in | u14.6 | [\] | - |  | awbs offset in |  |
| awbs_partition_info | acc_unknown | [\] | - |  | awbs partition information |  |
| afs_is_transposed | u1 | [\] | - |  | is afs in ITP |  |
| afs_wb_gain | u4.8 | [4\] | - |  | afs wbgain |  |
| afs_drc_mode | u8 | [\] | - |  | afs drc mode: 0: disable, 1: 8.4 -> 8.4(do nothing), 2: 8.4 -> 8.4 (gam), 3: 8.6 -> 8.4(MSB), 4: 8.6 -> 8.4(gam), 5: 14.6 -> 8.4(MSB) 6: 14.6 -> 8.4(gam) |  |
| afs_dgain | u10.8 | [4\] | - |  | afs dgain for statics |  |
| afs_offset_in | u14.6 | [\] | - |  | afs offset in |  |
| afs_partition_info | acc_unknown | [\] | - |  | afs partition information |  |

h3. eisstat
|| Conf Name || Acc || Size || Partition || Constraint || Description || Usage ||
| offset_in | u8.2 | [\] | - |  | eisstat offset_in |  |
| partition_info | acc_unknown | [\] | - |  | eisstat partition_info |  |

