{"context": {"AN_ID": {"size": [], "acc": [0, 16], "comment": "vpp is 0x2294", "type": "AX_U16"}, "shp_ud_freq": {"size": [], "type": "AX_F64", "comment": "ud_freq for motion gain lut", "default": 1.0}, "shp_dir_freq": {"size": [], "type": "AX_F64", "comment": "dir_freq for motion gain lut", "default": 1.0}, "input_0_size": {"acc": [0, 13], "size": [2], "type": "AX_U16", "comment": "input 0 size", "default": [1080, 1920]}, "input_1_size": {"acc": [0, 13], "size": [2], "type": "AX_U16", "comment": "input 0 size", "default": [1080, 1920]}, "input_2_size": {"acc": [0, 13], "size": [2], "type": "AX_U16", "comment": "input 0 size", "default": [1080, 1920]}, "input_offset_x": {"acc": [1, 13, 10], "size": [3], "type": "AX_S32", "comment": "input offset x", "default": [512, 512, 512]}, "output_stride": {"acc": [0, 13], "size": [6], "type": "AX_U16", "comment": "input 0 size", "default": [1920, 1920, 1920, 1920, 1920, 1920]}, "stream_mux": {"acc": [0, 2], "size": [6], "type": "AX_U8", "comment": "Choose different src for 6 streams", "default": [0, 0, 0, 0, 0, 0]}, "scale_output_size_0": {"acc": [0, 13], "size": [2], "type": "AX_U16", "comment": "Scalar output size", "default": [1080, 1920]}, "scale_output_size_1": {"acc": [0, 13], "size": [2], "type": "AX_U16", "comment": "Scalar output size", "default": [1080, 1920]}, "scale_output_size_2": {"acc": [0, 13], "size": [2], "type": "AX_U16", "comment": "Scalar output size", "default": [1080, 1920]}, "scale_output_size_3": {"acc": [0, 13], "size": [2], "type": "AX_U16", "comment": "Scalar output size", "default": [1080, 1920]}, "scale_output_size_4": {"acc": [0, 13], "size": [2], "type": "AX_U16", "comment": "Scalar output size", "default": [1080, 1920]}}, "autos": {"1": {"ref_mode": ["gain/lux"], "ref_group_num": [16], "ref_interp_method": ["linear"]}}, "params": {"input_0_size": {"acc": [0, 13], "auto": 0, "comment": "input 0 size", "default": [1080, 1920], "display": "input 0 size", "hidden": 0, "range": [2, 4096], "size": [2], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "input_1_size": {"acc": [0, 13], "auto": 0, "comment": "input 0 size", "default": [1080, 1920], "display": "input 0 size", "hidden": 0, "range": [2, 4096], "size": [2], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "input_2_size": {"acc": [0, 13], "auto": 0, "comment": "input 0 size", "default": [1080, 1920], "display": "input 0 size", "hidden": 0, "range": [2, 4096], "size": [2], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "input_offset_x": {"acc": [1, 13, 10], "auto": 0, "comment": "input offset x for fbcbc", "default": [512.0, 512.0, 512.0], "display": "input offset x", "hidden": 0, "range": [-8192.0, 8191.9990234375], "size": [3], "target_conf": [], "type": "AX_S32", "dependency": "user"}, "output_stride": {"acc": [0, 13], "auto": 0, "comment": "output stride for fbcbc", "default": [1920, 1920, 1920, 1920, 1920, 1920], "display": "output stride", "hidden": 0, "range": [128, 4096], "size": [6], "target_conf": [], "type": "AX_U16", "dependency": "user"}, "input_format": {"acc": [0, 6], "auto": 0, "comment": "input formats", "default": [0, 0, 0, 15], "display": "input format", "hidden": 0, "range": [0, 63], "size": [4], "target_conf": ["tde.input_format"], "type": "AX_U8", "dependency": "user", "enum_field": {"0": "NV12", "1": "NV21", "2": "NV16", "3": "NV61", "4": "YUYV", "5": "YVYU", "6": "UYVY", "7": "VYUY", "8": "Y", "9": "LAB", "10": "Y_P101010", "11": "Y_P010", "12": "BIT1", "13": "BIT2", "14": "BIT4", "15": "BIT8", "16": "NV12_P101010", "17": "NV21_P101010", "18": "NV16_P101010", "19": "NV61_P101010", "20": "YUYV_P101010", "21": "YVYU_P101010", "22": "UYVY_P101010", "23": "VYUY_P101010", "24": "NV12_P010", "25": "NV21_P010", "26": "NV16_P010", "27": "NV61_P010", "28": "YUYV_P010", "29": "YVYU_P010", "30": "UYVY_P010", "31": "VYUY_P010", "32": "RGB888", "33": "BGR888", "34": "RGB565", "35": "BGR565", "48": "ARGB8888", "49": "ABGR8888", "50": "ARGB8565", "51": "ABGR8565", "52": "ARGB1555", "53": "ABGR1555", "54": "ARGB4444", "55": "ABGR4444", "56": "RGBA8888", "57": "BGRA8888", "58": "RGBA5658", "59": "BGRA5658", "60": "RGBA5551", "61": "BGRA5551", "62": "RGBA4444", "63": "BGRA4444"}}, "stream_mux": {"acc": [0, 2], "auto": 0, "comment": "Choose different src for 6 streams", "default": [0, 0, 0, 0, 0, 0], "display": "stream mux", "hidden": 0, "range": [0, 2], "size": [6], "target_conf": ["tde.stream_mux"], "type": "AX_U8", "dependency": "user"}, "output_format": {"acc": [0, 6], "auto": 0, "comment": "input formats", "default": [0, 0, 0, 0, 0, 9], "display": "input format", "hidden": 0, "range": [0, 63], "size": [6], "target_conf": ["tde.output_format"], "type": "AX_U8", "dependency": "user", "enum_field": {"0": "NV12", "1": "NV21", "2": "NV16", "3": "NV61", "4": "YUYV", "5": "YVYU", "6": "UYVY", "7": "VYUY", "8": "Y", "9": "LAB", "10": "Y_P101010", "11": "Y_P010", "12": "BIT1", "13": "BIT2", "14": "BIT4", "15": "BIT8", "16": "NV12_P101010", "17": "NV21_P101010", "18": "NV16_P101010", "19": "NV61_P101010", "20": "YUYV_P101010", "21": "YVYU_P101010", "22": "UYVY_P101010", "23": "VYUY_P101010", "24": "NV12_P010", "25": "NV21_P010", "26": "NV16_P010", "27": "NV61_P010", "28": "YUYV_P010", "29": "YVYU_P010", "30": "UYVY_P010", "31": "VYUY_P010", "32": "RGB888", "33": "BGR888", "34": "RGB565", "35": "BGR565", "48": "ARGB8888", "49": "ABGR8888", "50": "ARGB8565", "51": "ABGR8565", "52": "ARGB1555", "53": "ABGR1555", "54": "ARGB4444", "55": "ABGR4444", "56": "RGBA8888", "57": "BGRA8888", "58": "RGBA5658", "59": "BGRA5658", "60": "RGBA5551", "61": "BGRA5551", "62": "RGBA4444", "63": "BGRA4444"}}, "quad_enable": {"display": "quad enable", "acc": [0, 1], "type": "AX_U8", "size": [8], "range": [0, 1], "default": [0, 0, 0, 0, 0, 0, 0, 0], "comment": "Quad enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.quad_enable"], "dependency": "user"}, "quad_mux": {"display": "quad mux", "acc": [0, 2], "type": "AX_U8", "size": [], "range": [0, 2], "default": 0, "comment": "Choose which input pic to draw quad on", "hidden": 0, "auto": 0, "target_conf": ["tde.quad_mux"], "dependency": "user"}, "quad_x_coords": {"display": "quad in x coords", "acc": [1, 13], "type": "AX_S16", "size": [8, 4], "range": [-8192, 8191], "default": [[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "comment": "x coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed", "hidden": 0, "auto": 0, "target_conf": ["tde.quad_x_coords", "tde.quad_steps", "tde.quad_directions"], "dependency": "user"}, "quad_y_coords": {"display": "quad in y coords", "acc": [1, 13], "type": "AX_S16", "size": [8, 4], "range": [-8192, 8191], "default": [[0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "comment": "y coordinates of intersection points between inner quad's edges, and image rectangle which is constrained by y_st and y_ed", "hidden": 0, "auto": 0, "target_conf": ["tde.quad_y_coords", "tde.quad_steps", "tde.quad_directions"], "dependency": "user"}, "quad_mode": {"acc": [0, 1], "auto": 0, "comment": "Quad feature mode control bit. 0 - mosaic; 1 - fill", "default": [0, 0, 0, 0, 0, 0, 0, 0], "display": "quad out mode", "hidden": 0, "range": [0, 1], "size": [8], "target_conf": ["tde.quad_mode"], "type": "AX_U8", "dependency": "user"}, "quad_mosaic_step_size": {"acc": [0, 7], "auto": 0, "comment": "Mosaic step size", "default": [16, 16], "display": "mosaic step size", "hidden": 0, "range": [0, 127], "size": [2], "target_conf": ["tde.quad_mosaic_step_size"], "type": "AX_U8", "dependency": "user"}, "quad_fill_luma": {"acc": [0, 8], "auto": 0, "comment": "Outer quad luma", "default": [128, 128, 128, 128, 128, 128, 128, 128], "display": "outer quad luma", "hidden": 0, "range": [0, 255], "size": [8], "target_conf": ["tde.quad_fill_luma"], "type": "AX_U8", "dependency": "user"}, "quad_fill_chroma": {"acc": [0, 8], "auto": 0, "comment": "Outer quad chroma(with 128 bias)", "default": [[128, 128], [128, 128], [128, 128], [128, 128], [128, 128], [128, 128], [128, 128], [128, 128]], "display": "Outer quad chroma", "hidden": 0, "range": [0, 255], "size": [8, 2], "target_conf": ["tde.quad_fill_chroma"], "type": "AX_U8", "dependency": "user"}, "quad_fill_mode": {"acc": [0, 2], "auto": 0, "comment": "Quad fill mode, 0 - inner Quad; 1 - outter Quad; 2 - inner clear Quad; 3 - outter clear Quad.", "default": [0, 0, 0, 0, 0, 0, 0, 0], "display": "quad fill mode", "hidden": 0, "range": [0, 3], "size": [8], "target_conf": ["tde.quad_fill_mode"], "type": "AX_U8", "dependency": "user"}, "quad_alpha": {"acc": [0, 8], "auto": 0, "comment": "outer quad blending alpha", "default": [255, 255, 255, 255, 255, 255, 255, 255], "display": "outer quad blending alpha", "hidden": 0, "range": [0, 255], "size": [8], "target_conf": ["tde.quad_alpha"], "type": "AX_U8", "dependency": "user"}, "scale_enable_0": {"acc": [0, 1], "auto": 0, "comment": "Scalar enable control bit", "default": 0, "display": "scale enable 0", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.scale_enable_0"], "type": "AX_U8", "dependency": "user"}, "scale_output_size_0": {"acc": [0, 13], "auto": 0, "comment": "Scalar output size", "default": [1080, 1920], "display": "scale output size 0", "hidden": 0, "range": [2, 4096], "size": [2], "target_conf": ["tde.scale_output_size_0", "tde.scale_step_0", "tde.scale_offset_luma_0", "tde.scale_offset_chroma_0"], "type": "AX_U16", "dependency": "user"}, "scale_filter_kernel_mode_0": {"acc": [0, 1], "auto": 0, "comment": "kernel mode to be selected, 0:triangle, 1:cubic", "default": [0, 0, 0, 0], "display": "scale filter kernel mode 0", "hidden": 0, "range": [0, 1], "size": [4], "target_conf": ["tde.scale_filter_x_luma_0", "tde.scale_filter_y_luma_0", "tde.scale_filter_x_chroma_0", "tde.scale_filter_y_chroma_0"], "type": "AX_U8", "dependency": "user"}, "scale_filter_bicubic_a_0": {"acc": [1, 1, 10], "auto": 0, "comment": "Param a of bicubic. The less the sharper. the large the more smooth", "default": [0, 0, 0, 0], "display": "scale filter bicubic a 0", "hidden": 0, "range": [-1.0, 1.0], "size": [4], "target_conf": ["tde.scale_filter_x_luma_0", "tde.scale_filter_y_luma_0", "tde.scale_filter_x_chroma_0", "tde.scale_filter_y_chroma_0"], "type": "AX_S16", "dependency": "user"}, "shp_enable": {"display": "shpEnable", "acc": [0, 1], "size": [], "range": [0, 1], "default": 1, "comment": "sharpen enable", "hidden": 0, "auto": 0, "target_conf": ["tde.shp_limit_sup_max_over_change_0", "tde.shp_limit_sup_max_under_change_0"]}, "shp_debug_mode": {"display": "Debug Mode", "acc": [0, 5], "size": [], "range": [0, 31], "default": 0, "comment": "sharpen debug mode", "dependency": "common", "hidden": 1, "auto": 0, "target_conf": ["tde.shp_debug_mode_0"]}, "shp_disable_for_prev_debug": {"acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "disable sharpen when using yuv3dnr debug mode", "dependency": "common", "hidden": 1, "auto": 0, "target_conf": []}, "shp_ud_gain_lut": {"display": "<PERSON><PERSON>", "acc": [0, 8, 4], "size": [33], "range": [0.0, 255.9375], "default": [8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0], "comment": "set undirectional gain w.r.t. texture strength", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_ud_gain_lut_0"]}, "shp_ud_gain_lut_anchor": {"display": "UD Lut Anchor", "acc": [0, 3], "size": [], "range": [0, 7], "default": 1, "comment": "smaller value means more anchor points in flat/weak texture regions", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_ud_texture_shift_0"]}, "shp_dir_gain_lut": {"display": "<PERSON><PERSON>", "acc": [0, 8, 4], "size": [33], "range": [0.0, 255.9375], "default": [8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0, 8.0], "comment": "set directional gain w.r.t. edge strength", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_dir_gain_lut_0"]}, "shp_dir_gain_lut_anchor": {"display": "<PERSON><PERSON>", "acc": [0, 3], "size": [], "range": [0, 7], "default": 1, "comment": "smaller value means more anchor points in flat/weak texture regions", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_dir_texture_shift_0"]}, "shp_ud_freq": {"display": "UD Frequency", "acc": [0, 5, 5], "size": [], "range": [0.0, 31.96875], "default": 1.0, "comment": "larger value means higher undirectional frequecny will be enhanced", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_filter_ud_0", "tde.shp_ud_gain_lut_0"]}, "shp_dir_freq": {"display": "<PERSON><PERSON>", "acc": [0, 5, 5], "size": [], "range": [0.0, 31.96875], "default": 1.0, "comment": "larger value means higher directional frequecny will be enhanced", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_filter_dir_0_0", "tde.shp_filter_dir_22_0", "tde.shp_filter_dir_45_0", "tde.shp_dir_gain_lut_0"]}, "shp_ud_coring": {"display": "UD Coring", "acc": [0, 3, 3], "size": [], "range": [0.0, 7.875], "default": 0.0, "comment": "larger value means stronger coring for undirectional details", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_coring_ud_level_0"]}, "shp_dir_coring": {"display": "<PERSON><PERSON>", "acc": [0, 3, 3], "size": [], "range": [0.0, 7.875], "default": 0.0, "comment": "larger value means stronger coring for directional details", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_coring_dir_level_0"]}, "shp_edge_strength": {"display": "Edge Strength", "acc": [0, 4], "size": [], "range": [0, 15], "default": 8, "comment": "larger value means to use more directional filter's result", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_ud_scale_0", "tde.shp_dir_scale_0"]}, "shp_edge_threshold": {"display": "Edge Threshold", "acc": [0, 8, 4], "size": [], "range": [0.0, 255.9375], "default": 16.0, "comment": "larger value means less pixels will be considered as edge pixels", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_edge_sad_noise_0"]}, "shp_edge_smooth_thin_ratio": {"display": "Edge Smooth Thin Ratio", "acc": [0, 4], "size": [], "range": [0, 15], "default": 0, "comment": "larger value means to make the edge much thinner", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_filter_dir_0_0", "tde.shp_filter_dir_22_0", "tde.shp_filter_dir_45_0"]}, "shp_overshoot": {"display": "Overshoot", "acc": [0, 0, 7], "size": [], "range": [0.0, 0.9921875], "default": 0.625, "comment": "larger value means stronger overshoot", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_overshoot_0"]}, "shp_undershoot": {"display": "Undershoot", "acc": [0, 0, 7], "size": [], "range": [0.0, 0.9921875], "default": 0.703125, "comment": "larger value means stronger undershoot", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_undershoot_0"]}, "shp_detail_region_threshold": {"display": "Detail Threshold", "acc": [0, 8], "size": [], "range": [0, 255], "default": 20, "comment": "larger value means more pixels will be considered as detail region", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_detail_overshoot_slope_0", "tde.shp_detail_overshoot_offset_0", "tde.shp_detail_overshoot_limit_0", "tde.shp_detail_undershoot_slope_0", "tde.shp_detail_undershoot_offset_0", "tde.shp_detail_undershoot_limit_0"]}, "shp_detail_overshoot_adjust": {"display": "Detail Overshoot Adjust", "acc": [1, 0, 7], "size": [], "range": [-0.9921875, 0.9921875], "default": 0.0, "comment": "larger value means stronger overshoot for detail region", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_detail_adj_enable_0", "tde.shp_detail_overshoot_slope_0", "tde.shp_detail_overshoot_offset_0", "tde.shp_detail_overshoot_limit_0"]}, "shp_detail_undershoot_adjust": {"display": "Detail Undershoot Adjust", "acc": [1, 0, 7], "size": [], "range": [-0.9921875, 0.9921875], "default": 0.0, "comment": "larger value means stronger undershoot for detail region", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_detail_adj_enable_0", "tde.shp_detail_undershoot_slope_0", "tde.shp_detail_undershoot_offset_0", "tde.shp_detail_undershoot_limit_0"]}, "shp_shoot_release_ratio": {"display": "Shoot Release Ratio", "acc": [0, 1, 4], "size": [], "range": [0.0, 1.0], "default": 0.0, "comment": "larger value means shoot suppression tends to ignore more weak texture/detail region", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_shoot_sup_enable_0", "tde.shp_shoot_sup_blend_ratio_0"]}, "shp_shoot_sup_range": {"display": "Shoot Suppression Range", "acc": [0, 8], "size": [], "range": [0, 255], "default": 0, "comment": "larger value means shoot suppression tends to control more edges", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_shoot_sup_enable_0", "tde.shp_shoot_sup_var_min_slope_0", "tde.shp_shoot_sup_var_min_offset_0", "tde.shp_shoot_sup_var_min_limit_0"]}, "shp_shoot_sup_strength": {"display": "Shoot Suppression Strength", "acc": [0, 0, 7], "size": [], "range": [0.0, 0.9921875], "default": 0.75, "comment": "larger value means stronger shoot suppression on edges", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_shoot_sup_enable_0", "tde.shp_shoot_sup_var_min_slope_0", "tde.shp_shoot_sup_var_min_offset_0", "tde.shp_shoot_sup_var_min_limit_0"]}, "shp_limit": {"display": "Sharpen <PERSON>", "acc": [0, 5, 5], "size": [], "range": [0.0, 31.96875], "default": 31.96875, "comment": "smaller value means more restriction on the pixel value change, and may reduce the number of white/black points", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_limit_sup_max_over_gain_0", "tde.shp_limit_sup_max_under_gain_0"]}, "shp_luma_gain_lut_negative": {"display": "Luma Negative Gain", "acc": [0, 1, 5], "size": [33], "range": [0.0, 1.96875], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "comment": "adjust sharpen gain for negative hf based on image brightness", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_luma_mask_enable_0", "tde.shp_luma_gain_lut_0"]}, "shp_luma_gain_lut_positive": {"display": "Luma Positive Gain", "acc": [0, 1, 5], "size": [33], "range": [0.0, 1.96875], "default": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "comment": "adjust sharpen gain for positive hf based on image brightness", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_luma_mask_enable_0", "tde.shp_luma_gain_lut_0"]}, "shp_nr_strength": {"display": "NR Strength", "acc": [0, 1, 5], "size": [], "range": [0.0, 1.0], "default": 0.0, "comment": "larger value means stronger noise reduction", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_nr_level_0"]}, "shp_nr_ud_texture_sensitivity": {"display": "UD Texture Sensitivity", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.25, "comment": "larger value means image has more undirectional textures and apply less noise reduction", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_nr_ud_texture_scale_0"]}, "shp_nr_ud_texture_threshold": {"display": "UD Texture Threshold", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.25, "comment": "larger value means less pixels will be considered as real texture and apply more noise reduction", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_nr_ud_texture_offset_0"]}, "shp_nr_ud_limit": {"display": "UD Noise Limit", "acc": [0, 8, 2], "size": [], "range": [0.0, 255.75], "default": 64.0, "comment": "larger value means less restriction on undirectional noise reduction", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_nr_ud_limit_0"]}, "shp_nr_dir_edge_sensitivity": {"display": "Dir Edge Sensitivity", "acc": [0, 0, 8], "size": [], "range": [0.0, 0.99609375], "default": 0.25, "comment": "larger value means image has more directional edges and apply less noise reduction", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_nr_dir_texture_scale_0"]}, "shp_nr_dir_edge_threshold": {"display": "<PERSON><PERSON>eshold", "acc": [0, 4, 4], "size": [], "range": [0.0, 15.9375], "default": 4.0, "comment": "larger value means less pixels will be considered as real edge and apply more noise reduction", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_nr_dir_texture_offset_0"]}, "shp_nr_dir_limit": {"display": "Dir Noise Limit", "acc": [0, 8, 2], "size": [], "range": [0.0, 255.75], "default": 64.0, "comment": "larger value means less restriction on directional noise reduction", "hidden": 0, "auto": 1, "target_conf": ["tde.shp_nr_dir_limit_0"]}, "rect_enable_0": {"display": "rect enable 0", "acc": [0, 1], "size": [16], "range": [0, 1], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "Rectangles' enable control bits", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_enable_0"], "dependency": "user"}, "rect_tl_coord_x_0": {"display": "rect tl coord x 0", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "top-left x coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_tl_coord_x_0"], "dependency": "user"}, "rect_tl_coord_y_0": {"display": "rect tl coord y 0", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "top-left y coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_tl_coord_y_0"], "dependency": "user"}, "rect_width_0": {"display": "rect width 0", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "width of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_br_coord_x_0"], "dependency": "user"}, "rect_height_0": {"display": "rect height 0", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "height coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_br_coord_y_0"], "dependency": "user"}, "rect_edge_thickness_0": {"display": "rect edge thickness 0", "acc": [0, 8], "size": [], "range": [0, 255], "default": 4, "comment": "Rectangles' edge thickness", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_thickness_0"], "dependency": "user"}, "rect_edge_color_select_0": {"display": "rect edge color select 0", "acc": [0, 2], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "Rectangles' color select", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_color_select_0"], "dependency": "user"}, "rect_edge_color_val_0": {"display": "rect edge color val 0", "acc": [0, 8], "size": [4, 4], "range": [0, 255], "default": [[255, 255, 255, 255], [255, 255, 255, 255], [255, 255, 255, 255], [255, 255, 255, 255]], "comment": "larger value means less restriction on directional noise reduction", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_color_val_0"], "dependency": "user"}, "roi_luma_sum_tl_coords_0": {"acc": [0, 12], "auto": 0, "comment": "roi luma sum tl coords 0", "default": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "display": "top-left corner coordinates of 8 roi areas", "hidden": 0, "range": [0, 4095], "size": [8, 2], "target_conf": ["tde.roi_luma_sum_tl_coords_0"], "type": "AX_U16", "dependency": "user"}, "roi_luma_sum_br_coords_0": {"acc": [0, 12], "auto": 0, "comment": "roi luma sum br coords 0", "default": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "display": "bottom-right corner coordinates of 8 roi areas", "hidden": 0, "range": [0, 4095], "size": [8, 2], "target_conf": ["tde.roi_luma_sum_br_coords_0"], "type": "AX_U16", "dependency": "user"}, "osd_enable_0": {"display": "rect enable 0", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "osd enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.osd_enable_0"], "dependency": "user"}, "osd_tl_coord_x_0": {"display": "osd tl coord x 0", "acc": [0, 13], "size": [32], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "osd blending top-left coord(x)", "hidden": 0, "auto": 0, "target_conf": ["tde.osd_tl_coord_x_0", "tde.osd_br_coord_x_0"], "dependency": "user"}, "osd_tl_coord_y_0": {"display": "osd tl coord x 0", "acc": [0, 13], "size": [32], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "4 groups of osd blending top-left coord(y)", "hidden": 0, "auto": 0, "target_conf": ["tde.osd_tl_coord_y_0", "tde.osd_br_coord_y_0"], "dependency": "user"}, "osd_width_0": {"display": "osd width 0", "acc": [0, 13], "size": [32], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "osd blending width", "hidden": 0, "auto": 0, "target_conf": ["tde.osd_tl_coord_x_0", "tde.osd_br_coord_x_0"], "dependency": "user"}, "osd_height_0": {"display": "osd height 0", "acc": [0, 13], "size": [32], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "osd blending height(y)", "hidden": 0, "auto": 0, "target_conf": ["tde.osd_tl_coord_y_0", "tde.osd_br_coord_y_0"], "dependency": "user"}, "yclip_enable_0": {"display": "yclip enable 0", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Luma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_enable_0"], "dependency": "user"}, "yclip_offset_0": {"display": "yclip offset 0", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 0], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 1, "auto": 0, "target_conf": ["tde.yclip_offset_0"], "dependency": "common"}, "yclip_gain_0": {"display": "yclip gain 0", "acc": [1, 4, 8], "size": [], "range": [-16.0, 15.99609375], "default": 1.0, "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_gain_0"], "dependency": "user"}, "yclip_boundary_0": {"display": "yclip boundary 0", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 255], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_boundary_0"], "dependency": "user"}, "cclip_enable_0": {"display": "cclip enable 0", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Chroma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_enable_0"], "dependency": "user"}, "cclip_cmtx_0": {"display": "cclip cmtx 0", "acc": [1, 2, 5], "size": [2, 2], "range": [-4.0, 3.96875], "default": [[1, 0], [0, 1]], "comment": "2x2 chroma adjustment matrix", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_cmtx_0"], "dependency": "user"}, "cclip_boundary_0": {"display": "cclip boundary 0", "acc": [1, 8], "size": [2], "range": [-256, 255], "default": [-128, 128], "comment": "2x2 chroma adjustment --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_boundary_0"], "dependency": "user"}, "crop_enable_0": {"display": "crop enable 0", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Crop enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_enable_0"], "dependency": "user"}, "crop_start_0": {"display": "crop start 0", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop start coordinates", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_start_0"], "dependency": "user"}, "crop_size_0": {"display": "crop size 0", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop size", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_size_0"], "dependency": "user"}, "lba_enable_0": {"display": "lba enable 0", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "lba enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_enable_0"], "dependency": "user"}, "lba_exd_size_0": {"display": "lba exd size 0", "acc": [0, 13], "size": [4], "range": [0, 8191], "default": [0, 0, 0, 0], "comment": "lba extension size(paddings on edges)", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_exd_size_0"], "dependency": "user"}, "lba_background_0": {"display": "lba enable 0", "acc": [1, 8], "size": [3], "range": [-256, 255], "default": [0, 0, 0], "comment": "lba background color", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_background_0"], "dependency": "user"}, "scale_input_mux_1": {"acc": [0, 1], "auto": 0, "comment": "Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "default": 0, "display": "scale input mux 1", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.scale_input_mux_1"], "type": "AX_U8", "dependency": "user"}, "scale_enable_1": {"acc": [0, 1], "auto": 0, "comment": "Scalar enable control bit", "default": 0, "display": "scale enable 1", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.scale_enable_1"], "type": "AX_U8", "dependency": "user"}, "scale_output_size_1": {"acc": [0, 13], "auto": 0, "comment": "Scalar output size", "default": [1080, 1920], "display": "scale output size 1", "hidden": 0, "range": [2, 4096], "size": [2], "target_conf": ["tde.scale_output_size_1", "tde.scale_step_1", "tde.scale_offset_luma_1", "tde.scale_offset_chroma_1"], "type": "AX_U16", "dependency": "user"}, "rect_enable_1": {"display": "rect enable 1", "acc": [0, 1], "size": [16], "range": [0, 1], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "Rectangles' enable control bits", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_enable_1"], "dependency": "user"}, "rect_tl_coord_x_1": {"display": "rect tl coord x 1", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "top-left x coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_tl_coord_x_1"], "dependency": "user"}, "rect_tl_coord_y_1": {"display": "rect tl coord y 1", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "top-left y coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_tl_coord_y_1"], "dependency": "user"}, "rect_width_1": {"display": "rect width 1", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "width of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_br_coord_x_1"], "dependency": "user"}, "rect_height_1": {"display": "rect height 1", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "height coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_br_coord_y_1"], "dependency": "user"}, "rect_edge_thickness_1": {"display": "rect edge thickness 1", "acc": [0, 8], "size": [], "range": [0, 255], "default": 4, "comment": "Rectangles' edge thickness", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_thickness_1"], "dependency": "user"}, "rect_edge_color_select_1": {"display": "rect edge color select 1", "acc": [0, 2], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "Rectangles' color select", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_color_select_1"], "dependency": "user"}, "rect_edge_color_val_1": {"display": "rect edge color val 1", "acc": [0, 8], "size": [4, 4], "range": [0, 255], "default": [[255, 255, 255, 255], [255, 255, 255, 255], [255, 255, 255, 255], [255, 255, 255, 255]], "comment": "larger value means less restriction on directional noise reduction", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_color_val_1"], "dependency": "user"}, "yclip_enable_1": {"display": "yclip enable 1", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Luma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_enable_1"], "dependency": "user"}, "yclip_offset_1": {"display": "yclip offset 1", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 0], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 1, "auto": 0, "target_conf": ["tde.yclip_offset_1"], "dependency": "common"}, "yclip_gain_1": {"display": "yclip gain 1", "acc": [1, 4, 8], "size": [], "range": [-16.0, 15.99609375], "default": 1.0, "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_gain_1"], "dependency": "user"}, "yclip_boundary_1": {"display": "yclip boundary 1", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 255], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_boundary_1"], "dependency": "user"}, "cclip_enable_1": {"display": "cclip enable 1", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Chroma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_enable_1"], "dependency": "user"}, "cclip_cmtx_1": {"display": "cclip cmtx 1", "acc": [1, 2, 5], "size": [2, 2], "range": [-4.0, 3.96875], "default": [[1, 0], [0, 1]], "comment": "2x2 chroma adjustment matrix", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_cmtx_1"], "dependency": "user"}, "cclip_boundary_1": {"display": "cclip boundary 1", "acc": [1, 8], "size": [2], "range": [-256, 255], "default": [-128, 128], "comment": "2x2 chroma adjustment --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_boundary_1"], "dependency": "user"}, "crop_enable_1": {"display": "crop enable 1", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Crop enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_enable_1"], "dependency": "user"}, "crop_start_1": {"display": "crop start 1", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop start coordinates", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_start_1"], "dependency": "user"}, "crop_size_1": {"display": "crop size 1", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop size", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_size_1"], "dependency": "user"}, "lba_enable_1": {"display": "lba enable 1", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "lba enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_enable_1"], "dependency": "user"}, "lba_exd_size_1": {"display": "lba exd size 1", "acc": [0, 13], "size": [4], "range": [0, 8191], "default": [0, 0, 0, 0], "comment": "lba extension size(paddings on edges)", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_exd_size_1"], "dependency": "user"}, "lba_background_1": {"display": "lba enable 1", "acc": [1, 8], "size": [3], "range": [-256, 255], "default": [0, 0, 0], "comment": "lba background color", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_background_1"], "dependency": "user"}, "scale_input_mux_2": {"acc": [0, 1], "auto": 0, "comment": "Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "default": 0, "display": "scale input mux 2", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.scale_input_mux_2"], "type": "AX_U8", "dependency": "user"}, "scale_enable_2": {"acc": [0, 1], "auto": 0, "comment": "Scalar enable control bit", "default": 0, "display": "scale enable 2", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.scale_enable_2"], "type": "AX_U8", "dependency": "user"}, "scale_output_size_2": {"acc": [0, 13], "auto": 0, "comment": "Scalar output size", "default": [1080, 1920], "display": "scale output size 2", "hidden": 0, "range": [2, 4096], "size": [2], "target_conf": ["tde.scale_output_size_2", "tde.scale_step_2", "tde.scale_offset_luma_2", "tde.scale_offset_chroma_2"], "type": "AX_U16", "dependency": "user"}, "scale_filter_kernel_mode_2": {"acc": [0, 1], "auto": 0, "comment": "kernel mode to be selected, 0:triangle, 1:cubic", "default": [0, 0, 0, 0], "display": "scale filter kernel mode 2", "hidden": 0, "range": [0, 1], "size": [4], "target_conf": ["tde.scale_filter_x_luma_2", "tde.scale_filter_y_luma_2", "tde.scale_filter_x_chroma_2", "tde.scale_filter_y_chroma_2"], "type": "AX_U8", "dependency": "user"}, "scale_filter_bicubic_a_2": {"acc": [1, 1, 10], "auto": 0, "comment": "Param a of bicubic. The less the sharper. the large the more smooth", "default": [0, 0, 0, 0], "display": "scale filter bicubic a 2", "hidden": 0, "range": [-1.0, 1.0], "size": [4], "target_conf": ["tde.scale_filter_x_luma_2", "tde.scale_filter_y_luma_2", "tde.scale_filter_x_chroma_2", "tde.scale_filter_y_chroma_2"], "type": "AX_S16", "dependency": "user"}, "rect_enable_2": {"display": "rect enable 2", "acc": [0, 1], "size": [16], "range": [0, 1], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "Rectangles' enable control bits", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_enable_2"], "dependency": "user"}, "rect_tl_coord_x_2": {"display": "rect tl coord x 2", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "top-left x coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_tl_coord_x_2"], "dependency": "user"}, "rect_tl_coord_y_2": {"display": "rect tl coord y 2", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "top-left y coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_tl_coord_y_2"], "dependency": "user"}, "rect_width_2": {"display": "rect width 2", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "width of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_br_coord_x_2"], "dependency": "user"}, "rect_height_2": {"display": "rect height 2", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "height coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_br_coord_y_2"], "dependency": "user"}, "rect_edge_thickness_2": {"display": "rect edge thickness 2", "acc": [0, 8], "size": [], "range": [0, 255], "default": 4, "comment": "Rectangles' edge thickness", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_thickness_2"], "dependency": "user"}, "rect_edge_color_select_2": {"display": "rect edge color select 2", "acc": [0, 2], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "Rectangles' color select", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_color_select_2"], "dependency": "user"}, "rect_edge_color_val_2": {"display": "rect edge color val 2", "acc": [0, 8], "size": [4, 4], "range": [0, 255], "default": [[255, 255, 255, 255], [255, 255, 255, 255], [255, 255, 255, 255], [255, 255, 255, 255]], "comment": "larger value means less restriction on directional noise reduction", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_color_val_2"], "dependency": "user"}, "yclip_enable_2": {"display": "yclip enable 2", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Luma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_enable_2"], "dependency": "user"}, "yclip_offset_2": {"display": "yclip offset 2", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 0], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 1, "auto": 0, "target_conf": ["tde.yclip_offset_2"], "dependency": "common"}, "yclip_gain_2": {"display": "yclip gain 2", "acc": [1, 4, 8], "size": [], "range": [-16.0, 15.99609375], "default": 1.0, "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_gain_2"], "dependency": "user"}, "yclip_boundary_2": {"display": "yclip boundary 2", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 255], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_boundary_2"], "dependency": "user"}, "cclip_enable_2": {"display": "cclip enable 2", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Chroma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_enable_2"], "dependency": "user"}, "cclip_cmtx_2": {"display": "cclip cmtx 2", "acc": [1, 2, 5], "size": [2, 2], "range": [-4.0, 3.96875], "default": [[1, 0], [0, 1]], "comment": "2x2 chroma adjustment matrix", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_cmtx_2"], "dependency": "user"}, "cclip_boundary_2": {"display": "cclip boundary 2", "acc": [1, 8], "size": [2], "range": [-256, 255], "default": [-128, 128], "comment": "2x2 chroma adjustment --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_boundary_2"], "dependency": "user"}, "crop_enable_2": {"display": "crop enable 2", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Crop enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_enable_2"], "dependency": "user"}, "crop_start_2": {"display": "crop start 2", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop start coordinates", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_start_2"], "dependency": "user"}, "crop_size_2": {"display": "crop size 2", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop size", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_size_2"], "dependency": "user"}, "lba_enable_2": {"display": "lba enable 1", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "lba enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_enable_2"], "dependency": "user"}, "lba_exd_size_2": {"display": "lba exd size 2", "acc": [0, 13], "size": [4], "range": [0, 8191], "default": [0, 0, 0, 0], "comment": "lba extension size(paddings on edges)", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_exd_size_2"], "dependency": "user"}, "lba_background_2": {"display": "lba enable 2", "acc": [1, 8], "size": [3], "range": [-256, 255], "default": [0, 0, 0], "comment": "lba background color", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_background_2"], "dependency": "user"}, "scale_input_mux_3": {"acc": [0, 1], "auto": 0, "comment": "Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "default": 0, "display": "scale input mux 3", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.scale_input_mux_3"], "type": "AX_U8", "dependency": "user"}, "scale_enable_3": {"acc": [0, 1], "auto": 0, "comment": "Scalar enable control bit", "default": 0, "display": "scale enable 3", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.scale_enable_3"], "type": "AX_U8", "dependency": "user"}, "scale_output_size_3": {"acc": [0, 13], "auto": 0, "comment": "Scalar output size", "default": [1080, 1920], "display": "scale output size 3", "hidden": 0, "range": [2, 4096], "size": [2], "target_conf": ["tde.scale_output_size_3", "tde.scale_step_3", "tde.scale_offset_luma_3", "tde.scale_offset_chroma_3"], "type": "AX_U16", "dependency": "user"}, "rect_enable_3": {"display": "rect enable 3", "acc": [0, 1], "size": [16], "range": [0, 1], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "Rectangles' enable control bits", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_enable_3"], "dependency": "user"}, "rect_tl_coord_x_3": {"display": "rect tl coord x 3", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "top-left x coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_tl_coord_x_3"], "dependency": "user"}, "rect_tl_coord_y_3": {"display": "rect tl coord y 3", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "top-left y coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_tl_coord_y_3"], "dependency": "user"}, "rect_width_3": {"display": "rect width 3", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "width of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_br_coord_x_3"], "dependency": "user"}, "rect_height_3": {"display": "rect height 3", "acc": [0, 13], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "height coordinates of rectangles", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_br_coord_y_3"], "dependency": "user"}, "rect_edge_thickness_3": {"display": "rect edge thickness 3", "acc": [0, 8], "size": [], "range": [0, 255], "default": 4, "comment": "Rectangles' edge thickness", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_thickness_3"], "dependency": "user"}, "rect_edge_color_select_3": {"display": "rect edge color select 3", "acc": [0, 2], "size": [16], "range": [0, 8191], "default": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "comment": "Rectangles' color select", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_color_select_3"], "dependency": "user"}, "rect_edge_color_val_3": {"display": "rect edge color val 3", "acc": [0, 8], "size": [4, 4], "range": [0, 255], "default": [[255, 255, 255, 255], [255, 255, 255, 255], [255, 255, 255, 255], [255, 255, 255, 255]], "comment": "larger value means less restriction on directional noise reduction", "hidden": 0, "auto": 0, "target_conf": ["tde.rect_edge_color_val_3"], "dependency": "user"}, "yclip_enable_3": {"display": "yclip enable 3", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Luma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_enable_3"], "dependency": "user"}, "yclip_offset_3": {"display": "yclip offset 3", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 0], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 1, "auto": 0, "target_conf": ["tde.yclip_offset_3"], "dependency": "common"}, "yclip_gain_3": {"display": "yclip gain 3", "acc": [1, 4, 8], "size": [], "range": [-16.0, 15.99609375], "default": 1.0, "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_gain_3"], "dependency": "user"}, "yclip_boundary_3": {"display": "yclip boundary 3", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 255], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_boundary_3"], "dependency": "user"}, "cclip_enable_3": {"display": "cclip enable 3", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Chroma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_enable_3"], "dependency": "user"}, "cclip_cmtx_3": {"display": "cclip cmtx 3", "acc": [1, 2, 5], "size": [2, 2], "range": [-4.0, 3.96875], "default": [[1, 0], [0, 1]], "comment": "2x2 chroma adjustment matrix", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_cmtx_3"], "dependency": "user"}, "cclip_boundary_3": {"display": "cclip boundary 3", "acc": [1, 8], "size": [2], "range": [-256, 255], "default": [-128, 128], "comment": "2x2 chroma adjustment --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_boundary_3"], "dependency": "user"}, "crop_enable_3": {"display": "crop enable 3", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Crop enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_enable_3"], "dependency": "user"}, "crop_start_3": {"display": "crop start 3", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop start coordinates", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_start_3"], "dependency": "user"}, "crop_size_3": {"display": "crop size 3", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop size", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_size_3"], "dependency": "user"}, "lba_enable_3": {"display": "lba enable 3", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "lba enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_enable_3"], "dependency": "user"}, "lba_exd_size_3": {"display": "lba exd size 3", "acc": [0, 13], "size": [4], "range": [0, 8191], "default": [0, 0, 0, 0], "comment": "lba extension size(paddings on edges)", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_exd_size_3"], "dependency": "user"}, "lba_background_3": {"display": "lba enable 3", "acc": [1, 8], "size": [3], "range": [-256, 255], "default": [0, 0, 0], "comment": "lba background color", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_background_3"], "dependency": "user"}, "scale_input_mux_4": {"acc": [0, 1], "auto": 0, "comment": "Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "default": 0, "display": "scale input mux 4", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.scale_input_mux_4"], "type": "AX_U8", "dependency": "user"}, "scale_enable_4": {"acc": [0, 1], "auto": 0, "comment": "Scalar enable control bit", "default": 0, "display": "scale enable 4", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.scale_enable_4"], "type": "AX_U8", "dependency": "user"}, "scale_output_size_4": {"acc": [0, 13], "auto": 0, "comment": "Scalar output size", "default": [1080, 1920], "display": "scale output size 4", "hidden": 0, "range": [2, 4096], "size": [2], "target_conf": ["tde.scale_output_size_4", "tde.scale_step_4", "tde.scale_offset_luma_4", "tde.scale_offset_chroma_4"], "type": "AX_U16", "dependency": "user"}, "yclip_enable_4": {"display": "yclip enable 4", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Luma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_enable_4"], "dependency": "user"}, "yclip_offset_4": {"display": "yclip offset 4", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 0], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 1, "auto": 0, "target_conf": ["tde.yclip_offset_4"], "dependency": "common"}, "yclip_gain_4": {"display": "yclip gain 4", "acc": [1, 4, 8], "size": [], "range": [-16.0, 15.99609375], "default": 1.0, "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_gain_4"], "dependency": "user"}, "yclip_boundary_4": {"display": "yclip boundary 4", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 255], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_boundary_4"], "dependency": "user"}, "cclip_enable_4": {"display": "cclip enable 4", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Chroma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_enable_4"], "dependency": "user"}, "cclip_cmtx_4": {"display": "cclip cmtx 4", "acc": [1, 2, 5], "size": [2, 2], "range": [-4.0, 3.96875], "default": [[1, 0], [0, 1]], "comment": "2x2 chroma adjustment matrix", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_cmtx_4"], "dependency": "user"}, "cclip_boundary_4": {"display": "cclip boundary 4", "acc": [1, 8], "size": [2], "range": [-256, 255], "default": [-128, 128], "comment": "2x2 chroma adjustment --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_boundary_4"], "dependency": "user"}, "scale_input_mux_5": {"acc": [0, 1], "auto": 0, "comment": "Choose the input for scalar, 0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "default": 0, "display": "scale input mux 5", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.scale_input_mux_5"], "type": "AX_U8", "dependency": "user"}, "scale_enable_5": {"acc": [0, 1], "auto": 0, "comment": "Scalar enable control bit", "default": 0, "display": "scale enable 5", "hidden": 0, "range": [0, 1], "size": [], "target_conf": ["tde.scale_enable_5"], "type": "AX_U8", "dependency": "user"}, "scale_output_size_5": {"acc": [0, 13], "auto": 0, "comment": "Scalar output size", "default": [1080, 1920], "display": "scale output size 5", "hidden": 0, "range": [2, 4096], "size": [2], "target_conf": ["tde.scale_output_size_5", "tde.scale_step_5", "tde.scale_offset_luma_5", "tde.scale_offset_chroma_5"], "type": "AX_U16", "dependency": "user"}, "yclip_enable_5": {"display": "yclip enable 5", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Luma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_enable_5"], "dependency": "user"}, "yclip_offset_5": {"display": "yclip offset 5", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 0], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 1, "auto": 0, "target_conf": ["tde.yclip_offset_5"], "dependency": "common"}, "yclip_gain_5": {"display": "yclip gain 5", "acc": [1, 4, 8], "size": [], "range": [-16.0, 15.99609375], "default": 1.0, "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_gain_5"], "dependency": "user"}, "yclip_boundary_5": {"display": "yclip boundary 5", "acc": [0, 8], "size": [2], "range": [0, 255], "default": [0, 255], "comment": "-offset[0] --> x gain --> +offset[1] --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.yclip_boundary_5"], "dependency": "user"}, "cclip_enable_5": {"display": "cclip enable 5", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Chroma clip enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_enable_5"], "dependency": "user"}, "cclip_cmtx_5": {"display": "cclip cmtx 5", "acc": [1, 2, 5], "size": [2, 2], "range": [-4.0, 3.96875], "default": [[1, 0], [0, 1]], "comment": "2x2 chroma adjustment matrix", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_cmtx_5"], "dependency": "user"}, "cclip_boundary_5": {"display": "cclip boundary 5", "acc": [1, 8], "size": [2], "range": [-256, 255], "default": [-128, 128], "comment": "2x2 chroma adjustment --> clip", "hidden": 0, "auto": 0, "target_conf": ["tde.cclip_boundary_5"], "dependency": "user"}, "crop_enable_5": {"display": "crop enable 5", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "Crop enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_enable_5"], "dependency": "user"}, "crop_start_5": {"display": "crop start 5", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop start coordinates", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_start_5"], "dependency": "user"}, "crop_size_5": {"display": "crop size 5", "acc": [0, 13], "size": [2], "range": [0, 8191], "default": [0, 0], "comment": "Crop size", "hidden": 0, "auto": 0, "target_conf": ["tde.crop_size_5"], "dependency": "user"}, "lba_enable_5": {"display": "lba enable 5", "acc": [0, 1], "size": [], "range": [0, 1], "default": 0, "comment": "lba enable control bit", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_enable_5"], "dependency": "user"}, "lba_exd_size_5": {"display": "lba exd size 5", "acc": [0, 13], "size": [4], "range": [0, 8191], "default": [0, 0, 0, 0], "comment": "lba extension size(paddings on edges)", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_exd_size_5"], "dependency": "user"}, "lba_background_5": {"display": "lba enable 5", "acc": [1, 8], "size": [3], "range": [-256, 255], "default": [0, 0, 0], "comment": "lba background color", "hidden": 0, "auto": 0, "target_conf": ["tde.lba_background_5"], "dependency": "user"}, "csc_yuvTrgb_offset_in_5": {"display": "csc yuvTrgb offset in 5", "acc": [1, 8], "size": [3], "range": [-256, 255], "default": [0, 0, 0], "comment": "YUV to RGB conversion offset in.", "hidden": 1, "auto": 0, "target_conf": ["tde.csc_yuvTrgb_offset_in_5"], "dependency": "common"}, "csc_yuvTrgb_offset_out_5": {"display": "csc yuvTrgb offset out 5", "acc": [1, 8], "size": [3], "range": [-256, 255], "default": [0, 0, 0], "comment": "YUV to RGB conversion offset out.", "hidden": 1, "auto": 0, "target_conf": ["tde.csc_yuvTrgb_offset_out_5"], "dependency": "common"}, "csc_rgbTxyz_offset_out_5": {"display": "csc rgbTxyz offset out 5", "acc": [1, 8], "size": [3], "range": [-256, 255], "default": [0, 0, 0], "comment": "rgb2xyz offset out.", "hidden": 1, "auto": 0, "target_conf": ["tde.csc_rgbTxyz_offset_out_5"], "dependency": "common"}, "csc_xyzTlab_offset_out_5": {"display": "csc xyzTlab offset out 5", "acc": [1, 8], "size": [3], "range": [-256, 255], "default": [0, 0, 0], "comment": "xyz2lab offset out.", "hidden": 1, "auto": 0, "target_conf": ["tde.csc_xyzTlab_offset_out_5"], "dependency": "common"}}, "submodules": {"setup": {"params": ["input_format", "stream_mux", "output_format", "input_0_size", "input_1_size", "input_2_size", "input_offset_x", "output_stride"], "configs": ["tde.enable", "tde.shp_enable_0", "tde.shp_luma_ref_select_0", "tde.shp_detail_texture_shift_0", "tde.shp_limit_sup_enable_0", "tde.shp_shoot_ref_ratio_0", "tde.shp_shoot_sup_var_diff_slope_0", "tde.shp_shoot_sup_var_diff_offset_0", "tde.shp_shoot_sup_var_diff_limit_0", "tde.csc_yuvTrgb_matrix_5", "tde.csc_gamma_lut_mode_5", "tde.csc_gamma_lut_5", "tde.csc_gamma_offset_out_5", "tde.csc_rgbTxyz_matrix_5", "tde.csc_xyzTlab_cbrt_lut_5", "tde.csc_xyzTlab_gain_out_5"]}, "quad": {"params": ["quad_enable", "quad_mux", "quad_x_coords", "quad_y_coords", "quad_mode", "quad_mosaic_step_size", "quad_fill_luma", "quad_fill_chroma", "quad_fill_mode", "quad_alpha"], "configs": ["tde.quad_y_start", "tde.quad_y_end"]}, "scale_0": {"params": ["scale_enable_0", "scale_output_size_0", "scale_filter_kernel_mode_0", "scale_filter_bicubic_a_0"], "configs": []}, "shp_basic": {"params": ["shp_enable", "shp_debug_mode", "shp_disable_for_prev_debug", "shp_ud_gain_lut", "shp_ud_gain_lut_anchor", "shp_dir_gain_lut", "shp_dir_gain_lut_anchor", "shp_ud_freq", "shp_dir_freq", "shp_edge_strength", "shp_edge_threshold", "shp_edge_smooth_thin_ratio", "shp_overshoot", "shp_undershoot", "shp_detail_region_threshold", "shp_detail_overshoot_adjust", "shp_detail_undershoot_adjust", "shp_shoot_release_ratio", "shp_shoot_sup_range", "shp_shoot_sup_strength", "shp_limit"], "configs": []}, "shp_luma": {"params": ["shp_luma_gain_lut_negative", "shp_luma_gain_lut_positive"], "configs": []}, "shp_nr_coring": {"params": ["shp_nr_strength", "shp_nr_ud_texture_sensitivity", "shp_nr_ud_texture_threshold", "shp_nr_ud_limit", "shp_ud_coring", "shp_nr_dir_edge_sensitivity", "shp_nr_dir_edge_threshold", "shp_nr_dir_limit", "shp_dir_coring"], "configs": []}, "rect_0": {"params": ["rect_enable_0", "rect_tl_coord_x_0", "rect_tl_coord_y_0", "rect_width_0", "rect_height_0", "rect_edge_thickness_0", "rect_edge_color_select_0", "rect_edge_color_val_0"], "configs": []}, "roi_luma_sum_0": {"params": ["roi_luma_sum_tl_coords_0", "roi_luma_sum_br_coords_0"], "configs": []}, "osd_0": {"params": ["osd_enable_0", "osd_tl_coord_x_0", "osd_tl_coord_y_0", "osd_width_0", "osd_height_0"], "configs": []}, "range_adj_0": {"params": ["yclip_enable_0", "yclip_offset_0", "yclip_gain_0", "yclip_boundary_0", "cclip_enable_0", "cclip_cmtx_0", "cclip_boundary_0"], "configs": []}, "crop_lba_0": {"params": ["crop_enable_0", "crop_start_0", "crop_size_0", "lba_enable_0", "lba_exd_size_0", "lba_background_0"], "configs": []}, "scale_1": {"params": ["scale_input_mux_1", "scale_enable_1", "scale_output_size_1"], "configs": []}, "rect_1": {"params": ["rect_enable_1", "rect_tl_coord_x_1", "rect_tl_coord_y_1", "rect_width_1", "rect_height_1", "rect_edge_thickness_1", "rect_edge_color_select_1", "rect_edge_color_val_1"], "configs": []}, "range_adj_1": {"params": ["yclip_enable_1", "yclip_offset_1", "yclip_gain_1", "yclip_boundary_1", "cclip_enable_1", "cclip_cmtx_1", "cclip_boundary_1"], "configs": []}, "crop_lba_1": {"params": ["crop_enable_1", "crop_start_1", "crop_size_1", "lba_enable_1", "lba_exd_size_1", "lba_background_1"], "configs": []}, "scale_2": {"params": ["scale_input_mux_2", "scale_enable_2", "scale_output_size_2", "scale_filter_kernel_mode_2", "scale_filter_bicubic_a_2"], "configs": []}, "rect_2": {"params": ["rect_enable_2", "rect_tl_coord_x_2", "rect_tl_coord_y_2", "rect_width_2", "rect_height_2", "rect_edge_thickness_2", "rect_edge_color_select_2", "rect_edge_color_val_2"], "configs": []}, "range_adj_2": {"params": ["yclip_enable_2", "yclip_offset_2", "yclip_gain_2", "yclip_boundary_2", "cclip_enable_2", "cclip_cmtx_2", "cclip_boundary_2"], "configs": []}, "crop_lba_2": {"params": ["crop_enable_2", "crop_start_2", "crop_size_2", "lba_enable_2", "lba_exd_size_2", "lba_background_2"], "configs": []}, "scale_3": {"params": ["scale_input_mux_3", "scale_enable_3", "scale_output_size_3"], "configs": []}, "rect_3": {"params": ["rect_enable_3", "rect_tl_coord_x_3", "rect_tl_coord_y_3", "rect_width_3", "rect_height_3", "rect_edge_thickness_3", "rect_edge_color_select_3", "rect_edge_color_val_3"], "configs": []}, "range_adj_3": {"params": ["yclip_enable_3", "yclip_offset_3", "yclip_gain_3", "yclip_boundary_3", "cclip_enable_3", "cclip_cmtx_3", "cclip_boundary_3"], "configs": []}, "crop_lba_3": {"params": ["crop_enable_3", "crop_start_3", "crop_size_3", "lba_enable_3", "lba_exd_size_3", "lba_background_3"], "configs": []}, "scale_4": {"params": ["scale_input_mux_4", "scale_enable_4", "scale_output_size_4"], "configs": []}, "range_adj_4": {"params": ["yclip_enable_4", "yclip_offset_4", "yclip_gain_4", "yclip_boundary_4", "cclip_enable_4", "cclip_cmtx_4", "cclip_boundary_4"], "configs": []}, "scale_5": {"params": ["scale_input_mux_5", "scale_enable_5", "scale_output_size_5"], "configs": []}, "range_adj_5": {"params": ["yclip_enable_5", "yclip_offset_5", "yclip_gain_5", "yclip_boundary_5", "cclip_enable_5", "cclip_cmtx_5", "cclip_boundary_5"], "configs": []}, "crop_lba_5": {"params": ["crop_enable_5", "crop_start_5", "crop_size_5", "lba_enable_5", "lba_exd_size_5", "lba_background_5"], "configs": []}, "csc_5": {"params": ["csc_yuvTrgb_offset_in_5", "csc_yuvTrgb_offset_out_5", "csc_rgbTxyz_offset_out_5", "csc_xyzTlab_offset_out_5"], "configs": []}}, "target_module": {"mc20l": {"tde": {"id": 6200, "method": 1}}}, "configs": {"tde": {"enable": {"acc": [0, 1], "size": [], "description": "VPP enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "input_format": {"acc": [0, 6], "size": [4], "description": "input formats", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_enable": {"acc": [0, 1], "size": [8], "description": "Quad enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_mux": {"acc": [0, 2], "size": [], "description": "Choose which input pic to draw quad on", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_x_coords": {"acc": [1, 13, 10], "size": [8, 4], "description": "x coordinates of intersection points between quad's edges, and image rectangle which is constrained by y_st and y_ed.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "quad_y_coords": {"acc": [0, 13], "size": [8, 4], "description": "y coordinates of intersection points between quad's edges, and image rectangle which is constrained by y_st and y_ed.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "quad_y_start": {"acc": [0, 13], "size": [8], "description": "y start coordinates of quad", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "quad_y_end": {"acc": [0, 13], "size": [8], "description": "y end coordinates of quad", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "quad_steps": {"acc": [1, 13, 10], "size": [8, 4], "description": "Quad step to form quad edges", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "quad_directions": {"acc": [0, 2], "size": [8], "description": "This register controls direction of edge x1 and x2; 0b01 selects right side of line x1 whereas 0b00 selects left side of line x1; 0b10 selects right side of line x2 and 0b00 selects left side of line x2.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_mode": {"acc": [0, 1], "size": [8], "description": "Quad feature mode control bit.", "usage": "0 - mosaic; 1 - fill", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_mosaic_step_size": {"acc": [0, 7], "size": [2], "description": "Mosaic step size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_fill_luma": {"acc": [0, 8, 0], "size": [8], "description": "Outer quad luma", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_fill_chroma": {"acc": [0, 8, 0], "size": [8, 2], "description": "Outer quad chroma(with 128 bias)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_fill_mode": {"acc": [0, 2], "size": [8], "description": "Quad fill mode.", "usage": "0 - inner Quad; 1 - outter Quad; 2 - inner clear Quad; 3 - outter clear Quad", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "quad_alpha": {"acc": [0, 8, 0], "size": [8], "description": "Outer quad blending alpha", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "stream_mux": {"acc": [0, 2], "size": [6], "description": "Choose different src for 6 streams", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_enable_0": {"acc": [0, 1], "size": [], "description": "Scalar enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_step_0": {"acc": [0, 6, 10], "size": [2], "description": "Scalar factor", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_output_size_0": {"acc": [0, 13], "size": [2], "description": "Scalar output size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_offset_luma_0": {"acc": [1, 13, 6], "size": [2], "description": "Luma channel initial phase", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "scale_offset_chroma_0": {"acc": [1, 13, 6], "size": [2], "description": "Chroma channel initial phase", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "scale_filter_x_luma_0": {"acc": [1, 1, 8], "size": [8, 5], "description": "8-phase filtering weights for LUMA channel horizontal direction", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "scale_filter_y_luma_0": {"acc": [1, 1, 8], "size": [8, 5], "description": "8-phase filtering weights for LUMA channel vertical direction", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "scale_filter_x_chroma_0": {"acc": [1, 1, 8], "size": [8, 3], "description": "8-phase filtering weights for LUMA channel horizontal direction", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "scale_filter_y_chroma_0": {"acc": [1, 1, 8], "size": [8, 3], "description": "8-phase filtering weights for LUMA channel vertical direction", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "shp_enable_0": {"acc": [0, 1], "size": [], "description": "sharpen enable, 0: disable, 1: enable", "usage": "", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_debug_mode_0": {"acc": [0, 5], "size": [], "description": "sharpen debug mode, 0: normal mode, others: visualized intermediate result for debugging", "usage": "", "constraints": "{0, 3 ~ 18}", "type": "AX_U8", "partition": "-"}, "shp_filter_ud_0": {"acc": [1, 0, 12], "size": [3, 3], "description": "undirectional filter coeffs", "usage": "", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "shp_filter_dir_0_0": {"acc": [1, 0, 12], "size": [9], "description": "0 directional filter coeffs", "usage": "", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "shp_filter_dir_22_0": {"acc": [1, 0, 12], "size": [13], "description": "22 directional filter coeffs", "usage": "", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "shp_filter_dir_45_0": {"acc": [1, 0, 12], "size": [9], "description": "45 directional filter coeffs", "usage": "", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "shp_ud_texture_shift_0": {"acc": [0, 3], "size": [], "description": "undirectional texture gain lut x-axis shift bits", "usage": "smaller value means more texture gain control points in flat or weak texture regions", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_dir_texture_shift_0": {"acc": [0, 3], "size": [], "description": "directional texture gain lut x-axis shift bits", "usage": "smaller value means more texture gain control points in flat or weak edge regions", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_ud_gain_lut_0": {"acc": [0, 8, 4], "size": [33], "description": "undirectional texture gain lut", "usage": "set undirectional sharpen gain w.r.t texture strength", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "shp_dir_gain_lut_0": {"acc": [0, 8, 4], "size": [33], "description": "directional texture gain lut", "usage": "set directional sharpen gain w.r.t texture strength", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "shp_ud_scale_0": {"acc": [0, 0, 4], "size": [], "description": "scaling factor for ud filters", "usage": "larger value means to use more undirectional filter result", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_dir_scale_0": {"acc": [0, 0, 4], "size": [], "description": "scaling factor for dir filters", "usage": "larger value means to use more directional filter result", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_edge_sad_noise_0": {"acc": [0, 8, 4], "size": [], "description": "edge noise level", "usage": "larger value means stronger noise level and reduce directional filter weight", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "shp_nr_ud_texture_scale_0": {"acc": [0, 0, 8], "size": [], "description": "scaling factor for undirectional nr's texture map", "usage": "larger value means more like a texture pixel (not noise pixel), and reduces undirectional nr strength", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_nr_ud_texture_offset_0": {"acc": [0, 0, 8], "size": [], "description": "coring offset for undirectional nr's texture map", "usage": "larger value means stronger coring on texutre map, and increases undirectional nr strength", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_nr_ud_limit_0": {"acc": [0, 8, 2], "size": [], "description": "undirectional noise limit", "usage": "larger value means less limitation for calculated undirectional noise", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "shp_nr_dir_texture_scale_0": {"acc": [0, 0, 8], "size": [], "description": "scaling factor for directional nr's texture map", "usage": "larger value means more like a edge pixel (not noise pixel), and reduces directional nr strength", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_nr_dir_texture_offset_0": {"acc": [0, 4, 4], "size": [], "description": "coring offset for directional nr's texture map", "usage": "larger value means stronger coring on texutre map, and increases directional nr strength", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_nr_dir_limit_0": {"acc": [0, 8, 2], "size": [], "description": "directional noise limit", "usage": "larger value means less limitation for calculated directional noise", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "shp_nr_level_0": {"acc": [0, 1, 5], "size": [], "description": "noise reduction level", "usage": "larger value means stronger noise reduction", "constraints": "shp_nr_level <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_coring_ud_level_0": {"acc": [0, 3, 3], "size": [], "description": "undirectional sharpen coring level", "usage": "larger value means stronger coring, and reduces undirectional sharpen strength", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_coring_dir_level_0": {"acc": [0, 3, 3], "size": [], "description": "directional sharpen coring level", "usage": "larger value means stronger coring, and reduces directional sharpen strength", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_shoot_ref_ratio_0": {"acc": [0, 1, 5], "size": [], "description": "the blending ratio of original pixel and local min max to generate shoot reference", "usage": "larger value means to use more local min max value for shoot reference, and leads to stronger shoot", "constraints": "shp_shoot_ref_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_overshoot_0": {"acc": [0, 0, 7], "size": [], "description": "global overshoot level", "usage": "larger value means stronger overshoot", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_undershoot_0": {"acc": [0, 0, 7], "size": [], "description": "global undershoot level", "usage": "larger value means stronger undershoot", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_luma_mask_enable_0": {"acc": [0, 1], "size": [], "description": "luma mask enable, 0: disable, 1: enable", "usage": "", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_luma_ref_select_0": {"acc": [0, 1], "size": [], "description": "luma mask reference selection, 0: blurred Y, 1: denoised Y", "usage": "usually set to 0", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_luma_gain_lut_0": {"acc": [0, 1, 5], "size": [2, 33], "description": "luma gain lut, [0]: negative, [1]: positive", "usage": "usually set lower gain for dark region to avoid enhancing noises", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_detail_adj_enable_0": {"acc": [0, 1], "size": [], "description": "detail enhancement enable, 0: disable, 1: enable", "usage": "", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_detail_texture_shift_0": {"acc": [0, 3], "size": [], "description": "texture map shift bits for detail region", "usage": "smaller value means detail region will have more flat or weak texture pixels", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_detail_overshoot_slope_0": {"acc": [1, 0, 9], "size": [], "description": "detail overshoot slope", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "shp_detail_overshoot_offset_0": {"acc": [1, 4, 7], "size": [], "description": "detail overshoot offset", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "shp_detail_overshoot_limit_0": {"acc": [0, 0, 7], "size": [2], "description": "detail overshoot limit", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_detail_undershoot_slope_0": {"acc": [1, 0, 9], "size": [], "description": "detail undershoot slope", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "shp_detail_undershoot_offset_0": {"acc": [1, 4, 7], "size": [], "description": "detail undershoot offset", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "shp_detail_undershoot_limit_0": {"acc": [0, 0, 7], "size": [2], "description": "detail undershoot limit", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_enable_0": {"acc": [0, 1], "size": [], "description": "shoot suppression enable, 0: disable, 1: enable", "usage": "", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_blend_ratio_0": {"acc": [0, 1, 4], "size": [], "description": "the blending ratio of two shoot control strategy", "usage": "larger value means less shoot control for texture region while keeping shoot control for strong edges", "constraints": "shp_shoot_sup_blend_ratio <= 1.0", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_var_min_slope_0": {"acc": [0, 0, 9], "size": [], "description": "shoot control (by min variance) curve slope", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "shp_shoot_sup_var_min_offset_0": {"acc": [1, 4, 7], "size": [], "description": "shoot control (by min variance) curve offset", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "shp_shoot_sup_var_min_limit_0": {"acc": [0, 0, 7], "size": [2], "description": "shoot control (by min variance) curve limit", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_shoot_sup_var_diff_slope_0": {"acc": [1, 0, 9], "size": [], "description": "shoot control (by variance difference) curve slope", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "shp_shoot_sup_var_diff_offset_0": {"acc": [0, 4, 7], "size": [], "description": "shoot control (by variance difference) curve offset", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "shp_shoot_sup_var_diff_limit_0": {"acc": [0, 0, 7], "size": [2], "description": "shoot control (by variance difference) curve limit", "usage": "automatically calculated in algo logic", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_limit_sup_enable_0": {"acc": [0, 1], "size": [], "description": "adaptive limit control enable, 0: disable, 1: enable", "usage": "", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "shp_limit_sup_max_over_change_0": {"acc": [0, 8, 2], "size": [], "description": "adaptive limit control max over change", "usage": "larger value means less limitation for pixel value over change", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "shp_limit_sup_max_under_change_0": {"acc": [0, 8, 2], "size": [], "description": "adaptive limit control max under change", "usage": "larger value means less limitation for pixel value under change", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "shp_limit_sup_max_over_gain_0": {"acc": [0, 5, 5], "size": [], "description": "adaptive limit control over change gain", "usage": "larger value means less limitation for pixel value over change", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "shp_limit_sup_max_under_gain_0": {"acc": [0, 5, 5], "size": [], "description": "adaptive limit control under change gain", "usage": "larger value means less limitation for pixel value under change", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_enable_0": {"acc": [0, 1], "size": [16], "description": "Rectangles' enable control bits", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_tl_coord_x_0": {"acc": [0, 13], "size": [16], "description": "top-left x coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_tl_coord_y_0": {"acc": [0, 13], "size": [16], "description": "top-left y coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_br_coord_x_0": {"acc": [0, 13], "size": [16], "description": "bottom-right x coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_br_coord_y_0": {"acc": [0, 13], "size": [16], "description": "bottom-right y coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_edge_thickness_0": {"acc": [0, 8], "size": [], "description": "Rectangles' edge thickness", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_edge_color_select_0": {"acc": [0, 2], "size": [16], "description": "Rectangles' color select", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_edge_color_val_0": {"acc": [0, 8], "size": [4, 4], "description": "Rectangles' value to be selected", "usage": "[m][n], m, 4 different kinds of colors. n, 0:y, 1:u, 2:v, 3:alpha", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "roi_luma_sum_tl_coords_0": {"acc": [0, 12, 0], "size": [8, 2], "description": "top-left corner coordinates of 8 roi areas", "usage": "roi_luma_sum_tl_coords[n][0] - nth roi area's y coordinate; roi_luma_sum_tl_coords[n][1] - nth roi area's x coordinate", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "roi_luma_sum_br_coords_0": {"acc": [0, 12, 0], "size": [8, 2], "description": "bottom-right corner coordinates of 8 roi areas", "usage": "roi_luma_sum_br_coords[n][0] - nth roi area's y coordinate; roi_luma_sum_br_coords[n][1] - nth roi area's x coordinate", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "osd_enable_0": {"acc": [0, 1], "size": [], "description": "osd enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "osd_bitmap_color_palette_0": {"osd_bitmap_color_palette_alpha_0": {"acc": [0, 8], "size": [256], "description": "Alpha value palette for pic whose format is BITMAP.", "usage": "Effective palette array length depends on BITMAP bit-width", "constraints": "N/A"}, "osd_bitmap_color_palette_luma_0": {"acc": [0, 8], "size": [256], "description": "Luma value palette for pic whose format is BITMAP.", "usage": "Effective palette array length depends on BITMAP bit-width", "constraints": "N/A"}, "osd_bitmap_color_palette_chroma_0": {"acc": [0, 8], "size": [2, 256], "description": "Chroma value(with 128 bias) palette for pic whose format is BITMAP.", "usage": "Effective palette array length depends on BITMAP bit-width; g0_color_palette_chroma[0][x] is U in YUV; g0_color_palette_chroma[1][x] is V in YUV.", "constraints": "N/A"}}, "osd_tl_coord_x_0": {"acc": [0, 13], "size": [], "description": "osd blending top-left coord(x)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "osd_br_coord_x_0": {"acc": [0, 13], "size": [], "description": "osd blending bottom-right coord(x)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "osd_tl_coord_y_0": {"acc": [0, 13], "size": [4], "description": "4 groups of osd blending top-left coord(y)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "osd_br_coord_y_0": {"acc": [0, 13], "size": [4], "description": "4 groups of osd blending bottom-right coord(y)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "yclip_enable_0": {"acc": [0, 1], "size": [], "description": "Luma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_offset_0": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_gain_0": {"acc": [1, 4, 8], "size": [], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "yclip_boundary_0": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_enable_0": {"acc": [0, 1], "size": [], "description": "Chroma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_cmtx_0": {"acc": [1, 2, 5], "size": [2, 2], "description": "2x2 chroma adjustment matrix", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S8", "partition": "-"}, "cclip_boundary_0": {"acc": [1, 8], "size": [2], "description": "2x2 chroma adjustment --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "crop_enable_0": {"acc": [0, 1], "size": [], "description": "Crop enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "crop_start_0": {"acc": [0, 13], "size": [2], "description": "Crop start coordinates", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "crop_size_0": {"acc": [0, 13], "size": [2], "description": "Crop size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "lba_enable_0": {"acc": [0, 1], "size": [], "description": "lba enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "lba_exd_size_0": {"acc": [0, 13], "size": [4], "description": "lba extension size(paddings on edges)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "lba_background_0": {"acc": [1, 8], "size": [3], "description": "lba background color", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "scale_input_mux_1": {"acc": [0, 1], "size": [], "description": "Choose the input for scalar", "usage": "0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_enable_1": {"acc": [0, 1], "size": [], "description": "Scalar enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_step_1": {"acc": [0, 6, 10], "size": [2], "description": "Scalar factor", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_output_size_1": {"acc": [0, 13], "size": [2], "description": "Scalar output size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_offset_luma_1": {"acc": [1, 13, 10], "size": [2], "description": "src_coords = dst_coords * scale_factor + offset", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "scale_offset_chroma_1": {"acc": [1, 13, 10], "size": [2], "description": "src_coords = dst_coords * scale_factor + offset", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "rect_enable_1": {"acc": [0, 1], "size": [16], "description": "Rectangles' enable control bits", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_tl_coord_x_1": {"acc": [0, 13], "size": [16], "description": "top-left x coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_tl_coord_y_1": {"acc": [0, 13], "size": [16], "description": "top-left y coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_br_coord_x_1": {"acc": [0, 13], "size": [16], "description": "bottom-right x coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_br_coord_y_1": {"acc": [0, 13], "size": [16], "description": "bottom-right y coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_edge_thickness_1": {"acc": [0, 8], "size": [], "description": "Rectangles' edge thickness", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_edge_color_select_1": {"acc": [0, 2], "size": [16], "description": "Rectangles' color select", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_edge_color_val_1": {"acc": [0, 8], "size": [4, 4], "description": "Rectangles' value to be selected", "usage": "[m][n], m, 4 different kinds of colors. n, 0:y, 1:u, 2:v, 3:alpha", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_enable_1": {"acc": [0, 1], "size": [], "description": "Luma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_offset_1": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_gain_1": {"acc": [1, 4, 8], "size": [], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "yclip_boundary_1": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_enable_1": {"acc": [0, 1], "size": [], "description": "Chroma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_cmtx_1": {"acc": [1, 2, 5], "size": [2, 2], "description": "2x2 chroma adjustment matrix", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S8", "partition": "-"}, "cclip_boundary_1": {"acc": [1, 8], "size": [2], "description": "2x2 chroma adjustment --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "crop_enable_1": {"acc": [0, 1], "size": [], "description": "Crop enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "crop_start_1": {"acc": [0, 13], "size": [2], "description": "Crop start coordinates", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "crop_size_1": {"acc": [0, 13], "size": [2], "description": "Crop size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "lba_enable_1": {"acc": [0, 1], "size": [], "description": "lba enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "lba_exd_size_1": {"acc": [0, 13], "size": [4], "description": "lba extension size(paddings on edges)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "lba_background_1": {"acc": [1, 8], "size": [3], "description": "lba background color", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "scale_input_mux_2": {"acc": [0, 1], "size": [], "description": "Choose the input for scalar", "usage": "0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_enable_2": {"acc": [0, 1], "size": [], "description": "Scalar enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_step_2": {"acc": [0, 6, 10], "size": [2], "description": "Scalar factor", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_output_size_2": {"acc": [0, 13], "size": [2], "description": "Scalar output size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_offset_luma_2": {"acc": [1, 13, 6], "size": [2], "description": "Luma channel initial phase", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "scale_offset_chroma_2": {"acc": [1, 13, 6], "size": [2], "description": "Chroma channel initial phase", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "scale_filter_x_luma_2": {"acc": [1, 1, 8], "size": [8, 5], "description": "8-phase filtering weights for LUMA channel horizontal direction", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "scale_filter_y_luma_2": {"acc": [1, 1, 8], "size": [8, 5], "description": "8-phase filtering weights for LUMA channel vertical direction", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "scale_filter_x_chroma_2": {"acc": [1, 1, 8], "size": [8, 3], "description": "8-phase filtering weights for LUMA channel horizontal direction", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "scale_filter_y_chroma_2": {"acc": [1, 1, 8], "size": [8, 3], "description": "8-phase filtering weights for LUMA channel vertical direction", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "rect_enable_2": {"acc": [0, 1], "size": [16], "description": "Rectangles' enable control bits", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_tl_coord_x_2": {"acc": [0, 13], "size": [16], "description": "top-left x coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_tl_coord_y_2": {"acc": [0, 13], "size": [16], "description": "top-left y coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_br_coord_x_2": {"acc": [0, 13], "size": [16], "description": "bottom-right x coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_br_coord_y_2": {"acc": [0, 13], "size": [16], "description": "bottom-right y coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_edge_thickness_2": {"acc": [0, 8], "size": [], "description": "Rectangles' edge thickness", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_edge_color_select_2": {"acc": [0, 2], "size": [16], "description": "Rectangles' color select", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_edge_color_val_2": {"acc": [0, 8], "size": [4, 4], "description": "Rectangles' value to be selected", "usage": "[m][n], m, 4 different kinds of colors. n, 0:y, 1:u, 2:v, 3:alpha", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_enable_2": {"acc": [0, 1], "size": [], "description": "Luma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_offset_2": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_gain_2": {"acc": [1, 4, 8], "size": [], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "yclip_boundary_2": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_enable_2": {"acc": [0, 1], "size": [], "description": "Chroma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_cmtx_2": {"acc": [1, 2, 5], "size": [2, 2], "description": "2x2 chroma adjustment matrix", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S8", "partition": "-"}, "cclip_boundary_2": {"acc": [1, 8], "size": [2], "description": "2x2 chroma adjustment --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "crop_enable_2": {"acc": [0, 1], "size": [], "description": "Crop enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "crop_start_2": {"acc": [0, 13], "size": [2], "description": "Crop start coordinates", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "crop_size_2": {"acc": [0, 13], "size": [2], "description": "Crop size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "lba_enable_2": {"acc": [0, 1], "size": [], "description": "lba enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "lba_exd_size_2": {"acc": [0, 13], "size": [4], "description": "lba extension size(paddings on edges)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "lba_background_2": {"acc": [1, 8], "size": [3], "description": "lba background color", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "scale_input_mux_3": {"acc": [0, 1], "size": [], "description": "Choose the input for scalar", "usage": "0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_enable_3": {"acc": [0, 1], "size": [], "description": "Scalar enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_step_3": {"acc": [0, 6, 10], "size": [2], "description": "Scalar factor", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_output_size_3": {"acc": [0, 13], "size": [2], "description": "Scalar output size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_offset_luma_3": {"acc": [1, 13, 10], "size": [2], "description": "src_coords = dst_coords * scale_factor + offset", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "scale_offset_chroma_3": {"acc": [1, 13, 10], "size": [2], "description": "src_coords = dst_coords * scale_factor + offset", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "rect_enable_3": {"acc": [0, 1], "size": [16], "description": "Rectangles' enable control bits", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_tl_coord_x_3": {"acc": [0, 13], "size": [16], "description": "top-left x coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_tl_coord_y_3": {"acc": [0, 13], "size": [16], "description": "top-left y coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_br_coord_x_3": {"acc": [0, 13], "size": [16], "description": "bottom-right x coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_br_coord_y_3": {"acc": [0, 13], "size": [16], "description": "bottom-right y coordinates of rectangles", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "rect_edge_thickness_3": {"acc": [0, 8], "size": [], "description": "Rectangles' edge thickness", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_edge_color_select_3": {"acc": [0, 2], "size": [16], "description": "Rectangles' color select", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "rect_edge_color_val_3": {"acc": [0, 8], "size": [4, 4], "description": "Rectangles' value to be selected", "usage": "[m][n], m, 4 different kinds of colors. n, 0:y, 1:u, 2:v, 3:alpha", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_enable_3": {"acc": [0, 1], "size": [], "description": "Luma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_offset_3": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_gain_3": {"acc": [1, 4, 8], "size": [], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "yclip_boundary_3": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_enable_3": {"acc": [0, 1], "size": [], "description": "Chroma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_cmtx_3": {"acc": [1, 2, 5], "size": [2, 2], "description": "2x2 chroma adjustment matrix", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S8", "partition": "-"}, "cclip_boundary_3": {"acc": [1, 8], "size": [2], "description": "2x2 chroma adjustment --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "crop_enable_3": {"acc": [0, 1], "size": [], "description": "Crop enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "crop_start_3": {"acc": [0, 13], "size": [2], "description": "Crop start coordinates", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "crop_size_3": {"acc": [0, 13], "size": [2], "description": "Crop size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "lba_enable_3": {"acc": [0, 1], "size": [], "description": "lba enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "lba_exd_size_3": {"acc": [0, 13], "size": [4], "description": "lba extension size(paddings on edges)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "lba_background_3": {"acc": [1, 8], "size": [3], "description": "lba background color", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "scale_input_mux_4": {"acc": [0, 1], "size": [], "description": "Choose the input for scalar", "usage": "0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_enable_4": {"acc": [0, 1], "size": [], "description": "Scalar enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_step_4": {"acc": [0, 6, 10], "size": [2], "description": "Scalar factor", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_output_size_4": {"acc": [0, 13], "size": [2], "description": "Scalar output size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_offset_luma_4": {"acc": [1, 13, 10], "size": [2], "description": "src_coords = dst_coords * scale_factor + offset", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "scale_offset_chroma_4": {"acc": [1, 13, 10], "size": [2], "description": "src_coords = dst_coords * scale_factor + offset", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "yclip_enable_4": {"acc": [0, 1], "size": [], "description": "Luma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_offset_4": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_gain_4": {"acc": [1, 4, 8], "size": [], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "yclip_boundary_4": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_enable_4": {"acc": [0, 1], "size": [], "description": "Chroma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_cmtx_4": {"acc": [1, 2, 5], "size": [2, 2], "description": "2x2 chroma adjustment matrix", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S8", "partition": "-"}, "cclip_boundary_4": {"acc": [1, 8], "size": [2], "description": "2x2 chroma adjustment --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "scale_input_mux_5": {"acc": [0, 1], "size": [], "description": "Choose the input for scalar", "usage": "0 - choose stream pic as mux output; 1 - choose previous scalar output as mux output", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_enable_5": {"acc": [0, 1], "size": [], "description": "Scalar enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "scale_step_5": {"acc": [0, 6, 10], "size": [2], "description": "Scalar factor", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_output_size_5": {"acc": [0, 13], "size": [2], "description": "Scalar output size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "scale_offset_luma_5": {"acc": [1, 13, 10], "size": [2], "description": "src_coords = dst_coords * scale_factor + offset", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "scale_offset_chroma_5": {"acc": [1, 13, 10], "size": [2], "description": "src_coords = dst_coords * scale_factor + offset", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S32", "partition": "-"}, "yclip_enable_5": {"acc": [0, 1], "size": [], "description": "Luma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_offset_5": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "yclip_gain_5": {"acc": [1, 4, 8], "size": [], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "yclip_boundary_5": {"acc": [0, 8], "size": [2], "description": "-offset[0] --> x gain --> +offset[1] --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_enable_5": {"acc": [0, 1], "size": [], "description": "Chroma clip enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "cclip_cmtx_5": {"acc": [1, 2, 5], "size": [2, 2], "description": "2x2 chroma adjustment matrix", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S8", "partition": "-"}, "cclip_boundary_5": {"acc": [1, 8], "size": [2], "description": "2x2 chroma adjustment --> clip", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "crop_enable_5": {"acc": [0, 1], "size": [], "description": "Crop enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "crop_start_5": {"acc": [0, 13], "size": [2], "description": "Crop start coordinates", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "crop_size_5": {"acc": [0, 13], "size": [2], "description": "Crop size", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "lba_enable_5": {"acc": [0, 1], "size": [], "description": "lba enable control bit", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "lba_exd_size_5": {"acc": [0, 13], "size": [4], "description": "lba extension size(paddings on edges)", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "lba_background_5": {"acc": [1, 8], "size": [3], "description": "lba background color", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "csc_yuvTrgb_matrix_5": {"acc": [1, 2, 8], "size": [3, 3], "description": "", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "csc_yuvTrgb_offset_in_5": {"acc": [1, 8], "size": [3], "description": "YUV to RGB conversion offset in.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "csc_yuvTrgb_offset_out_5": {"acc": [1, 8], "size": [3], "description": "YUV to RGB conversion offset out.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "csc_gamma_lut_5": {"acc": [0, 8, 4], "size": [33], "description": "To LAB csc gamma lut", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "csc_gamma_lut_mode_5": {"acc": [0, 1], "size": [], "description": "0 - linear lut; 1 - log lut", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}, "csc_gamma_offset_out_5": {"acc": [1, 8], "size": [], "description": "gamma offset out.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "csc_rgbTxyz_matrix_5": {"acc": [1, 2, 13], "size": [3, 3], "description": "", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "csc_rgbTxyz_offset_out_5": {"acc": [1, 8], "size": [3], "description": "rgb2xyz offset out.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "csc_xyzTlab_cbrt_lut_5": {"acc": [0, 0, 16], "size": [129], "description": "xyz2lab cube root lut.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U16", "partition": "-"}, "csc_xyzTlab_gain_out_5": {"acc": [1, 3, 12], "size": [3], "description": "xyz2lab gain out.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "csc_xyzTlab_offset_out_5": {"acc": [1, 8], "size": [3], "description": "xyz2lab offset out.", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_S16", "partition": "-"}, "output_format": {"acc": [0, 6], "size": [6], "description": "Output formats", "usage": "Set to proper value", "constraints": "N/A", "type": "AX_U8", "partition": "-"}}}, "partition_configs": []}